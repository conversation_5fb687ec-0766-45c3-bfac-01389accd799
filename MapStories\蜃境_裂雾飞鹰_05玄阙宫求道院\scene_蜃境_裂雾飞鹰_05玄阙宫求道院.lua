
---@type 蜃境_裂雾飞鹰_05玄阙宫求道院
local context = require("MapStories/蜃境_裂雾飞鹰_05玄阙宫求道院/scene_蜃境_裂雾飞鹰_05玄阙宫求道院_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_裂雾飞鹰_05玄阙宫求道院
local flags = context.flags
--不可省略，用于外部获取flags

local newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")



function getFlags(...)
    return flags
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function start()
    print("start called")
    refreshMapStates()
    s.readyToStart(true)
    try_execute_newbieGuide()
end

function try_execute_newbieGuide(...)
    s.nextStep("newbieGuideInLevel")
end


function newbieGuideInLevel()end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.lwfy_15, false)
    s.setActive(npcs.lwfy_16, false)
    s.setActive(npcs.lwfy_17, false)
    s.setActive(npcs.lwfy_18, false)
    s.setActive(npcs.lwfy_19, false)

    -- s.setActive(npcs.enemy_1, flags.enemy_1 == 0)
    -- s.setActive(npcs.enemy_2, flags.enemy_2 == 0)
    -- s.setActive(npcs.enemy_3, flags.enemy_3 == 0)
    -- s.setActive(npcs.enemy_4, flags.enemy_4 == 0)
    -- s.setActive(npcs.enemy_5, flags.enemy_5 == 0)
    -- s.setActive(npcs.boss_6, flags.boss_6 == 0)

    s.setActive(objs.lwfy_1005, not s.hasUnlockMemorySlot("lwfy_1005"))
    s.setActive(objs.lwfy_1006, not s.hasUnlockMemorySlot("lwfy_1006"))
    s.setActive(objs.lwfy_1007, not s.hasUnlockMemorySlot("lwfy_1007"))

    s.setActive(npcs.yuqiong, (flags.alyd_step < 3) and s.hasUnlockMemorySlot("lwfy_sideTask_alyd"))
    s.setActive(npcs.jiguanshou,flags.alyd_step == 1)

    s.removeAllGameMapGuides()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 6) then
        taskStep = "forwardToExplore"
    elseif (step == 6) then
        taskStep = "leaveXuanQueQiuDaoYuan"
    end
    s.setTaskStep("裂雾飞鹰玄阙宫求道院", taskStep)

    print("refreshMapStates level step = " .. tostring(step))
    reset_all()

    
    if step == 0 then
        ----第一次进入场景, 这里加入入场演出
        s.setTaskStep("裂雾飞鹰玄阙宫求道院","forwardToExplore")

        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.lwfy_15, 0.8)
    elseif step == 2 then
        level_guide_to(npcs.lwfy_16, 0.8)
    elseif step == 3 then
        level_guide_to(npcs.lwfy_17, 0.8)
    elseif step == 4 then
        level_guide_to(npcs.lwfy_18, 0.8)
    elseif step == 5 then
        level_guide_to(npcs.lwfy_19, 0.8)
    else
        --do nothing
        level_guide_to(objs.exit)
    end

    if(flags.sideTask_HuHua_complete==1)then
        complete_sideTask_HuHua()
    end
end

----------------------OVERRIDE----------------------------------

function lwfy_01_test()
    flags.lwfy_15_playing = 0
    flags.level_step = 1
    refreshMapStates()
end

function lwfy_15()
    executeMemSlot("lwfy_15", quick_play_avg)
end

function lwfy_16()
    executeMemSlot("lwfy_16", quick_play_avg)
end

function lwfy_17()
    executeMemSlot("lwfy_17", quick_play_avg)
end

function lwfy_18()
    executeMemSlot("lwfy_18", quick_play_avg)
end

function lwfy_19()
    executeMemSlot("lwfy_19", quick_play_avg,function()
    s.setTaskStep("裂雾飞鹰玄阙宫求道院","leaveXuanQueQiuDaoYuan")
    next_level_step(true)
    end)
end

function exitScene()
    print("离开场景")
    capi.exitMemoryScene("裂雾飞鹰")
end

function fly1()
    s.setPos(npcs.zhujue,pos.fly_pos_1)
end

function fly2()
    s.setPos(npcs.zhujue,pos.fly_pos_2)
end

function sideTask_HuHua_hint()
    local ret = s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_05玄阙宫求道院", "蜃境_裂雾飞鹰_05玄阙宫求道院_护花_1", nil, 0)
    local isWin, battleResult = s.battle("蜃境_裂雾飞鹰5_护花_打玄阙宫弟子们")
    if isWin then
        if(ret == 1)then
            s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_05玄阙宫求道院", "蜃境_裂雾飞鹰_05玄阙宫求道院_护花_1_帮助护花少年", nil, 0)
        elseif(ret == 2)then
            s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_05玄阙宫求道院", "蜃境_裂雾飞鹰_05玄阙宫求道院_护花_1_帮助佝偻老仆", nil, 0)
        end
        isHuHuaTask()
    else
        if not s.hasTask(40052006,10)then
            s.setTaskStep("护花","talkHuHua")
        end
    end
end

function complete_sideTask_HuHua(...)
    s.setActive(objs.sideTask_HuHua_hint,false)
    s.setActive(npcs.npc_1,false)
    s.setActive(npcs.npc_2,false)
end

function isHuHuaTask(...)
    capi.finishMemorySlot("lwfy_sideTask_hh")

    flags.sideTask_HuHua_complete = 1
    if s.hasTask(40052006,10) then
        s.finishTask(40052006,10)
    end

    complete_sideTask_HuHua()
end

function test()
    capi.finishMemorySlot("lwfy_sideTask_alyd2")
    s.setTaskStep("暗流涌动3","active")
end

function alyd()
    if flags.alyd_step == 0 then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_05玄阙宫求道院", "蜃境_裂雾飞鹰_05玄阙宫求道院_暗流涌动_1", nil, 0)
        s.setTaskStep("暗流涌动3","battleJiGuanShou")
        flags.alyd_step = 1
        s.setActive(npcs.jiguanshou,true)
    elseif flags.alyd_step == 1 then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_05玄阙宫求道院", "重复", nil, 0)
    else
        flags.alyd_step = 3
        s.finishTask(40052003,"20")
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_05玄阙宫求道院", "蜃境_裂雾飞鹰_05玄阙宫求道院_暗流涌动_4", nil, 0)
        s.blackScreen()
        s.setActive(npcs.yuqiong,false)
        s.wait(0.5)
        s.lightScreen()
        capi.finishMemorySlot("lwfy_sideTask_alyd3")
    end
end

function alyd2()
    s.soloAnimState(npcs.jiguanshou, "Special_1", true)
    s.soloAnimState(npcs.zhujue,"BFight_Loop",true)
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_05玄阙宫求道院", "蜃境_裂雾飞鹰_05玄阙宫求道院_暗流涌动_2", nil, 0)
    s.soloAnimState(npcs.zhujue,"BFight_Loop",false)
    local isWin = s.battle("蜃境_裂雾飞鹰_05玄阙宫求道院")
    if isWin then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_05玄阙宫求道院", "蜃境_裂雾飞鹰_05玄阙宫求道院_暗流涌动_3", nil, 0)
        s.setTaskStep("暗流涌动3","backToYuQiong")
        s.setDeath(npcs.jiguanshou)
        flags.alyd_step = 2
    end
end