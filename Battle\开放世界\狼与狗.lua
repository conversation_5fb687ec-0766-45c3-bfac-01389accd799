local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    -- 开局创建角色
    createRoles()
end

function start()
    --0秒时开始执行剧情，不然小狼会漂移，猜测大概是索敌逻辑的缘故
    bcapi.add_timer_trigger(0, startTalk)
    --增添战败的触发器
    bcapi.add_condition_trigger("[%teammate:世界_小女孩狼->hp%][<]1", 0, lose)
    --添加头狼被激活的触发器
    bcapi.add_condition_trigger("[%enemy:世界_狼与狗_头狼->flag:召唤雪狼%][>]0", 0, enemyActive)
end

function createRoles()
    bcapi.async_call(function()
        --创建需要保护的小狼
        local girl = bcapi.create_join_role("世界_小女孩狼", 35, team, bcapi.Vector2(-6, 0), 0)
        --扣除小狼部分的生命，符合剧情设定
        bcapi.change_role_hp(girl, -4500)
        --给敌方BOSS增添一个观望状态，避免其行动
        local enemyBoss = bcapi.get_role(1,"世界_狼与狗_头狼")
        enemyBoss.Buff:AddBuff("世界_观望", 300, 1, 1, enemyBoss)
        setLockEnemyToGirl(enemyBoss,girl)
    end)
end

function alleyActive()
    bcapi.async_call(function()
        bcapi.show_pop_info("小狼得到了暂时的救治，重新投入了战斗！", 3)
    end)
end

function enemyActive()
    bcapi.async_call(function()
        --召唤4只雪狼
        local wolf1 = bcapi.create_join_role("通用_动物_小怪_狼_狼攻击", 35, eTeam, bcapi.Vector2(-6, 4), 0)
        local wolf2 = bcapi.create_join_role("通用_动物_小怪_狼_狼攻击", 35, eTeam, bcapi.Vector2(-6, -4), 0)
        local wolf3 = bcapi.create_join_role("通用_动物_小怪_狼_狼攻击", 35, eTeam, bcapi.Vector2(6, 4), 0)
        local wolf4 = bcapi.create_join_role("通用_动物_小怪_狼_狼攻击", 35, eTeam, bcapi.Vector2(6, -4), 0)

        local girl = bcapi.get_role(0,"世界_小女孩狼")
        setLockEnemyToGirl(wolf1,girl)
        setLockEnemyToGirl(wolf2,girl)
        setLockEnemyToGirl(wolf3,girl)
        setLockEnemyToGirl(wolf4,girl)

        bcapi.wait(1)
        bcapi.pause_and_hide_ui()
        bcapi.talk("主角", "狼群首领被激怒了，接下来恐怕是一场恶战！")
        bcapi.resume_and_show_ui()
    end)
end

function setLockEnemyToGirl(role1,role2)
    role1.AI:ClearLockedEnemy(true);
    role1.AI:SetForceLockEnemy(role2, 6, 99);
end

function startTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("世界_小女孩狼", "嗷呜……")
        local girl = bcapi.get_role(0,"世界_小女孩狼")
        girl.Buff:AddBuff("世界_恐惧", 300, 1, 1, girl)
        bcapi.talk("主角", "这条小狼伤得很重，不能再让它受到进一步的伤害了。")
        bcapi.talk("主角", "对面的狼群首领似乎还在观望，先不要激怒它，专注解决眼前的狼群吧。")
        bcapi.resume_and_show_ui()
        bcapi.show_pop_info("小狼被击倒时，战斗将会失败！", 4)
        bcapi.add_condition_trigger("[%teammate:世界_小女孩狼->buff_lv:世界_恐惧%][<]1", 0, alleyActive)
    end)
end

function lose()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("主角", "不好，没有保护好小狼……")
        bcapi.resume_and_show_ui()
        bcapi.lose()
    end)
end
