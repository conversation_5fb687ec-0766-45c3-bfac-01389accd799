local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)
local show = 1

function init()

end

function start()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        bcapi.add_condition_trigger("[%enemy:世界_贼寇山洞_土老大->flag:土老大表演二阶段%][>]0", 1, escapeTalk)
        -- 一定时间后召唤金燕桃登场
        bcapi.add_timer_trigger(5, createRole)
        -- 为保险起见，BOSS一定血量要提前召唤
        bcapi.add_condition_trigger("[%enemy:世界_贼寇山洞_土老大->hp_pct%][<=]0.2", 0.1, createRole)
    end)
end

function createRole()
    print("创建角色")
    bcapi.async_call(function()
        if (show == 1) then
            show = 0 
            bcapi.create_join_role("世界_剧情侠客_金燕桃", 20, team, bcapi.Vector2(-8, 3), 0)
            local npc = bcapi.get_role(0, "世界_剧情侠客_金燕桃")
            print(npc)
            bcapi.add_talent(npc,"世界_蜀境迷踪_金燕桃_触发器")
            -- print("天赋加上了")
            bcapi.wait(0.5)
            jintantaoTalk()
            bcapi.add_condition_trigger("[%c->fighting_friend_count%][<=]1", 0.1, lose)
        end
    end)
end

function jintantaoTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("金燕桃", "以多欺少，算什么英雄好汉？")
        bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "哪来的臭丫头，穿这么娇艳，唱戏啊！")
        bcapi.talk("金燕桃", "姑奶奶大名金燕桃，你最好记住了，阎王爷问你死因的时候，得把姑奶奶名字报得响亮点！")
        bcapi.resume_and_show_ui()
        local npc = bcapi.get_role(0, "世界_剧情侠客_金燕桃")
        local Boss = bcapi.get_role(1, "世界_贼寇山洞_土老大")
        bcapi.use_skill_to_role(npc,"金燕桃_绝招",Boss,1)
        -- npc.Buff:AddBuff("世界_通用BUFF_状态_不移动不攻击", 3, 1, 1, npc)
        -- bcapi.add_timer_trigger(0.5, enemySkill)
    end)
end

function enemySkill(...)
    bcapi.async_call(function()
        local Boss = bcapi.get_role(1, "世界_贼寇山洞_土老大")
        local npc = bcapi.get_role(0, "世界_剧情侠客_金燕桃")
        bcapi.use_skill_to_role(Boss,"世界_贼寇山洞_土老大_技能3_表演调用",npc,1)
    end)
end

-- function EnemyEnhance()
--     bcapi.async_call(function()
--         bcapi.pause_and_hide_ui()
--         bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "你们……果然有些本事。")
--         bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "不过，也就到此为止了，素衣心经——起！")
--         local Boss = bcapi.get_role(1, "世界_贼寇山洞_土老大")
--         bcapi.resume_and_show_ui()
--         bcapi.use_skill_to_role(Boss,"副本_贼寇山洞_土老大_变身",Boss,1)
--     end)
-- end

-- function jinyantaoCheck()
--     bcapi.add_condition_trigger("[%c->teammate:金燕桃%][<]1", 0.1, jinyantaoSet)
-- end

-- function jinyantaoSet()
--     summon = 1
-- end

function escapeTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        -- bcapi.wait(1)
        -- if summon == 1 then
        --     bcapi.create_and_summon_role("金燕桃", 20, team, bcapi.Vector2(-7, 0), 0)
        --     bcapi.wait(1)
        -- end
        bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "留得青山在不怕没柴烧，后会无期！")
        bcapi.resume_and_show_ui()
        bcapi.add_timer_trigger(0.2, escapeSkill)
    end)
end

function escapeSkill(...)
    bcapi.async_call(function()
        local Boss = bcapi.get_role(1, "世界_贼寇山洞_土老大")
        bcapi.use_skill_to_role(Boss,"世界_蜀境迷踪_土老大逃跑",Boss,1)
        bcapi.add_timer_trigger(0.2, chasingTalk)
    end)
end



function chasingTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        -- bcapi.wait(1)
        bcapi.talk("金燕桃", "休走！")
        bcapi.talk("主角", "他往蜀中方向去了！不能让他跑了，追上他。")
        -- local npc = bcapi.get_role(0, "世界_剧情侠客_金燕桃")
        -- bcapi.resume_and_show_ui()
        -- bcapi.add_timer_trigger(0.2, chasingSkill)
        bcapi.wait(0.2)
        bcapi.win()
    end)
end

-- function chasingSkill(...)
--     bcapi.async_call(function()
--         local npc = bcapi.get_role(0, "世界_剧情侠客_金燕桃")
--         bcapi.use_skill_to_role(npc,"世界_蜀境迷踪_金燕桃追",npc,1)
--         bcapi.add_timer_trigger(0.2, winTalk)
--     end)
-- end


-- function winTalk()
--     bcapi.async_call(function()
--         bcapi.pause_and_hide_ui()
--         bcapi.talk("主角", "他往蜀中方向去了！不能让他跑了，追上他。")
--         bcapi.resume_and_show_ui()
--         bcapi.win()
--     end)
-- end

function lose(...)
    bcapi.async_call(function()
        bcapi.lose()
    end)
end

