local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

local level = 20

function init()
    bcapi.hide_ui("疗伤")
    bcapi.reset_limit_time(180)
    -- 开局创建角色
    createRoles()
end

function start()
    -- 1秒后开始第1次AVG
    bcapi.add_timer_trigger(0.1, startTalk)
    -- 每8秒，生成随机僵尸
    for i=0, 16 do
        bcapi.add_timer_trigger(8*(i+1), createRandomZombie)
    end
    --每20秒生成回血的僵尸
    for i=0, 5 do
        bcapi.add_timer_trigger(20*(i+1), createHealZombie)
    end
    --特殊胜利和失败
    bcapi.add_condition_trigger("[%battle_time%][>]179", 0, win)
    -- 不再需要特殊胜利，每次战斗都需要清理全部伥怪或存活到3分钟
    -- bcapi.add_timer_trigger(1, checkWin)
    bcapi.add_timer_trigger(1, checkLose)
end

-- 取玩家队伍存的字典，后面两个函数中用到
local dict = bcapi.get_team(0).ValueDict

function createRoles()
    bcapi.async_call(function()
        -- 创建玩家角色（不带绝招牌）
        bcapi.create_join_role("世界_保卫战村民", 20, team, bcapi.Vector2(-7, 0), 0)
        bcapi.create_join_role("世界_保卫战村民栅栏", 20, team, bcapi.Vector2(-1, 0), 0)
        bcapi.create_join_role("世界_保卫战村民栅栏", 20, team, bcapi.Vector2(-1, 2), 0)
        bcapi.create_join_role("世界_保卫战村民栅栏", 20, team, bcapi.Vector2(-1, -2), 0)

        --给村民激活flag（因为角色用的这个系列战斗的统一角色，方便一起调整数值，flag只在本场战斗激活）
        -- local coreRole = bcapi.get_role(team,"世界_保卫战村民")
        -- coreRole.Stat.BattleFlagDic:set_Item("给敌人上AOE减伤","1")
        -- coreRole.Stat.BattleFlagDic:set_Item("友军死亡给怒气","1")
        -- coreRole.Stat.BattleFlagDic:set_Item("敌军死亡给怒气","0")

        -- 带了四张默认的召唤生物牌，等级为玩家对应策略卡等级
        bcapi.add_strategy_card("神射手", getStrategyLevel("神射手等级"), team)
        bcapi.add_strategy_card("江湖刀客", getStrategyLevel("江湖刀客等级"), team)
        bcapi.add_strategy_card("叱咤狂熊", getStrategyLevel("叱咤狂熊等级"), team)
        bcapi.add_strategy_card("江湖刀客", getStrategyLevel("灵兽助阵等级"), team)
        bcapi.add_strategy_card("万钧重锤", getStrategyLevel("万钧重锤等级"), team)

    end)
end

--function ResetCardSkillParameter()
--准备拿来修改召唤物弓箭手的上限，暂时没用
---end

-- 随机怪物池(去掉了伥化老人)
local zombieTable = {"世界_保卫战伥化男子",
"世界_保卫战伥化喽啰",
"世界_保卫战伥化壮汉",
"世界_保卫战伥化女子",
"世界_保卫战伥化恶徒",
"世界_保卫战伥化普通门派弟子男",
"世界_保卫战伥化村姑",
"世界_保卫战伥化泼皮"}


--创建一个随机的怪物
function createRandomZombie()
    bcapi.async_call(function()
        --创建怪物前检查场上怪物数量
        if eTeam.FightingRoleCount < 6 then
            local index = math.random(1, #zombieTable)  
            bcapi.create_join_role(zombieTable[index], level, eTeam, bcapi.Vector2(7, 0), 0.1) 
        end
    end)
end

function createHealZombie()
    bcapi.async_call(function()
        --创建怪物前检查场上怪物数量
        if eTeam.FightingRoleCount < 6 then
            bcapi.create_join_role("世界_保卫战苗疆毒男", level, eTeam, bcapi.Vector2(7, -1), 0.1) 
        end
    end)
end

--取策略卡等级函数
function getStrategyLevel(cardLevelstr)
    local cardLevel = 1
    for k,v in pairs(dict) do
        if(k == cardLevelstr )then
            cardLevel = v
            break
        end
    end
    return cardLevel
end

function checkLose()
    bcapi.add_condition_trigger("[%c->teammate:世界_保卫战村民%][<]1", 0, lose)
end

-- function checkWin()
--     bcapi.add_condition_trigger("[%t->teammate:世界_保卫战精英伥怪%][<]1", 0, earlyWin)
-- end

function startTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("男村民_村庄保卫", "听说，如今的伥怪甚至学会了掩人耳目地前进。")
        bcapi.talk("主角", "嗯，我亦有耳闻，也有应对的办法。")
        bcapi.resume_and_show_ui()
    end)
end

function win()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("男村民_村庄保卫", "附近官兵的援军赶来了，看来终于是活下来了！")
        bcapi.win()
    end)
end

function earlyWin()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("男村民_村庄保卫", "我们击败了伥怪首领，我们活下来了！")
        bcapi.win()
    end)
end

function lose()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("男村民_村庄保卫", "我还有个三岁的孩子……")
        bcapi.talk("主角", "……")
        bcapi.resume_and_show_ui()
        bcapi.lose()
    end)
end

