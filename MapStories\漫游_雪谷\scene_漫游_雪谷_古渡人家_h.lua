--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/漫游_雪谷/flags_漫游_雪谷")


---@class 角色_漫游_雪谷_古渡人家
local npcs = {
    --- "贺老"
    helao = "角色/helao",
    --- "厨子马三"
    masan = "角色/masan",
    --- "渔夫阿飞"
    afei = "角色/afei",
    --- "织女小云"
    xiaoyun = "角色/xiaoyun",
    --- "大牛"
    daniu = "角色/daniu",
    --- "看守弟子"
    kanshou = "角色/kanshou",
    --- "墨七"
    moqi = "角色/moqi",
}

---@class 物体_漫游_雪谷_古渡人家
local objs = {
    --- "丢失的米袋"
    midai = "物体/midai",
    --- "初始剧情触发器"
    triggerOpening = "物体/triggerOpening",
    --- "琴谱"
    qinpu = "物体/qinpu",
    --- "朱雀心"
    zhuque = "物体/zhuque",
}

---@class 相机_漫游_雪谷_古渡人家
local cameras = {
}

---@class 位置_漫游_雪谷_古渡人家
local pos = {
    --- "通往流亡者营地"
    Yingdi = "位置/Yingdi",
}

---@class 资产_漫游_雪谷_古渡人家
local assets = {
}

---@class 动作_漫游_雪谷_古渡人家
local animationClips = {
}

---@class 漫游_雪谷_古渡人家
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
