local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    CreateRoles()
end

function start()
    Talk()
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("椿岁", 79, team, bcapi.Vector2(-2, 0), 0)
    end)
end

function Talk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("裘地仙", "你难道不渴望长生吗？")
        bcapi.talk("椿岁", "区区戏法，有什么好渴求的。")
        bcapi.talk("裘地仙", "哈哈哈哈……外邦人，如果是戏法，你何不一走了之？我知道，你对我还是有所期盼的……")
        bcapi.talk("裘地仙", "我们谈谈，如果我真有长生妙法，你又会怎么做呢？")
        bcapi.talk("椿岁", "要是你这样阴险恶毒、心狠手辣、色胆包天的渣滓都能活那么多年，那些平日里积德累善、从不为恶之人又怎么能……")
        bcapi.talk("椿岁", "嘁！")
        bcapi.talk("椿岁", "既然老天无眼，赐你长生，那我就替天正轨，送你归西！")
        bcapi.resume_and_show_ui()
    end)
end
