--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_鹿鸣天下_01诸葛乔居/flags_蜃境_鹿鸣天下_01诸葛乔居")


---@class 角色_蜃境_鹿鸣天下_01诸葛乔居
local npcs = {
    lmtx_01 = "角色/lmtx_01",
    lmtx_02 = "角色/lmtx_02",
    lmtx_03 = "角色/lmtx_03",
    lmtx_04 = "角色/lmtx_04",
    lmtx_05 = "角色/lmtx_05",
    enemy_1 = "角色/enemy_1",
    zhujue = "角色/zhujue",
    branchTask_Lu01 = "角色/branchTask_Lu01",
    branchTask_Lu02 = "角色/branchTask_Lu02",
    branchTask_Lu03 = "角色/branchTask_Lu03",
    branchTask_Qiao = "角色/branchTask_Qiao",
    branchTask_Lu04 = "角色/branchTask_Lu04",
    branchTask_Dizi = "角色/branchTask_Dizi",
    branchTask_Lu02_1 = "角色/branchTask_Lu02_1",
    branchTask_Lu01_1 = "角色/branchTask_Lu01_1",
    branchTask_Lu05 = "角色/branchTask_Lu05",
    enemy_Branchtask = "角色/enemy_Branchtask",
}

---@class 物体_蜃境_鹿鸣天下_01诸葛乔居
local objs = {
    exit = "物体/exit",
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
    Timeline02_box = "物体/Timeline02_box",
    Timeline03_box = "物体/Timeline03_box",
    branchTask_box = "物体/branchTask_box",
    branchTask_water = "物体/branchTask_water",
    lmtx_1001 = "物体/lmtx_1001",
    lmtx_1002 = "物体/lmtx_1002",
    branchTaskTrigger_2 = "物体/branchTaskTrigger_2",
}

---@class 相机_蜃境_鹿鸣天下_01诸葛乔居
local cameras = {
    camera_start = "相机/camera_start",
}

---@class 位置_蜃境_鹿鸣天下_01诸葛乔居
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
    zhujue_start_pos_1 = "位置/zhujue_start_pos_1",
    zhujue_bt_pos_1 = "位置/zhujue_bt_pos_1",
    branchTask_Lu02_pos_2 = "位置/branchTask_Lu02_pos_2",
    branchTask_Lu02_pos_1 = "位置/branchTask_Lu02_pos_1",
}

---@class 资产_蜃境_鹿鸣天下_01诸葛乔居
local assets = {
    timeline_01 = "资产/timeline_01",
    timeline_02 = "资产/timeline_02",
    timeline_03 = "资产/timeline_03",
    timeline_04 = "资产/timeline_04",
    timeline_02_NvZhu = "资产/timeline_02_NvZhu",
    timeline_01_NvZhu = "资产/timeline_01_NvZhu",
    timeline_05 = "资产/timeline_05",
    timeline_05_Nvzhu = "资产/timeline_05_Nvzhu",
}

---@class 动作_蜃境_鹿鸣天下_01诸葛乔居
local animationClips = {
}

---@class 蜃境_鹿鸣天下_01诸葛乔居
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
