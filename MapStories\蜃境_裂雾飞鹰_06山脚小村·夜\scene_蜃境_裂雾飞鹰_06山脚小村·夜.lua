
---@type 蜃境_裂雾飞鹰_06山脚小村·夜
local context = require("MapStories/蜃境_裂雾飞鹰_06山脚小村·夜/scene_蜃境_裂雾飞鹰_06山脚小村·夜_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_裂雾飞鹰_06山脚小村·夜
local flags = context.flags
--不可省略，用于外部获取flags

local newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")



function getFlags(...)
    return flags
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function start()
    print("start called")
    refreshMapStates()
    s.readyToStart(true)
    try_execute_newbieGuide()
end

function try_execute_newbieGuide(...)
    s.nextStep("newbieGuideInLevel")
end


function newbieGuideInLevel()end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点

    s.setActive(npcs.lwfy_20, false)
    s.setActive(npcs.lwfy_21, false)
    s.setActive(npcs.lwfy_22, false)
    s.setActive(npcs.lwfy_23, false)
    s.setActive(npcs.lwfy_24, false)

    -- s.setActive(npcs.enemy_1, flags.enemy_1 == 0)
    -- s.setActive(npcs.enemy_2, flags.enemy_2 == 0)
    -- s.setActive(npcs.enemy_3, flags.enemy_3 == 0)
    -- s.setActive(npcs.enemy_4, flags.enemy_4 == 0)
    -- s.setActive(npcs.elite_5, flags.elite_5 == 0)
    -- s.setActive(npcs.boss_6, flags.boss_6 == 0)

    s.setActive(objs.lwfy_1008, not s.hasUnlockMemorySlot("lwfy_1008"))
    s.setActive(objs.lwfy_1009, not s.hasUnlockMemorySlot("lwfy_1009"))

    finishFindParents()

    s.removeAllGameMapGuides()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()
    
    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 6) then
        taskStep = "forwardToExplore"
    elseif (step == 6) then
        taskStep = "leaveShanJiaoXiaoCunYe"
    end
    s.setTaskStep("裂雾飞鹰山脚小村夜", taskStep)

    reset_all()

    
    if step == 0 then
        ----第一次进入场景, 这里加入入场演出
        s.setTaskStep("裂雾飞鹰山脚小村夜","forwardToExplore")

        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.lwfy_20, 0.8)
    elseif step == 2 then
        level_guide_to(npcs.lwfy_21, 0.8)
    elseif step == 3 then
        level_guide_to(npcs.lwfy_22, 0.8)
    elseif step == 4 then
        level_guide_to(npcs.lwfy_23, 0.8)
    elseif step == 5 then
        level_guide_to(npcs.lwfy_24, 0.8)
    else
        --do nothing
        level_guide_to(objs.door)
    end
end

----------------------OVERRIDE----------------------------------

function lwfy_01_test()
    flags.lwfy_20_playing = 0
    flags.level_step = 1
    refreshMapStates()
end

function lwfy_20()
    executeMemSlot("lwfy_20", quick_play_avg)
end

function lwfy_21()
    executeMemSlot("lwfy_21", quick_play_avg)
end

function lwfy_22()
    executeMemSlot("lwfy_22", quick_play_avg)
end

function lwfy_23()
    executeMemSlot("lwfy_23", quick_play_avg)
end

function lwfy_24()
    executeMemSlot("lwfy_24", quick_play_avg,function()
        s.blackScreen()
        s.camera(cameras.camera1,true)
        s.setActive(objs.door,true)
        s.wait(0.5)
        s.lightScreen()
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_BOSS渲染", nil, 0)
        s.setActive(npcs.yuchi,true)
        s.soloAnimState(npcs.yuchi,"Special_2")
        s.camera(cameras.camera1,false,true,blendHintEnum.EaseInOut,1.5,true,true)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_BOSS渲染2", nil, 0)
        s.setTaskStep("裂雾飞鹰山脚小村夜","leaveShanJiaoXiaoCunYe")
        next_level_step(true)
    end)
end

function enemy_1()
    quick_battle("青羽录2-2", "NodeReward_裂雾飞鹰1_4", function()
        flags.enemy_1 = 1
    end)
end

function enemy_2()
    quick_battle("青羽录2-5", "NodeReward_裂雾飞鹰1_5", function()
        flags.enemy_2 = 1
    end)
end

function enemy_3()
    quick_battle("青羽录2-6", "NodeReward_裂雾飞鹰1_6", function()
        flags.enemy_3 = 1
    end)
end

function enemy_4()
    quick_battle("青羽录2-12", "NodeReward_裂雾飞鹰1_7", function()
        flags.enemy_4 = 1
    end)
end

function elite_5()
    quick_battle("青羽录2-14", "NodeReward_裂雾飞鹰1_8", function()
        flags.elite_5 = 1
    end)
end

function boss_6()
    quick_battle("青羽录2-15", "NodeReward_裂雾飞鹰1_9", function()
        flags.boss_6 = 1
    end)
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("裂雾飞鹰")
end

--小女孩谜题
function xnh_1(...)
    s.animState(npcs.zhujue,roleAnims.BSquat_Loop,true)
    if(flags.xnv_first_talk==0)then
        s.playSound("sfx","少女哭泣")
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_小女孩对话_1", nil, 0)
        flags.xnv_first_talk = 1
    end
    
    local ret = s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_小女孩对话选项", nil, 0)
    if(ret == 1)then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_小女孩对话_2", nil, 0)
        flags.accept_xnh = 1
        flags.xnv_talk_1 = 1
        s.setActive(objs.trigger_parents,true)
        s.blackScreen()
        finishFindParents()
        s.wait(0.5)
        s.lightScreen()
    else
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_小女孩对话_3", nil, 0)
    end
    s.animState(npcs.zhujue, roleAnims.BSquat_Loop,false)
end

function xnh_1_1(...)
    s.animState(npcs.zhujue,roleAnims.BSquat_Loop,true)
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_小女孩安慰", nil, 0)
    s.animState(npcs.zhujue, roleAnims.BSquat_Loop,false)
    s.wait(0.5)
end

function xnh_1_2(...)
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_小女孩跳舞", nil, 0)
end

--触发父母事件
function enterParents(...)
    if (flags.accept_xnh == 1) and (flags.complete_xnh == 0) then
        s.playSound("sfx","古琴编钟带旋律",0.5,-1,0.5,true,-1)
        s.setActive(objs.trigger_parents,false)
        s.setActive(objs.luoye,true)
        s.cameraAsync(cameras.camera_parents,true,true,blendHintEnum.EaseIn,3,true,true)
        s.setPos(npcs.zhujue,pos.pos_set_start_miti)
        s.setPos(npcs.xnh_1_1,pos.pos_set_start_miti)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_父母对话_1", nil, 0)
        --主角走向前，摊手
        s.moveAsync(npcs.zhujue,pos.pos_zhujue_miti,2,true)
        s.animState(npcs.npc_1,"Special_Loop_3",false)
        s.animState(npcs.npc_2,"Special_Loop_2",false)
        s.wait(2)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_主角和父母对话_1", nil, 0)

        --npc_1 转向主角
        s.turnToAsync(npcs.npc_1, npcs.zhujue)
        s.soloAnimState(npcs.npc_1,"BGesture",true)
        s.turnToAsync(npcs.npc_2, npcs.zhujue)
        s.soloAnimState(npcs.npc_2,"BShock_Loop",true)
        s.wait(0.5)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_主角和父母对话_2", nil, 0)

        --xnh1_1 跑到父亲前，然后转向npc_2
        s.moveAsync(npcs.xnh_1_1,pos.pos_xnh_1_2,4,true)
        s.wait(0.5)
        s.turnToAsync(npcs.xnh_1_1, npcs.npc_2)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_小女孩跟父母对话_1", nil, 0)

        --npc_2 转向xnh1_1，然后做出捂嘴的动作 
        s.turnToAsync(npcs.npc_2, npcs.xnh_1_1)
        s.soloAnimState(npcs.npc_2,"BRubNose",true)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_父母和小女孩对话_1", nil, 0)

        --xnh1_1 做出跳跃兴奋的动作
        s.soloAnimState(npcs.xnh_1_1,"Special_3",true)
        s.wait(0.5)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_小女孩跟父母对话_2", nil, 0)

        --npc_2 转向xnh1_1
        s.turnToAsync(npcs.npc_2, npcs.xnh_1_1)
        s.soloAnimState(npcs.npc_2,"BScratch",true)
        s.playSound("sfx","青年女叹息：呃")
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_父母和小女孩对话_2", nil, 0)

        --npc_1 转向主角，然后做出饶头的动作，总之尴尬
        s.turnToAsync(npcs.npc_1, npcs.zhujue)
        s.animState(npcs.npc_1,"BScratch",true)
        s.playSound("sfx","青年男苦笑：呵")
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_主角和父母对话_3", nil, 0)
        s.wait(1)

        --npc_1 做出抱拳动作，表示感谢
        s.animState(npcs.npc_1,"BGreet",true)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_父母感谢_1", nil, 0)
        s.wait(1)

        --xnh1_2 转向主角，做出跳跃兴奋的动作表示感谢
        s.turnToAsync(npcs.xnh_1_1, npcs.zhujue)
        s.soloAnimState(npcs.xnh_1_1,"Special_3",true)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_小女孩感谢", nil, 0)
        s.wait(1)

        --npc_2 转向主角，做出请示的动作
        s.turnToAsync(npcs.npc_2, npcs.zhujue)
        s.animState(npcs.npc_2,"BGesture",true)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_06山脚小村·夜", "蜃境_裂雾飞鹰_06山脚小村·夜_找爸妈谜题_父母感谢_2", nil, 0)
        s.playSound("sfx","古琴编钟带旋律")
        s.camera(cameras.camera_parents, false,true,blendHintEnum.EaseOut,1.5,true,false)
        s.setActive(objs.luoye,false)
        s.blackScreen()
        s.appendAsyncAside("青年男子",0,"来，接着奏乐，接着舞！")
        flags.complete_xnh = 1
        finishFindParents()
        s.wait(0.5)
        s.lightScreen()
        local capi = require("ChapterApi")
        capi.finishMemorySlot("lwfy_question_findParents")
    end
end

--是否完成小女孩找爸妈谜题
function finishFindParents(...)
    if(flags.complete_xnh == 1)then
        s.setActive(npcs.npc_1, true)
        s.setActive(npcs.npc_2, true)
        s.setActive(npcs.xnh_1_2, true)
        s.setActive(npcs.xnh_1, false)
        s.setActive(npcs.xnh_1_1, false)
        s.setActive(objs.guqin, true)
        s.soloAnimState(npcs.npc_1,"Special_Loop_3",true)
        s.soloAnimState(npcs.npc_2,"Special_Loop_2",true)
    elseif(flags.accept_xnh == 1)then
        s.setActive(objs.trigger_parents,true)
        s.setActive(npcs.npc_1, true)
        s.setActive(npcs.npc_2, true)
        s.setActive(npcs.xnh_1, false)
        s.setActive(npcs.xnh_1_1, true)
        s.setActive(npcs.xnh_1_2, false)
        s.setActive(objs.guqin, true)
        s.soloAnimState(npcs.npc_1,"Special_Loop_3",true)
        s.soloAnimState(npcs.npc_2,"Special_Loop_2",true)
    end
    if(flags.xnv_talk_1 == 0)then
        s.setActive(objs.trigger_xnhTalk_1,false)
    else
        s.setActive(objs.trigger_xnhTalk_1,true)
    end
end

function xnhTalk_1(...)
    if(flags.xnv_talk_1 == 1)then
        flags.xnv_talk_1 = 0
        s.playSound("sfx","单声折断",0)
        s.setActive(objs.trigger_xnhTalk_1,false)
        s.appendAsyncAside("小女孩",0,"前面有个石墙，爹爹以前总让我坐在上面举高高。")
    end
   
end

function door()
    s.changeMap("Assets/GameScenes/游历场景/蜃境_裂雾飞鹰_06最终房/蜃境_裂雾飞鹰_06最终房.unity", "位置/zhujue_start_pos")
end