local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    battle.ForceNotEndBattle = true
end

function start()
    bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 1, TalkEnd)
end

function TalkEnd()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("荆成师父", "徒儿不辱使命……七百年基业……未绝我手……")
        bcapi.talk("荆成", "师父……")
        bcapi.resume_and_show_ui()
        battle.ForceNotEndBattle = false
    end)
end
