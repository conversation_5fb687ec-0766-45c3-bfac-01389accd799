local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

local summonTag1 = 0
local summonTag2 = 0
local enemyLevel = 60
-- 脚本内容：
-- 

function init()

end

function start()
    bcapi.async_call(function()
        -- bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        bcapi.add_timer_trigger(5, summonWarrior1)
        bcapi.add_timer_trigger(7, summonReinforcements)
        bcapi.add_timer_trigger(1, winCheck)
        -- bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]5", 0, winTalk)
        bcapi.add_condition_trigger("[%enemy:世界_芙蕖望月_南理将军->flag:芙蕖望月_南理将军清除BUFF%][=]1", 0, finalWarrior)
    end)
end

function winCheck(...)
    bcapi.async_call(function()
        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0, winTalk)
    end)
end

function summonWarrior1(...)
    bcapi.async_call(function()
        --场上人少一点才召唤
        if eTeam.FightingRoleCount < 6 and summonTag1 == 0 then
            bcapi.append_async_aside("将军",0,"包夹之势！","","张熙和")
            bcapi.wait(2)
            --四个盾兵
            bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, 4.5), 0)
            bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, 1.5), 0)
            bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, -1.5), 0)
            bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, -4.5), 0)
            --四个偷屁股兵
            bcapi.create_join_role("通用_甲士_小怪_守卫男_刀", enemyLevel, eTeam, bcapi.Vector2(-6, 4.5), 0)
            bcapi.create_join_role("通用_甲士_小怪_守卫男_刀", enemyLevel, eTeam, bcapi.Vector2(-6, 1.5), 0)
            bcapi.create_join_role("通用_甲士_小怪_守卫男_刀", enemyLevel, eTeam, bcapi.Vector2(-6, -1.5), 0)
            bcapi.create_join_role("通用_甲士_小怪_守卫男_刀", enemyLevel, eTeam, bcapi.Vector2(-6, -4.5), 0)

            summonTag1 = 1
        end
        --创建下一次召唤触发器
        bcapi.add_timer_trigger(20, summonWarrior2)
        --强化小兵
        local index = math.random(1, 2)
        if index == 1 then
            bcapi.add_timer_trigger(5, skill01)
        else
            bcapi.add_timer_trigger(5, skill02) 
        end
    end)
end

function summonWarrior2(...)
    bcapi.async_call(function()
        --场上人少一点才召唤
        if eTeam.FightingRoleCount < 6 and summonTag2 == 0 then
            bcapi.append_async_aside("将军",0,"防御工事！","","张熙和")
            bcapi.wait(2)
            --四个盾兵
            bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, 4.5), 0)
            bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, 1.5), 0)
            bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, -1.5), 0)
            bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, -4.5), 0)
            --等盾兵位移完成，召唤下一波
            bcapi.add_timer_trigger(1.5, summonWarrior2_2)

            summonTag2 = 1
        end
        --创建下一次召唤触发器
        bcapi.add_timer_trigger(20, summonWarrior1)
        --强化小兵
        bcapi.add_timer_trigger(5, skill01)
    end)
end

function summonWarrior2_2(...)
    bcapi.async_call(function()
        --四个弓兵
        bcapi.create_join_role("通用_甲士_小怪_守卫男_弓", enemyLevel, eTeam, bcapi.Vector2(6, 4.5), 0)
        bcapi.create_join_role("通用_甲士_小怪_守卫男_弓", enemyLevel, eTeam, bcapi.Vector2(6, 1.5), 0)
        bcapi.create_join_role("通用_甲士_小怪_守卫男_弓", enemyLevel, eTeam, bcapi.Vector2(6, -1.5), 0)
        bcapi.create_join_role("通用_甲士_小怪_守卫男_弓", enemyLevel, eTeam, bcapi.Vector2(6, -4.5), 0)
    end)
end

function finalWarrior(...)
    bcapi.async_call(function()
        --最后一波
        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]5", 0.1, finalSunmon)
    end)
end

function finalSunmon(...)
    bcapi.async_call(function()
    --最后一波
        bcapi.append_async_aside("将军",0,"跟我冲锋！","","张熙和")
        bcapi.wait(2)
        --四个盾兵
        bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, 4.5), 0)
        bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, 1.5), 0)
        bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, -1.5), 0)
        bcapi.create_join_role("世界_芙蕖望月_战争_盾兵", enemyLevel, eTeam, bcapi.Vector2(6, -4.5), 0)
        --等盾兵位移完成，召唤下一波
        bcapi.add_timer_trigger(1.5, summonWarrior2_2)
    end)
end

function skill01(...)
    bcapi.async_call(function()
        local Boss1 = bcapi.get_role(1, "世界_芙蕖望月_南理将军")

        --使用强化小兵的技能
        bcapi.use_skill_to_role(Boss1,"世界_精英技能_强化小兵防御_2_调用技能",Boss1,1)
    end)
end

function skill02(...)
    bcapi.async_call(function()
        local Boss1 = bcapi.get_role(1, "世界_芙蕖望月_南理将军")

        --使用强化小兵的技能
        bcapi.use_skill_to_role(Boss1,"世界_精英技能_给前排回血强化攻击暴击_2_调用技能",Boss1,1)
    end)
end

function summonReinforcements(...)
    bcapi.async_call(function()
        bcapi.create_join_role("世界_芙蕖望月_战争_援军", enemyLevel, team, bcapi.Vector2(-6, 3), 0)
        bcapi.create_join_role("世界_芙蕖望月_战争_援军", enemyLevel, team, bcapi.Vector2(-6, -3), 0)
        bcapi.add_condition_trigger("[%c->teammate:世界_芙蕖望月_战争_援军%][<]1", 0, callForReinforcements)
    end)
end

function callForReinforcements(...)
    bcapi.async_call(function()
        bcapi.create_join_role("世界_芙蕖望月_战争_援军", enemyLevel, team, bcapi.Vector2(-6, 0), 0)
        bcapi.add_condition_trigger("[%c->teammate:世界_芙蕖望月_战争_援军%][<]1", 0, callForReinforcements)
    end)
end

--胜利
function winTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("南理国将军", "（惊恐）这……这是什么人？竟有如此神威！")
        bcapi.resume_and_show_ui()
        bcapi.win()
    end)
end
