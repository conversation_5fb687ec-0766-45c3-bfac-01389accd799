
---@type 蜃境_鹿鸣天下_03长安城郊
local context = require("MapStories/蜃境_鹿鸣天下_03长安城郊/scene_蜃境_鹿鸣天下_03长安城郊_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_鹿鸣天下_03长安城郊
local flags = context.flags
--不可省略，用于外部获取flags

local newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")

function getFlags(...)
    return flags
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function start()
    print("start called")
    s.readyToStart(true)
    refreshMapStates()
    try_execute_newbieGuide()
end

function try_execute_newbieGuide(...)
    s.nextStep("newbieGuideInLevel")
end


function newbieGuideInLevel()end

--默认初始化的地图显隐状态
function reset_all()
    
    if s.getCurrentTaskStep("心有玲珑·上") == "deactive" then
        s.setActive(npcs.branchTask_xyll_Linglong,true)
        s.setActive(npcs.branchTask_xyll_Shusheng,false)
    end
    
    if s.getCurrentTaskStep("心有玲珑·上") == "findPlant" or s.getCurrentTaskStep("心有玲珑·上") == "givePlant"  then
        s.setActive(npcs.branchTask_xyll_Linglong,true)
        s.setActive(npcs.branchTask_xyll_Shusheng,true)
    end
    
    if flags.find_Plant == 0 then
        s.setActive(objs.check_XueJie,false)
        s.setActive(objs.check_TianQi,false)
    elseif flags.find_Plant == 1 and flags.find_XueJie == 0 and flags.find_TianQi == 0 then
        s.setActive(objs.check_XueJie,true)
        s.setActive(objs.check_TianQi,true)
    elseif flags.find_XueJie == 0 and flags.find_TianQi == 1 then
        s.setActive(objs.check_XueJie,true)
        s.setActive(objs.check_TianQi,false)
    elseif flags.find_TianQi == 0 and flags.find_XueJie == 1 then
        s.setActive(objs.check_XueJie,false)
        s.setActive(objs.check_TianQi,true)
    elseif flags.find_TianQi == 1 and flags.find_XueJie == 1 then
        s.setActive(objs.check_XueJie,false)
        s.setActive(objs.check_TianQi,false)
    end
    
    if s.getCurrentTaskStep("心有玲珑·上") == "finished" then
        s.setActive(npcs.branchTask_xyll_Linglong,false)
        s.setActive(npcs.branchTask_xyll_Shusheng,false)
    end

    --隐藏所有的剧情节点
    s.setActive(npcs.lmtx_12, false)
    s.setActive(npcs.lmtx_13, false)
    s.setActive(npcs.lmtx_14, false)
    s.setActive(npcs.lmtx_15, false)

    --隐藏所有的剧情相关NPC
    s.setActive(npcs.lmtx_12_npc01,false)
    s.setActive(npcs.lmtx_12_npc02,false)
    s.setActive(npcs.lmtx_12_npc03,false)
    s.setActive(npcs.lmtx_12_npc04,false)
    s.setActive(npcs.lmtx_13_npc01,false)
    s.setActive(npcs.lmtx_13_npc02,false)
    s.setActive(npcs.lmtx_14_npc01,false)
    s.setActive(npcs.lmtx_15_npc01,false)

    s.removeAllGameMapGuides()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 5) then
        taskStep = "forwardToExplore"
    elseif (step == 5) then
        taskStep = "leaveChangAnChengJiao"
    end
    s.setTaskStep("鹿鸣天下长安城郊", taskStep)

    
    print("refreshMapStates level step = " .. tostring(step))
    reset_all()

    s.setActive(objs.lmtx_1004,not s.hasUnlockMemorySlot("lmtx_1004"))

    if s.getCurrentTaskStep("逃亡者") == "deactive" then
        s.setActive(objs.branchTask_twz_Trigger,true)
        s.setActive(objs.BranchTask_twz_Trigger_1,false)
        s.setActive(npcs.branchTask_twz_NvZi,true)
        s.setActive(npcs.branchTask_twz_aLiang01,true)
        s.setActive(npcs.branchTask_twz_aLiang02,false)
        s.setActive(npcs.branchTask_twz_CiKe,false)
        s.setActive(npcs.bt_twz_npc1_S1,true)
        s.setActive(npcs.bt_twz_npc2_S1,true)
        s.setActive(npcs.bt_twz_npc3_S1,true)
        s.setActive(npcs.bt_twz_npc4_S1,true)  
        s.setActive(npcs.bt_twz_npc5_S1,true)
        s.setActive(npcs.bt_twz_npc6_S1,true)
        s.setActive(npcs.bt_twz_npc7_S1,true)
        s.setActive(npcs.bt_twz_npc8_S1,true)
        s.setActive(npcs.bt_twz_npc1_S2,false)
        s.setActive(npcs.bt_twz_npc2_S2,false)
        s.setActive(npcs.bt_twz_npc3_S2,false)
        s.setActive(npcs.bt_twz_npc4_S2,false)
        s.setActive(npcs.bt_twz_npc5_S2,false)
        s.setActive(npcs.bt_twz_npc6_S2,false)
        s.setActive(npcs.bt_twz_npc7_S2,false)
        s.setActive(npcs.bt_twz_npc8_S2,false)
    end

    if s.getCurrentTaskStep("逃亡者") == "findLiang" then
        s.setActive(objs.branchTask_twz_Trigger,false)
        s.setActive(objs.BranchTask_twz_Trigger_1,false)
        s.setActive(npcs.branchTask_twz_NvZi,false)
        s.setActive(npcs.branchTask_twz_aLiang01,false)
        s.setActive(npcs.branchTask_twz_aLiang02,true)
        s.setActive(npcs.branchTask_twz_CiKe,true)
        s.setActive(npcs.bt_twz_npc1_S1,false)
        s.setActive(npcs.bt_twz_npc2_S1,false)
        s.setActive(npcs.bt_twz_npc3_S1,false)
        s.setActive(npcs.bt_twz_npc4_S1,false)  
        s.setActive(npcs.bt_twz_npc5_S1,false)
        s.setActive(npcs.bt_twz_npc6_S1,false)
        s.setActive(npcs.bt_twz_npc7_S1,false)
        s.setActive(npcs.bt_twz_npc8_S1,false)
        s.setActive(npcs.bt_twz_npc1_S2,true)
        s.setActive(npcs.bt_twz_npc2_S2,true)
        s.setActive(npcs.bt_twz_npc3_S2,true)
        s.setActive(npcs.bt_twz_npc4_S2,true)
        s.setActive(npcs.bt_twz_npc5_S2,true)
        s.setActive(npcs.bt_twz_npc6_S2,true)
        s.setActive(npcs.bt_twz_npc7_S2,true)
        s.setActive(npcs.bt_twz_npc8_S2,true)
    end
    
    if step == 0 then
        ----第一次进入场景, 这里加入入场演出
        s.setPos(npcs.Player,pos.zhujue_start_pos)
        s.setTaskStep("鹿鸣天下长安城郊","forwardToExplore")

        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.lmtx_12, 0.8)
        s.setActive(npcs.lmtx_12_npc01,true)
        s.setActive(npcs.lmtx_12_npc02,true)
        s.setActive(npcs.lmtx_12_npc03,true)
        s.setActive(npcs.lmtx_12_npc04,true)
    elseif step == 2 then
        level_guide_to(npcs.lmtx_13, 0.8)
        s.setActive(npcs.lmtx_13_npc01,true)
        s.setActive(npcs.lmtx_13_npc02,true)
    elseif step == 3 then
        level_guide_to(npcs.lmtx_14, 0.8)
        s.setActive(npcs.lmtx_14_npc01,true)
    elseif step == 4 then
        level_guide_to(npcs.lmtx_15, 0.8)
        s.setActive(npcs.lmtx_15_npc01,true)
    else
        --do nothing
        level_guide_to(objs.exit)
    end
end

----------------------OVERRIDE----------------------------------

function lmtx_12()
    executeMemSlot("lmtx_12", quick_play_avg)
end

function lmtx_13()
    executeMemSlot("lmtx_13", quick_play_avg)
end

function lmtx_14()
    executeMemSlot("lmtx_14", quick_play_avg)
end

function lmtx_15()
    executeMemSlot("lmtx_15", quick_play_avg,function()
    s.setTaskStep("鹿鸣天下长安城郊","leaveChangAnChengJiao")
    next_level_step(true)
    end)
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("鹿鸣天下")
end

--------------------branchTask_xyll---------------------------
function bt_xyll_Step1()
    if s.getCurrentTaskStep("心有玲珑·上") == "deactive" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_心有玲珑任务激活1")
        s.blackScreen(0.7)
        s.setActive(npcs.branchTask_xyll_Shusheng,true)
        s.lightScreen(0.7)
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_心有玲珑任务激活2")
        s.setActive(objs.check_XueJie,true)
        s.setActive(objs.check_TianQi,true)
        s.setTaskStep("心有玲珑·上","findPlant")
        flags.find_Plant = 1
        
    elseif s.getCurrentTaskStep("心有玲珑·上") == "findPlant" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_心有玲珑Step1和玲珑交互")
    elseif s.getCurrentTaskStep("心有玲珑·上") == "givePlant" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_心有玲珑Step2和玲珑交互")
    end
end

function bt_xyll_Step3()
    if s.getCurrentTaskStep("心有玲珑·上") == "findPlant" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_心有玲珑Step1和书生交互")
    elseif s.getCurrentTaskStep("心有玲珑·上") == "givePlant" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_心有玲珑Step2")
        s.blackScreen(0.7)
        s.setActive(npcs.branchTask_xyll_Linglong,false)
        s.setActive(npcs.branchTask_xyll_Shusheng,false)
        s.lightScreen(0.7)
        s.aside("你一回眸，身后的书生没了踪影，看回原处，少女也不知去了哪儿。")
        s.setTaskStep("心有玲珑·上","finished")
        local capi = require("ChapterApi")
        capi.finishMemorySlot("lmtx_branchTask_xyll_01")
    end
end

function bt_xyll_checkXueJie()
    if flags.find_Plant == 1 and flags.find_XueJie == 0 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_心有玲珑Step1血竭")
        s.setActive(objs.check_XueJie,false)
        flags.find_Plant = flags.find_Plant + 1
        flags.find_XueJie = flags.find_XueJie + 1
    elseif flags.find_Plant == 2 and flags.find_XueJie == 0 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_心有玲珑Step1全部采完")
        s.setActive(objs.check_XueJie,false)
        flags.find_Plant = flags.find_Plant + 1
        flags.find_XueJie = flags.find_XueJie + 1
        s.setTaskStep("心有玲珑·上","givePlant")
    end
end

function bt_xyll_checkTianQi()
    if flags.find_Plant == 1 and flags.find_TianQi == 0 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_心有玲珑Step1田七")
        s.setActive(objs.check_TianQi,false)
        flags.find_Plant = flags.find_Plant + 1
        flags.find_TianQi = flags.find_TianQi + 1
    elseif flags.find_Plant == 2 and flags.find_TianQi == 0 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_心有玲珑Step1全部采完")
        s.setActive(objs.check_TianQi,false)
        flags.find_Plant = flags.find_Plant + 1
        flags.find_TianQi = flags.find_TianQi + 1
        s.setTaskStep("心有玲珑·上","givePlant")
    end
end

--------------------branchTask_twz---------------------------

function bt_twz_NvziTalk()
    if s.getCurrentTaskStep("逃亡者") == "deactive" then
        s.setActive(objs.branchTask_twz_Trigger,false)
        -- 黑屏后切换镜头并销毁逃亡者_触发器
        s.blackScreen(0.7)
        s.camera(cameras.branchTaskS1cam,true,true,blendHintEnum.Cut,0.5,true,true)
        s.lightScreen(0.7)
        --亮屏后进入剧情环节
        s.soloAnimState(npcs.branchTask_twz_NvZi, "BPoint", true)
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_逃亡者任务激活P1")
        s.soloAnimState(npcs.branchTask_twz_NvZi, "BPoint", false)
        s.soloAnimState(npcs.branchTask_twz_aLiang01, "BGive_Loop", true)
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_逃亡者任务激活P1补充")
        s.soloAnimState(npcs.branchTask_twz_aLiang01, "BGive_Loop", false)
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_逃亡者任务激活P2")
        s.moveAsync(npcs.branchTask_twz_aLiang01, pos.branchTask_twz_AliangS1_pos,1,true)
        s.wait(1)
        s.blackScreen(0.7)
        s.setActive(npcs.branchTask_twz_NvZi,false)
        s.setActive(npcs.branchTask_twz_aLiang01,false)
        s.setActive(npcs.bt_twz_npc1_S1,false)
        s.setActive(npcs.bt_twz_npc2_S1,false)
        s.setActive(npcs.bt_twz_npc3_S1,false)
        s.setActive(npcs.bt_twz_npc4_S1,false)  
        s.setActive(npcs.bt_twz_npc5_S1,false)
        s.setActive(npcs.bt_twz_npc6_S1,false)
        s.setActive(npcs.bt_twz_npc7_S1,false)
        s.setActive(npcs.bt_twz_npc8_S1,false)
        s.setActive(npcs.bt_twz_npc1_S2,true)
        s.setActive(npcs.bt_twz_npc2_S2,true)
        s.setActive(npcs.bt_twz_npc3_S2,true)
        s.setActive(npcs.bt_twz_npc4_S2,true)
        s.setActive(npcs.bt_twz_npc5_S2,true)
        s.setActive(npcs.bt_twz_npc6_S2,true)
        s.setActive(npcs.bt_twz_npc7_S2,true)
        s.setActive(npcs.bt_twz_npc8_S2,true)
        s.setActive(npcs.branchTask_twz_aLiang02,true)
        s.setActive(npcs.branchTask_twz_CiKe,true)
        s.setActive(objs.BranchTask_twz_Trigger_1,true)
        s.camera(cameras.maincam,true,true,blendHintEnum.Cut,0.5,true,true)
        s.lightScreen(0.7)
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_逃亡者任务激活P3")
        s.setTaskStep("逃亡者","findLiang")
    end
end

function bt_twz_aLiang02()
    if s.getCurrentTaskStep("逃亡者") == "findLiang" then
        s.setActive(objs.BranchTask_twz_Trigger_1,false)
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_逃亡者Step1")
        local lmtx_bt_battleWin =  s.battle("蜃境_鹿鸣天下3_支线与追杀者战斗")
        if lmtx_bt_battleWin then
            s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_逃亡者任务Step1战后P1")
            s.blackScreen(0.7)
            s.setActive(npcs.branchTask_twz_CiKe,false)
            s.soloAnimState(npcs.branchTask_twz_aLiang02, "BScratch", true)
            s.turnTo(npcs.branchTask_twz_aLiang02,npcs.Player)
            s.turnTo(npcs.Player,npcs.branchTask_twz_aLiang02)
            s.lightScreen(0.7)
    
            s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_逃亡者任务Step1战后P2")
            s.soloAnimState(npcs.branchTask_twz_aLiang02, "BScratch", false)
            s.moveAsync(npcs.branchTask_twz_aLiang02, pos.branchTask_twz_AliangS2_pos,1,true)
            s.wait(1.2)
    
            s.blackScreen(0.7)
            s.setActive(npcs.branchTask_twz_aLiang02,false)
            s.lightScreen(0.7)

            s.aside("阿凉披上外衣，消失在街角。这闹市一角依旧喧嚣，仿佛什么也没有发生过。")
            
            s.setTaskStep("逃亡者","finished")
            local capi = require("ChapterApi")
            capi.finishMemorySlot("lmtx_branchTask_twz")
        end
    end
end

function bt_twz_helpAliang()
    local lmtx_bt_battleWin =  s.battle("蜃境_鹿鸣天下3_支线与追杀者战斗")
    if lmtx_bt_battleWin then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_逃亡者任务Step1战后P1")
        s.blackScreen(0.7)
        s.setActive(npcs.branchTask_twz_CiKe,false)
        s.soloAnimState(npcs.branchTask_twz_aLiang02, "BScratch", true)
        s.turnTo(npcs.branchTask_twz_aLiang02,npcs.Player)
        s.turnTo(npcs.Player,npcs.branchTask_twz_aLiang02)
        s.lightScreen(0.7)

        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_长安城郊", "蜃境_鹿鸣天下_03长安城郊_逃亡者任务Step1战后P2")
        s.soloAnimState(npcs.branchTask_twz_aLiang02, "BScratch", false)
        s.moveAsync(npcs.branchTask_twz_aLiang02, pos.branchTask_twz_AliangS2_pos,1,true)
        s.wait(1.2)

        s.blackScreen(1)
        s.setActive(npcs.branchTask_twz_aLiang02,false)
        s.lightScreen(1)

        s.aside("阿凉披上外衣，消失在街角。这闹市一角依旧喧嚣，仿佛什么也没有发生过。")

        s.setTaskStep("逃亡者","finished")
        local capi = require("ChapterApi")
        capi.finishMemorySlot("lmtx_branchTask_twz")
    end
end


function lmtx_1004()
    s.setActive(objs.lmtx_1004,false)
end


