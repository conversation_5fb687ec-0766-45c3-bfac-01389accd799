local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

local enemyLevel = 59
-- 脚本内容：
-- 

function init()

end

function start()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0, secondCallForEnemy)
    end)
end

function secondCallForEnemy(...)
    bcapi.async_call(function()
        bcapi.create_join_role("世界_妄心攀援_僧人", enemyLevel, eTeam, bcapi.Vector2(6, 0), 0)
        bcapi.wait(0.5)
        bcapi.pause_and_hide_ui()
        bcapi.talk("男村民_村庄保卫", "太好了，多谢大侠除了这怪物！救了大家！")
        bcapi.talk("僧人", "呜……我的脑袋……")
        bcapi.talk("僧人", "！！")
        bcapi.talk("僧人", "你这恶徒，竟敢毁了你爷爷的无量功德，纳命来！")
        bcapi.talk("主角", "阁下冷静！我杀伥怪是为保村民平安，不想与你为敌，你不要执迷不悟。")
        bcapi.talk("僧人", "呸！卑鄙之徒，休要多言！")
        bcapi.talk("主角", "我所做之事问心无愧，阁下既要一战，那便奉陪。")
        bcapi.resume_and_show_ui()
        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0, win)
    end)
end


function win()
    bcapi.async_call(function()
        bcapi.win()
    end)
end
