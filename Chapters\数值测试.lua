---数值测试
---@type ChapterApi
local capi = require("ChapterApi")

---@type NewbieApi
local newbieApi = require("NewbieApi") -- 不允许改名

---@type StageMapApi
local stageApi = require("StageMapApi")

function on_unlock()
    -- 播放开门视频
    newbieApi.playVideo("CriAtom/蜃景-full.usm")
end

function unlock_zuihuaying()
    defaultBattle(function ()
        capi.pass(7)
        capi.unlockMapStory("副本_醉花荫")
        newbieApi.popinfo("副本 - 醉花荫 已解锁！")
        capi.finishChapter()
    end, nil)
end

function unlock_yuangudigong()
    defaultBattle(function ()
        capi.pass(7)
        capi.unlockMapStory("挑战_远古地宫")
        newbieApi.popinfo("副本 - 远古地宫 已解锁！")
        capi.finishChapter()
    end, nil)
end