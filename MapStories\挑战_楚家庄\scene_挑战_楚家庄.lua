
---@type 挑战_楚家庄
local context = require("MapStories/挑战_楚家庄/scene_挑战_楚家庄_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_挑战_楚家庄
local flags = context.flags

local extensions = require 'MapStories/挑战_楚家庄/extension'

---@type StringWidget
local stringWidget = extensions.widgets.StringWidgetInfo.widget
---@type 挑战_楚家庄_StringItemData
local stringItemData = extensions.widgets.StringWidgetInfo.items

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    flags.initBattleTimeTotal = 0
    s.blackScreen()
    s.setPos(npcs.jingcheng, pos.start)
    s.camera(cameras.overview, true)
    s.lightScreen()
    s.readyToStart()
    s.talk(npcs.jingcheng, "楚家庄已经完成笼罩在火海之中了……")
    s.aside("荆成侧身聆听，庄内的火烧声、建筑损毁声、呼救哭闹声嘈杂一片，不由得加紧了脚步。")
    s.camera(cameras.overview, false)
    
    s.talk(npcs.jingcheng, "……楚姑娘。")
    s.talk(npcs.jingcheng, "也罢，眼下救人要紧，速速入庄吧。")
    init_string_item()
end

function init_string_item()
    stringWidget:showItem(stringItemData.battletotalTime)
    stringWidget:showItem(stringItemData.fire)
end

function eventBattle1()
    s.blackScreen()
    s.setPos(npcs.jingcheng, pos.boss1)
    s.turnTo(npcs.jingcheng, npcs.changguaiBoss)
    --s.animState(npcs.jingcheng, roleAnims.BFight_Loop, true)
    s.setActive(objs.boss1trigger, false)

    s.lightScreen()

    s.talk(npcs.shinv, "救命！救命！")
    --s.camera("boss1",true)
    s.camera(cameras.boss1, true)
    s.animState(npcs.changguaiBoss, "Attack",true)
    s.talk(npcs.changguaiBoss, "吼！！")
    s.wait(0.5)
    s.cameraAsync(cameras.boss1, false,true,blendHintEnum.EaseInOut,2,true,false)
    --s.camera("boss1",false)
    --s.camera(cameras.boss1, false)
    s.animState(npcs.jingcheng, roleAnims.BFight_Loop, true)
    s.talk(npcs.jingcheng, "住手！")
    --s.animState(npcs.jingcheng, roleAnims.BFight_Loop, true)
    s.talk(npcs.jingcheng, "姑娘，快躲到我的身后！")
    --s.selectTalk(npcs.jingcheng, "...", {"战斗胜利"})
    s.blackScreen()
    -- s.setActive(npcs.changguaiBoss, false)
    -- s.setActive(npcs.shinv, false)
    s.animState(npcs.jingcheng, roleAnims.BFight_Loop, false)
    s.lightScreen()

    local timeTb = {}
    timeTb = CalcCurrentFireDamage(timeTb)
    local isWin,bResult2 = s.battle("楚家庄_挑战_开场战斗", timeTb)
    if(isWin)then
        s.setActive(npcs.changguaiBoss, false)
        s.setActive(npcs.shinv, false)
        local time = math.floor(bResult2.BattleDuration)
        flags.initBattleTimeTotal = flags.initBattleTimeTotal + time
        CalcCurrentFireLv()
    end 
end

function CalcCurrentFireLv()
    if flags.initBattleTimeTotal >= 30 then
        flags.initFireLv = 1
    end
    if flags.initBattleTimeTotal >= 90 then
        flags.initFireLv = 2
    end
    if flags.initBattleTimeTotal >= 180 then
        flags.initFireLv = 3
    end
    if flags.initBattleTimeTotal >= 300 then
        flags.initFireLv = 4
    end
    if flags.initBattleTimeTotal >= 480 then
        flags.initFireLv = 5
    end
    -- stringWidget:showItem(stringItemData.fire)
    -- stringWidget:showItem(stringItemData.battletotalTime)
    -- return flags.initFireLv
end

function CalcCurrentFireDamage(table)
    local fireDamage = 30
    if flags.initFireLv == 1 then
        fireDamage = 50
    elseif flags.initFireLv == 2 then
        fireDamage = 80
    elseif flags.initFireLv == 3 then
        fireDamage = 120
    elseif flags.initFireLv == 4 then
        fireDamage = 180
    elseif flags.initFireLv == 5 then
        fireDamage = 300
    end
    table["火焰伤害"] = fireDamage
    return table
end

function testAni()
    s.animState(npcs.rscg, "Skill",true)
end


function eventBattle2()
    s.blackScreen()
    s.setPos(npcs.jingcheng, pos.boss2)
    s.turnTo(npcs.jingcheng, npcs.rscg)
    s.lookAt(npcs.jingcheng, nil, npcs.rscg, true)

    --s.camera(cameras.boss2, true)
    s.lightScreen()
    s.talk(npcs.rscg, "好烫……好疼……好不甘心……为什么，我会死在这里……")
    s.talk(npcs.jingcheng, "是一个已经伥化的武者，应该是葬身在火海之中。")
    s.talk(npcs.rscg, "(沙哑地嘶吼)不对的……不该是这样的……")
    s.talk(npcs.jingcheng, "……对不起，是我来晚了。")
    s.talk(npcs.rscg, "嗬嗬嗬，你——还我命来，还我命来！")
    s.animState(npcs.rscg, "Skill",true)
    s.wait(1)
    s.camera(cameras.boss2, true)
    s.wait(1)
    s.camera(cameras.boss2, false,true,blendHintEnum.EaseInOut,0.8,true,false)

    --s.selectTalk(npcs.jingcheng, "...", {"战斗胜利"})
    s.blackScreen()
    --s.animState(npcs.jingcheng, roleAnims.BFight_Loop, false)
    s.camera(cameras.boss2, false)
    s.lightScreen()

    local timeTb = {}
    timeTb = CalcCurrentFireDamage(timeTb)
    local isWin,bResult2 = s.battle("楚家庄_挑战_燃烧伥怪",timeTb)
    if(isWin)then
        s.setActive(npcs.rscg, false)
        s.setActive(objs.boss2trigger_1, false)
        local time = math.floor(bResult2.BattleDuration)
        flags.initBattleTimeTotal = flags.initBattleTimeTotal + time
        CalcCurrentFireLv()
    end 
end

function boss3Sub()
    s.setActive(objs.boss2trigger, false)
    s.blackScreen()
    s.camera(cameras.boss3Near, true)
    s.setPos(npcs.jingcheng, pos.boss3)
    s.turnTo(npcs.jingcheng, npcs.dunv)
    s.turnTo(npcs.dunv, npcs.jingcheng)
    s.lookAt(npcs.dunv, nil, npcs.jingcheng, false)
    s.lookAt(npcs.jingcheng, nil, npcs.dunv, false)
    s.lightScreen()

    s.talk(npcs.dunv, "冤冤相报何时了……呵呵，说这句话的人一定不懂仇恨的滋味。")
    s.talk(npcs.dunv, "若是懂了，又怎会这般——")
    s.animState(npcs.dunv, "Jump",true)
    s.wait(0.4)
    s.setPos(npcs.dunv, pos.boss3Sub)
    s.camera(cameras.boss3Side, true,true,blendHintEnum.EaseInOut,0.8,true,false)
    s.talk(npcs.dunv, "高高在上！")
    s.blackScreen()
    s.camera(cameras.boss3Near, false)
    s.camera(cameras.boss3Side, false)
    s.lightScreen()

    local timeTb = {}
    timeTb = CalcCurrentFireDamage(timeTb)
    local isWin,bResult2 = s.battle("楚家庄_挑战_毒女",timeTb)
    if(isWin)then
        s.setActive(npcs.dunv, false)
        s.setActive(objs.boss3trigger, true)
        s.setActive(npcs.chuqing, true)
        s.appendAsyncAside("提示",0,"楚家庄的中央似乎传来了响动，去看看吧。")
        local time = math.floor(bResult2.BattleDuration)
        flags.initBattleTimeTotal = flags.initBattleTimeTotal + time
        --s.setActive(objs.boss4trigger, true)
        CalcCurrentFireLv()
    end
end

function eventBattle3()
    local timeTb = {}
    timeTb = CalcCurrentFireDamage(timeTb)
    local isWin,bResult2 = s.battle("楚家庄_挑战_毒女",timeTb)
    if(isWin)then
        s.setActive(npcs.dunv, false)
        s.setActive(objs.boss3trigger, true)
        s.setActive(npcs.chuqing, true)
        s.appendAsyncAside("提示",0,"楚家庄的中央似乎传来了响动，去看看吧。")
        local time = math.floor(bResult2.BattleDuration)
        flags.initBattleTimeTotal = flags.initBattleTimeTotal + time
        --s.setActive(objs.boss4trigger, true)
        CalcCurrentFireLv()
    end 
end

function boss4Sub()
    s.blackScreen()
    s.setPos(npcs.jingcheng, pos.boss4)
    s.turnTo(npcs.jingcheng, npcs.chuqing)
    --s.turnTo(npcs.chuqing, npcs.jingcheng)
    --s.animState(npcs.jingcheng, roleAnims.BFight_Loop, true)
    s.setActive(objs.boss3trigger, false)
    --s.setActive(objs.boss4interc, true)
    s.camera(cameras.boss4, true)
    s.lightScreen()
    
    s.talk(npcs.jingcheng, "楚姑娘，不要！")
    s.turnTo(npcs.chuqing, npcs.jingcheng)
    s.animState(npcs.chuqing, roleAnims.BApplaud)
    s.wait(1)
    s.camera(cameras.boss4_1, true,true,blendHintEnum.EaseInOut,0.8,true,true)

    --[[楚青：哈哈哈哈！！这一桩弥天恨事，透骨酸心，时至今日，终得一报！
    楚青：楚家庄覆灭我手，无人可阻！]]
    s.talk(npcs.chuqing, "阿成……这是我与楚家庄之间的恩怨。这一桩弥天恨事，透骨酸心……")
    s.talk(npcs.chuqing, "即便是你，今日也休想阻拦我！")
    s.camera(cameras.boss4, true,true,blendHintEnum.EaseInOut,0.8,true,true)

    s.talk(npcs.jingcheng, "阿青……不要再错下去了。")
    s.talk(npcs.chuqing, "呵呵呵……也罢，你还是这副样子。那就让我看看你的本事吧，阿成。让我看看这些日子过去，你又领悟到了些什么呢。")
    s.talk(npcs.jingcheng, "……")

    s.blackScreen()
    s.camera(cameras.boss4, false)
    s.lightScreen()

    local timeTb = {}
    timeTb = CalcCurrentFireDamage(timeTb)
    local isWin,bResult2 = s.battle("楚家庄_挑战_楚青",timeTb)
    if(isWin)then
        s.setActive(npcs.chuqing, false)
        s.setActive(objs.boss4interc, false)
        s.setActive(objs.boss3trigger, false)
        s.talk(npcs.chuqing, "我还是……不愿你看见我枯朽的模样……")
        local time = math.floor(bResult2.BattleDuration)
        flags.initBattleTimeTotal = flags.initBattleTimeTotal + time
        --s.setActive(objs.boss4trigger, true)
        CalcCurrentFireLv()
    end 
end

function eventBattle4()
    local timeTb = {}
    timeTb = CalcCurrentFireDamage(timeTb)
    local isWin,bResult2 = s.battle("楚家庄_挑战_燃烧伥怪",timeTb)
    if(isWin)then
        s.setActive(npcs.chuqing, false)
        s.setActive(objs.boss4interc, false)
        s.talk(npcs.chuqing, "我还是……不愿你看见我枯朽的模样……")
        local time = math.floor(bResult2.BattleDuration)
        flags.initBattleTimeTotal = flags.initBattleTimeTotal + time
        --s.setActive(objs.boss4trigger, true)
        CalcCurrentFireLv()
    end 
end