local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    CreateRoles()
    battle.ForceNotEndBattle = true
end

function start()
    bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0, BattleWin)
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("椿岁", 76, team, bcapi.Vector2(-2, 0), 0)
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("山匪头子", "这姑娘不简单。")
        bcapi.talk("山匪头子", "这不是咱们的山头，没必要在这里丢了命，撤！")
        bcapi.resume_and_show_ui()
        battle.ForceNotEndBattle = false
    end)
end
