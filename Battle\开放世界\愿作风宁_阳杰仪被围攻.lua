local bcapi = require("BattleClientA<PERSON>")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

local level = 30

function init()
    createRoles()
end

function start()  

end

function createRoles()
    bcapi.async_call(function()
        --创建角色
        bcapi.create_join_role("世界_愿作风宁_阳杰仪", level, team, bcapi.Vector2(-5, -1), 0)
        bcapi.create_join_role("世界_愿作风宁_朱百灵", level, team, bcapi.Vector2(-7, 2), 0)
        bcapi.create_join_role("世界_愿作风宁_卫宁风", level, team, bcapi.Vector2(-7, -4), 0)
    end)
end

