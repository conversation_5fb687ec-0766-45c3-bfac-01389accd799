--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/主线_青羽录5/flags_主线_青羽录5")


---@class 角色_主线_青羽录5_楚七楚八
local npcs = {
    --- "荆成"
    zhujue = "角色/zhujue",
    --- "莫知酉"
    mozhiyou = "角色/mozhiyou",
    --- "楚家庄护卫"
    huwei = "角色/huwei",
    mozhiyou2 = "角色/mozhiyou2",
    --- "楚家庄护卫引导点"
    guide_huwei = "角色/guide_huwei",
}

---@class 物体_主线_青羽录5_楚七楚八
local objs = {
    --- "任务提示护卫"
    task_huwei = "物体/task_huwei",
    --- "陶罐"
    pot = "物体/pot",
    --- "楚家庄大门"
    gate = "物体/gate",
    --- "石碑"
    shibei = "物体/shibei",
}

---@class 相机_主线_青羽录5_楚七楚八
local cameras = {
    --- "大门口"
    cam_door = "相机/cam_door",
}

---@class 位置_主线_青羽录5_楚七楚八
local pos = {
    --- "荆成开始位置"
    JC_init = "位置/JC_init",
    pos_mzy_door = "位置/pos_mzy_door",
}

---@class 资产_主线_青羽录5_楚七楚八
local assets = {
    --- "青羽录5_楚七楚八"
    qyl1_5_1 = "资产/qyl1_5_1",
    --- "青羽录5_楚七楚八2"
    qyl1_5_2 = "资产/qyl1_5_2",
}

---@class 动作_主线_青羽录5_楚七楚八
local animationClips = {
}

---@class 主线_青羽录5_楚七楚八
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
