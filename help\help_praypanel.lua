aside("此处便是祈愿灵池。")
aside("之前你也知道了，剑之川是人间万灵心魂汇聚之地，你可以通过意念的连接唤醒那些沉睡的英灵。")
aside("他们可以助你清除混沌魔障，也能助你战胜剑之川的幻影。")
-- local option = Select("", "选择回答", { "总之就是能在这儿呼朋引伴", "这样，这里也不算寂寥无人了" })

-- if (option == 0) then
--     aside("嗯，这是你作为剑之川主人的力量，只有你能让飘散的意识凝聚成形，只有你能唤醒他们。")
--     aside("若非劫难降临，剑之川也不会像如今这样空空荡荡。")
-- else
--     aside("原本剑之川里有许多英灵协助，若非劫难降临……无妨，你作为剑之川主人，可以再度让飘散的意识凝聚成形，唤醒他们。")
--     aside("这是只有你才能做到的事情。")
-- end
-- local option = Select("", "选择回答", { "原来这是召唤英灵的地方", "剑之川曾经发生了什么？" })
-- if (option == 0) then
--     aside("这是你作为剑之川主人独有的能力，请善用这份力量，让剑之川复苏吧。")
-- else
--     aside("我非有意瞒你，只是具体细节我也记不太清，只记得灾厄降临，河水逆流，地裂天崩……")
--     aside("待我有一日恢复记忆，一定如实禀告。")
-- end
