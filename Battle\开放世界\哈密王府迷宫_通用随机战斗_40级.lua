local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.async_call(function()
        local level = 40
        local enemyTb = {"通用_猎人_小怪_青年男_斧","通用_猎人_小怪_青年男_弓","通用_偷猎者_小怪_偷猎者男_斧","通用_偷猎者_小怪_偷猎者男_弓","通用_流氓_小怪_男小弟_拳",
        "通用_流氓_小怪_男小弟_脚","通用_乞丐_小怪_乞丐男_拳","通用_乞丐_小怪_乞丐男_脚","通用_打手_小怪_护院男_掌","通用_打手_小怪_护院男_长棍",
        "通用_和尚_小怪_和尚_掌","通用_和尚_小怪_和尚_长棍","通用_恶僧_小怪_恶僧_脚","通用_恶僧_小怪_恶僧_刀","通用_江湖武人_小怪_门派弟子男_暗器",
        "通用_江湖武人_小怪_门派弟子女_暗器","通用_刺客_小怪_男杀手_弓","通用_刺客_小怪_女飞贼_弓"}

        
        --随机召唤其中6个角色，等级为level
        local index = math.random(1, #enemyTb)
        local hpRate = 1 + 0.001*level*math.random(15, 25)
        local opRate = 1 + 0.001*level*math.random(15, 25)
        local odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(4, 2), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(4, -4), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(6, 2), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(6, -4), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(4, 0), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(6, 0), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        local sLevel = math.floor(1 + level/5)
        local sTable = {"万钧重锤","霜天封雪","银针返命","风露杏雪","大藏心经","吐纳法","不动如山","天罡真气","龙虎心法","铁甲阵法",
                        "藏锋积锐","霸王怒吼","梧叶舞秋风","浴火涅槃","江湖刀客","叱咤狂熊","木人筑","豪云剑魂","血沸剑魂",
                        "刺隐剑魂","如归剑魂","青莲剑魂","朔风吹雪","千面无常","分身解厄","灵兽助阵","迷风剪影","神射手",
                        "神机诛恶","光明圣火"}

        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
    end)
end

function start()
    
end
