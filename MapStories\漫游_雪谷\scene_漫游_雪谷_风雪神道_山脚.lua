
---@type 漫游_雪谷_风雪神道_山脚
local context = require("MapStories/漫游_雪谷/scene_漫游_雪谷_风雪神道_山脚_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

---组件相关
local extensions = require("MapStories/漫游_雪谷/extension")
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type StringWidget
local stringWidget = extensions.widgets.StringWidgetInfo.widget
---@type 雪谷_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 雪谷_ItemData]]
---@type 雪谷_StringItemData
local stringItemData = extensions.widgets.StringWidgetInfo.items

s.loadFlags(context.flags)
---@type flags_漫游_雪谷
local flags = context.flags

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.readyToStart()
    reloadAllSceneStates()
end

function reloadAllSceneStates()
    --明慧
    s.setActive(npcs.minghui,flags.sd_xqg_step ~= 2)
    if flags.sd_drug_step == 2 then
        s.setPos(npcs.minghui,pos.MH_shangshan)
    end
    
    --洞口
    s.setActive(objs.caveGate,flags.sd_cave_open == 0)
end

function eventGrass()
    if flags.sd_huixiangcao_collected == 0 then
        s.aside("这里是回香草采集。")
        itemWidget:addItem(itemData.huixiangcao,1)

        flags.sd_huixiangcao_collected = 1
        s.finishTask(400013, 1,2)
    else
        s.aside("回香草已经被采完了。")
    end
    
end

function eventHerb()
    if flags.sd_zhuzishen_collected == 0 then
        s.aside("这里是珠子参采集。")
        itemWidget:addItem(itemData.zhuzishen,1)

        flags.sd_zhuzishen_collected = 1
    else
        s.aside("珠子参已经被采完了。")
    end
    
end

function talkToMinghui()
    if flags.sd_drug_step == 0 then
        talkToMinghui001()
        flags.sd_drug_step = 1
    elseif flags.sd_drug_step == 1 then
        talkToMinghui002()
    elseif flags.sd_xqg_step == 0 then
        talkToXqgdizi001()
        flags.sd_xqg_step = 1
    elseif flags.sd_xqg_step == 1 then
        talkToXqgdizi002()
    end
end

function talkToMinghui001()
    s.talk(npcs.minghui,"且慢，请问小施主身上可有治疗淤血的草药？")
    s.talk(npcs.moqi,"没有……")
    s.talk(npcs.minghui,"哦，不打紧，贫僧在原地歇息一阵，或许就自行恢复了。")
    s.talk(npcs.moqi,"这位师傅哪里受伤了？")
    s.talk(npcs.minghui,"贫僧从东海边一座古刹寺庙而来，因这雪谷结冰路滑，不慎扭伤了脚踝，让小施主见笑了。")
    s.talk(npcs.moqi,"东海？那可是千里之外！")
    s.talk(npcs.minghui,"路虽遥远，但只要心中念着目标，双眼顾着脚下，倒也没那么漫长艰巨了。")
    s.talk(npcs.moqi,"来此为了何事？")
    s.talk(npcs.minghui,"贫僧打算攀上前面的悬崖，赴一位故人之约，可是眼下却因腿脚不便而寸步难行，佛祖保佑，但愿贫僧不要因此失约。")
    
    s.addTask(400310,1)
end

function talkToMinghui002()
    s.talk(npcs.minghui,"本以为近在咫尺，没想到却在这雪山脚下扭伤了脚踝……")

    local ret = s.selectTalk(npcs.moqi, "……", {"你看这个", "没事"})

    if ret == 0 then
        local submitItem = s.submitTempItem("请提交可以止血化瘀的草药！")
        if (submitItem ~= nil) then
            if (submitItem[0].Key == itemData.zhuzishen.key) then
                
                itemWidget:removeItem(itemData.zhuzishen,1)

                s.talk(npcs.minghui,"小施主，这草药是你特意采给贫僧的？萍水相逢却蒙你照顾，贫僧真不知该如何感谢。")
                s.blackScreen()
                s.aside("明慧在患处敷好伤药。")
                s.aside("片刻后……")
                s.lightScreen()

                s.talk(npcs.minghui,"唔……感觉好多了，多谢小施主相助，贫僧这就上山去。")
                s.blackScreen()
                s.aside("明慧受伤的腿脚似乎并未好透，不过他显然不愿再耽搁片刻，立即往山上小路走去……")
                
                flags.sd_main_step = flags.sd_main_step + 1
                flags.sd_drug_step = 2

                s.finishTask(400310,1)
                reloadAllSceneStates()
                s.lightScreen()

            else
                s.talk(npcs.minghui,"这个……贫僧以为似乎并没有用。")
            end
        else
            s.aside("你没有提交任何东西。")
        end
    end
end


function talkToXqgdizi()
    if flags.sd_main_step == 0 then
        talkToXqgdizi000()
    elseif flags.sd_xqg_step == 0 then
        talkToXqgdizi001()
        flags.sd_xqg_step = 1
    elseif flags.sd_xqg_step == 1 then
        talkToXqgdizi002()
    else
        talkToXqgdizi003()
    end
end

function talkToXqgdizi000()
    s.talk("玄阙宫守山弟子甲","走走走！玄阙宫有令，闲杂人等一律不准入山！")
end

function talkToXqgdizi001()
    s.talk("玄阙宫守山弟子甲","走走走！管你是僧是道，玄阙宫有令，闲杂人等一律不准入山！")
    s.talk(npcs.minghui,"施主误会了，贫僧并无歹意，只是打算往那雪崖——")
    s.talk("玄阙宫守山弟子乙","少废话！咱兄弟俩一年到头守在这里，像你这样的不知要见多少，可规矩就是规矩，懂吗？")
    s.talk(npcs.minghui,"施主真的误会贫僧了，贫僧——")
    s.talk("玄阙宫守山弟子乙","啰里吧嗦！再纠缠下去休怪我不客气！")
    s.talk(npcs.moqi,"你们为何为难这位师傅？")
    s.talk("玄阙宫守山弟子乙","你这爱管闲事的穷小子又是谁？")
    s.talk(npcs.moqi,"我是谁不重要，这位师傅千里迢迢来到这里，只为了赴一场故人之约，你们也太不近人情了。")
    s.talk("玄阙宫守山弟子甲","笑话，玄阙宫是你家开的？外人一律不得入山，这是早就定好的规矩。")
    
    s.talkThink(npcs.moqi,"（看来我得想想办法……）")
    s.addTask(400311,1)
end

function talkToXqgdizi002()
    s.talk("玄阙宫守山弟子甲","外人一律不得入山，这是早就定好的规矩。")

    local ret = s.selectTalk(npcs.moqi, "……", {"施以恩惠", "强行闯关","事不关己"})
    if ret == 0 then
        local submitItem = s.submitTempItem("请提交有价值的东西！")
        if (submitItem ~= nil) then
            if (submitItem[0].Key == itemData.zhuzishen.key) then--todo:待调整

                itemWidget:removeItem(itemData.zhuzishen,1)--todo:待调整

                s.talk("玄阙宫守山弟子甲","嗯……你这小子心肠不错，倒是知道体恤我们这些守山弟子的不易。")
                s.talk("玄阙宫守山弟子甲","你们可以上去了，但是记住千万不要捣乱，若是给长老发现，咱们都脱不了干系。")

                talkToMinghui003()
                flags.sd_main_step = flags.sd_main_step + 1
                flags.sd_xqg_step = 2

                s.finishTask(400311,1)
                s.blackScreen()
                reloadAllSceneStates()
                s.lightScreen()

            else
                s.talk("玄阙宫守山弟子甲","你耍我们呢？这个顶什么用！")
            end
        else
            s.aside("你没有提交任何东西。")
        end
    elseif ret == 1 then
        s.talk("玄阙宫守山弟子甲","怎么？好生劝你不听，打算来硬的？咱哥俩就是专门干这个的！")

        local isWin = s.battle("楚家庄_漫游_黑衣人")--todo：待调整
        if isWin then
            s.talk("玄阙宫守山弟子甲","别打了，别打了！让你们过去总行了吧？")
            s.talk("玄阙宫守山弟子甲","唉！咱兄弟俩做这份差事也不容易，油水没多少，一年到头还不让回家，你们上去以后可千万不要捣乱，若是给长老发现，咱们都脱不了干系。")

            talkToMinghui003()
            flags.sd_main_step = flags.sd_main_step + 1
            flags.sd_xqg_step = 2

            s.finishTask(400311,1)
            s.blackScreen()
            reloadAllSceneStates()
            s.lightScreen()
        else
            s.talkThink(npcs.moqi,"（打不过他们，看来我得想想别的办法……）")
        end
    end
end


function talkToXqgdizi003()
    s.talk("玄阙宫守山弟子甲","我没什么好说的了。")
end

function talkToMinghui003()
    --△明慧双手合十，向墨七深深作了一揖。
    s.talk(npcs.minghui,"小施主古道热肠，贫僧铭记在心。")
    s.talk(npcs.moqi,"刚才守山弟子对你言辞凶狠激烈，你却毫不退缩，难道不怕真把对方给逼急了？")
    s.talk(npcs.minghui,"他们也不是大奸大恶之人，都有各自的苦衷罢了，如果把贫僧痛打一顿能够使他们好受，贫僧也绝不会有怨言。")
    s.talk(npcs.moqi,"真没见过如你一般迂腐之人。")
    --△明慧淡淡一笑，似乎并不觉得你这话冒犯了自己。
    s.talk(npcs.minghui,"时候不早了，贫僧还要继续往山上赶路，你我不如稍后再叙。")
end


function talkToShoushanren()
    s.talk(npcs.shoushanren,"我是守山人。")
end


function transportShanlu()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_风雪神道_山路.unity", "位置/Shanjiao")
end

function transportShanya()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_风雪神道_悬崖.unity", "位置/Shanjiao")
end

function transportYingdi()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_流亡者营地.unity", "位置/Shanjiao")
end