--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/主线_青羽录6/flags_主线_青羽录6")


---@class 角色_主线_青羽录6_楚正雄交谈
local npcs = {
    --- "荆成"
    zhujue = "角色/zhujue",
    --- "莫知酉"
    mozhiyou = "角色/mozhiyou",
    --- "楚正雄"
    chuzhengxiong = "角色/chuzhengxiong",
    --- "老仆"
    laopu = "角色/laopu",
    --- "侍女"
    shinv = "角色/shinv",
    --- "神秘人"
    shenmiren = "角色/shenmiren",
    --- "侍女甲"
    shinvA = "角色/shinvA",
    --- "侍女乙"
    shinvB = "角色/shinvB",
}

---@class 物体_主线_青羽录6_楚正雄交谈
local objs = {
    trigger_behind = "物体/trigger_behind",
    trigger_left = "物体/trigger_left",
    trigger_right = "物体/trigger_right",
    guide_chuzhengxiong = "物体/guide_chuzhengxiong",
}

---@class 相机_主线_青羽录6_楚正雄交谈
local cameras = {
    --- "初始相机"
    init = "相机/init",
    --- "初始推进相机"
    init_zoomIn = "相机/init_zoomIn",
}

---@class 位置_主线_青羽录6_楚正雄交谈
local pos = {
    --- "开始位置"
    JC = "位置/JC",
    --- "神秘人走开位置"
    SMR_leave = "位置/SMR_leave",
}

---@class 资产_主线_青羽录6_楚正雄交谈
local assets = {
    --- "青羽录6_楚正雄交谈"
    qyl1_6_1 = "资产/qyl1_6_1",
}

---@class 动作_主线_青羽录6_楚正雄交谈
local animationClips = {
}

---@class 主线_青羽录6_楚正雄交谈
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
