local bcapi = require("BattleClientA<PERSON>")
local battle = args["battle"]
bcapi.battle = battle

function init()
end

function start()
    bcapi.add_timer_trigger(0, TalkStart)
end

function TalkStart()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_沙匪营地_老大#副本_沙匪营地_老大", "事已至此，多说无益。")
        bcapi.talk("副本_沙匪营地_老大#副本_沙匪营地_老大", "我等皆已研习化血神功，合气连枝，今日便拿你们小试牛刀！")
        bcapi.resume_and_show_ui()
    end)
end