local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    bcapi.async_call(function()
        local teamValues = bcapi.get_team(0).ValueDict
        local team = bcapi.get_team(0)

        local hpLvStr = teamValues["生命强化等级"]
        local atkLvStr = teamValues["攻击强化等级"]
        local defLvStr = teamValues["防御强化等级"]

        ---@type number
        local hpRate = 1
        local hpLv = tonumber(hpLvStr)
        if(hpLv == 1)then
            hpRate = 1.2
        elseif(hpLv == 2)then
            hpRate = 1.45
        elseif(hpLv == 3)then
            hpRate = 1.75
        end

        ---@type number
        local opRate = 1
        local atkLv = tonumber(atkLvStr)
        --atkLv为1的时候提高15%，为2的时候提高35%，为3的时候提高60%
        if(atkLv == 1)then
            opRate = 1.15
        elseif(atkLv == 2)then
            opRate = 1.35
        elseif(atkLv == 3)then
            opRate = 1.6
        end
        
        --defLv为1的时候提高10%，为2的时候提高50%，为3的时候提高150%
        ---@type number
        local odRate = 1
        local defLv = tonumber(defLvStr)
        if(defLv == 1)then
            odRate = 1.1
        elseif(defLv == 2)then
            odRate = 1.5
        elseif(defLv == 3)then
            odRate = 2.5
        end

        bcapi.create_and_summon_role("挑战_玄阙地宫_机关熊", 45, bcapi.get_team(0), bcapi.Vector2(-6, 0), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")
        --local sCard = bcapi.add_strategy_card("梧叶舞秋风", bcapi.get_average_strategy_card_level, team)
    end)
end

function start()

end
