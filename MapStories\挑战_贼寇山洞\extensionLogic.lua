local s = require("StoryApi")

local extension = require 'MapStories/挑战_贼寇山洞/extension'
---@type flags_挑战_贼寇山洞
local flags = require("MapStories/挑战_贼寇山洞/flags_挑战_贼寇山洞")
s.loadFlags(flags)
---@type 挑战_贼寇山洞
local main = require 'MapStories/挑战_贼寇山洞/scene_挑战_贼寇山洞_h'

---不可改名
function get_string_item_desc(itemKey,sceneId)
    
    return "没有描述"
end

function on_use_item(itemKey,sceneId)

end

function on_use_testItem1()
end