
---@type 主线_青羽录9
local context = require("MapStories/主线_青羽录9/scene_主线_青羽录9_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local assets = context.assets
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

local nani = "蜃境/主线/青羽录_幻梦"

local function playNani(tag,transitionTime,alpha)
    return s.runAvgTag(nani, tag,transitionTime,alpha)
end


s.loadFlags(context.flags)
---@type flags_主线_青羽录9
local flags = context.flags
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start() 
    s.setActive(objs.trigger_left_c,false)
    s.setActive(npcs.shifu,true)
    s.setActive(npcs.shixiong,true)
    s.setActive(objs.trigger_left,false)
    s.setPos(npcs.shixiong,pos.pos_shixiong)
    s.setPos(npcs.zhujue,pos.pos_start)

    s.playMusic("茧",5,nil,3)
    s.readyToStart(false)

    s.playTimeline(assets.gwl_start,false)
    s.lightScreen()
end 

function bridge()
    s.camera(cameras.camera_bridge,true,true,blendHintEnum.Custom,1,true,true)
    playNani("桥",nil,0)
    s.camera(cameras.camera_follow,true,true,blendHintEnum.Custom,1,true,true)
end

function grass()
    s.setActive(objs.trigger_grass,false)
    s.camera(cameras.camera_grass,true,true,blendHintEnum.Custom,1,true,true)
    playNani("草地",nil,0)
    s.camera(cameras.camera_follow,true,true,blendHintEnum.Custom,1,true,true)
end

function colliderTips()
    s.camera(cameras.camera_left,true,true,blendHintEnum.Custom,1,true,true)
    playNani("逃离此地",nil,0)
    s.camera(cameras.camera_follow,true,true,blendHintEnum.Custom,1,true,true)
    s.move(npcs.zhujue,pos.pos_back,1,true)
end


function colliderTips1()
    playNani("逃离此地",nil,0)
    s.camera(cameras.camera_follow_1,true,true,blendHintEnum.Custom,1,true,true)
    s.move(npcs.zhujue,pos.pos_back_1,1,true)
end


function tree()
    --关闭触发
    s.setActive(objs.trigger_tree,false)

    local offset = CS.UnityEngine.Vector3(0,1.75,0)
    s.camera(cameras.camera_tree1,true,true,blendHintEnum.Custom,1,true,true)
    s.wait(1)
    s.lookAt(npcs.zhujue,offset,pos.pos_looktree,true)
    s.camera(cameras.camera_tree2,true,true,blendHintEnum.Custom,1,true,true)

    playNani("树_1",nil,0)

    s.wait(1)
    s.camera(cameras.camera_tree1,true,true,blendHintEnum.Custom,1,true,true)
    s.lookAt(npcs.zhujue,offset,pos.pos_world_right,false)
    playNani("树_2",nil,0)

    s.soloAnimState(npcs.zhujue,roleAnims.BDeny,true)
    playNani("树_3",nil,0)
    s.wait(1)

    playNani("树_4",nil,0)

    s.camera(cameras.camera_follow,true,true,blendHintEnum.Custom,1.5,true,true)
    s.wait(0.5)

    --打开左边的碰撞器
    s.setActive(objs.trigger_left,true)
end

function pipa()
    s.camera(cameras.camera_pipa,true,true,blendHintEnum.Custom,1.5,true,true)
    playNani("枇杷_1",nil,0)

    s.playTimeline(assets.gwl_shijie,false)
    s.lightScreen()

    playNani("枇杷_2",nil,0)

    s.camera(cameras.camera_follow,true,true,blendHintEnum.Custom,1.5,true,true)
end

function stone()
    s.camera(cameras.camera_stone,true,true,blendHintEnum.Custom,1.5,true,true)
    playNani("太极石_1",nil,0)

    s.playTimeline(assets.gwl_shixiong,false)
    s.lightScreen(3)

    playNani("太极石_2",nil,0)
    s.camera(cameras.camera_follow,true,true,blendHintEnum.Custom,1.5,true,true)
end

function enter_pollution()
    s.setActive(objs.trigger_camChange,false)
    
    s.cameraAsync(cameras.camera_p_n_bridge,true,true,blendHintEnum.Custom,1,true,true)

    playNani("进入污染之地_1",nil,0)

    s.playMusic("遗忘之地",3,nil,3)
    s.cameraAsync(cameras.camera_p_f_bridge,true,true,blendHintEnum.Custom,1,true,true)

    playNani("进入污染之地_2",nil,0)

    s.setActive(objs.trigger_left_c,true)
    s.camera(cameras.camera_follow_1,true,true,blendHintEnum.Custom,1,true,true)
end

function shifu()
    s.setActive(objs.trigger_shifu ,false)

    s.cameraAsync(cameras.camera_shifu,true,true,blendHintEnum.Custom,1.5,true,true)
    --s.blackScreen()
    s.setActive(npcs.shifu,false)


    s.playTimeline(assets.gwl_shifu,false)

    --临时跳过timeline
    --s.popInfo("跳过timeline")


    s.lightScreen()

    --s.cameraAsync(cameras.camera_shifu,true,true,blendHintEnum.Cut,true,true)

    playNani("师父_1",nil,0)

    s.animState(npcs.zhujue,"BShock_Loop",false)
    s.cameraAsync(cameras.camera_shifu_end,true,true,blendHintEnum.Custom,1,true,true)
    s.moveAsync(npcs.zhujue,pos.pos_shifu,5,true,"",0,nil)
    s.wait(0.5)
    playNani("师父_2",nil,0)
    s.wait(0.5)

    playNani("师父_3",nil,0)

    s.wait(0.5)
    s.soloAnimState(npcs.zhujue,roleAnims.BDeny,true)
    playNani("师父_4",nil,0)
    s.soloAnimState(npcs.zhujue,roleAnims.BDeny,false)
    s.wait(0.5)

    s.turnTo(npcs.zhujue,pos.pos_world_right,true)
    playNani("师父_5",nil,0)


    s.camera(cameras.camera_follow_1,true,true,blendHintEnum.Custom,1,true,true)
end

function shixiong()
    s.setActive(objs.trigger_shixiong,false)
    s.camera(cameras.camera_shixiong,true,true,blendHintEnum.Custom,1.5,true,true)
    s.setActive(npcs.shixiong,false)


    s.playTimeline(assets.gwl_shixiong_change)
    s.setNewbieFlags("chapter_qyl_15_finished",1)
    s.finishInfiniteStory(true)

end

function darker()
    s.setActive(objs.trigger_darker,false)
    s.soloAnimState(npcs.shifu,"BGesture",true)

    playNani("更深处_1",nil,0)

    s.wait(1)
    s.cameraAsync(cameras.camera_darker,true,true,blendHintEnum.Custom,1,true,true)

    playNani("更深处_2",nil,0)

    s.cameraAsync(cameras.camera_follow_1,true,true,blendHintEnum.Custom,1,true,true)
    playNani("更深处_3",nil,0)
end

function tingzi ()
    s.camera(cameras.camera_tingzi,true,true,blendHintEnum.Custom,1,true,true)
    playNani("逃离此地",nil,0)
    -- s.soloAnimState(npcs.zhujue,roleAnims.BDeny,true)
    -- s.talk(npcs.zhujue,"什么意思？附近哪有心？难道是……这个<color=yellow>长得像“心”的果子？</color>")
    -- s.talk(npcs.zhujue,"也许这些<color=yellow>果子</color>能指引我走出此地。")
    -- s.soloAnimState(npcs.zhujue,roleAnims.BDeny,false)
    s.camera(cameras.camera_follow_1,true,true,blendHintEnum.Custom,1,true,true)
end