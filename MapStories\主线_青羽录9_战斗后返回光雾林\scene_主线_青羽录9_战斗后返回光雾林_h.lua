--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/主线_青羽录9_战斗后返回光雾林/flags_主线_青羽录9_战斗后返回光雾林")


local npcs = {
    --- "荆成"
    JingCheng = "角色/JingCheng",
    --- "师父"
    ShiFu = "角色/ShiFu",
    --- "楚青"
    ChuQing = "角色/ChuQing",
}

local objs = {
}

local cameras = {
}

local pos = {
    --- "楚青移动终点位置"
    Pos_ChuQing = "位置/Pos_ChuQing",
    Pos_start = "位置/Pos_start",
}

local assets = {
}

local animationClips = {
}

---@class 主线_青羽录9_战斗后返回光雾林
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
