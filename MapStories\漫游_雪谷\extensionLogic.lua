local s = require("StoryApi")

local extension = require 'MapStories/漫游_雪谷/extension'
---@type flags_漫游_雪谷
local flags = require("MapStories/漫游_雪谷/flags_漫游_雪谷")
s.loadFlags(flags)
---@type 漫游_雪谷_古渡人家
local gudurenjia = require 'MapStories/漫游_雪谷/scene_漫游_雪谷_古渡人家_h'
---@type 漫游_雪谷_流亡者营地
local liuwnagzheyingdi = require 'MapStories/漫游_雪谷/scene_漫游_雪谷_流亡者营地_h'
---@type 漫游_雪谷_荒原雪林
local huangyuanxuelin = require 'MapStories/漫游_雪谷/scene_漫游_雪谷_荒原雪林_h'
---@type 漫游_雪谷_远古地宫_入口
local yuangudigong = require 'MapStories/漫游_雪谷/scene_漫游_雪谷_远古地宫_入口_h'
---@type 漫游_雪谷_远古地宫_地下
local yuangudigongdixia = require 'MapStories/漫游_雪谷/scene_漫游_雪谷_远古地宫_地下_h'
---@type 漫游_雪谷_风雪神道_山脚
local fengxueshendao = require 'MapStories/漫游_雪谷/scene_漫游_雪谷_风雪神道_山脚_h'
---@type 漫游_雪谷_风雪神道_山路
local fengxueshendaoshanlu = require 'MapStories/漫游_雪谷/scene_漫游_雪谷_风雪神道_山路_h'
---@type 漫游_雪谷_风雪神道_悬崖
local fengxueshendaoyuanya = require 'MapStories/漫游_雪谷/scene_漫游_雪谷_风雪神道_悬崖_h'

local moqi = "角色/moqi"

---@type ItemWidget
local itemWidget = extension.widgets.ItemWidgetInfo.widget
---@type 雪谷_ItemData
local itemData = extension.widgets.ItemWidgetInfo.items --[[@as 雪谷_ItemData]]

---@type StringWidget
local stringWidget = extension.widgets.StringWidgetInfo.widget
---@type 雪谷_StringItemData
local stringItemData = extension.widgets.StringWidgetInfo.items

function on_use_item(itemKey,sceneId)
    if itemKey == itemData.yangpizhi1.key or itemKey == itemData.yangpizhi2.key or itemKey == itemData.yangpizhi3.key then
        on_use_yangpizhi()
    end
end

---不可改名
function get_string_item_desc(itemKey,sceneId)
    if itemKey == stringItemData.yd_rep.key then
        return "墨七在流亡者营地声望是"..stringItemData.yd_rep.value
    elseif itemKey == stringItemData.xl_rep.key then
        return "墨七与雪林原住民的关系是"..stringItemData.xl_rep.value
    elseif itemKey == stringItemData.gd_rep.key then
        return "墨七与古渡名门的关系是"..stringItemData.gd_rep.value
    end
    return ""
end

function on_use_yangpizhi()
    if flags.gd_main_step == 3 then
        s.talk(moqi,"羊皮纸似乎可以拼凑起来。")
        s.talk(moqi,"唔……拼凑之后，原来是一张地图，莫非是在指引我前往标记的位置？")
        s.talk(moqi,"看来我得去一趟神道雪崖了。")
        --todo；可能还是需要一个任务指示
        flags.gd_main_step = 4

        itemWidget:removeItem(itemData.yangpizhi1,1)
        itemWidget:removeItem(itemData.yangpizhi2,1)
        itemWidget:removeItem(itemData.yangpizhi3,1)
        
        --todo:后续是否需要投放一个地图？——给张图？
    else
        s.aside("羊皮纸还没凑齐。")
    end
end