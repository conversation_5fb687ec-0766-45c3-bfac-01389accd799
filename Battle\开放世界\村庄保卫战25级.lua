local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

local level = 20

function init()
    bcapi.hide_ui("疗伤")
    bcapi.reset_limit_time(180)
    -- 开局创建角色
    createRoles()
end

function start()
    -- 1秒后开始第1次AVG
    bcapi.add_timer_trigger(0.1, startTalk)
    -- 每五秒，生成僵尸
    for i=0, 23 do
        bcapi.add_timer_trigger(6*(i+1), createZombie)
    end
    --特殊胜利和失败
    bcapi.add_timer_trigger(90, createWave3)
    bcapi.add_condition_trigger("[%battle_time%][>]179", 0, win)
    -- bcapi.add_timer_trigger(1, checkLose)
end

-- 取玩家队伍提交的策略卡
local dict = bcapi.get_team(0).ValueDict

function createRoles()
    bcapi.async_call(function()
        -- 创建玩家角色（不带绝招牌）
        bcapi.create_join_role("世界_保卫战村民", 20, team, bcapi.Vector2(-7, 0), 0)
        bcapi.create_join_role("世界_保卫战村民栅栏", 20, team, bcapi.Vector2(-1, 0), 0)
        bcapi.create_join_role("世界_保卫战村民栅栏", 20, team, bcapi.Vector2(-1, 2), 0)
        bcapi.create_join_role("世界_保卫战村民栅栏", 20, team, bcapi.Vector2(-1, -2), 0)
        -- 带了六张默认的召唤生物牌，等级为玩家对应策略卡等级
        bcapi.add_strategy_card("江湖刀客", getStrategyLevel("江湖刀客等级"), team)
        bcapi.add_strategy_card("叱咤狂熊", getStrategyLevel("叱咤狂熊等级"), team)
        bcapi.add_strategy_card("木人筑", getStrategyLevel("木人筑等级"), team)
        bcapi.add_strategy_card("灵兽助阵", getStrategyLevel("灵兽助阵等级"), team)
        bcapi.add_strategy_card("神射手", getStrategyLevel("神射手等级"), team)
        bcapi.add_strategy_card("神机诛恶", getStrategyLevel("神机诛恶等级"), team)
        bcapi.add_timer_trigger(1, checkLose)
    end)
end

-- 随机怪物池(去掉了伥化老人)
local zombieTable = {"世界_保卫战伥化男子",
"世界_保卫战伥化喽啰",
"世界_保卫战伥化壮汉",
"世界_保卫战伥化女子",
"世界_保卫战伥化恶徒",
"世界_保卫战伥化普通门派弟子男",
"世界_保卫战伥化村姑",
"世界_保卫战伥化泼皮"}


--创建一个随机的怪物
function createZombie()
    bcapi.async_call(function()
        --创建怪物前检查场上怪物数量
        local posXTable = {6,3}
        local posYTable = {3,-3,0}
        local posXIndex = math.random(1,#posXTable)
        local posYIndex = math.random(1,#posYTable)
        if eTeam.FightingRoleCount < 6 then
            local index = math.random(1, #zombieTable)  
            bcapi.create_join_role(zombieTable[index], level, eTeam, bcapi.Vector2(posXTable[posXIndex], posYTable[posYIndex]), 0.1) 
        end
    end)
end

function createWave3(...)
    bcapi.async_call(function()
        bcapi.create_join_role("世界_保卫战伥化女子", level, eTeam, bcapi.Vector2(-6, -2), 0.1) 
        bcapi.create_join_role("世界_保卫战伥化恶徒", level, eTeam, bcapi.Vector2(-6, 2), 0.1) 
        bcapi.create_join_role("世界_保卫战伥化老人", level, eTeam, bcapi.Vector2(6, -5), 0.1) 
        bcapi.create_join_role("世界_保卫战伥化壮汉", level, eTeam, bcapi.Vector2(3, 3), 0.1) 
        bcapi.create_join_role("世界_保卫战伥化泼皮", level, eTeam, bcapi.Vector2(3, -3), 0.1) 
        bcapi.create_join_role("世界_保卫战伥化村姑", level, eTeam, bcapi.Vector2(6, 5), 0.1) 
    end)
end

--取策略卡等级函数
function getStrategyLevel(cardLevelstr)
    local cardLevel = 1
    for k,v in pairs(dict) do
        -- print("当前值"..k..v)
        if(k == cardLevelstr )then
            cardLevel = v
            break
        end
    end
    return cardLevel
end

function checkLose()
    bcapi.add_condition_trigger("[%c->teammate:世界_保卫战村民%][<]1", 0, lose)
end

function startTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("男村民_村庄保卫", "大侠救我！！")
        bcapi.talk("主角", "冷静，听我指挥！")
        bcapi.resume_and_show_ui()
    end)
end

function win()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("男村民_村庄保卫", "官兵赶来救援了，看来终于是活下来了！")
        bcapi.win()
    end)
end

function lose()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("男村民_村庄保卫", "我还有个三岁的孩子……")
        bcapi.talk("主角", "……")
        bcapi.resume_and_show_ui()
        bcapi.lose()
    end)
end

