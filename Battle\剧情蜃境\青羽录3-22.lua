local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    battle.ForceNotEndBattle = true
end

function start()
    TalkBegin()
    bcapi.add_condition_trigger("[%c->enemy:蜃境_楚青_蝎子精%][<]1", 0, TalkEnd)
    bcapi.add_condition_trigger("[%c->fighting_friend_hero_count%][<]1", 0, BattleLose)
end

function TalkBegin()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("主角#主角","看来她就是这间蜃境的幕后之主。")
        bcapi.talk("云舞笙#云舞笙#惊讶", "楚青……怎会变成这般模样……")
        bcapi.talk("荆成#荆成#严肃", "不过是怨念残影而已，阁主，小舞，一起结束这一切吧。")
        bcapi.talk("楚青#蜃境_楚青_蝎子精#蜃境", "哈哈哈哈！早该结束了！")
        bcapi.resume_and_show_ui()
    end)
end

function TalkEnd()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("楚青#蜃境_楚青_蝎子精#蜃境", "剑川之主……")
        bcapi.talk("楚青#蜃境_楚青_蝎子精#蜃境", "你终究会与我一样，身形俱灭，彻底消亡。恍若……不曾存在过。")
        bcapi.resume_and_show_ui()
        battle.ForceNotEndBattle = false
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
