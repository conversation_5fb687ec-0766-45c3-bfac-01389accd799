--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_风雪神道/flags_挑战_风雪神道")


---@class 角色_挑战_风雪神道山路
local npcs = {
    zhujue = "角色/zhujue",
    boss2 = "角色/boss2",
    enemy_4 = "角色/enemy_4",
    enemy_5 = "角色/enemy_5",
    enemy_6 = "角色/enemy_6",
}

---@class 物体_挑战_风雪神道山路
local objs = {
    trash9 = "物体/trash9",
    trash10 = "物体/trash10",
    trash11 = "物体/trash11",
    trash12 = "物体/trash12",
    trash13 = "物体/trash13",
    trash14 = "物体/trash14",
    trash15 = "物体/trash15",
    trash16 = "物体/trash16",
    trash17 = "物体/trash17",
    boss2Trigger = "物体/boss2Trigger",
    teleport = "物体/teleport",
    lost_shoe = "物体/lost_shoe",
    teleport2 = "物体/teleport2",
}

---@class 相机_挑战_风雪神道山路
local cameras = {
    camera_boss1 = "相机/camera_boss1",
}

---@class 位置_挑战_风雪神道山路
local pos = {
    start = "位置/start",
    teleport = "位置/teleport",
    pos_boss = "位置/pos_boss",
    pos_boss_1 = "位置/pos_boss_1",
    pos_boss_2 = "位置/pos_boss_2",
    pos_boss_3 = "位置/pos_boss_3",
    pos_boss_defeat = "位置/pos_boss_defeat",
}

---@class 资产_挑战_风雪神道山路
local assets = {
}

---@class 动作_挑战_风雪神道山路
local animationClips = {
}

---@class 挑战_风雪神道山路
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
