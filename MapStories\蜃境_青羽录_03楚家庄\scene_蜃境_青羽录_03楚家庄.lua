
local context = require("MapStories/蜃境_青羽录_03楚家庄/scene_蜃境_青羽录_03楚家庄_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_青羽录_03楚家庄
local flags = context.flags
--不可省略，用于外部获取flags
newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")

function getFlags(...)
    return flags
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")
require("MapStories/MapStoryCommonTools")

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function start()
    print("start called")
    refreshMapStates()
    s.readyToStart(true)
    try_execute_newbieGuide()
end

function try_execute_newbieGuide(...)
    -- 如果qyl_08未解锁，且qyl_08已通关，则finish qyl_08
    if (not s.hasUnlockMemorySlot("qyl_08") and newbieApi.getNewbieFlag(newbieFlags.chapter_qyl_08_finished) == 1) then
        capi.finishMemorySlot("qyl_08")
        next_level_step(true)
    end
    -- qyl_09
    if (not s.hasUnlockMemorySlot("qyl_09") and newbieApi.getNewbieFlag(newbieFlags.chapter_qyl_09_finished) == 1) then
        capi.finishMemorySlot("qyl_09")
        next_level_step(true)
    end

    s.nextStep("newbieGuideInLevel")
end


function newbieGuideInLevel()
    if s.hasUnlockMemorySlot("qyl_13") and newbieApi.getNewbieFlag(newbieFlags.tutorShefangKezhan) == 0 then
        require("Newbie/newbie_guide")
        wait_close_bonusPanel()
        switchToJianzhichuan()
        -- tutorShefangKezhan()
        -- newbieFlags.tutorShefangKezhan = 1
        return
    end


    -- if(flags.qyl_08_playing == 1) then
    --     capi.finishMemorySlot("qyl_08") --CG：这里先临时这样写，回头要改下API
    --     next_level_step(true)
    --     flags.qyl_08_playing = 0
    -- end

    -- if(flags.qyl_09_playing == 1) then
    --     capi.finishMemorySlot("qyl_09") --CG：这里先临时这样写，回头要改下API
    --     next_level_step(true)
    --     flags.qyl_09_playing = 0
    -- end
end


--默认初始化的地图显隐状态
function reset_all()
    s.setActive(npcs.qyl_08, flags.level_step == 1)
    s.setActive(npcs.qyl_09, flags.level_step == 2)
    s.setActive(npcs.qyl_10, flags.level_step == 3)
    s.setActive(npcs.qyl_11, flags.level_step == 4)
    s.setActive(npcs.qyl_12, flags.level_step == 5)
    s.setActive(npcs.qyl_13, flags.level_step == 6)
    s.setActive(npcs.qyl_14, flags.level_step == 7)

    -- s.setActive(npcs.enemy_01, flags.enemy_01 == 0)
    s.animState(npcs.enemy_01,"Special_Loop_1",true)
    -- s.setActive(npcs.enemy_02, flags.enemy_02 == 0)
    -- s.setActive(npcs.enemy_03, flags.enemy_03 == 0)
    -- s.setActive(npcs.enemy_04, flags.enemy_04 == 0)
    -- s.setActive(npcs.enemy_05, flags.enemy_05 == 0)
    -- s.setActive(npcs.elite_06, flags.elite_06 == 0)
    -- s.setActive(npcs.elite_07, flags.elite_07 == 0)

    s.setActive(npcs.jingcheng,flags.level_step == 0)
    s.setActive(npcs.yunwusheng,flags.level_step == 0)
    s.setActive(npcs.lingling,flags.lingling ~= 2)

    if(flags.fire_done == 0)then
        flags.fire = 0
        flags.gmsh = 0
        flags.yhnp = 0
    else
        s.setActive(objs["fireTrigger1"],false)
        s.setActive(objs["fire1"],true)
        s.setActive(objs["fireTrigger2"],false)
        s.setActive(objs["fire2"],true)
        s.setActive(objs.chest_3,false)
    end

    if(flags.tree == 1)then
        s.setActive(objs.tree,false)
        s.setActive(objs.hint,false)
    elseif(flags.hint == 1)then
        s.setActive(objs.tree,true)
    else
        s.setActive(objs.tree,false)
        s.setActive(objs.hint,true)
    end
        
    s.removeAllGameMapGuides()
end

function disable_all()
    s.setActive(npcs.qyl_08, false)
    s.setActive(npcs.qyl_09, false)
    s.setActive(npcs.qyl_10, false)
    s.setActive(npcs.qyl_11, false)
    s.setActive(npcs.qyl_12, false)
    s.setActive(npcs.qyl_13, false)
    s.setActive(npcs.qyl_14, false)

    s.setActive(npcs.enemy_01, false)
    s.setActive(npcs.enemy_02, false)
    s.setActive(npcs.enemy_03, false)
    s.setActive(npcs.enemy_04, false)
    s.setActive(npcs.enemy_05, false)
end

function active_all()
    s.setActive(npcs.qyl_08, true)
    -- s.setActive(npcs.qyl_09, true)
    -- s.setActive(npcs.qyl_10, true)
    -- s.setActive(npcs.qyl_11, true)
    -- s.setActive(npcs.qyl_12, true)
    -- s.setActive(npcs.qyl_13, true)
    -- s.setActive(npcs.qyl_14, true)

    s.setActive(npcs.enemy_01, true)
    s.setActive(npcs.enemy_02, true)
    s.setActive(npcs.enemy_03, true)
    s.setActive(npcs.enemy_04, true)
    s.setActive(npcs.enemy_05, true)
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()
    s.playMusic("shangwuhui")

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 8) then
        taskStep = "forwardToExplore"
    elseif (step == 8) then
        taskStep = "leaveChuJiaZhuangShenJing"
    end
    s.setTaskStep("青羽录楚家庄", taskStep)

    reset_all()
    s.unTraceTask(10000001,50)
    if step == 0 then
        s.blackScreen()
        disable_all()
        s.camera(cameras.overview,true)
        ----第一次进入场景, 这里加入入场演出
        s.setTaskStep("青羽录楚家庄","forwardToExplore")
        s.setPos(npcs.zhujue,pos.zhujue_start_pos)
        s.wait(0.5)
        s.lightScreen()
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_楚家庄", "蜃境_青羽录_楚家庄_进入")
        s.blackScreen()
        s.camera(cameras.overview,false)
        s.camera(cameras.camera1,true)
        active_all()
        s.animState(npcs.enemy_01,"Special_Loop_1",true)
        s.lightScreen()
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_楚家庄", "蜃境_青羽录_楚家庄_进入2")
        s.camera(cameras.camera2,true,true,blendHintEnum.EaseInOut,1.5,true,true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_楚家庄", "蜃境_青羽录_楚家庄_进入3")
        s.blackScreen()
        s.setActive(npcs.jingcheng,false)
        s.setActive(npcs.yunwusheng,false)
        s.camera(cameras.camera2,false)
        s.wait(0.5)
        s.lightScreen()
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.qyl_08, 0.8)
    elseif step == 2 then
        level_guide_to(npcs.qyl_09, 0.8)
    elseif step == 3 then
        level_guide_to(npcs.qyl_10, 0.8)
    elseif step == 4 then
        --特殊处理一下，因为这个状态机是偏移的
        level_guide_to(npcs.qyl_11, 0.8)
        s.removeGameMapGuide(npcs.qyl_11)
        s.addGameMapGuide(objs.qyl_11_guide)
    elseif step == 5 then
        level_guide_to(npcs.qyl_12, 0.8)
    elseif step == 6 then
        level_guide_to(npcs.qyl_13, 0.8)
    elseif step == 7 then
        level_guide_to(npcs.qyl_14, 0.8)
    else
        --do nothing
        level_guide_to(objs.exit)
    end

    s.setActive(objs.qyl3_1002,not s.hasUnlockMemorySlot("qyl3_1002"))
    s.setActive(objs.qyl3_1003,not s.hasUnlockMemorySlot("qyl3_1003"))
    s.setActive(objs.qyl3_1004,not s.hasUnlockMemorySlot("qyl3_1004"))
end

----------------------OVERRIDE----------------------------------

function qyl_08_test()
    flags.qyl_08_playing = 0
    flags.level_step = 1
    refreshMapStates()
end

-- function qyl_08()
--     local id = "qyl_08"
--     if (s.ShowMemorySlot(id) == false) then
--         return
--     end
--     capi.startMemorySlot(id)
--     flags.qyl_08_playing = 1
--     s.removeAllGameMapGuides() --否则新地图有BUG，错误的引导
--     s.nextStep("play_qyl_08")
-- end

-- function play_qyl_08()
--     s.changeMapStory("主线_青羽录5") 
-- end

-- function qyl_09()
--     local id = "qyl_09"
--     if (s.ShowMemorySlot(id) == false) then
--         return
--     end
--     capi.startMemorySlot(id)
--     flags.qyl_09_playing = 1
--     s.removeAllGameMapGuides() --否则新地图有BUG，错误的引导
--     s.nextStep("play_qyl_09")
-- end

-- function play_qyl_09()
--     s.changeMapStory("主线_青羽录6") 
-- end

function qyl_08()
    executeMemSlot("qyl_08", quick_map_story)
end

function qyl_09()
    executeMemSlot("qyl_09", quick_map_story)
end

function qyl_10()
    executeMemSlot("qyl_10", quick_play_avg)
end

function qyl_11()
    executeMemSlot("qyl_11", function()
        quick_play_avg()
        s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录7_楚青客栈.unity")
    end)
end

function qyl_12()
    executeMemSlot("qyl_12", quick_play_avg)
end

function qyl_13()
    executeMemSlot("qyl_13", quick_play_avg)
end

function qyl_14()
    executeMemSlot("qyl_14", quick_play_avg,function()
    s.setTaskStep("青羽录楚家庄","leaveChuJiaZhuangShenJing")
    next_level_step(true)
    end)
end

function lingling()
    if(flags.lingling == 0)then
        flags.lingling = 1
        s.setTaskStep("青羽录楚家庄支线","findQinXian")
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_楚家庄", "蜃境_青羽录_楚家庄_泠泠")
        tryIfFirstSideTask()
        refreshMapStates()
    elseif(flags.lingling == 1)then
        if(flags.qinxian == 0)then
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_楚家庄", "蜃境_青羽录_楚家庄_泠泠反复")
        else
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_楚家庄", "蜃境_青羽录_楚家庄_泠泠2")
            s.finishTask(40050001,10)
            local capi = require("ChapterApi")
            capi.finishMemorySlot("qyl_sideTask_llzy", false)
            flags.lingling = 2
            s.setActive(npcs.lingling,false)
        end
    end
end

function enemy_02(id)
    executeMemSlot(id,
    nil,
    function()
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_楚家庄", "蜃境_青羽录_楚家庄_琴弦拿到")
            flags.qinxian = 1
            refreshMapStates()
    end,function()
            refreshMapStates()
    end,function()
            defaultLose()
    end)
end


function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("青羽录")
end

function touchFire(id)
    local rapi = require("RpgMapApi")
    local banTb = {}
    if(flags.gmsh == 1)then
        table.insert(banTb,"光明圣火")
    end
    if(flags.yhnp == 1)then
        table.insert(banTb,"浴火涅槃")
    end

    --  print(#banTb)
    local ret = rapi.submitStrategyCard("请选择要使用的策略卡",nil,banTb)
    
    if (ret ~= nil) then
        if (string.find(ret.getSubmitStrategyCardItem(0).Key,"火") ~= nil)then
            if(flags.yhnp == 1 and ret.getSubmitStrategyCardItem(0).Key == "浴火涅槃" or (flags.gmsh == 1 and ret.getSubmitStrategyCardItem(0).Key == "光明圣火"))then
                s.aside("这张策略卡已经使用过了。")
            else
                if(ret.getSubmitStrategyCardItem(0).Key == "光明圣火")then
                    flags.gmsh = 1
                end
                if(ret.getSubmitStrategyCardItem(0).Key == "浴火涅槃")then
                    flags.yhnp = 1
                end

                s.turnTo(npcs.zhujue,objs["fireTrigger" .. id])
                s.setActive(objs["fireTrigger" .. id],false)
                s.setActive(objs["fire" .. id],true)
                flags.fire = flags.fire + 1
            end
        else
            s.aside("好像没什么效果。")
            return
        end

        if(flags.fire >= 2)then
            s.blackScreen()
            s.setActive(objs.chest_3,true)
            s.wait(0.5)
            s.turnTo(npcs.zhujue,objs.chest_3)
            s.lightScreen()
        end
    end
end

function chest()
    local capi = require("ChapterApi")
    capi.finishMemorySlot("qyl_question_lightSoul")
    openTreasure("3")
    flags.fire_done = 1
    refreshMapStates()
end

function test1()
    s.setActive(objs["fireTrigger1"],true)
    s.setActive(objs["fire1"],false)
    s.setActive(objs["fireTrigger2"],true)
    s.setActive(objs["fire2"],false)
    s.setActive(objs.chest_3,false)
    flags.fire = 0
end

function hint()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_楚家庄", "蜃境_青羽录_楚家庄_谜题")
    flags.hint = 1
    s.setActive(objs.tree,true)
end

function tree()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_楚家庄", "蜃境_青羽录_楚家庄_解答")
    local capi = require("ChapterApi")
    capi.finishMemorySlot("qyl_question_sickTree")
    flags.tree = 1
    s.setActive(objs.tree,false)
    s.setActive(objs.hint,false)
    refreshMapStates()
end