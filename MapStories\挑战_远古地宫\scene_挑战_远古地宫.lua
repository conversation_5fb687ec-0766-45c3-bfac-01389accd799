---@type 挑战_远古地宫
local context = require("MapStories/挑战_远古地宫/scene_挑战_远古地宫_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_挑战_远古地宫
local flags = context.flags

local extensions = require 'MapStories/挑战_远古地宫/extension'

---@type StringWidget
local stringWidget = extensions.widgets.StringWidgetInfo.widget
---@type 挑战_远古地宫_StringItemData
local stringItemData = extensions.widgets.StringWidgetInfo.items
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type 挑战_远古地宫_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 挑战_远古地宫_ItemData]]

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
--每次载入调用
function start()
    s.playMusic("huanxing")

    if(s.getChallengeMapPersistentFlag("挑战_远古地宫","开场动画") == 0)then
        fullStart()
    else
        fastStart()
    end
    --fullStart()
end

function fullStart()
    s.setPos(npcs.zhujue, pos.start)
    s.talk(npcs.zhujue, "上次离开之后，这座地宫的防御系统已经被全面激活了。")
    s.blackScreen()
    s.camera(cameras.start_camera1, true)
    s.lightScreen()
    s.readyToStart()
    s.wait(0.5)
    s.camera(cameras.start_camera1_1,true,true,blendHintEnum.EaseInOut,1,true,true)

    s.blackScreen()
    s.camera(cameras.start_camera1_1, false)
    s.wait(1)
    s.camera(cameras.start_camera2, true)
    s.lightScreen()
    s.wait(0.5)
    s.camera(cameras.start_camera2_1,true,true,blendHintEnum.EaseInOut,1,true,true)
    s.blackScreen()
    s.camera(cameras.start_camera2_1, false)
    s.wait(1)
    s.camera(cameras.start_camera3, true)
    s.lightScreen()
    s.wait(0.5)
    s.camera(cameras.start_camera3_1,true,true,blendHintEnum.EaseInOut,1,true,true)
    
    s.talk(npcs.zhujue, "墨家先贤们虽然离开了，他们所制作的机关兽却还在忠心耿耿地执行着主人们所安排的职责——守卫这座地宫。")
    s.talk(npcs.zhujue, "……小心前进吧。")
    s.addTask("990001", "1")

    if(s.getChallengeMapPersistentFlag("挑战_远古地宫","获得成就任务") == 0)then
        s.addTask("990002", "1")
        s.addTask("990003", "1")
        s.addTask("990004", "1")
        s.addTask("990006", "1")
        s.addTask("990007", "1")
        s.addTask("990008", "1")
        s.addTask("990009", "1")
        s.addTask("990010", "1")
        s.addTask("990011", "1")
        s.setChallengeMapPersistentFlag("挑战_远古地宫","获得成就任务",1)
    end
    s.camera(cameras.overview, false)
    init_string_item()
    --出现小熊
    s.setActive(npcs.bear, true)
    s.setPos(npcs.bear, pos.start1)
    s.setChallengeMapPersistentFlag("挑战_远古地宫","开场动画",1)
end

function fastStart()
    s.blackScreen()
    s.setPos(npcs.zhujue, pos.start)
    s.addTask("990001", "1")
    init_string_item()
    s.setActive(npcs.bear, true)
    s.setPos(npcs.bear, pos.start1)
    s.wait(1)
    s.lightScreen()
end

function testF()
    s.setPos(npcs.zhujue, pos.boss4_test_pos)
    s.setPos(npcs.bear, pos.boss4_test_pos)
end

function init_string_item()
    stringWidget:showItem(stringItemData.CreatureInfo)
end

function storyStart()
    s.setActive(objs.storyStartTrigger, false)
end

function testOpenDoor()
    s.setActive(objs.testOpenDoor, false)
    --s.setActive(objs.stoneDoor0, false)
    s.move(objs.stoneDoor0, pos.door_pos, 1)
end

function teleportToPalace()
    s.blackScreen()
    s.setPos(npcs.zhujue, pos.secret_telepot)
    s.wait(1.1)
    s.lightScreen()
end

function teleportToSecretPlace()
    s.blackScreen()
    s.setPos(npcs.zhujue, pos.secret_start)
    s.wait(1.1)
    s.lightScreen()

    if(s.getChallengeMapPersistentFlag("挑战_远古地宫","初见泰坦") == 0)then
        s.setChallengeMapPersistentFlag("挑战_远古地宫","初见泰坦",1)
        firstEnterExBoss()
    end
end

function startBattle()
    --隐藏小熊避免遮挡
    s.setActive(npcs.bear, false)
    s.setActive(objs.testOpenDoor, false)
    s.blackScreen()
    s.setPos(npcs.zhujue, pos.battle_pos_1)
    s.turnTo(npcs.enemy1,npcs.zhujue)
    s.camera(cameras.start_enemy_camera_pos, true)
    s.lightScreen()
    s.wait(1)
    s.camera(cameras.start_enemy_camera_pos_1,true,true,blendHintEnum.EaseInOut,1,true,true)
    s.wait(1)
    s.talk(npcs.enemy1,"(发出尖锐的警告声)")
    s.talk(npcs.zhujue, "(叹息)这些机关造物的忠心，有时候倒比人类都更值得信任。")
    s.talk(npcs.zhujue, "可惜，我要先让你沉睡一会儿了。")

    local battleTb = {}
    local isWin,bResult = s.battle("远古地宫_战斗_开场", battleTb)
    if(isWin)then
        startBattleEnd()
    end 
end

function test1()
    -- flags.CreaturePower = 3
end

function startBattleEnd()
    s.blackScreen()
    s.camera(cameras.start_enemy_camera_pos_1, false)
    s.setActive(npcs.enemy1,false)
    s.lightScreen()
    s.talk(npcs.zhujue,"(从倒下的机关守卫中拾取了一些特制的机关零件)这是……")
    s.talk(npcs.zhujue,"(拍拍机关熊的外壳)小家伙，这些零件和你身上的似乎出于同源，也许能用来改造一下你。")
    s.popInfo("获得机关零件 × 1!")
    flags.isUpgradeAvailable = 1
    itemWidget:addItem(itemData.CreatureMoney,1)
    itemWidget:addItem(itemData.CreatureHpLvUpItem0,1)
    itemWidget:addItem(itemData.CreaturePowerLvUpItem0,1)
    itemWidget:addItem(itemData.CreatureDefenceLvUpItem0,1)
    s.aside("机关熊的改造系统已经激活！在物品中使用对应的升级核心，可以消耗机关零件对机关熊进行升级！")
    --s.addTask("990002", "1")
    --s.blackScreen()
    --s.camera(cameras.start_enemy_camera_pos, true,true,blendHintEnum.EaseInOut,1,true,true)
    --s.wait(1)
    --s.lightScreen()
    s.aside("远处传来了机关开启的声音")
    s.turnTo(npcs.zhujue, objs.stoneDoor0)
    s.camera(cameras.start_enemy_camera_pos_2,true,true,blendHintEnum.EaseInOut,1,true,true)
    s.move(objs.stoneDoor0, pos.door_pos, 1)
    --s.wait(1)
    s.talk(npcs.zhujue,"……这里竟然还有一道暗门，似乎可以更快通向地宫内部。但是也许会遇到更强大的看守。")
    s.camera(cameras.start_enemy_camera_pos_3,true,true,blendHintEnum.EaseInOut,1,true,true)
    s.talk(npcs.zhujue,"另一条道路则较为稳妥，也可以在沿途收集更多机关零件。")
    s.blackScreen()
    s.camera(cameras.start_enemy_camera_pos_3,false)
    s.lightScreen()
    s.talk(npcs.zhujue,"……该怎么样选择呢。")
    --出现小熊
    s.setActive(npcs.bear, true)
    s.finishTask("990001","1","2")
    --s.addTask("990003", "1")
    --s.addTask("990004", "1")
    s.addTask("990005", "1")
end

function enemyBattle(roleKey,cnt)
    local battleTb = {}

    --print("战斗的key是:" .. "远古地宫_战斗_" .. roleKey)
    battleTb = CalcBearAbility(battleTb)
    local isWin,bResult = s.battle("远古地宫_战斗_" .. roleKey, battleTb)
    if(isWin)then
        if(cnt == nil)then
            enemyBattleEnd(roleKey,1)
        else
            enemyBattleEnd(roleKey,cnt)
        end
    else
        s.finishInfiniteStory(true)
    end 
end

function CalcBearAbility(battleTb)
    battleTb["生命强化等级"] = flags.CreatureHp
    return battleTb
end

function enemyBattleEnd(roleKey,cnt)
    if(cnt == nil)then cnt = 1 end
    s.blackScreen()
    s.setActive("角色/" .. roleKey,false)
    s.lightScreen()
    --下发重复奖励
    s.openChest(nil,"farmfield_Enemy_Common_Bonus")
    s.popInfo("获得机关零件 × " .. cnt .. "!")
    itemWidget:addItem(itemData.CreatureMoney,cnt)
end

function boss1Trigger1()
    --只要触发一次就不再触发，包括boss1的2个trigger
    s.setActive(objs.boss_1_trigger1,false)
    s.setActive(objs.boss_1_trigger2,false)
    s.blackScreen()
    s.turnTo(npcs.zhujue, npcs.boss_1)
    s.turnTo(npcs.boss_1, npcs.zhujue)
    --s.blackScreen()
    s.camera(cameras.boss_1_camera_1,true)
    --s.camera(cameras.boss_1_camera_1, true)
    s.setPos(npcs.zhujue,pos.boss1_pos1)
    s.lightScreen()
    s.talk(npcs.boss_1, "(发出了低沉而危险的嘶吼声)")
    s.talk(npcs.zhujue, "如此大型的机关犬啊……真好奇你的弱点会在什么地方。")
    s.talk(npcs.zhujue, "不过，想伤害到我还是有些难度。")
    s.talk(npcs.zhujue, "等把你击倒后，我会好好研究一下你的构造的。")
    local battleTb = {}

    local isWin,bResult = s.battle("远古地宫_战斗_机关犬", battleTb)
    if(isWin)then
        boss1End()
    else
        s.finishInfiniteStory(true)
    end 
end

function boss1End()
    --下发一次性奖励
    if(s.getChallengeMapPersistentFlag("挑战_远古地宫","机关狗") == 0)then
        s.finishTask(990006,1)
        s.setChallengeMapPersistentFlag("挑战_远古地宫","机关狗",1)
    end
    --下发重复奖励
    s.openChest(nil,"farmfield_YuanGuDiGong_Boss1_repeat")

    s.camera(cameras.boss_1_camera_1,false)
    s.setActive(npcs.boss_1,false)
    s.popInfo("获得机关零件 × 3!")
    itemWidget:addItem(itemData.CreatureMoney,3)
    s.talk(npcs.zhujue, "……这个机关核心的构造，真是精妙。")
    s.talk(npcs.zhujue, "我可以将其改造一下，来增强机关熊的力量。")
    local ret = s.selectTalk(npcs.zhujue, "需要将机关核心向哪个方向改造呢？", {"狂化核心(残血时强化)", "运动核心(额外提高血量、双防)"})
    if ret == 0 then
        flags.Boss1EnhanceType = 1
        s.popInfo("机关熊已激活特殊能力:<color=red>狂化核心</color>!")
    else
        flags.Boss1EnhanceType = 2
        s.popInfo("机关熊已激活特殊能力:<color=red>运动核心</color>!")
    end

    flags.MiddleKey1 = 1
    s.popInfo("获得机关钥匙·上!")
    --如果已经去过中央，则获得机关钥匙时直接提示回中央
    if(flags.FirstTouchMiddleTrigger == 1)then
        s.talk(npcs.zhujue, "这个机关钥匙……看形制上，似乎可以和地宫中央的灯台相匹配。")
        s.talk(npcs.zhujue, "不妨去中央将它摆放至灯台上试试吧。")
    else
        --如果没有去过，则喃喃自语继续引导去中央
        s.talk(npcs.zhujue, "这机关犬还掉落了一片奇形的钥匙碎片……")
        s.talk(npcs.zhujue, "应该和地宫中的某个机关相关，继续探索看看吧。")
    end

    CheckIfKeysAllFound()
end

function CheckIfKeysAllFound()
    if(flags.MiddleKey1 == 1 and flags.MiddleKey2 == 1 and flags.MiddleKey3 == 1)then
        s.wait(1)
        s.talk(npcs.zhujue,"这下，三个碎片应该就都齐全了。")
        s.talk(npcs.zhujue,"回到地宫中央，将它们放置到灯台上试试吧。")
        s.finishTask("990001","4","5")
    end
end

function boss1Trigger2()
    --只要触发一次就不再触发，包括boss1的2个trigger
    s.setActive(objs.boss_1_trigger1,false)
    s.setActive(objs.boss_1_trigger2,false)
    s.blackScreen()
    s.turnTo(npcs.zhujue, npcs.boss_1)
    s.turnTo(npcs.boss_1, npcs.zhujue)
    --s.blackScreen()
    s.camera(cameras.boss_1_camera_1,true)
    --s.camera(cameras.boss_1_camera_1, true)
    s.setPos(npcs.zhujue,pos.boss1_pos2)
    s.lightScreen()
    s.talk(npcs.boss_1, "(发出了低沉而危险的嘶吼声)")
    s.talk(npcs.zhujue, "如此大型的机关犬啊……真好奇你的弱点会在什么地方。")
    s.talk(npcs.zhujue, "不过，想伤害到我还是有些难度。")
    s.talk(npcs.zhujue, "等把你击倒后，我会好好研究一下你的构造的。")
    local battleTb = {}

    local isWin,bResult = s.battle("远古地宫_战斗_机关犬", battleTb)
    if(isWin)then
        boss1End()
    else
        s.finishInfiniteStory(true)
    end 
end

function boss2Trigger()
    s.setActive(objs.boss_2_trigger,false)
    s.blackScreen()
    s.camera(cameras.boss_2_camera,true)
    s.setPos(npcs.zhujue, pos.boss2_pos)
    s.turnTo(npcs.zhujue, npcs.boss_2)
    s.lightScreen()
    s.wait(1)
    s.moveAsync(npcs.zhujue, pos.boss2_pos_move, 2,true)
    s.turnTo(npcs.zhujue, npcs.boss_2)
    s.wait(2)
    s.talk(npcs.zhujue,"这是机关……巨鹰？")
    s.talk(npcs.zhujue,"(目不转睛地盯着机关鹰看)前辈先人的智慧的确令人赞叹，竟然可以驱动这样沉重的机关造物长久地在空中进行运动……")
    s.talk(npcs.zhujue,"得罪了。")
    s.camera(cameras.boss_2_camera, false)

    local battleTb = {}

    local isWin,bResult = s.battle("远古地宫_战斗_机关鹰", battleTb)
    if(isWin)then
        boss2End()
    else
        s.finishInfiniteStory(true)
    end 
end

function boss2End()
    --下发一次性奖励
    if(s.getChallengeMapPersistentFlag("挑战_远古地宫","机关鹰") == 0)then
        s.finishTask(990007,1)
        s.setChallengeMapPersistentFlag("挑战_远古地宫","机关鹰",1)
    end
    --下发重复奖励
    s.openChest(nil,"farmfield_YuanGuDiGong_Boss2_repeat")

    s.setActive(npcs.boss_2,false)
    s.popInfo("获得机关零件 × 3!")
    itemWidget:addItem(itemData.CreatureMoney,3)
    s.talk(npcs.zhujue, "(从机关巨鹰的身上翻出一个奇形核心)这是你的力量来源吗？")
    s.talk(npcs.zhujue, "(仔细研究片刻)嗯，似乎只要稍作调整，就可以安装到我的机关熊身上了。")
    local ret = s.selectTalk(npcs.zhujue, "需要将机关核心向哪个方向改造呢？", {"狂化核心(残血时强化)", "运动核心(额外提高血量、双防)"})
    if ret == 0 then
        flags.Boss2EnhanceType = 1
        s.popInfo("机关熊已激活特殊能力:<color=red>狂化核心</color>!")
    else
        flags.Boss2EnhanceType = 2
        s.popInfo("机关熊已激活特殊能力:<color=red>运动核心</color>!")
    end

    flags.MiddleKey2 = 1
    s.popInfo("获得机关钥匙·中!")
    --如果已经去过中央，则获得机关钥匙时直接提示回中央
    if(flags.FirstTouchMiddleTrigger == 1)then
        s.talk(npcs.zhujue, "这机关鹰身上也有一片钥匙的碎片，应该和地宫中央的灯台相关。")
        s.talk(npcs.zhujue, "嗯……不知道剩下的钥匙碎片会在什么地方呢。")
    else
        --如果没有去过，则喃喃自语继续引导去中央
        s.talk(npcs.zhujue, "这机关鹰还掉落了一片奇形的钥匙碎片……")
        s.talk(npcs.zhujue, "应该和地宫中的某个机关相关，继续探索看看吧。")
    end

    CheckIfKeysAllFound()
end

function boss3Trigger()
    s.setActive(objs.boss_3_trigger,false)
    s.blackScreen()
    s.camera(cameras.boss_3_camera,true)
    s.setPos(npcs.zhujue, pos.boss3_pos)
    s.turnTo(npcs.zhujue, npcs.boss_3)
    s.lightScreen()
    s.wait(1)
    s.talk(npcs.zhujue,"这个房间还真适合藏一些凶猛的大杀器呢。你说对吧，机关——炮？")
    s.talk(npcs.zhujue,"炮管确实看着很有威力，我突然有了个很好的想法……")
    s.camera(cameras.boss_3_camera, false,true,blendHintEnum.EaseInOut,1.5,true,false)
    s.talk(npcs.zhujue,"(拍拍机关熊的脑壳)小家伙，是时候也给你增加一些远程的火力啦。")
    local battleTb = {}

    local isWin,bResult = s.battle("远古地宫_战斗_机关炮", battleTb)
    if(isWin)then
        boss3End()
    else
        s.finishInfiniteStory(true)
    end 
end

function boss3End()
    --下发一次性奖励
    if(s.getChallengeMapPersistentFlag("挑战_远古地宫","机关炮") == 0)then
        s.finishTask(990008,1)
        s.setChallengeMapPersistentFlag("挑战_远古地宫","机关炮",1)
    end
    --下发重复奖励
    s.openChest(nil,"farmfield_YuanGuDiGong_Boss3_repeat")

    s.setActive(npcs.boss_3,false)
    s.popInfo("获得机关零件 × 3!")
    itemWidget:addItem(itemData.CreatureMoney,3)
    s.talk(npcs.zhujue, "有趣的造物，的确给了我不少的启发。")
    s.talk(npcs.zhujue, "那么现在，到我的强化时间了。")
    local ret = s.selectTalk(npcs.zhujue, "需要将机关核心向哪个方向改造呢？", {"狂化核心(残血时强化)", "运动核心(额外提高血量、双防)"})
    if ret == 0 then
        flags.Boss2EnhanceType = 1
        s.popInfo("机关熊已激活特殊能力:<color=red>狂化核心</color>!")
    else
        flags.Boss2EnhanceType = 2
        s.popInfo("机关熊已激活特殊能力:<color=red>运动核心</color>!")
    end

    flags.MiddleKey3 = 1

    s.popInfo("获得机关钥匙·下!")
    s.talk(npcs.zhujue, "果然，这个机关炮的构造中，藏着可以和地宫中央灯台所匹配的钥匙碎片。")
    s.talk(npcs.zhujue, "这地宫中究竟还有多少秘密呢，真是令人好奇啊。")

    CheckIfKeysAllFound()
end

function boss4Trigger()
    s.setActive(objs.boss_4_trigger,false)
    s.blackScreen()
    s.camera(cameras.boss_4_camera_1,true)
    s.setPos(npcs.zhujue, pos.boss4_pos1)
    s.setPos(npcs.bear, pos.boss4_bear_pos)
    s.turnTo(npcs.zhujue, npcs.boss_4)
    s.lightScreen()
    s.wait(1)
    s.talk(npcs.zhujue,"这就是墨家前辈们研制的终极机关兽吗？")
    s.talk(npcs.zhujue,"没想到会是个人形的机关兽……")
    s.camera(cameras.boss_4_camera_3, true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.talk(npcs.boss_4,"哼，小老鼠，我等你可有好一阵了。")
    s.camera(cameras.boss_4_camera_2, true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.talk(npcs.zhujue,"(震惊)你……你会讲话？")
    s.talk(npcs.zhujue,"(认真地盯着眼前的机关兽)不，不止如此，在你的身上，我分明可以看见之前收集的机关核心的影子……")
    s.camera(cameras.boss_4_camera_3, true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.talk(npcs.boss_4,"老鼠——就是老鼠，你既可以将它们的核心拿来改造，我又有何不可？")
    s.talk(npcs.boss_4,"在进入地宫之后，我就一直关注着你，你也的确引起了我的兴趣，毕竟——太容易碾碎的老鼠，玩起来就没意思了。")
    s.camera(cameras.boss_4_camera_2, true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.talk(npcs.zhujue,"(暗自思忖)眼前的机关兽分明就是个机械造物，但却仿佛已经有了自主的意识一般，这样的技术，真是闻所未闻……")
    s.talk(npcs.zhujue,"(叹了口气)看来，眼下你是想捉住你口中的这只小老鼠了。")
    s.moveAsync(npcs.bear, pos.boss4_pos2, 6,true)
    s.wait(1)
    s.aside("在墨七身后的机关熊突然发出了一阵低沉的嗡鸣声，随后径直挡在了墨七和亚当之间。")
    s.camera(cameras.boss_4_camera_1, true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.talk(npcs.boss_4,"有趣……老鼠的幼稚玩具，竟然都想妄图和我角力么？")
    s.talk(npcs.bear,"(发出了低沉的嗡鸣声)")
    s.camera(cameras.boss_4_camera_4,true)
    s.wait(1)
    s.camera(cameras.boss_4_camera_5, true)
    s.wait(1)
    s.setActive(objs.bear_eye,true)
    s.wait(0.5)
    local battleTb = {}

    local isWin,bResult = s.battle("远古地宫_战斗_亚当", battleTb)
    if(isWin)then
        boss4End()
    else
        s.finishInfiniteStory(true)
    end
end

function boss4End()
    --下发一次性奖励
    if(s.getChallengeMapPersistentFlag("挑战_远古地宫","亚当") == 0)then
        s.finishTask(990010,1)
        s.setChallengeMapPersistentFlag("挑战_远古地宫","亚当",1)
    end
    --下发重复奖励
    s.openChest(nil,"farmfield_YuanGuDiGong_Boss5_repeat")

    s.setActive(objs.bear_eye,false)
    s.camera(cameras.boss_4_camera_5, false)
    s.setActive(npcs.boss_4,false)

    flags.Boss4Defeat = 1
    --如果此时已经击败机关门，且不满足隐藏BOSS的攻略条件，则直接结算副本
    if(flags.DoorDefeated == 1 and flags.isExBossAvailable == 0)then
        s.talk(npcs.zhujue, "我……击败了他吗？")
        s.talk(npcs.zhujue, "(亲昵地摸了摸机关熊)小家伙，这次还真是多亏了你。")
        s.talk(npcs.bear, "(表达欢快的嗡鸣声)")
        s.talk(npcs.zhujue, "这地宫应该也没有更多需要探索的东西了，就此离开吧。")
        s.finishTask("990001","6")
        s.finishInfiniteStory(true)
        return
    end

    s.talk(npcs.zhujue, "我……击败了他吗？")
    s.talk(npcs.zhujue, "(亲昵地摸了摸机关熊)小家伙，这次还真是多亏了你。")
    s.talk(npcs.bear, "(表达欢快的嗡鸣声)")
    s.talk(npcs.zhujue, "总觉得这地宫中也许还有一些我会感兴趣的事物，不妨再探索一下吧。")
    s.finishTask("990001","6")
end

function testEnd()
    s.finishInfiniteStory(true)
end

function middlePlace()
    s.setActive(objs.middle_trigger1,false)
    s.setActive(objs.middle_trigger2,false)
    s.setActive(objs.middle_trigger3,false)
    s.talk(npcs.zhujue,"这里是……")
    s.blackScreen()
    s.camera(cameras.middle_camera_3, true)
    s.lightScreen()
    s.wait(1.5)
    s.camera(cameras.middle_camera_4,true,true,blendHintEnum.EaseInOut,2.5,true,true)
    s.blackScreen()
    s.camera(cameras.middle_camera_4, false)
    s.wait(0.5)
    s.lightScreen()
    s.talk(npcs.zhujue,"没想到这个地宫中央竟有如此宽阔的一处平台。")
    s.talk(npcs.zhujue,"在这边应该会有机关通向地宫的深处，耐心寻找一下看看吧。")
    s.finishTask("990001","2","3")
end

function testCamera2()
    s.blackScreen()
    s.camera(cameras.middle_camera_3, true)
    s.lightScreen()
    s.wait(0.5)
    s.camera(cameras.middle_camera_4,true,true,blendHintEnum.EaseInOut,1.5,true,true)
end

function boss5trigger()
    s.setActive(objs.boss_5_trigger,false)
    s.blackScreen()
    s.camera(cameras.boss_5_camera,true)
    s.setPos(npcs.zhujue, pos.boss5_pos1)
    s.turnTo(npcs.zhujue, npcs.boss_5)
    s.lightScreen()
    s.moveAsync(npcs.zhujue, pos.boss5_pos2, 2,true)
    s.wait(2)
    s.talk(npcs.zhujue,"这扇门……在这个地方显得有些突兀啊。")
    s.talk(npcs.boss_5,"(眼前的机关门沉默着回应)")
    s.talk(npcs.zhujue,"(来回仔细检查了下机关门的构造)奇怪，好像没有发现任何和开门相关的机关设置。")
    s.talk(npcs.boss_5,"(眼前的机关门沉默着回应)")
    s.talk(npcs.zhujue,"(有些无奈地掏出武器)要不然……我试着破坏一下门看看？")
    s.talk(npcs.boss_5,"(眼前的机关门仿佛听懂了一般，缓缓升起2个炮台，对准了墨七)")
    s.talk(npcs.zhujue,"……")
    local battleTb = {}

    local isWin,bResult = s.battle("远古地宫_战斗_机关门", battleTb)
    if(isWin)then
        boss5End(bResult)
    else
        s.finishInfiniteStory(true)
    end 
end

function boss5End(bResult)
     --下发一次性奖励
     if(s.getChallengeMapPersistentFlag("挑战_远古地宫","亚当") == 0)then
        s.finishTask(990009,1)
        s.setChallengeMapPersistentFlag("挑战_远古地宫","亚当",1)
    end
    --下发重复奖励
    s.openChest(nil,"farmfield_YuanGuDiGong_Boss4_once")

    flags.DoorDefeated = 1
    s.camera(cameras.boss_5_camera,false)
    s.popInfo("获得机关零件 × 3!")
    itemWidget:addItem(itemData.CreatureMoney,3)
    local ret = s.selectTalk(npcs.zhujue, "需要将机关核心向哪个方向改造呢？", {"壁垒核心(提高减伤并免疫暴击)", "熔岩核心(攻击附带内伤和点燃)"})
    if ret == 0 then
        flags.Boss4EnhanceType = 1
        s.popInfo("机关熊已激活特殊能力:<color=red>壁垒核心</color>!")
    else
        flags.Boss4EnhanceType = 2
        s.popInfo("机关熊已激活特殊能力:<color=red>熔岩核心</color>!")
    end

    local ret2 = false
    if(bResult.BattleDuration < 90)then
        ret2 = true
    end
    if (ret2) then
        flags.isExBossAvailable = 1
        s.blackScreen()
        s.setActive(objs.BOSS_Door,false)
        s.setActive(objs.exboss_door1,true)
        s.setActive(objs.exboss_trigger,true)
        s.wait(0.5)
        s.lightScreen()
        s.talk(npcs.zhujue,"这是……隐藏的暗门？")
        s.talk(npcs.zhujue,"不知道会通向什么地方，不过我已经做好准备了。")
    else
        s.talk(npcs.zhujue,"前面在战斗中的时候，似乎感觉这机关门另有奥秘，可惜我在<color=red>战斗中花的时间太久</color>，未能及时破解。")
        s.aside("机关门归于沉寂，再也没有任何的回应。")
    end
end

function firstEnterExBoss()
    s.talk(npcs.zhujue,"穿过暗门，竟然来到了一处隐藏的空间……")
    s.blackScreen()
    s.camera(cameras.exboss_camera_1,true)
    s.wait(0.5)
    s.lightScreen()
    s.wait(0.5)
    s.camera(cameras.exboss_camera_2,true,true,blendHintEnum.EaseInOut,2,true,true)

    s.blackScreen()
    s.camera(cameras.exboss_camera_2, false)
    s.lightScreen()
    s.talk(npcs.zhujue,"……在此处探索一番吧。")
end

function ExBossTrigger()
    s.setActive(objs.exboss_1_trigger,false)
    s.blackScreen()
    s.camera(cameras.exboss_camera,true)
    s.setPos(npcs.zhujue, pos.exboss_pos_2)
    s.turnTo(npcs.zhujue, pos.exboss_pos_1)
    s.lightScreen()
    s.talk(npcs.zhujue,"(吃惊)如此繁复的构造，如此细节的设计，这，这应该是专门用于战争的秘密兵器……")
    s.talk(npcs.zhujue,"(沉吟)看样子，它现在应该还处于休眠状态，仅保留了一些基本的防御机制。")
    s.talk(npcs.zhujue,"如此好的机会，就让我来研究一下你的奥秘吧。")
    local battleTb = {}

    local isWin,bResult = s.battle("远古地宫_战斗_机关泰坦", battleTb)
    if(isWin)then
        ExBossEnd()
    else
        s.finishInfiniteStory(true)
    end
end

function ExBossEnd()
    --下发一次性奖励
    if(s.getChallengeMapPersistentFlag("挑战_远古地宫","零型泰坦") == 0)then
        s.finishTask(990011,1)
        s.setChallengeMapPersistentFlag("挑战_远古地宫","零型泰坦",1)
    end
    --下发重复奖励
    s.openChest(nil,"farmfield_YuanGuDiGong_BossEx_repeat")

    flags.ExBossDefeat = 1
    s.blackScreen()
    s.camera(cameras.exboss_camera,true)
    s.setActive(npcs.boss_6,false)
    s.lightScreen()

    s.talk(npcs.zhujue, "这地宫应该也没有更多需要探索的东西了，就此离开吧。")
    s.finishInfiniteStory(true)
end

function eliteBattle()

end

function testKeys()
    flags.MiddleKey1 = 1
    flags.MiddleKey2 = 1
    flags.MiddleKey3 = 1
    CheckIfKeysAllFound()
end

function mechMiddleLight()
    --首次触碰中央灯台
    if(flags.FirstTouchMiddleTrigger == 0)then
        flags.FirstTouchMiddleTrigger = 1
        s.blackScreen()
        s.camera(cameras.mid_camera_d, true)
        s.lightScreen()
        --隐藏小熊避免遮挡
        s.setActive(npcs.bear, false)
        s.talk(npcs.zhujue,"这个灯台好生奇怪……")
        s.talk(npcs.zhujue,"(仔细检查灯台的构造)看来这个灯台是由3个机关灯组成的，而且可以放置一些物品。看形状，应该是<color=red>钥匙碎片</color>的样子。")
        s.talk(npcs.zhujue,"(沉思)在这个地宫内应该可以找寻得到这些钥匙，继续探索下看看吧。")
        s.finishTask("990001","3","4")
        s.camera(cameras.mid_camera_d, false,true,blendHintEnum.EaseInOut,1.5,true,false)
        --出现小熊
        s.setActive(npcs.bear, true)
    else
        --如果三个钥匙都没找到
        if(flags.MiddleKey1 == 0 and flags.MiddleKey2 == 0 and flags.MiddleKey3 == 0)then
            s.talk(npcs.zhujue,"还没有找到任何的钥匙碎片，还是先继续去地宫中探索一番吧。")
        else
            local keyCond = {}
            keyCond[1] = flags.MiddleKey1 == 1
            keyCond[2] = flags.MiddleKey2 == 1
            keyCond[3] = flags.MiddleKey3 == 1
            keyCond[4] = true
            local opts = {}
            if(flags.MiddleKey1Setted == 0)then
                opts[1] = "放置钥匙碎片·上"
            end
            if(flags.MiddleKey2Setted == 0)then
                opts[2] = "放置钥匙碎片·中"
            end
            if(flags.MiddleKey3Setted == 0)then
                opts[3] = "放置钥匙碎片·下"
            end
            opts[4] = "没事了"
            local ret = s.selectTalkExWithCondition(npcs.zhujue, "……", opts,keyCond)
            if(ret == 1)then
                s.talk(npcs.zhujue,"(将钥匙碎片·上放置进灯台对应的凹槽上)")
                s.blackScreen()
                --切换至1号摄像机
                s.camera(cameras.mid_camera_1, true)
                s.lightScreen()
                s.wait(1)
                s.setActive(objs.mech_light1, true)
                s.aside("灯台上的机关灯发出了微弱的光芒")
                flags.MiddleKey1Setted = 1
                s.talk(npcs.zhujue,"……")
                s.wait(1)
                s.camera(cameras.mid_camera_1, false,true,blendHintEnum.EaseInOut,1.5,true,false)
            elseif(ret == 2)then
                s.talk(npcs.zhujue,"(将钥匙碎片·中放置进灯台对应的凹槽上)")
                s.blackScreen()
                --切换至2号摄像机
                s.camera(cameras.mid_camera_2, true)
                s.lightScreen()
                s.wait(1)
                s.setActive(objs.mech_light2, true)
                s.aside("灯台上的机关灯发出了微弱的光芒")
                flags.MiddleKey2Setted = 1
                s.talk(npcs.zhujue,"……")
                s.wait(1)
                s.camera(cameras.mid_camera_2, false,true,blendHintEnum.EaseInOut,1.5,true,false)
            elseif(ret == 3)then
                s.talk(npcs.zhujue,"(将钥匙碎片·下放置进灯台对应的凹槽上)")
                s.blackScreen()
                --切换至3号摄像机
                s.camera(cameras.mid_camera_3, true)
                s.lightScreen()
                s.wait(1)
                s.setActive(objs.mech_light3, true)
                s.aside("灯台上的机关灯发出了微弱的光芒")
                flags.MiddleKey3Setted = 1
                s.talk(npcs.zhujue,"……")
                s.wait(1)
                s.camera(cameras.mid_camera_3, false,true,blendHintEnum.EaseInOut,1.5,true,false)
            end
            --三个碎片都齐全了，触发机关
            if(flags.MiddleKey1Setted == 1 and flags.MiddleKey2Setted == 1 and flags.MiddleKey3Setted == 1)then
                s.wait(1.5)
                s.talk(npcs.zhujue,"这下，三个碎片应该就都齐全了。")
                s.talk(npcs.zhujue,"……(望向灯台)")
                s.blackScreen()
                s.camera(cameras.mid_camera_d, true)
                s.lightScreen()
                s.wait(1)
                s.setActive(objs.mech_light_main, true)
                s.aside("地宫深处传来了机关开启的声音")
                s.wait(1)
                s.camera(cameras.mid_camera_f, true,true,blendHintEnum.EaseInOut,1.5,true,true)
                s.move(objs.stoneDoor2,pos.door3_pos,1.5)
                s.setActive(objs.mech_light_trigger,false)
                s.finishTask("990001","5","6")
                s.camera(cameras.mid_camera_f, false)
            end
        end
    end
end