
---@type 蜃境_长生劫_05仙外山径
local context = require("MapStories/蜃境_长生劫_05仙外山径/scene_蜃境_长生劫_05仙外山径_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger
s.loadFlags(context.flags)

s.loadFlags(context.flags)
---@type flags_蜃境_长生劫_05仙外山径

local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")


----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

--精英怪战斗
function eliteBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数
    reset_all()

    --第一次进入场景
    if step == 0 then
        s.setTaskStep("长生劫仙外山径","forwardToExplore")
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.csj_18, 1)
    elseif step == 2 then
        level_guide_to(npcs.csj_19, 1)
    elseif step == 3 then
        level_guide_to(npcs.csj_20, 1)
    else
        level_guide_to(objs.exit)
    end
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.csj_18, false)
    s.setActive(npcs.csj_19, false)
    s.setActive(npcs.csj_20, false)
    --删除所有的引导点
    s.removeAllGameMapGuides()
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("长生劫")
end


----------------------------------跑图----------------------------------
-------------------------------支线剧情 何以为家 场景五----------------------------------
--第一段
function HYWJ_06()
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_仙外山径","蜃境_仙外山径_支线_何以为家6")
end

--第二段
function HYWJ_07()
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_仙外山径","蜃境_仙外山径_支线_何以为家7")
end

--第三段
function HYWJ_08()
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_仙外山径","蜃境_仙外山径_支线_何以为家8")
end


-------------------关卡记忆碎片和战斗实现---------------------

function csj_18()
    executeMemSlot("csj_18", quick_play_avg)
end

function csj_19()
    executeMemSlot("csj_19", quick_play_avg)
end

function csj_20()
    executeMemSlot("csj_20", quick_play_avg)
    s.setTaskStep("长生劫仙外山径","leave")
end