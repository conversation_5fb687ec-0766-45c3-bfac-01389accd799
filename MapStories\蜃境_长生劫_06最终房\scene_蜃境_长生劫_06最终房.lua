
---@type 蜃境_长生劫_06最终房
local context = require("MapStories/蜃境_长生劫_06最终房/scene_蜃境_长生劫_06最终房_h")


local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger
s.loadFlags(context.flags)

s.loadFlags(context.flags)
---@type flags_蜃境_长生劫_06最终房

local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")


----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")
function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

--每次载入调用
function start()
    refreshMapStates()
    s.readyToStart(true)
    try_execute_newbieGuide()
end


function refreshMapStates()
    local step = get_level_step() --当前关卡步数

    if step == 0 then

    end

    reset_all()
end

function reset_all()
    --删除所有的引导点
    s.removeAllGameMapGuides()
end

function enemy_battle()
    executeMemSlot("wshs6_battle_401",
    function()

    end,
    function()

    end,
    function()

    end,
    function()

    end)

    s.wait(0.5)
    refreshMapStates()
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("长生劫")
end
