---@type 漫游_楚家庄_前院
local context = require("MapStories/漫游_楚家庄/scene_漫游_楚家庄_前院_h")
local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi


---@type flags_漫游_楚家庄
local flags = context.flags
s.loadFlags(flags)
---组件相关
local extensions = require("MapStories/漫游_楚家庄/extension")
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type 楚家庄_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 楚家庄_ItemData]]

-- 没有使用到
--[[
---@type StringWidget
local stringWidget = extensions.widgets.StringWidgetInfo.widget


---@type 楚家庄_StringItemData
local stringItemData = extensions.widgets.StringWidgetInfo.items
]]

---@type RoleTrigger
local roleAnims = RoleTrigger

---@type ObjectTrigger
local objAnims = ObjTrigger

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--游戏初始化
function start()
    --开场剧情
    if (flags.open_scene_flag == 0) then
        zhuxian001()
        flags.open_scene_flag = 1
    end

    s.readyToStart()
    --刷新所有场景状态
    reloadAllSceneStates()
end

--region test
function testWin()
    --s.finishInfiniteStory(true);
    --itemWidget:addItem(itemData.cangbaotu4,1)
    flags.main_story_step = flags.main_story_step + 1
end

function testTalkNoHead()
    s.talk(npcs.jingcheng, "无头像对话框测试！！", "没有头像")
end

function testTalkAmazed()
    s.talkAmazed(npcs.jingcheng, "啊啊啊啊啊!!!!")
    s.talkAmazed(npcs.jingcheng, "我要消灭你！！！")
end

function testTalkThink()
    s.talkThink(npcs.jingcheng, "哪是个啥子呢？")
    s.talkThink(npcs.jingcheng, "好像是那么一回事！")
end

function testAddTimer()
    s.talk(npcs.jingcheng, "添加一个倒计时")
    --s.addTimer("100006")
end

function testFinishTimer()
    s.talk(npcs.jingcheng, "结束一个倒计时")
    s.finishTimer("100006")
end

function testHasTimerUp()
    s.talk(npcs.jingcheng, "判断一个倒计时是否结束")
    if (s.hasTimerUp("100001")) then
        s.talk(npcs.jingcheng, "倒计时结束了")
    else
        s.talk(npcs.jingcheng, "倒计时没有结束")
    end
end

function testBattle()
    local ret = s.battle("Main1-5")
    if (ret) then
        s.talk(npcs.jingcheng, "战斗胜利")
    else
        s.talk(npcs.jingcheng, "战斗失败")
    end
end

--endregion

--刷新所有场景状态
function reloadAllSceneStates()
    --风筝与侍女
    s.setActive(npcs.fengzhengshinv, flags.fengzheng_step == 0)
    s.setActive(objs.fengzheng, flags.fengzheng_step == 0)
    s.setActive(npcs.fengzhengshinv2, flags.fengzheng_step == 1)

    --狂妄剑客
    s.setActive(npcs.kuangwangjianke, flags.kuangwangjianke_win == 0)
    --楚八和厢房
    s.setActive(npcs.chuba, flags.chuba_step < 3)
    s.setActive(objs.chu8xiangfang, flags.chuba_step >= 3)

    --石碑提示特效
    s.setActive(objs.suggest_shibei, flags.shibei_step == 0)

    --枯木提示特效
    s.setActive(objs.suggest_kumu, flags.kumu_step == 0 and flags.shibei_step == 1)

    --最终宝箱与交互
    s.setActive(objs.finalchest, flags.main_story_step == 7)
    s.setActive(objs.finalinteract, flags.main_story_step == 7)

    --宝箱
    s.setActive(objs.triggerChest1, flags.chest1 == 0)
    if (flags.chest1 == 1) then
        s.soloAnimState(objs.chest1, objAnims.TurnOnDirect)
    end

    --猫的位置
    if (flags.cat_talked == 1) then
        local catpos = "位置/cat" .. tonumber(flags.cat_pos)
        s.setPos(npcs.cat, catpos)
    end
    --todo：idle动作现在都是一次性的，暂时处理掉
    --[[
    --猫的动作
    local rndAnim = math.random(6)
    flags.cat_anim = rndAnim
    if rndAnim == 1 then
        s.animState(npcs.cat,"Idle_4",true)
    elseif rndAnim == 2 then
        s.animState(npcs.cat,"Idle_5",true)
    elseif rndAnim == 3 then
        s.animState(npcs.cat,"Idle_6",true)
    elseif rndAnim == 4 then
        s.animState(npcs.cat,"Idle_7",true)
    elseif rndAnim == 5 then
        s.animState(npcs.cat,"Lick",true)
    elseif rndAnim == 6 then
        s.animState(npcs.cat,"Scratching",true)
    end
    ]]
    --莫知酉的位置
    if (flags.main_story_step >= 5) then
        s.setActive(npcs.mozhiyou, true)
        s.setPos(npcs.mozhiyou, pos.MZY_enter)
    end
end

function wrappedReloadAllSceneStates()
    s.blackScreen()
    reloadAllSceneStates()
    s.lightScreen()
end

function zhuxian001()
    s.blackScreen()
    s.camera(cameras.start, true)
    s.setPos(npcs.jingcheng, pos.JC_start)
    s.wait(0.5)
    s.lightScreen()
    s.talk(npcs.mozhiyou, "哦对了，阿成，我听闻楚家庄有一样被放弃的宝物，要是有空可以找找看")
    s.selectTalk(npcs.jingcheng, "...", { "那是什么？" })

    s.animState(npcs.mozhiyou, roleAnims.BScratch)
    s.talk(npcs.mozhiyou, "他们家小公子突发兴致把庄里一样宝物藏了起来，但他却忘了藏在了什么地方。")
    s.talk(npcs.mozhiyou, "他爹娘问他是不是水边，他说是，问他说不是山头，他也说是，一会儿又说在屋顶，一会儿又说在树梢……")
    s.talk(npcs.mozhiyou, "小孩儿也许不会撒谎，但是一定会胡说八道。")
    s.animState(npcs.mozhiyou, roleAnims.BGesture)
    s.talk(npcs.mozhiyou, "总之，宝物就这样下落不明了，楚家庄庄主也曾说，失之他命，就当是赠予有缘人。")
    s.talk(npcs.mozhiyou, "不过我听说那楚家小公子当时画了藏宝图，只是贪玩的时候当做废纸撕碎了。")
    s.animState(npcs.mozhiyou, roleAnims.BGive_Loop, true)
    s.talk(npcs.mozhiyou, "要是能凑齐藏宝图，宝藏那不是手到擒来。")
    s.talk(npcs.mozhiyou, "……前提是这小孩儿没有胡画。")
    s.animState(npcs.mozhiyou, roleAnims.BGive_Loop, false)

    s.setActive(npcs.mozhiyou, false)

    s.camera(cameras.start, false)
    s.addTask(300010, 1)
end

--该函数已改到extensionLogic,通过使用道具触发
function zhuxian002()
    s.blackScreen()
    s.lightScreen()
    s.animState(npcs.jingcheng, roleAnims.BThink_Loop, true)
    s.talk(npcs.jingcheng, "……")
    s.talk(npcs.jingcheng, "…………")
    s.talk(npcs.jingcheng, "莫大哥，藏宝图好像已经凑齐了。")
    s.animState(npcs.jingcheng, roleAnims.BThink_Loop, false)

    s.talk(npcs.mozhiyou, "真的找到了？")
    s.animState(npcs.jingcheng, roleAnims.BAgree)
    s.blackScreen()
    s.aside("你们找了一处僻静角落，对着藏宝图研究了一阵。")
    s.lightScreen()
    s.talk(npcs.mozhiyou, "小孩儿画的图真是难以理解，但还好有几样标志物能对上。")
    s.talk(npcs.jingcheng, "所以，这是在什么地方？")
    s.talk(npcs.mozhiyou, "看起来正是楚家庄前山的某处……") --todo:待调整
    s.talk(npcs.mozhiyou, "阿成，我先去前山看看，你若是准备好了，便来找我。")

    flags.main_story_step = flags.main_story_step + 1
    wrappedReloadAllSceneStates() --改变莫知酉的位置
    s.finishTask(300010, 1, 2)
end

function zhuxian003()
    --[[
    s.blackScreen()
    --黑屏位移到门口的树林某处，或者是某个方便做延伸场景的地方。
    s.camera(cameras.enter,true)
    s.setPos(npcs.mozhiyou,pos.MZY_enter)
    s.setPos(npcs.jingcheng,pos.JC_enter)
    s.lightScreen()
    ]]

    s.talk(npcs.mozhiyou, "就在这，顺着这条小道往前走，一处空地，空地上有三块石头。")
    s.talk(npcs.jingcheng, "在石头下？")
    s.talk(npcs.mozhiyou, "在最大的石头指向的那棵树下。")
    s.talk(npcs.jingcheng, "……")
    s.talk(npcs.mozhiyou, "事不宜迟，准备好了我们就一起过去。")
    s.camera(cameras.enter, false)

    flags.main_story_step = flags.main_story_step + 1
    wrappedReloadAllSceneStates()
    s.finishTask(300010, 2, 3)
end

function zhuxian005()
    --TODO:此处应该接timeline
    s.blackScreen()
    s.setActive(objs.finalscene, true)
    s.camera(cameras.final, true)
    s.setPos(npcs.jingcheng, pos.JC_final)
    s.setPos(npcs.mozhiyou, pos.MZY_final)
    s.setPos(npcs.heiyiren, pos.HYR_final)
    s.lightScreen()

    s.animState(npcs.mozhiyou, roleAnims.BSquat_Loop, true)
    s.talk(npcs.mozhiyou, "对了对了就是这里……")
    s.talk(npcs.mozhiyou, "这小孩儿怎么埋得这么深！")
    s.talk(npcs.heiyiren, "很好，替我们省去了不少功夫。")
    s.animState(npcs.mozhiyou, roleAnims.BSquat_Loop, false)
    s.setActive(npcs.heiyiren, true)
    s.setPos(npcs.heiyiren, pos.HYR_final)
    s.camera(cameras.final_back, true, true, blendHintEnum.EaseInOut, 1, true, true)
    s.move(npcs.heiyiren, pos.HYR_final_move, 1, true)
    s.turnToAsync(npcs.jingcheng, npcs.heiyiren)
    s.turnToAsync(npcs.mozhiyou, npcs.heiyiren)
    s.wait(2)
    s.animState(npcs.mozhiyou, roleAnims.BScratch)
    s.talk(npcs.mozhiyou, "大白天穿黑衣服不是更醒目？")
    s.talkAmazed(npcs.heiyiren, "要你管！")
    s.animState(npcs.heiyiren, roleAnims.BFight_Loop, true)
    s.talk(npcs.heiyiren, "既然你们已经替我寻得了宝藏，留着也没用了！")
    s.talk(npcs.mozhiyou, "唉，螳螂捕蝉黄雀在后，但……")
    s.animState(npcs.mozhiyou, roleAnims.BFight_Loop, true)
    s.animState(npcs.jingcheng, roleAnims.BFight_Loop, true)
    s.talk(npcs.mozhiyou, "我们不一定是螳螂，你也不一定是黄雀。")

    s.finishTask(300010, 3, 4)

    s.animState(npcs.mozhiyou, roleAnims.BFight_Loop, false)
    s.animState(npcs.jingcheng, roleAnims.BFight_Loop, false)
    s.animState(npcs.heiyiren, roleAnims.BFight_Loop, false)
    local isWin = s.battle("楚家庄_漫游_黑衣人")
    if (isWin) then
        flags.main_story_step = flags.main_story_step + 1
        s.camera(cameras.final, false)
        s.setActive(npcs.heiyiren, false)
        wrappedReloadAllSceneStates()
    else
        s.aside("战斗失败，重新来过吧！")
        s.blackScreen()
        s.camera(cameras.final, false)
        s.setPos(npcs.jingcheng, pos.JC_enter)
        s.setActive(objs.finalscene, false)
        wrappedReloadAllSceneStates()
        s.lightScreen()
    end
end

function zhuxian006()
    s.talk(npcs.mozhiyou, "哇，好东西！发了发了！")
    s.finishTask(300010, 4)

    s.finishInfiniteStory(true)
end

---@gameMapEvent 与莫知酉对话
function talkToMozhiyou()
    if (flags.main_story_step == 5) then
        zhuxian003()
    elseif (flags.main_story_step == 6) then
        s.talk(npcs.mozhiyou, "准备好了吗？")
        local ret = s.selectTalk(npcs.jingcheng, "……", { "出发", "再等等" })
        if (ret == 0) then
            zhuxian005()
        end
    else
        s.talk(npcs.mozhiyou, "有线索了吗？")
    end
end

---@gameMapEvent 与风筝侍女对话
function talkToFengzhengshinv()
    if (flags.fengzheng_step == 0) then
        --TODO:此处应该接timeline

        s.blackScreen()
        s.camera(cameras.fengzheng, true)
        s.setPos(npcs.jingcheng, pos.JC_fengzheng)

        s.setActive(objs.hiddentree_fz, false)

        s.turnTo(npcs.jingcheng, npcs.fengzhengshinv)
        s.lightScreen()
        s.talk(npcs.jingcheng, "是你的风筝吗？")
        s.animState(npcs.fengzhengshinv, roleAnims.BDeny) --摇头
        s.talk(npcs.fengzhengshinv, "是六姑娘的。自她出嫁离开后我已经好些年没见到她了。")
        s.talk(npcs.jingcheng, "为什么不把风筝取下来呢？")
        s.talk(npcs.fengzhengshinv, "太高了")
        s.talk(npcs.fengzhengshinv, "老爷不让六姑娘习武。")

        s.selectTalk(npcs.jingcheng, "...", { "我帮你取下来好吗？" })
        s.animState(npcs.fengzhengshinv, roleAnims.BAgree) --点头

        s.blackScreen()
        s.aside("荆成攀上树梢，伸手解开纠缠的线。")
        s.aside("忽然一阵大风吹过，风筝被吹得远远的，越飞越高。")
        s.setActive(objs.fengzheng, false)
        s.lightScreen()

        s.talk(npcs.jingcheng, "真对不起。")
        s.animState(npcs.fengzhengshinv, roleAnims.BGesture)
        s.talk(npcs.fengzhengshinv, "你瞧，它飞得好远好高啊。") --todo：演出补充
        s.talk(npcs.fengzhengshinv, "唔……树上飘下来什么东西？")
        s.talk(npcs.jingcheng, "好像是刚刚理线的时候碰到的。")
        s.talk(npcs.fengzhengshinv, "好脏的布头。")
        s.talk(npcs.jingcheng, "（这是……）")
        s.aside("【获得藏宝图碎片之二】")
        itemWidget:addItem(itemData.cangbaotu2, 1)
        s.camera(cameras.fengzheng, false)
        flags.fengzheng_step = 1
        flags.main_story_step = flags.main_story_step + 1
        wrappedReloadAllSceneStates()

        s.setActive(objs.hiddentree_fz, true)
    else
        s.turnTo(npcs.fengzhengshinv2, npcs.jingcheng)
        local offset = CS.UnityEngine.Vector3(0, 1.75, 0)
        s.lookAt(npcs.fengzhengshinv2, offset, npcs.jingcheng, true)

        s.talk(npcs.fengzhengshinv, "少侠，总有天我也会习武，像你们一样闯荡江湖，那个时候我就能见到六姑娘了！")
        --s.lookAt(npcs.fengzhengshinv2, offset, npcs.jingcheng, true)
    end
end

---@gameMapEvent 与石碑交互
function eventShibei()
    if (flags.shibei_step == 0) then
        shibei001()
        s.addTask(300050, 1)
        flags.shibei_step = 1
        wrappedReloadAllSceneStates()
    else
        shibei002()
    end
end

function showShibei(isShow)
    if isShow then
        s.blackScreen()
        s.camera(cameras.shibei, true)
        s.setActive(npcs.mozhiyou, true)
        s.setPos(npcs.jingcheng, pos.JC_shibei)
        s.setPos(npcs.mozhiyou, pos.MZY_shibei)
        s.wait(0.5)
        s.lightScreen()
    else
        s.blackScreen()
        s.setActive(npcs.mozhiyou, false)
        s.camera(cameras.shibei, false)
        s.wait(0.5)
        s.lightScreen()
    end
end

function shibei001()
    showShibei(true)

    s.talk(npcs.jingcheng, "上面刻的是“沉舟侧畔千帆过，病树前头万木春”。")
    s.talk(npcs.mozhiyou, "怎么刻这句？我以为是“欢迎来到楚家庄”呢。")
    s.animState(npcs.jingcheng, roleAnims.BScratch)
    s.talk(npcs.mozhiyou, "不说笑了，阿成，你瞧瞧这字，有什么不寻常？")
    s.animState(npcs.jingcheng, roleAnims.BThink_Loop, true)
    s.talk(npcs.jingcheng, "凿痕流畅，却没有反复打磨的痕迹，像是一笔而成。")
    s.talk(npcs.jingcheng, "这不是寻常工匠能做到的。")
    s.animState(npcs.jingcheng, roleAnims.BThink_Loop, false)
    s.animState(npcs.mozhiyou, roleAnims.BAgree)
    s.talk(npcs.mozhiyou, "不错，这是剑锋所刻。")
    s.talk(npcs.mozhiyou, "剑不似钢钉质硬，用剑在石头上刻字绝非易事，这刻字之人的功夫不差。")
    s.talk(npcs.jingcheng, "“病树”，这个两个字刻得格外深些。")
    s.talk(npcs.mozhiyou, "确实啊，我都没注意。")
    s.animState(npcs.jingcheng, roleAnims.BScratch)
    s.talk(npcs.mozhiyou, "难道又是什么谜题？唉，现在的人，能不能学会有话直说。")
    s.talk(npcs.jingcheng, "“病树”……")

    showShibei(false)
end

function shibei002()
    showShibei(true)

    s.talk(npcs.jingcheng, "沉舟侧畔千帆过，病树前头万木春。")
    s.animState(npcs.jingcheng, roleAnims.BScratch)
    s.talk(npcs.jingcheng, "这首诗念起来好像很悲伤。")
    s.talk(npcs.mozhiyou, "是吗？我却觉得有些振奋人心。")

    showShibei(false)
end

---@gameEvent 枯木
function eventKumu()
    if (flags.shibei_step == 0) then --尚未与石碑交互
        shibei004()
    else
        if (flags.kumu_step == 0) then --完成任务
            shibei003()
            flags.kumu_step = 1
        else
            shibei004()
        end
    end
end

function showKumu(isShow)
    if isShow then
        s.blackScreen()
        s.camera(cameras.kumu, true)
        s.setActive(npcs.mozhiyou, true)
        s.setActive(objs.suggest_kumu, false)
        s.setPos(npcs.jingcheng, pos.JC_kumu)
        s.setPos(npcs.mozhiyou, pos.MZY_kumu)
        s.wait(0.5)
        s.lightScreen()
    else
        s.blackScreen()
        s.setActive(npcs.mozhiyou, false)
        s.camera(cameras.kumu, false)
        s.wait(0.5)
        s.lightScreen()
    end
end

function shibei003()
    --TODO:此处应该接timeline
    showKumu(true)
    s.talk(npcs.jingcheng, "莫大师，你说这个会不会是石碑上说的“病树”？")
    s.animState(npcs.mozhiyou, roleAnims.BAgree)
    s.talk(npcs.mozhiyou, "啊——我想你说的没错。")
    s.animState(npcs.mozhiyou, roleAnims.BSquat_Loop, true)
    s.talk(npcs.mozhiyou, "那就让我们看看“病树前头”到底有些什么吧！")
    s.talk(npcs.mozhiyou, "嗯？这是……")
    s.animState(npcs.mozhiyou, roleAnims.BSquat_Loop, false)
    s.wait(2)
    s.turnToAsync(npcs.mozhiyou, npcs.jingcheng)
    s.turnToAsync(npcs.jingcheng, npcs.mozhiyou)
    s.wait(1)
    s.animState(npcs.mozhiyou, roleAnims.BGive_Loop, true)
    s.wait(1)
    s.animState(npcs.mozhiyou, roleAnims.BGive_Loop, false)
    s.wait(1)

    s.setPos(npcs.chuqi, pos.CQ_kumu_huawai)
    s.move(npcs.chuqi, pos.CQ_kumu, 1.2, true)
    s.talk("??", "这是四姐的东西。大师在哪里找到？")
    s.turnToAsync(npcs.jingcheng, npcs.chuqi)
    s.turnToAsync(npcs.mozhiyou, npcs.chuqi)
    s.wait(1)

    s.talk(npcs.mozhiyou, "啊，原来如此。")
    s.talk(npcs.mozhiyou, "我方才看到这树中好像有什么怪物，这才……")
    s.talk(npcs.chuqi, "里面是何物？")
    s.animState(npcs.mozhiyou, roleAnims.BDeny)
    s.talk(npcs.mozhiyou, "没什么，就是、就是……")
    s.talk(npcs.jingcheng, "一枚珍珠。")
    s.talkThink(npcs.mozhiyou, "（真是个老实孩子，这下一点回旋的余地都没有。）")
    s.talk(npcs.chuqi, "还君明珠双泪垂……")
    s.talk(npcs.mozhiyou, "楚七公子？")

    s.talk(npcs.chuqi, "这是我四姐的东西，多谢二位找回。")

    s.blackScreen()
    s.setPos(npcs.chuqi, pos.CQ_init) --todo:楚七位移
    s.turnToAsync(npcs.jingcheng, npcs.mozhiyou)
    s.turnToAsync(npcs.mozhiyou, npcs.jingcheng)
    s.wait(1)
    s.lightScreen()

    s.animState(npcs.mozhiyou, roleAnims.BScratch)
    s.talk(npcs.mozhiyou, "白忙活，竹篮打水一场空。")
    s.talk(npcs.jingcheng, "这个东西对她来说应该很重要，但为什么埋在了这里。")
    s.talk(npcs.mozhiyou, "石碑字迹笔力遒劲有力，入石三分，定是习武人士所写，但我听说楚家的四小姐嫁的是个书香人家。")
    s.talk(npcs.jingcheng, "原来……")

    s.blackScreen()
    s.talk(npcs.chuqi, "咳咳……")
    s.setPos(npcs.chuqi, pos.CQ_kumu_huawai)
    s.lightScreen()

    s.move(npcs.chuqi, pos.CQ_kumu, 1.2, true)
    s.turnToAsync(npcs.mozhiyou, npcs.jingcheng)
    s.turnToAsync(npcs.jingcheng, npcs.mozhiyou)
    s.wait(1)
    s.animState(npcs.mozhiyou, roleAnims.BLaugh)
    s.talk(npcs.mozhiyou, "哈，荆兄原来出自书香人家，怪不得……怪不得……")
    s.animState(npcs.jingcheng, roleAnims.BScratch)
    s.talk(npcs.jingcheng, "我、我不是……")

    s.wait(1)
    s.turnToAsync(npcs.jingcheng, npcs.chuqi)
    s.turnToAsync(npcs.mozhiyou, npcs.chuqi)
    s.talk(npcs.chuqi, "两位侠士，我方才思念姐姐，失了礼数，忘记感谢二位。")
    s.animState(npcs.chuqi, roleAnims.BGive_Loop, true)
    s.talk(npcs.chuqi, "这是我珍藏的好纸，就赠予二位，以表谢意。")
    s.animState(npcs.chuqi, roleAnims.BGive_Loop, false)

    s.talk(npcs.mozhiyou, "没事没事，这么见外做什么。")
    s.animState(npcs.chuqi, roleAnims.BGreet)
    s.talk(npcs.chuqi, "小小薄礼，二位见笑了。")
    s.talk(npcs.chuqi, "不打扰二位，楚七先行告退。")

    s.move(npcs.chuqi, pos.CQ_kumu_huawai, 1.2, true)
    s.wait(1)
    s.setPos(npcs.chuqi, pos.CQ_init)
    s.turnToAsync(npcs.mozhiyou, npcs.jingcheng)
    s.turnToAsync(npcs.jingcheng, npcs.mozhiyou)
    s.wait(1)
    s.talk(npcs.mozhiyou, "……")
    s.talk(npcs.mozhiyou, "…………")
    s.talk(npcs.mozhiyou, "………………我要这纸能干嘛。")
    s.talk(npcs.jingcheng, "这里好像夹着什么东西。")
    s.talkAmazed(npcs.mozhiyou, "嗯？这是……")
    s.aside("【获得藏宝图碎片之一】")
    itemWidget:addItem(itemData.cangbaotu1, 1)

    showKumu(false)

    s.finishTask(300050, 1)
    flags.main_story_step = flags.main_story_step + 1
end

function shibei004()
    s.aside("这是一棵早已枯死的树。")
end

function showJianke(isShow)
    if isShow then
        s.blackScreen()
        s.camera(cameras.jianke, true)
        s.setActive(npcs.mozhiyou, true)
        s.setPos(npcs.jingcheng, pos.JC_jianke)
        s.setPos(npcs.mozhiyou, pos.MZY_jianke)
        s.wait(0.5)
        s.lightScreen()
    else
        s.blackScreen()
        s.setActive(npcs.mozhiyou, false)
        s.camera(cameras.jianke, false)
        s.wait(0.5)
        s.lightScreen()
    end
end

---@gameEvent 狂妄剑客
function eventKuangwangjianke()
    showJianke(true)
    if (flags.kuangwangjianke_step == 0) then
        s.turnTo(npcs.kuangwangjianke, npcs.jingcheng)
        s.animState(npcs.kuangwangjianke, roleAnims.BFight_Loop, true)
        s.talk(npcs.kuangwangjianke, "你就是楚七？")
        s.animState(npcs.jingcheng, roleAnims.BDeny)
        s.wait(1)
        s.talk(npcs.kuangwangjianke, "我听说楚家老七在江湖上兴风作浪，为非作歹！")
        s.talkAmazed(npcs.kuangwangjianke, "我这就替江湖正道好好教训教训你！")
        s.animState(npcs.kuangwangjianke, roleAnims.BFight_Loop, false)

        flags.kuangwangjianke_step = 1
        s.addTask(300030, 1)
        local ret = s.selectTalk(npcs.jingcheng, "...", { "应战！", "一会再说" })
        if ret == 0 then
            battleWithKuangwangjianke()
        end
    else
        s.turnTo(npcs.kuangwangjianke, npcs.jingcheng)
        s.animState(npcs.kuangwangjianke, roleAnims.BFight_Loop, true)
        --剑客：哼，不死心的家伙！看招！
        s.talkAmazed(npcs.kuangwangjianke, "哼，不死心的家伙！看招！")
        s.animState(npcs.kuangwangjianke, roleAnims.BFight_Loop, false)
        local ret = s.selectTalk(npcs.jingcheng, "...", { "应战！", "一会再说" })
        if ret == 0 then
            battleWithKuangwangjianke()
        end
    end
    showJianke(false)
end

--与狂妄剑客战斗
function battleWithKuangwangjianke()
    local isWin = s.battle("楚家庄_漫游_狂妄剑客")

    if (isWin) then
        s.talk(npcs.kuangwangjianke, "魔头好厉害的身手。")
        s.talk(npcs.kuangwangjianke, "但我不会倒在这里，我总有一天会把整个楚家庄收入囊中。")

        wrappedReloadAllSceneStates()

        s.turnToAsync(npcs.jingcheng, npcs.mozhiyou)
        s.turnToAsync(npcs.mozhiyou, npcs.jingcheng)
        s.talk(npcs.jingcheng, "莫大师，这人究竟要做什么？") --todo：调整相机
        s.talk(npcs.jingcheng, "他好像都不知道楚七究竟是什么人。")
        s.talk(npcs.mozhiyou, "不知道哇，但他说要把楚家庄收入囊中……")

        s.talk(npcs.mozhiyou, "可能是个强盗吧。")
        s.animState(npcs.jingcheng, roleAnims.BScratch)
        s.talk(npcs.jingcheng, "是这样吗……")
        s.talk(npcs.jingcheng, "呃，他好像掉了什么东西。")

        flags.kuangwangjianke_win = 1
        s.finishTask(300030, 1) --todo：奖励放在任务后面
    else
        s.talk(npcs.kuangwangjianke, "哈哈哈——找你爹告状去吧！")
    end
end

---@gameEvent 楚八
function eventChuba()
    showChuba(true)
    if (flags.chuba_step == 0) then
        chubaTalk001()
    elseif (flags.chuba_step == 1) then
        chubaTalk002()
    elseif (flags.chuba_step == 2) then
        chubaTalk003()
    end
    showChuba(false)
end

function showChuba(isShow)
    if isShow then
        s.blackScreen()
        s.camera(cameras.chuba, true)
        s.setActive(npcs.mozhiyou, true)
        s.setPos(npcs.jingcheng, pos.JC_chuba)
        s.setPos(npcs.mozhiyou, pos.MZY_chuba)
        s.wait(0.5)
        s.lightScreen()
    else
        s.blackScreen()
        s.setActive(npcs.mozhiyou, false)
        s.camera(cameras.chuba, false)
        s.camera(cameras.chuba_move, false)
        s.wait(0.5)
        s.lightScreen()
    end
end

function chubaTalk001()
    --TODO:此处应该接timeline
    s.aside("楚八从门缝里张望，确认没有看到莫知酉") --todo:需替换成演出
    s.animState(npcs.mozhiyou, roleAnims.BHostile, true)
    s.animState(npcs.chuba, roleAnims.BAkimbo_Loop, true)
    s.talk(npcs.chuba, "喂！你是不是也来带走我姐姐？")
    local ret = s.selectTalk(npcs.jingcheng, "...", { "姐姐？", "也许是" })
    if (ret == 0) then
        s.talk(npcs.chuba, "哼，我告诉你，我已经没有姐姐可以带走了，她们都离开了！")
        s.animState(npcs.chuba, roleAnims.BAkimbo_Loop, false)
        s.talkAmazed(npcs.chuba, "啊啊啊！好烦好烦！")
        s.talk(npcs.chuba, "都怪你，要不是你来，我就不会被大哥关起来！")
        s.talk(npcs.chuba, "你要是帮我把这扇门打开，我就原谅你了！")
    else
        s.talk(npcs.chuba, "哼！你们每次来都会带走我一位姐姐。")
        s.animState(npcs.chuba, roleAnims.BAkimbo_Loop, false)
        s.talk(npcs.chuba, "但我已经没有姐姐可以带走了，她们都离开了！")
        s.talkAmazed(npcs.chuba, "啊啊啊！好烦好烦！")
        s.talk(npcs.chuba, "你要是帮我把这扇门打开，我以后有新姐姐了就准你带走！")
    end

    s.camera(cameras.chuba_move, true, true, blendHintEnum.EaseInOut, 1, true, true)
    s.moveAsync(npcs.jingcheng, pos.JC_chuba_move, 1, true)
    s.animState(npcs.mozhiyou, roleAnims.BHostile, false)
    s.wait(1.5)
    s.turnToAsync(npcs.jingcheng, npcs.mozhiyou)
    s.turnToAsync(npcs.mozhiyou, npcs.jingcheng)
    s.wait(0.5)
    s.animState(npcs.mozhiyou, roleAnims.BDeny)
    s.talk(npcs.mozhiyou, "那个小鬼头又动什么歪心思了，还要专程逮着你说。")
    s.talk(npcs.mozhiyou, "楚家小公子人小鬼大，你可不能被他骗了。")
    s.talk(npcs.jingcheng, "他被关起来了，找我帮忙。")
    s.animState(npcs.mozhiyou, roleAnims.BGesture)
    s.talk(npcs.mozhiyou, "哈，我就知道。这样，你就这么跟他说……")
    s.talk(npcs.mozhiyou, "……")
    s.talk(npcs.jingcheng, "…………")
    s.animState(npcs.jingcheng, roleAnims.BAgree)
    s.wait(2)

    --新任务 “救出”楚八
    s.addTask(300060, 1)
    flags.chuba_step = 1
end

function chubaTalk002()
    --TODO:此处应该接timeline
    s.animState(npcs.mozhiyou, roleAnims.BHostile, true)
    s.talk(npcs.chuba, "好了吗好了吗？我可以出去了吗？")
    s.animState(npcs.jingcheng, roleAnims.BScratch)
    s.talk(npcs.jingcheng, "这个锁是……是……")
    s.animState(npcs.jingcheng, roleAnims.BScratch)
    s.talk(npcs.chuba, "快说啊快说啊！急死人了！")
    s.talk(npcs.jingcheng, "是……七色八宝玲珑锁，我需要找齐七种矿材，八种宝物，才能炼制出解锁的钥匙。")
    s.talk(npcs.jingcheng, "寻常钥匙只会被锁扣反咬，让这把锁就此成为一把再也打不开的死锁。")
    s.talkAmazed(npcs.chuba, "什么！")
    s.animState(npcs.jingcheng, roleAnims.BScratch)
    s.talk(npcs.jingcheng, "……")
    s.talk(npcs.chuba, "啊啊啊！我就知道！他们居然藏着这种宝物！果然厉害的宝物要用在厉害的人身上！")
    s.talk(npcs.chuba, "那你什么时候才能找齐材料，要找多久？")
    s.animState(npcs.jingcheng, roleAnims.BDeny)
    s.talk(npcs.jingcheng, "……三个时辰。")
    s.talk(npcs.chuba, "还好，里面有厨房，我饿不死。")
    s.talk(npcs.chuba, "那个，你！你从那边花厅走，再转两次，找到我的房间，我屋子里可多可多宝贝了，肯定有七八宝的其中几个。")
    s.talk(npcs.chuba, "还有，你不准别人用普通的钥匙开门，不然我就一辈子出不来了。")
    s.talk(npcs.jingcheng, "……")
    s.animState(npcs.jingcheng, roleAnims.BAgree)
    s.talk(npcs.jingcheng, "……好。")
    s.animState(npcs.mozhiyou, roleAnims.BHostile, false)

    s.finishTask(300060, 1, 2)
    --s.addTimer("100006")
    flags.chuba_step = 2
end

function chubaTalk003()
    if (s.hasTimerUp("100006")) then
        --TODO:此处应该接timeline
        s.talk(npcs.chuba, "怎么样了怎么样了？")
        s.talk(npcs.jingcheng, "钥匙做好了，我这就开锁。")
        s.talk(npcs.jingcheng, "（这是个寻常的门闩，拨开就行了。）")
        s.move(npcs.mozhiyou, pos.MZY_chuba_show, 1, true)
        s.animState(npcs.mozhiyou, roleAnims.BShock_Loop, true)
        s.talk(npcs.mozhiyou, "这不是——传闻中的七色八宝玲珑锁吗！")
        s.talk(npcs.mozhiyou, "阿成，你手里拿的，难道是五光十色琉璃钥！")
        s.animState(npcs.mozhiyou, roleAnims.BShock_Loop, false)
        s.talk(npcs.chuba, "不关你的事，你走开！")
        s.animState(npcs.mozhiyou, roleAnims.BDeny)
        s.talk(npcs.mozhiyou, "那我就不管这个了，虽然五光十色琉璃钥能够打开七色八宝玲珑锁，但在打开的一瞬间会触发百发百中机关盒，只有轻功高超的人才能躲开。")
        s.talk(npcs.chuba, "什么？大哥他、他居然这么狠毒！")
        s.talk(npcs.chuba, "那怎么办……我不会武功，你们、你们想想办法。")
        s.turnTo(npcs.mozhiyou, npcs.chuba)
        s.talk(npcs.mozhiyou, "哎呀哎呀，小少爷，武功是要练的，就算是高人传功，传过去的也只是些许内力。")
        s.talk(npcs.mozhiyou, "更何况这轻功，更多的是要练外功，平日里一点不练，现在想要说会就会也太不可能了。")
        s.talk(npcs.chuba, "【哽咽】那、那你说怎么办嘛……哇……我不要死在这里……哇……")
        s.talk(npcs.jingcheng, "【轻声】莫大哥，还是不要吓他了。")
        s.turnTo(npcs.mozhiyou, npcs.jingcheng)
        s.animState(npcs.mozhiyou, roleAnims.BDeny)
        s.talk(npcs.mozhiyou, "这嚣张跋扈的小鬼头，现在不收拾，以后就是个混世魔王。")
        s.talk(npcs.mozhiyou, "算了，看在你的面子上，就点到为止吧。")
        s.turnTo(npcs.mozhiyou, npcs.chuba)
        s.talk(npcs.mozhiyou, "咳咳，楚八公子，别急别急，我早些年在不器门学过些机关巧术，这种小机关，对我来说轻轻松松。")
        s.talk(npcs.chuba, "【抽泣】……真的？")
        s.talk(npcs.mozhiyou, "看我的。")
        s.animState(npcs.mozhiyou, roleAnims.BGesture)
        s.talk(npcs.mozhiyou, "嘿哈——哈——哈！！")
        s.talk(npcs.mozhiyou, "开！")
        s.blackScreen()
        s.aside("荆成轻轻拨开了门闩。")
        s.aside("门打开了。") --todo：处理门
        s.aside("楚八走到两人面前。")
        s.talk(npcs.chuba, "我、我饿了，我要找奶娘给我做好吃的。")
        s.talk(npcs.chuba, "……哼。")
        s.aside("楚八跑开了。")
        s.lightScreen()
        flags.chuba_step = 3
        s.finishTask(300060, 2)
        wrappedReloadAllSceneStates()

        s.turnToAsync(npcs.mozhiyou, npcs.jingcheng)
        s.turnToAsync(npcs.jingcheng, npcs.mozhiyou)
        s.animState(npcs.mozhiyou, roleAnims.BDeny)
        s.talk(npcs.mozhiyou, "怎么连句谢谢都不说。")
        s.talk(npcs.jingcheng, "他可能是怕你了。")
        s.animState(npcs.mozhiyou, roleAnims.BGesture)
        s.talk(npcs.mozhiyou, "我有这么可怕吗？")
        s.talk(npcs.jingcheng, "……")
        s.talk(npcs.jingcheng, "他刚刚好像故意丢了个什么东西。")
        s.talk(npcs.mozhiyou, "这是什么？尿片？！")
        s.talk(npcs.jingcheng, "好像……")
        s.animState(npcs.jingcheng, roleAnims.BSquat_Loop, true)
        s.talk(npcs.mozhiyou, "阿成小心！")
        s.animState(npcs.jingcheng, roleAnims.BSquat_Loop, false)
        s.talk(npcs.jingcheng, "是藏宝图的碎片。")
        itemWidget:addItem(itemData.cangbaotu4, 1)

        s.talk(npcs.mozhiyou, "这小家伙，吓我一跳。")
        s.talk(npcs.jingcheng, "他虽然怕你，但还是表达了谢意。")
        s.talk(npcs.mozhiyou, "也就是你，总把别人往好处想。")

        flags.main_story_step = flags.main_story_step + 1
    else
        s.aside("三个时辰还没到呢。") --todo:修改文案
    end
end

---@gameEvent 影壁
function eventYingbi()
    print("yingbi called")
    if (flags.yingbi_step == 0) then
        s.aside("一块雕花影壁，壁上皆是些吉祥寓意的图案，这雕花的青砖一看就价值不菲。")
    elseif (flags.yingbi_step == 1) then
        s.aside("这雕花的青砖一看就价值不菲。")
    elseif (flags.yingbi_step == 2) then
        s.aside("别打它主意了，再价值不菲的青砖，你也撬不走。")
    else
        s.aside("一块雕花影壁，壁上皆是些吉祥寓意的图案，这雕花的青砖一看就撬不走。")
    end

    flags.yingbi_step = flags.yingbi_step + 1
end

---@gameEvent 花厅
function eventHuating()
    if (flags.huating_step == 0) then
        s.aside("此为一处花厅，厅中除了行人的走道外，皆是些瑶草琪葩。")
        s.aside("此间奇花绽锦、争奇斗艳，一抹胭脂映着一抹绛紫，又缀着三两处明黄，那或深或浅的莹莹蓝绿见缝插针，花叶锦簇，塞的无一隙空地。")
    elseif (flags.huating_step == 1) then
        s.aside("此为楚家庄花厅，厅内奇花布锦，瑶草喷香，处处是砌红堆绿，开遍了魏紫姚黄。")
        s.aside("只到门口，便觉得香花艳丽，瑞气缤纷。")
    elseif (flags.huating_step == 2) then
        s.aside("这是一间花厅，里面有很多花，红的紫的黄的……")
        s.aside("还很香。")
    else
        s.aside("私人花园，非请勿入。")
    end

    flags.huating_step = flags.huating_step + 1
end

---@gameEvent 厢房
function eventXiangfang()
    if (flags.xiangfang_step == 0) then
        local ret = s.selectTalk(npcs.jingcheng, "里面似乎是间空屋子，是否要在里面住下？", { "住下吧", "再想想" })
        if (ret == 0) then
            s.aside("大侠，别人家不是想住就住的。")
            flags.xiangfang_step = 1
        end
    elseif (flags.xiangfang_step == 1) then
        s.aside("里面的屋子有人住了，烦请大侠另寻别处定居。")
        flags.xiangfang_step = 2
    elseif (flags.xiangfang_step == 2) then
        s.aside("他人屋舍，非请勿入。")
    end
end

---@gameEvent 大树
function eventBigTree()
    --从4个中随机一个
    local ret = math.random(4)
    if (ret == 1) then
        s.aside("一年之计，莫如树谷；十年之计，莫如树木；终身之计，莫如树人。")
    elseif (ret == 2) then
        s.aside("一树一获者，谷也；一树十获者，木也；一树百获者，人也。")
    elseif (ret == 3) then
        s.aside("蚍蜉撼大树，可笑不自量。")
        s.aside("若无撼树心，何堪笑蜉蝣。")
    else
        s.aside("百年之树，移根至此。")
        s.aside("若在野外，或早因山火化为焦炭，如今虽困于围墙之中，尚且能开枝散叶。")
    end
end

---@gameEvent 猫
function eventCat()
    if (flags.cat_conquered <= 2) then
        catRunAway()
    else
        catConquered()
    end
end

function catStopIdle()
    --todo：暂时没用，先放在这里
    if flags.cat_anim == 1 then
        s.animState(npcs.cat, "Idle_4", false)
    elseif flags.cat_anim == 2 then
        s.animState(npcs.cat, "Idle_5", false)
    elseif flags.cat_anim == 3 then
        s.animState(npcs.cat, "Idle_6", false)
    elseif flags.cat_anim == 4 then
        s.animState(npcs.cat, "Idle_7", false)
    elseif flags.cat_anim == 5 then
        s.animState(npcs.cat, "Lick", false)
    elseif flags.cat_anim == 6 then
        s.animState(npcs.cat, "Scratching", false)
    end
end

function catRunAway()
    if (flags.cat_talked == 0) then
        s.talk(npcs.jingcheng, "只是干净的小猫。")
        flags.cat_pos = 3
        flags.cat_talked = 1
        s.addTask(300020, 1)
    end

    s.talk(npcs.cat, "喵呜~")
    local ret = s.selectTalk(npcs.jingcheng, "要做什么呢？", { "摸摸下巴", "摸摸头", "摸摸肚子" })

    s.animState(npcs.jingcheng, roleAnims.BSquat_Loop, true)

    if (ret == 0) then
        s.talk(npcs.cat, "喵~")
        s.animState(npcs.cat, "Caress")
        s.aside("小猫嘬起了嘴，发出了咕噜噜的声音。")
        --撸猫次数+1
        flags.cat_conquered = flags.cat_conquered + 1
    elseif (ret == 1) then
        s.talk(npcs.cat, "喵呜~")
        s.animState(npcs.cat, "Caress")
        s.aside("小猫闭上了眼睛。")
        --撸猫次数+1
        flags.cat_conquered = flags.cat_conquered + 1
    elseif (ret == 2) then
        s.animState(npcs.cat, "Combat", true)
        s.talk(npcs.cat, "喵！")
        s.animState(npcs.cat, "Attack_01")
        s.aside("小猫警惕地后退了几步。")
        s.animState(npcs.cat, "Combat", false)
    end

    s.animState(npcs.jingcheng, roleAnims.BSquat_Loop, false)

    --设置随机猫的位置1-5
    local rndPos = math.random(5)
    ::A::
    if rndPos == flags.cat_pos then
        rndPos = math.random(5)
        goto A
    end
    flags.cat_pos = rndPos
    s.aside("小猫迅速的逃跑了")
    wrappedReloadAllSceneStates()
end

function catConquered()
    s.aside("小猫在你的脚边打转。")
    s.animState(npcs.cat, "Caress")
    if (flags.cat_rewarded == 0) then
        flags.cat_rewarded = 1
        s.finishTask(300020, 1)
    end
end

---@gameEvent NPC菜农对话
function eventNpcCainong()
    s.animState(npcs.cainong, roleAnims.BGesture)
    if (flags.cainong_step == 0) then
        cainong001()
    elseif (flags.cainong_step == 1) then
        cainong002()
    elseif (flags.cainong_step == 2) then
        cainong003()
    elseif (flags.cainong_step == 3) then
        cainong004()
    else
        cainong005()
    end

    flags.cainong_step = flags.cainong_step + 1
end

function cainong001()
    --[[△首次对话
    楚家庄菜农：今天上山送的折耳根，刚送来就让我装一半回去自己吃。
    楚家庄菜农：奇怪了，这不是小少爷最爱吃的蔬菜吗？
    ——————选项开始——————
    选项一：折耳根是什么？
    楚家庄菜农：又能解暑做药材，又能拌着吃的野菜，你居然不知道？
    楚家庄菜农：这样，你来我园子，我给你抓三把不同的折耳根给你回去尝尝。
    选项二：折耳根人间美味
    楚家庄菜农：就是，有人还嫌弃它有股味儿呢！
    楚家庄菜农：萝卜带点苦，折耳根带点腥，地瓜叶带点涩，但它们都各有各的好处，带点儿味儿反而更有意思了，是吧。
    选项三：折……耳……根……
    楚家庄菜农：干嘛，你什么表情，你眉毛都拧成麻花了，是有啥心事吗？
    ——————选项结束——————]]
    s.talk(npcs.cainong, "今天上山送的折耳根，刚送来就让我装一半回去自己吃。")
    s.talk(npcs.cainong, "奇怪了，这不是小少爷最爱吃的蔬菜吗？")
    local ret = s.selectTalk(npcs.jingcheng, "……", { "折耳根是什么？", "折耳根人间美味", "折……耳……根……" })
    if (ret == 0) then
        s.talk(npcs.cainong, "又能解暑做药材，又能拌着吃的野菜，你居然不知道？")
        s.talk(npcs.cainong, "这样，你来我园子，我给你抓三把不同的折耳根给你回去尝尝。")
    elseif (ret == 1) then
        s.talk(npcs.cainong, "就是，有人还嫌弃它有股味儿呢！")
        s.talk(npcs.cainong, "萝卜带点苦，折耳根带点腥，地瓜叶带点涩，但它们都各有各的好处，带点儿味儿反而更有意思了，是吧。")
    elseif (ret == 2) then
        s.talk(npcs.cainong, "干嘛，你什么表情，你眉毛都拧成麻花了，是有啥心事吗？")
    end
end

function cainong002()
    --[[楚家庄菜农：夫人这几日胃口不咋滴啊？
    楚家庄菜农：王大娘说夫人爱吃南瓜，但我看厨房里头茬的嫩南瓜都快放烂了。]]
    s.talk(npcs.cainong, "夫人这几日胃口不咋滴啊？")
    s.talk(npcs.cainong, "王大娘说夫人爱吃南瓜，但我看厨房里头茬的嫩南瓜都快放烂了。")
end

function cainong003()
    --[[楚家庄菜农：上次在菜园子门口有几个莫名其妙的年轻人拦住我，说我是个什么……攀……什么……就是舔着有钱人家的孬人。
    楚家庄菜农：一个劲地我为啥要每天起早贪黑地给楚家庄送菜，不把这些菜分给村里的大家。
    楚家庄菜农：我真是哭也不是，笑也不是，这菜园子本来就是楚家庄的，要分菜也轮不到我做主啊。
    楚家庄菜农：结果那几个说不出话来，最后只是憋出一句他们想讨些菜吃。]]
    s.talk(npcs.cainong, "上次在菜园子门口有几个莫名其妙的年轻人拦住我，说我是个什么……攀……什么……就是舔着有钱人家的孬人。")
    s.talk(npcs.cainong, "一个劲地我为啥要每天起早贪黑地给楚家庄送菜，不把这些菜分给村里的大家。")
    s.talk(npcs.cainong, "我真是哭也不是，笑也不是，这菜园子本来就是楚家庄的，要分菜也轮不到我做主啊。")
    s.talk(npcs.cainong, "结果那几个说不出话来，最后只是憋出一句他们想讨些菜吃。")
end

function cainong004()
    --[[楚家庄菜农：种地看着简单，其中学问多着呢！咱们的菜养得鲜亮，又少见虫子，吃着鲜嫩多汁。
    楚家庄菜农：我听说咱庄主的成名绝技《瀚海功》是他师父看海的时候领悟出来的，那是不是我这种菜也能领悟出什么《菜园功》、《打虫功》……]]
    s.talk(npcs.cainong, "种地看着简单，其中学问多着呢！咱们的菜养得鲜亮，又少见虫子，吃着鲜嫩多汁。")
    s.talk(npcs.cainong, "我听说咱庄主的成名绝技《瀚海功》是他师父看海的时候领悟出来的，那是不是我这种菜也能领悟出什么《菜园功》、《打虫功》……")
end

function cainong005()
    --[[楚家庄菜农：我口好渴，暂时不想说话。]]
    s.talk(npcs.cainong, "我口好渴，暂时不想说话。")
end

---@gameEvent 楚家庄侍女甲
function eventNpcShinvjia()
    print("eventNpcShinvjia")
    s.animState(npcs.shinvjia, roleAnims.BAgree)

    --随机1-4
    local ret = math.random(4)
    if (ret == 1) then
        --[[楚家庄侍女：楚夫人原来是江湖人士，好像是什么天鹰……什么的门派。
        楚家庄侍女：这名字好生奇怪，鹰是一种很凶的鸟禽，这门派里却全是女子。]]
        s.talk(npcs.shinvjia, "楚夫人原来是江湖人士，好像是什么天鹰……什么的门派。")
        s.talk(npcs.shinvjia, "这名字好生奇怪，鹰是一种很凶的鸟禽，这门派里却全是女子。")
    elseif (ret == 2) then
        --[[楚家庄侍女：楚夫人对我们很好，没有什么架子，知书达理不说，还是远近闻名的女神医。
        楚家庄侍女：好些达官显贵的女眷生了病，都要抬着轿子专程过来请楚夫人帮忙。
        楚家庄侍女：楚夫人本是个不爱出门的人，但一年也要为此奔波好几次，谁叫她是个心善的大好人呢。]]
        s.talk(npcs.shinvjia, "楚夫人对我们很好，没有什么架子，知书达理不说，还是远近闻名的女神医。")
        s.talk(npcs.shinvjia, "好些达官显贵的女眷生了病，都要抬着轿子专程过来请楚夫人帮忙。")
        s.talk(npcs.shinvjia, "楚夫人本是个不爱出门的人，但一年也要为此奔波好几次，谁叫她是个心善的大好人呢。")
    elseif (ret == 3) then
        --[[楚家庄侍女：听说大公子出生前后的那段时间，庄主正在外地远游。
        楚家庄侍女：楚庄主为了楚家庄四处奔波，连孩子出生都无暇顾及，我觉得这和大禹治水时过门不入的心情是差不多的。]]
        s.talk(npcs.shinvjia, "听说大公子出生前后的那段时间，庄主正在外地远游。")
        s.talk(npcs.shinvjia, "楚庄主为了楚家庄四处奔波，连孩子出生都无暇顾及，我觉得这和大禹治水时过门不入的心情是差不多的。")
    else
        --[[楚家庄侍女：六小姐远嫁之后，庄里忽然间就安静下来了。
        楚家庄侍女：不，也不算安静，毕竟还有八公子呢……]]
        s.talk(npcs.shinvjia, "六小姐远嫁之后，庄里忽然间就安静下来了。")
        s.talk(npcs.shinvjia, "不，也不算安静，毕竟还有八公子呢……")
    end
end

---@gameEvent 楚家庄侍女乙
function eventNpcShinvyi()
    local ret = math.random(3)
    s.animState(npcs.shinvyi, roleAnims.BGesture)
    if (ret == 1) then
        --[[楚家庄侍女：二公子顽皮，不许旁人称他乳名，只有大公子能治得了他。
        楚家庄侍女：大公子常言，在家里只能自称老七、老八，勿要忘了上面六位姐姐。
        楚家庄侍女：你们说，他这样有情有义的人，怎的老天爷就偏要刁难呢？]]
        s.talk(npcs.shinvyi, "二公子顽皮，不许旁人称他乳名，只有大公子能治得了他。")
        s.talk(npcs.shinvyi, "大公子常言，在家里只能自称老七、老八，勿要忘了上面六位姐姐。")
        s.talk(npcs.shinvyi, "你们说，他这样有情有义的人，怎的老天爷就偏要刁难呢？")
    elseif (ret == 2) then
        --[[楚家庄侍女：大公子素日身子弱，经不得冷风，可不能一直这样在屋外站着。]]
        s.talk(npcs.shinvyi, "大公子素日身子弱，经不得冷风，可不能一直这样在屋外站着。")
    else
        --[[楚家庄侍女：大公子风度翩翩，不知哪家小姐有这个福分和他共结连理。]]
        s.talk(npcs.shinvyi, "大公子风度翩翩，不知哪家小姐有这个福分和他共结连理。")
    end
end

---@gameEvent 马夫
function eventNpcMafu()
    s.animState(npcs.mafu, roleAnims.BScratch)
    if (flags.mafu_step == 0) then
        s.talk(npcs.mafu, "做什么？这是楚家庄养的马，外人骑不得。")
        flags.mafu_step = flags.mafu_step + 1
    elseif (flags.mafu_step == 1) then
        s.talk(npcs.mafu, "马拉的马车是楚家庄定制的，外人用不得。")
        flags.mafu_step = flags.mafu_step + 1
    elseif (flags.mafu_step == 2) then
        s.talk(npcs.mafu, "我是楚家庄聘请的马夫，外人借不得。")
        flags.mafu_step = flags.mafu_step + 1
    elseif (flags.mafu_step == 3) then
        s.talk(npcs.mafu, "不要问我了，他们的行程，外人探不得。")
        flags.mafu_step = flags.mafu_step + 1
    else
        s.talk(npcs.mafu, "你到底要做什么？")
        local ret = s.selectTalk(npcs.jingcheng, "……", { "说不得", "驾！", "无事" })
        if (ret == 0) then
            s.talk(npcs.mafu, "嘁，我不会对养马护马驭马之外的任何事情感兴趣！")
            s.talk(npcs.mafu, "【咬牙切齿】（可恶，这人到底要干嘛……好好奇！）")
        elseif (ret == 1) then
            s.talk(npcs.mafu, "……")
            s.talkThink(npcs.mafu, "（幻想自己和我一样是位优秀的马夫吗？好可怜……）")
        elseif (ret == 2) then
            s.talk(npcs.mafu, "别觊觎我的宝贝马儿们。")
            s.talk(npcs.mafu, "更别觊觎我！")
        end
    end

    s.animState(npcs.mafu, roleAnims.BThink_Loop, false)
end

---@gameEvent 楚七
function eventNpcChuqi()
    --[[——————选项开始——————
    选项一 询问姓名
    荆成：楚公子，你的名字……
    楚七：家中排行第七，前面有六位姐姐，故称楚七。
    楚七：在下本名为希声，字蕴常。名由父母所赐，取自“大音希声”，意求包容万物，顺其自然。
    楚七：说来，这大音希声的上一句是“大器免成”，谁料一语成谶，我年至弱冠，仍碌碌无成……
    楚七：【笑】自怨自艾而已，荆兄万不可因此坏了心情。

    选项二 询问楚家庄相关
    楚七：楚家庄是二十年前我父亲一手成立，他凭借一身高强武艺，锄强扶弱，灭妖除祸，在剑南颇有些声望，也结交了不少知己好友。
    楚七：家父少时以护镖为生，而后又自立门户，做起了一些小生意。
    楚七：一开始，楚家庄不过半山腰的一处农舍而已，随着家父的日夜辛劳，不倦筋力，方从小小一隅变成了如今偌大的楚家庄。
    楚七：家父白手起家，年少有为，策名褒扬，只恐我这一生都不可望其项背。

    选项三 询问楚家庄庄主在哪
    楚七：家父正在别院等候，二位可随时前往。

    选项四 行礼
    △楚七作揖回礼
    ——————选项结束——————
    备注：选项四行礼三次后楚七不再回礼，而是弹出对话
    楚七：荆兄……这是何意？]]
    local ret = s.selectTalk(npcs.jingcheng, "……", { "询问姓名", "询问楚家庄相关", "询问楚家庄庄主在哪", "行礼" })
    if (ret == 0) then
        s.talk(npcs.jingcheng, "楚公子，你的名字……")
        s.talk(npcs.chuqi, "家中排行第七，前面有六位姐姐，故称楚七。")
        s.talk(npcs.chuqi, "在下本名为希声，字蕴常。名由父母所赐，取自“大音希声”，意求包容万物，顺其自然。")
        s.talk(npcs.chuqi, "说来，这大音希声的上一句是“大器免成”，谁料一语成谶，我年至弱冠，仍碌碌无成……")
        s.talk(npcs.chuqi, "【笑】自怨自艾而已，荆兄万不可因此坏了心情。")
    elseif (ret == 1) then
        s.talk(npcs.chuqi, "楚家庄是二十年前我父亲一手成立，他凭借一身高强武艺，锄强扶弱，灭妖除祸，在剑南颇有些声望，也结交了不少知己好友。")
        s.talk(npcs.chuqi, "家父少时以护镖为生，而后又自立门户，做起了一些小生意。")
        s.talk(npcs.chuqi, "一开始，楚家庄不过半山腰的一处农舍而已，随着家父的日夜辛劳，不倦筋力，方从小小一隅变成了如今偌大的楚家庄。")
        s.talk(npcs.chuqi, "家父白手起家，年少有为，策名褒扬，只恐我这一生都不可望其项背。")
    elseif (ret == 2) then
        s.talk(npcs.chuqi, "家父正在别院等候，二位可随时前往。")
    else
        s.animState(npcs.jingcheng, roleAnims.BGreet)
        s.wait(2)
        if (flags.chuqi_greet_count < 4) then
            s.animState(npcs.chuqi, roleAnims.BGreet)
            s.wait(3)
            flags.chuqi_greet_count = flags.chuqi_greet_count + 1
        else
            s.animState(npcs.chuqi, roleAnims.BScratch)
            s.wait(1)
            s.talk(npcs.chuqi, "荆兄……这是何意？")
        end
    end
end

---@gameEvent 楚家庄护卫
function eventNpcHuwei()
    if (flags.huwei_step == 0) then
        s.talk(npcs.huwei, "咱们庄主自幼习武，而且不是像我们这种，他会内功的。")
        s.talk(npcs.huwei, "那内功叫什么来着……什么海功。")

        local opts = { "苦海功？", "法海功？", "学海功？" }
        local optRead = { false, false, false } --todo:程序需求，需要一个无法被选择的选项

        ::huwei_select::

        local ret = s.selectTalk(npcs.jingcheng, "……", opts)
        if (ret == 0) then
            s.talk(npcs.huwei, "【皱眉】好像……好像没有这么难听。")
            opts[1] = "<color=grey>苦海功？</color>"
            optRead[1] = true
        elseif (ret == 1) then
            s.talk(npcs.huwei, "啊？听起来很深奥的样子，但是似乎不叫这个。")
            opts[2] = "<color=grey>法海功？</color>"
            optRead[2] = true
        elseif (ret == 2) then
            s.talk(npcs.huwei, "没有这么血腥！我们家老爷是正人君子！")
            s.talk(npcs.huwei, "不，不是你想的那个字……")
            opts[3] = "<color=grey>学海功？</color>"
            optRead[3] = true
        end

        --必须三个选项都选过了，才能继续
        if (optRead[1] == false or optRead[2] == false or optRead[3] == false) then
            goto huwei_select
        end

        s.talk(npcs.huwei, "我想起来了！分明是“瀚海功”。")
        s.talk(npcs.huwei, "正所谓……")
        s.talk(npcs.huwei, "……")
        s.talk(npcs.huwei, "我书读得不多，也不知道所谓什么，总之就是“瀚海”两个字，你记清楚了。")
        s.selectTalk(npcs.jingcheng, "……", { "（明明是你记不清楚）", "哦……" })
        flags.huwei_step = 1
    elseif (flags.huwei_step == 1) then
        s.talk(npcs.huwei, "老爷吩咐过，莫大师可以畅通无阻。")
        s.talk(npcs.huwei, "莫大师带过来的人也是一样。")
    end
end

---@gameEvent 传送到后山
function transportHoushan()
    print("传送到后山")
    s.changeMap("Assets/GameScenes/游历场景/漫游_楚家庄/漫游_楚家庄_别院.unity", "位置/start")
end

---@gameEvent 宝箱
function chest1()
    if (flags.chest1 == 0) then
        s.openChest(objs.chest1, "TravelChest_1cjz_1")
        s.wait(0.5)

        flags.chest1 = 1
        wrappedReloadAllSceneStates()
    end
end
