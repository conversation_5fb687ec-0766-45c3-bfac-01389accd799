--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/主线_鹿鸣天下5_3/flags_主线_鹿鸣天下5_3")


---@class 角色_主线_鹿鸣天下5_3
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    changguai3 = "角色/changguai3",
    changguai4 = "角色/changguai4",
    unknownNPC = "角色/unknownNPC",
    zhugelu_timeline = "角色/zhugelu_timeline",
    zhugelu_avg = "角色/zhugelu_avg",
    --- "有拳套的主角"
    zhujue1 = "角色/zhujue1",
}

---@class 物体_主线_鹿鸣天下5_3
local objs = {
    --- "石台"
    shitai = "物体/shitai",
    --- "左侧大门"
    door_left = "物体/door_left",
    talk_2 = "物体/talk_2",
    talk_3 = "物体/talk_3",
    shitai_fx = "物体/shitai_fx",
    door_left_fx = "物体/door_left_fx",
    cave_1 = "物体/cave_1",
    cave_2 = "物体/cave_2",
}

---@class 相机_主线_鹿鸣天下5_3
local cameras = {
    --- "Main"
    cam_main = "相机/cam_main",
    --- "密室开场"
    cam_backroom_start = "相机/cam_backroom_start",
}

---@class 位置_主线_鹿鸣天下5_3
local pos = {
    --- "开始位置"
    pos_start = "位置/pos_start",
    --- "拳套密室"
    pos_backroom = "位置/pos_backroom",
}

---@class 资产_主线_鹿鸣天下5_3
local assets = {
    --- "主线_鹿鸣天下5_3_1"
    lmtx5_3_1 = "资产/lmtx5_3_1",
    --- "主线_鹿鸣天下5_3_2"
    lmtx5_3_2 = "资产/lmtx5_3_2",
    --- "主线_鹿鸣天下5_3_3"
    lmtx5_3_3 = "资产/lmtx5_3_3",
    --- "主线_鹿鸣天下5_3_4"
    lmtx5_3_4 = "资产/lmtx5_3_4",
}

---@class 动作_主线_鹿鸣天下5_3
local animationClips = {
}

---@class 主线_鹿鸣天下5_3
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
