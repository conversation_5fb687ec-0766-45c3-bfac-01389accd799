--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_青羽录_01芦苇地/flags_蜃境_青羽录_01芦苇地")


---@class 角色_蜃境_青羽录_01芦苇地
local npcs = {
    zhujue = "角色/zhujue",
    jingcheng = "角色/jingcheng",
    yunwusheng = "角色/yunwusheng",
    shenjingjingcheng = "角色/shenjingjingcheng",
    elite_02 = "角色/elite_02",
    elite_02_PreShow = "角色/elite_02_PreShow",
    enemy_01 = "角色/enemy_01",
}

---@class 物体_蜃境_青羽录_01芦苇地
local objs = {
    trigger = "物体/trigger",
    xiesui = "物体/xiesui",
    exit = "物体/exit",
    qyl1_1000 = "物体/qyl1_1000",
    qyl1_1001 = "物体/qyl1_1001",
    elite_02_battle = "物体/elite_02_battle",
    elite_02_Interact = "物体/elite_02_Interact",
    elite_02_muren = "物体/elite_02_muren",
}

---@class 相机_蜃境_青羽录_01芦苇地
local cameras = {
    camera1 = "相机/camera1",
    camera2 = "相机/camera2",
    camera3 = "相机/camera3",
}

---@class 位置_蜃境_青羽录_01芦苇地
local pos = {
    start = "位置/start",
    jingcheng_start = "位置/jingcheng_start",
    yunwusheng_start = "位置/yunwusheng_start",
    jingcheng_play_pos = "位置/jingcheng_play_pos",
    zhujue_play_pos = "位置/zhujue_play_pos",
    yunwusheng_play_pos = "位置/yunwusheng_play_pos",
    --- "虚影表演点"
    elite_show = "位置/elite_show",
}

---@class 资产_蜃境_青羽录_01芦苇地
local assets = {
    --- "出示荆成timeline"
    elitePreShow = "资产/elitePreShow",
}

---@class 动作_蜃境_青羽录_01芦苇地
local animationClips = {
}

---@class 蜃境_青羽录_01芦苇地
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
