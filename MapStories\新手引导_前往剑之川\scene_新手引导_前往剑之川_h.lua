--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/新手引导_前往剑之川/flags_新手引导_前往剑之川")


---@class 角色_新手引导_前往剑之川
local npcs = {
    --- "荆成"
    jingcheng = "角色/jingcheng",
}

---@class 物体_新手引导_前往剑之川
local objs = {
    step1 = "物体/step1",
    step2 = "物体/step2",
    step3 = "物体/step3",
    step4 = "物体/step4",
    stepfinal = "物体/stepfinal",
}

---@class 相机_新手引导_前往剑之川
local cameras = {
}

---@class 位置_新手引导_前往剑之川
local pos = {
    --- "posStart"
    posStart = "位置/posStart",
}

---@class 资产_新手引导_前往剑之川
local assets = {
}

---@class 动作_新手引导_前往剑之川
local animationClips = {
}

---@class 新手引导_前往剑之川
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
