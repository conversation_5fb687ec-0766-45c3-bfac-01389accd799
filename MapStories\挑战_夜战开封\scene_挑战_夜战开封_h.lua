--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_夜战开封/flags_挑战_夜战开封")


---@class 角色_挑战_夜战开封
local npcs = {
    zhujue = "角色/zhujue",
    boss1 = "角色/boss1",
    boss2 = "角色/boss2",
    boss3 = "角色/boss3",
    boss4 = "角色/boss4",
    boss5 = "角色/boss5",
    yanluchong = "角色/yanluchong",
    butou = "角色/butou",
    sb = "角色/sb",
}

---@class 物体_挑战_夜战开封
local objs = {
    boss1Trigger = "物体/boss1Trigger",
    boss2Trigger = "物体/boss2Trigger",
    boss3Trigger = "物体/boss3Trigger",
    boss4Trigger = "物体/boss4Trigger",
    boss5Trigger = "物体/boss5Trigger",
    boss5Effect = "物体/boss5Effect",
    quit = "物体/quit",
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
    chest_3 = "物体/chest_3",
    chest_4 = "物体/chest_4",
    chest_5 = "物体/chest_5",
    chest_6 = "物体/chest_6",
    teleport_1 = "物体/teleport_1",
    teleport_2 = "物体/teleport_2",
    teleport_3 = "物体/teleport_3",
    teleport_4 = "物体/teleport_4",
    teleport_5 = "物体/teleport_5",
}

---@class 相机_挑战_夜战开封
local cameras = {
    start_camera = "相机/start_camera",
    boss1_camera = "相机/boss1_camera",
    boss2_camera1 = "相机/boss2_camera1",
    boss2_camera2 = "相机/boss2_camera2",
    boss3_camera1 = "相机/boss3_camera1",
    boss3_camera2 = "相机/boss3_camera2",
    boss4_camera1 = "相机/boss4_camera1",
    boss4_camera2 = "相机/boss4_camera2",
    boss5_camera1 = "相机/boss5_camera1",
    boss5_camera2 = "相机/boss5_camera2",
}

---@class 位置_挑战_夜战开封
local pos = {
    start = "位置/start",
    yanluchong_initPos = "位置/yanluchong_initPos",
    boss1_zhujuePos = "位置/boss1_zhujuePos",
    boss1_yanluchongPos = "位置/boss1_yanluchongPos",
    boss1_hulaohaiPos_2 = "位置/boss1_hulaohaiPos_2",
    boss1_hulaohaiPos_1 = "位置/boss1_hulaohaiPos_1",
    boss2_bossPos = "位置/boss2_bossPos",
    boss2_zhujuePos = "位置/boss2_zhujuePos",
    boss2_yanluchongPos = "位置/boss2_yanluchongPos",
    boss2_lookPos = "位置/boss2_lookPos",
    boss3_bossPos = "位置/boss3_bossPos",
    boss3_zhujuePos = "位置/boss3_zhujuePos",
    boss3_zhujuePos2 = "位置/boss3_zhujuePos2",
    boss3_yanluchongPos = "位置/boss3_yanluchongPos",
    boss3_yanluchongPos2 = "位置/boss3_yanluchongPos2",
    boss4_zhujuePos = "位置/boss4_zhujuePos",
    boss4_zhujuePos2 = "位置/boss4_zhujuePos2",
    boss4_yanluchongPos = "位置/boss4_yanluchongPos",
    boss4_yanluchongPos2 = "位置/boss4_yanluchongPos2",
    boss5_bossPos = "位置/boss5_bossPos",
    boss5_zhujuePos = "位置/boss5_zhujuePos",
    boss5_yanluchongPos = "位置/boss5_yanluchongPos",
    boss1_revive_zhujue = "位置/boss1_revive_zhujue",
    boss1_revive_yanluchong = "位置/boss1_revive_yanluchong",
    boss2_revive_zhujue = "位置/boss2_revive_zhujue",
    boss2_revive_yanluchong = "位置/boss2_revive_yanluchong",
    boss3_revive_zhujue = "位置/boss3_revive_zhujue",
    boss3_revive_yanluchong = "位置/boss3_revive_yanluchong",
    boss4_revive_zhujue = "位置/boss4_revive_zhujue",
    boss4_revive_yanluchong = "位置/boss4_revive_yanluchong",
    boss5_revive_zhujue = "位置/boss5_revive_zhujue",
    boss5_revive_yanluchong = "位置/boss5_revive_yanluchong",
    bossAreaEnter_zhujue = "位置/bossAreaEnter_zhujue",
    bossAreaEnter_yanluchong = "位置/bossAreaEnter_yanluchong",
    bossAreaQuit_zhujue = "位置/bossAreaQuit_zhujue",
    bossAreaQuit_yanluchong = "位置/bossAreaQuit_yanluchong",
}

---@class 资产_挑战_夜战开封
local assets = {
}

---@class 动作_挑战_夜战开封
local animationClips = {
}

---@class 挑战_夜战开封
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
