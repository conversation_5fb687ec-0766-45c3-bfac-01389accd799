local bcapi = require("BattleClient<PERSON><PERSON>")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

local level = 30

function init()

end

function start()  
    bcapi.add_timer_trigger(0.1, Talk)
end

function Talk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("主角", "藏头露尾的鼠辈，敢不敢露出你的真面目？")
        bcapi.talk("神秘刺客", "【声音故意变得闷声怪气】少说废话，你要是乖乖束手就擒，我或许可以饶你不死！")
        bcapi.talk("主角", "好大的口气，那就看看你有没有这个本事了！")
        bcapi.resume_and_show_ui()
    end)
end