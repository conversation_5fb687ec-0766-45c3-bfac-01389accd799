---@type 挑战_贼寇山洞
local context = require("MapStories/挑战_贼寇山洞/scene_挑战_贼寇山洞_h")
local s = context.sapi
local flags = context.flags
s.loadFlags(flags)


---@class 挑战_贼寇山洞_StringItemData
local stringItemData = {
    
}

---@class 挑战_贼寇山洞_ItemData
local itemData = {
    
}

local stringWidget = StringWidget:new(stringItemData,flags)
local itemWidget = ItemWidget:new(itemData,flags)

local itemWidgetInfo = {
    title = "道具",
    items = itemData,
    widget = itemWidget
}

local stringWidgetInfo = {
    
}

extensions = {
    useWidgets = {},
    widgets = {
    }
}

context.sapi.syncExtendsInfo(extensions)
return extensions