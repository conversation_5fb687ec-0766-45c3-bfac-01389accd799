
--变量设置
---@class flags_蜃境_雾锁寒山_04流亡者营地
local flags = {
    --- "变量名"
    foo = "foo",

    --- "关卡步数"
    level_step = 0,

    --- "是否打开宝箱1"
    open_shadowChest_1 = 0,

    --- "是否打开宝箱2"
    open_shadowChest_2 = 0,
    
    --- "小怪战斗状态"
    enemy_1_battle = 0,
    enemy_2_battle = 0,
    enemy_3_battle = 0,
    enemy_4_battle = 0,
    enemy_5_battle = 0,

    --- "小怪击倒状态"
    enemy_1_down = 0,
    enemy_2_down = 0,
    enemy_3_down = 0,
    enemy_4_down = 0,
    enemy_5_down = 0,


    --- "boss战斗状态"
    boss_8_battle = 0,

    --- "boss演出"
    boss_8_show = 0,

    --- "开场演出"
    start_show = 0,

    --- "道士解密"
    daoshi_jiemi = 0,

    --- "道士解密状态:1-击倒所有敌人 2-击败所有敌人" 
    daoshi_jiemi_state = 0,

    --- "道士解密提示完成"
    daoshi_jiemi_hint_done = 0,

    --- "道士解密第一次击倒敌人"
    daoshi_first_down = 0,

    --- "收集品"
    collect_1 = 0,
    collect_2 = 0,

    
    --- "墨七对话"
    moqi_talk = 0,
}

return flags
