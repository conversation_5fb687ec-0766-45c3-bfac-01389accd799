local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

local level = 30

function init()
    createRoles()
end

function start()  

end

function createRoles()
    bcapi.async_call(function()
        --创建角色
        bcapi.create_join_role("世界_剧情侠客_金燕桃", level, team, bcapi.Vector2(-1, 0), 0)
    end)
end
