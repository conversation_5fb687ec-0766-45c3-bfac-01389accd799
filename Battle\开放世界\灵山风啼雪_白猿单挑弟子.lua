local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.show_memory_effect()
    createRoles()
end

function start()  

end

function createRoles()
    bcapi.async_call(function()
        --创建角色
        bcapi.create_join_role_with_card("白猿",45, team, bcapi.Vector2(-4, 0), 0)
    end)
end

