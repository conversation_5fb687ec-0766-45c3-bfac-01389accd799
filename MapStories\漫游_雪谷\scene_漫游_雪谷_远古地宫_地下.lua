
---@type 漫游_雪谷_远古地宫_地下
local context = require("MapStories/漫游_雪谷/scene_漫游_雪谷_远古地宫_地下_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

---组件相关
local extensions = require("MapStories/漫游_雪谷/extension")
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type 雪谷_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 雪谷_ItemData]]


s.loadFlags(context.flags)
---@type flags_漫游_雪谷
local flags = context.flags

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.readyToStart()
    reloadAllSceneStates()
end

function reloadAllSceneStates()
    --药方
    s.setActive(objs.yaofang,flags.dg_main_step < 3)
    
    --玄恩
    s.setActive(npcs.xuanen,flags.dg_xuanen_killed == 0)
    
end

function wrappedReloadAllSceneStates()
    s.blackScreen()
    reloadAllSceneStates()
    s.lightScreen()
end

function talkToLaoli()
    if flags.dg_main_step < 2 then
        talkToLaoli001()
    elseif flags.dg_main_step < 4 then
        talkToLaoli002()
    elseif flags.dg_main_step == 4 then
        talkToLaoli003()
    else
        talkToLaoli004()
    end
end


function talkToLaoli001()
    --△你在地宫深处发现身体僵硬，目光呆滞的奇怪男子老黎。
    --△你忽然发现，老黎的脖子上竟然挂着一枚琳琅玉键。
    s.talk(npcs.laoli,"时候不多了，不多了……")
    s.talk(npcs.laoli,"药……必须找到……")

    if itemWidget:getItemCount(itemData.jiguanlingjian1) ~= 0 or itemWidget:getItemCount(itemData.jiguanlingjian2) ~= 0 or itemWidget:getItemCount(itemData.jiguanlingjian3) ~= 0 or itemWidget:getItemCount(itemData.jiguanlingjian4) ~= 0 then
        s.talk(npcs.moqi,"这是……琳琅玉键？此人衣衫褴褛，怎么会拥有如此宝物？其中必有蹊跷。")
    else
        s.talk(npcs.moqi,"此人衣衫褴褛，脖子上挂着的东西却异常华贵……其中想必有什么蹊跷。")
    end

    s.talk(npcs.moqi,"喂！你没事吧？")
    s.talk(npcs.laoli,"太迟了……走……你走……")
    s.talk(npcs.moqi,"走？为什么？")
    s.talk(npcs.laoli,"你若不走，那就只有——死！")
    flags.dg_main_step = 1
    
    --△老黎双眼突然变得血红，向你扑来。
    
    local isWin = s.battle("楚家庄_漫游_黑衣人")--todo：待调整
        
    if isWin then
        --△老黎战败后不再具有攻击性，变得眼神木然一言不发，如同傀儡一般。
        s.talk(npcs.moqi,"喂，你听得见我讲话么？")
        s.talk(npcs.laoli,"……")
        s.talkThink(npcs.moqi,"（此人双眼空洞，意识游离，也不知究竟得了什么病。）")
        s.talkThink(npcs.moqi,"（对了……他刚才嘴里嘟囔着找什么解药，莫非就在这附近？）")

        flags.dg_main_step = 2
        s.addTask(400420,1)
    else
        s.aside("看来还不是时候。")--todo:刷出地宫？？
    end
    
end

function talkToLaoli002()
    s.talk(npcs.laoli,"……")

    local ret = s.selectTalk(npcs.moqi, "……", {"将药粉喂老黎服下", "没事"})

    if ret == 0 then
        local submitItem = s.submitTempItem("请提交药粉。")
        if (submitItem ~= nil) then
            if (submitItem[0].Key == itemData.yaofen.key) then

                itemWidget:removeItem(itemData.yaofen,1)
                --△墨七掏出药粉，有些犹豫。
                s.talk(npcs.moqi,"我已按照要求合成了解药，但愿能够奏效。")
                --△墨七喂老黎服下药粉，片刻后，只见对方捶胸长啸，双眼也变得通红。
                s.talk(npcs.laoli,"好烫……心在灼烧，血在翻涌……你给我吃了什么……")
                --△老黎使劲抓挠胸口。
                s.blackScreen()
                s.aside("过了许久，怪人才渐渐平息下来，眼神也恢复如常……")
                s.lightScreen()
                s.talk(npcs.moqi,"你好点了？")
                s.talk(npcs.laoli,"唔……我刚才是怎么了……")
                s.talk(npcs.laoli,"胸口不烫了，头脑也清楚了几分，莫非是你小子把我体内的毒给治好的？哈哈哈哈哈！真是天降之喜！")
                s.talk(npcs.moqi,"你中了什么毒？")
                s.talk(npcs.laoli,"一种世间罕有的毒药，中毒者每过十日就会间歇性发作，就像你刚才看到的那样——陷入疯狂，见人就杀。")
                s.talk(npcs.laoli,"我现在感到前所未有的舒坦，就像一根扎了多年的刺，终于从喉咙深处被你拔去。")
                s.talk(npcs.moqi,"谁给你下的毒？")
                s.talk(npcs.laoli,"一个女魔头，她是个极度危险的人物。那些打听她消息的最后都没落得好下场。")
                s.talk(npcs.moqi,"你知道解药就在附近？")
                s.talk(npcs.laoli,"不错，我曾偶遇一神秘老者，他见我面带亡者般的幽暗煞气，直言我所中剧毒与地宫墓穴有关，他还称这毒药与解药相克相生，若是回到地宫中寻找或许尚有一线生机，并给我一张药方。")
                s.talk(npcs.moqi,"你到底经历了什么？")
                s.talk(npcs.laoli,"好了，换我来问你，我叫老黎，小子，你叫什么名字？")
                s.talk(npcs.moqi,"我叫墨七。")
                s.talk(npcs.laoli,"墨七？没听过，你是不是在家里排行老七？一个小孩子家家来这地宫里做什么？")
                s.talk(npcs.moqi,"你又是来做什么的？")
                s.talk(npcs.laoli,"我是来寻找答案的——一个危险的答案。")
                s.talk(npcs.laoli,"你猜这地宫为何危机四伏伥怪横行？那些沉睡的机关巨兽又是什么来头？")
                s.talk(npcs.moqi,"所以，你找到了吗？")
                --△老黎微微一笑，似乎在等你这句话。
                s.talk(npcs.laoli,"你能集齐这些解药，证明你不仅身手了得，而且还有一副侠义心肠，现在麻烦你再到外面跑一趟，替我干掉这份清单上的三个人，我就将这里发生的一切原原本本都告诉你。")
                s.talk(npcs.moqi,"这三人犯了什么罪？")
                s.talk(npcs.laoli,"熊豪性子暴躁，出手不知分寸，以致仇家众多，他有时为了躲避寻仇之人，会藏匿在雪崖之上的一座小屋中。")
                s.talk(npcs.laoli,"汪虎为人冷僻，刀法十分了得，他还擅长易容伪装之术，人越嘈杂热闹的地方，他反而越是自在。")
                s.talk(npcs.laoli,"毛欢从小修炼祖传爪功，他下手毒辣，表面却极为儒雅，颇喜音律，对丝竹钟磬声很是痴迷。")
                
                flags.dg_main_step = flags.dg_main_step + 1
                s.finishTask(400420,3)
                s.addTask(400430,1)
            else
                s.talkThink(npcs.moqi,"（这显然不是我需要的东西）")
            end
        else
            s.aside("你没有提交任何东西。")
        end
    end
    
end

function talkToLaoli003()
    s.talk(npcs.laoli,"只要找到谜底，一切就好办了……")
    if flags.dg_main_step == 7 then
        s.talk(npcs.laoli,"这么快就已除掉那三人？他们可都是百里挑一的练家子，你怎么向我证明？")
        s.selectTalk(npcs.moqi, "……", {"提交证物"})
        s.talk(npcs.laoli,"这些是……")
        
        itemWidget:removeItem(itemData.fengmao,1)
        itemWidget:removeItem(itemData.lingpai,1)
        itemWidget:removeItem(itemData.tudao,1)
        
        s.talk(npcs.laoli,"哈哈哈哈哈！好好好！你干得不错，这三个狗贼死有余辜！")
        s.talk(npcs.moqi,"他们怎么招惹你了？")
        s.talk(npcs.laoli,"背叛之罪，死不足惜。")
        s.talk(npcs.moqi,"其中两人竟是伥怪……")
        s.talk(npcs.laoli,"是吗，我一点也不意外，这都是他们罪有应得！")
        s.talk(npcs.moqi,"到底怎么回事？")
        s.talk(npcs.laoli,"一切都要从这座地宫说起了。")
        
        --todo：演出级别
        s.talk(npcs.laoli,"当年，我与汪虎、苟欢、熊豪三人曾为拜把子兄弟，我们啸聚山林，在这雪谷外面安营扎寨，手下还有几十号汉子，日子过得好不快活。")
        s.talk(npcs.moqi,"你们竟然是兄弟！")
        s.talk(npcs.laoli,"曾经是，但再也不是了。")
        s.talk(npcs.moqi,"把做土匪说得这么好听。")
        s.talk(npcs.laoli,"呸！什么话！我老黎向来只劫贪得无厌的奸商狗官，从不与穷苦百姓为难！可不是你想象的那种土匪流寇！")
        s.talk(npcs.moqi,"……")
        s.talk(npcs.laoli,"直到那个女魔头出现，噩梦也开始了。")
        s.talk(npcs.moqi,"女魔头是什么人？")
        --△老黎摇了摇头。
        s.talk(npcs.laoli,"似乎是某个门派的高人，她出言不逊，竟要求我们为她守住雪谷出入口，一有异动就向她汇报，我老黎做了半辈子山大王，岂会乖乖听命？当场就将她骂了个狗血喷头！")
        s.talk(npcs.laoli,"谁知这女魔头脸上若无其事，私下却设计将我们兄弟几人骗来这地宫，囚禁在地牢中日夜折磨报复，久而久之，我才渐渐察觉她的秘密！")
        s.talk(npcs.moqi,"什么秘密？")
        s.talk(npcs.laoli,"她在研究如何控制这些机关怪兽！")
        s.talk(npcs.moqi,"她想……利用它们？")
        s.talk(npcs.laoli,"哼，可惜女魔头失算了——这些巨兽隔三差五地失控发疯，而我们这些倒霉鬼，就成了她的实验品。")
        s.talk(npcs.moqi,"……实验品？")
        s.talk(npcs.laoli,"不错！她捉来许多像我们这样的有武学基础之人，逼迫我们修炼一种邪功，等时机一旦成熟就被投入房中，与那些怪兽搏斗以测试其破绽，不，那根本是一边倒的屠杀……")
        s.talk(npcs.laoli,"而且这邪功颇为古怪，每次修炼时，浑身血脉都如同火烧刀割一般，许多人抵挡不住，渐渐化为伥怪。")
        s.talk(npcs.moqi,"这么说来，地宫里的伥怪都是……")
        s.talk(npcs.laoli,"这女魔头心思缜密，又命一黑衣杀手日夜盯住我们，以为这样就万无一失，但凭我老黎观察，只要我们四兄弟一拥而上，前后夹攻，并非没有逃生的机会！")
        s.talk(npcs.laoli,"趁着还未伥化，我将一份周密计划悄悄传与那三人，不料他们非但不肯配合，反而直接将这计划泄密给了女魔头，妄想换得一线生机，真是天真至极！")
        s.talk(npcs.moqi,"所以，你们失败了……")
        s.talk(npcs.laoli,"嘿！这女魔头远比我想象中毒辣，她并未直接杀了我，而是给我体内注入一种奇特的毒药作为惩罚，这毒药每十日发作一次， 犯病时状如疯魔，就如你初次见我那般。")
        s.talk(npcs.laoli,"谁知没过多久，地宫里的机关怪兽忽然集体失控，守卫被杀，大批伥怪与囚徒向外涌逃，我那时还不知晓这毒药的厉害，趁机回到村中与亲人团聚，恰逢毒性发作……")
        s.talk(npcs.moqi,"难道说，你……")
        --△老黎长长地叹了口气，不愿继续回忆下去，眼神如冰一般冷漠。
        s.talk(npcs.laoli,"女魔头一直在雪谷中四处寻找机关怪兽的秘密，显然这件事对她极为重要，倒是点醒了我。")
        s.talk(npcs.laoli,"我决定拼了老命也要找到这个秘密，然后当着她的面，将其彻底销毁！")
        s.talk(npcs.moqi,"原来如此……")
        s.talk(npcs.laoli,"这是我近日在搜索地宫时偶然发现的，想必是个值钱宝贝，可我老黎心里如今只剩下复仇，留着也没用，给你做个纪念吧。")
        s.talk(npcs.laoli,"大仇未报，我暂时还不会离开，你先走吧！")
        s.talk(npcs.moqi,"后会有期……")

        itemWidget:addItem(itemData.jiguanlingjian5,1)
        flags.dg_main_step = flags.dg_main_step + 1
        s.finishTask(400430,1)
    end
end

function talkToLaoli004()
    s.talk(npcs.laoli,"大仇未报，我暂时还不会离开，你先走吧！")
end

function talkToXuanen()
    if flags.xl_baogui_step == 1 then
        s.talk(npcs.xuanen,"我是玄恩。")
        s.talk(npcs.moqi,"可恶，你明明是伥怪！")

        local isWin = s.battle("楚家庄_漫游_黑衣人") --todo：待配置

        if isWin then
            s.talk(npcs.moqi,"获得了一个奇怪的枫叶。")
            itemWidget:addItem(itemData.fengye,1)

            flags.dg_xuanen_killed = 1
            flags.xl_baogui_step = 2
            wrappedReloadAllSceneStates()

            s.finishTask(400213, 1,2)
        else
            s.talkThink(npcs.moqi,"（看来现在还不是时候……）")
        end
        
    else
        s.talkThink(npcs.moqi,"（看来现在还不是时候……）")
    end
    
end

function eventYaofang()
    if flags.dg_main_step == 2 then
        s.talk(npcs.moqi,"雪山之巅，苍龙筑精。")
        s.talk(npcs.moqi,"竹林深处，朱雀葬心。")
        s.talk(npcs.moqi,"荒原洞中，白虎埋骨。")
        s.talk(npcs.moqi,"古墓之外，玄武泣血。")
        s.talk(npcs.moqi,"这药方提到一些方位，莫非解药就在那里？")
        
        itemWidget:addItem(itemData.yaofang,1)
        flags.dg_main_step = 3
        wrappedReloadAllSceneStates()
        s.finishTask(400420,1,2)
    else
        s.aside("这是一张鬼画符，没什么好看的。")
    end
    
end

function eventDrugTable()
    local submitItem = s.submitTempItem("请提交可使用的药方！")
    if (submitItem ~= nil) then
        if (submitItem[0].Key == itemData.yaofang.key) then

            s.aside("这是解药的药方！上面说苍龙精、白虎骨、朱雀心、玄武血可以合成解药！")
            
            local ret = 0
            local opt = {}
            local showConds = {}

            ::A::
            showConds = {}
            showConds[1] = itemWidget:getItemCount(itemData.xuanwuxue) ~= 0
            showConds[2] = itemWidget:getItemCount(itemData.baihugu) ~= 0
            showConds[3] = itemWidget:getItemCount(itemData.canglongjing) ~= 0
            showConds[4] = itemWidget:getItemCount(itemData.zhuquexin) ~= 0
            showConds[5] = true
            showConds[6] = true
            
            opt = {}
            if flags.dg_main_xuanwu ~= 2 then
                opt[1] = "加入玄武血"
            end
            if flags.dg_main_baihu ~= 2 then
                opt[2] = "加入白虎骨"
            end
            if flags.dg_main_canglong ~= 2 then
                opt[3] = "加入苍龙精"
            end
            if flags.dg_main_zhuque ~= 2 then
                opt[4] = "加入朱雀心"
            end
            if flags.dg_main_xuanwu == 2 and flags.dg_main_baihu == 2 and flags.dg_main_canglong == 2 and flags.dg_main_zhuque == 2 and flags.dg_main_jieyao == 0 then
                opt[5] = "完成解药"
            end
            opt[6] = "什么也不做"
            
            ret = s.selectTalkExWithCondition(npcs.moqi, "……", opt,showConds)
            
            if (ret == 1) then
                s.aside("你将玄武血加入炼药台中！")
                
                itemWidget:removeItem(itemData.xuanwuxue,1)
                flags.dg_main_xuanwu = 2
                goto A
            elseif (ret == 2) then
                s.aside("你将白虎骨加入炼药台中！")

                itemWidget:removeItem(itemData.baihugu,1)
                flags.dg_main_baihu = 2
                goto A
            elseif (ret == 3) then
                s.aside("你将苍龙精加入炼药台中！")

                itemWidget:removeItem(itemData.canglongjing,1)
                flags.dg_main_canglong = 2
                goto A
            elseif (ret == 4) then
                s.aside("你将朱雀心加入炼药台中！")

                itemWidget:removeItem(itemData.zhuquexin,1)
                flags.dg_main_zhuque = 2
                goto A
            elseif (ret == 5) then
                s.aside("解药合成了！")

                flags.dg_main_jieyao = 1
                itemWidget:addItem(itemData.yaofen,1)

                s.finishTask(400420,2,3)
            end
            
        else
            s.aside("这不是药方！")
        end
    else
        s.aside("你没有提交任何东西。")
    end
end

function transportEntre()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_远古地宫_入口.unity", "位置/Neath")
end