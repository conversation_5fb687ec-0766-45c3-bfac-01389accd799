local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()

end

function start()
    StartTalk()
    bcapi.add_condition_trigger("[%enemy:剧情_伥化楚青->hp_pct%][<]0.9", 1, Talk2)
    bcapi.add_condition_trigger("[%enemy:剧情_伥化楚青->hp_pct%][<]0.65", 1, Talk3)
    bcapi.add_condition_trigger("[%enemy:剧情_伥化楚青->hp_pct%][<]0.3", 1, Talk4)
    bcapi.add_condition_trigger("[%enemy:剧情_伥化楚青->hp_pct%][<=]0", 1, BattleWin)
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("莫知酉", "楚青！楚正雄伤势过重，已经回天乏术，你现在理智尚存，还能收手！")
        bcapi.talk("剧情_伥化楚青", "为何要收手？收了手我还怎么杀光他的家眷！")
        bcapi.talk("剧情_伥化楚青", "当年……当年……楚家全家出游江南，我和我娘早就埋伏在路上，他们本该在那时就死去。")
        bcapi.talk("剧情_伥化楚青", "但我当时……我当时我害怕了……因为害怕，我没能下得去手……")
        bcapi.talk("剧情_伥化楚青", "楚家那些人早就该死了！现在只有楚正雄一个，不够！不够！")
        bcapi.resume_and_show_ui()
    end)
end

function Talk2()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("剧情_伥化楚青", "我原本认同我娘说的，他们一家都是罪有应得，但到了需要我亲自动手的那刻，我居然畏缩了。")
        bcapi.talk("剧情_伥化楚青", "我恨！我恨我的柔懦寡断，我恨我的无能为力，我恨我来得太迟了！")
        bcapi.talk("剧情_伥化楚青", "我娘含恨而终，她的仇恨没有结束，我的仇恨也无法结束！")
        bcapi.resume_and_show_ui()
    end)
end

function Talk3()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("剧情_伥化楚青", "所有人，所有人都要和我一样！")
        bcapi.talk("云舞笙", "她疯了！")
        bcapi.talk("莫知酉", "伥化的过程极为痛苦，她已经不太能控制住自己了。")
        bcapi.resume_and_show_ui()
        bcapi.create_and_summon_role("剧情_楚正雄", 25, bcapi.get_team(0), bcapi.Vector2(-5, 0), 0.1)
        bcapi.add_condition_trigger("[%c->teammate:剧情_楚正雄%][<]1", 0, BattleLose)
        bcapi.wait(0.5)
        bcapi.pause_and_hide_ui()
        bcapi.talk("楚正雄", "孽畜！我不会让你离开楚家庄！")
        bcapi.talk("云舞笙", "楚庄主，你伤得那么重，再动两下就真的一命呜呼啦！")
        bcapi.talk("楚正雄", "死又如何！老夫不会让这个怪物动我妻儿分毫！")
        bcapi.show_pop_info("楚正雄被击倒时，战斗将告失败!", 2)
        bcapi.resume_and_show_ui()
    end)
end

function Talk4()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("楚正雄", "我这瀚海神功……是一对恩爱夫妇所授，他们伉俪情深，惹人羡艳……")
        bcapi.talk("楚正雄", "临到此时，我才明白，一直以来我所追寻的，我早就得到了……")
        local teammate = bcapi.get_role(0, "剧情_楚正雄")
        teammate.Stat.BattleFlagDic:set_Item("可以自爆", "1")
        teammate.Stat.BattleFlagDic:set_Item("CMD__last_setted_flag", "可以自爆")
        bcapi.resume_and_show_ui()
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
