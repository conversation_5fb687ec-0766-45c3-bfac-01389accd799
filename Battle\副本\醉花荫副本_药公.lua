local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
end

function start()
    bcapi.add_timer_trigger(0, TalkStart)
    bcapi.add_condition_trigger("[%c->enemy:副本_醉花荫_药公%][<]1", 0, BattleWin)
end

function TalkStart()
    bcapi.async_call(function()
        bcapi.create_join_role("副本_醉花荫_药公_盾卫", 65, bcapi.get_team(1), bcapi.Vector2(0.5, 0), 0.1)
        bcapi.wait(0.5)
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_醉花荫_药公_盾卫#副本_醉花荫_药公_盾卫", "某乃<color=#ABB2B9>不动如山——徐浪</color>，受药公战场救命之恩，前来助阵！")
        bcapi.add_condition_trigger("[%c->enemy:副本_醉花荫_药公_盾卫%][<]1", 0, Enemy1Lose)
        bcapi.resume_and_show_ui()

        bcapi.create_join_role_with_card("副本_醉花荫_药公_剑", 65, bcapi.get_team(1), bcapi.Vector2(4, -2), 0.1)
        bcapi.wait(0.5)
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_醉花荫_药公_剑#副本_醉花荫_药公_剑", "<color=#FF5733>千尺剑——谢凋零</color>，受药公根治旧疾之恩，请！")
        bcapi.add_condition_trigger("[%c->enemy:副本_醉花荫_药公_剑%][<]1", 0, Enemy2Lose)
        bcapi.resume_and_show_ui()

        bcapi.create_join_role_with_card("副本_醉花荫_药公_弓箭", 65, bcapi.get_team(1), bcapi.Vector2(4, 2), 0.1)
        bcapi.wait(0.5)
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_醉花荫_药公_弓箭#副本_醉花荫_药公_弓箭", "我是<color=#3498DB>啸林箭——林三响</color>，受药公救治村里之恩，助恩公出战！")
        bcapi.add_condition_trigger("[%c->enemy:副本_醉花荫_药公_弓箭%][<]1", 0, Enemy3Lose)
        bcapi.talk("副本_醉花荫_药公#副本_醉花荫_药公", "哈哈哈哈！老夫一生钻研药理，不善武斗。如若诸位小友能<color=#FF0800>胜过我三位好友，或者我</color>，这一关便算是过了！")
        bcapi.talk("副本_醉花荫_药公#副本_醉花荫_药公", "诸位小心！")
        bcapi.resume_and_show_ui()
    end) 
end

function Enemy1Lose()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        local Yaogong = bcapi.get_role(1, "副本_醉花荫_药公")
        Yaogong.Stat.BattleFlagDic:set_Item("小怪死亡人数", tostring(bcapi.get_dict_int_value(Yaogong.Stat.BattleFlagDic,"小怪死亡人数")+1))
        bcapi.talk("副本_醉花荫_药公_盾卫#副本_醉花荫_药公_盾卫", "某有愧于药公......")
        bcapi.talk("副本_醉花荫_药公#副本_醉花荫_药公", "无碍，辛苦徐统领！")
        bcapi.resume_and_show_ui()
        BattleWinIf()
    end)
end

function Enemy2Lose()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        local Yaogong = bcapi.get_role(1, "副本_醉花荫_药公")
        Yaogong.Stat.BattleFlagDic:set_Item("小怪死亡人数", tostring(bcapi.get_dict_int_value(Yaogong.Stat.BattleFlagDic,"小怪死亡人数")+1))
        bcapi.talk("副本_醉花荫_药公_剑#副本_醉花荫_药公_剑", "可惜......药公赎罪！")
        bcapi.talk("副本_醉花荫_药公#副本_醉花荫_药公", "谢小姐客气了！")
        bcapi.resume_and_show_ui()
        BattleWinIf()
    end)
end

function Enemy3Lose()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        local Yaogong = bcapi.get_role(1, "副本_醉花荫_药公")
        Yaogong.Stat.BattleFlagDic:set_Item("小怪死亡人数", tostring(bcapi.get_dict_int_value(Yaogong.Stat.BattleFlagDic,"小怪死亡人数")+1))
        bcapi.talk("副本_醉花荫_药公_弓箭#副本_醉花荫_药公_弓箭", "我竟然......也罢。")
        bcapi.talk("副本_醉花荫_药公#副本_醉花荫_药公", "哈哈哈，林小兄弟打得不错！")
        bcapi.resume_and_show_ui()
        BattleWinIf()
    end)
end

function BattleWinIf()
    bcapi.async_call(function()
        local Yaogong = bcapi.get_role(1, "副本_醉花荫_药公")
        -- 输出battleflagdic的type
        --print(type(Yaogong.Stat.BattleFlagDic))
        local EnemyLoseNum = bcapi.get_dict_int_value(Yaogong.Stat.BattleFlagDic,"小怪死亡人数")
        --print("EnemyLoseNum" .. EnemyLoseNum)
        if(EnemyLoseNum == 1)then
            bcapi.pause_and_hide_ui()
            bcapi.talk("副本_醉花荫_药公#副本_醉花荫_药公", "待我收集药气，化为己用！")
            bcapi.resume_and_show_ui()
        end
        if(EnemyLoseNum >= 3)then
            BattleWin()
        end
    end)
end

function BattleWin()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_醉花荫_药公#副本_醉花荫_药公", "哈哈哈哈，后生可畏，后生可畏呀！")
        bcapi.talk("副本_醉花荫_药公#副本_醉花荫_药公", "几位赢了！")
        bcapi.resume_and_show_ui()
        bcapi.win()
    end)
end