local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle

function init()
    CreateRoles()
    battle.ForceNotEndBattle = true
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_墨七", 64, bcapi.get_team(0), bcapi.Vector2(-5, 0), 0)
        bcapi.create_join_role("剧情_诸葛鹿", 64, bcapi.get_team(0), bcapi.Vector2(0, 0), 0)
    
        local moqi = bcapi.get_role(0, "剧情_墨七")
        moqi.Buff:AddBuff("剧情_巨能扛", 300, 1, 1, moqi)
        bcapi.change_role_hp(moqi, 50000)
    
        local zhugelu = bcapi.get_role(0, "剧情_诸葛鹿")
        zhugelu.Buff:AddBuff("剧情_持续恢复", 300, 1, 1, zhugelu)
        zhugelu.Buff:AddBuff("剧情_巨能扛", 300, 1, 1, zhugelu)
        bcapi.change_role_hp(zhugelu, 50000)
    
    end)
end

function start()
    StartTalk()
    bcapi.add_timer_trigger(30, MidTalk)
    bcapi.add_timer_trigger(60, BattleWin)
    bcapi.add_condition_trigger("[%c->teammate:剧情_墨七%][<]1", 1, BattleLose)
    bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 1, BattleWin)
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("诸葛鹿_名字问号", "这位小友，你可还有一战之力。")
        bcapi.talk("墨七", "......再坚持一会儿，我来想出制胜之法。")
        bcapi.talk("诸葛鹿_名字问号", "好！")
        bcapi.talk("", "请保护好墨七，墨七死亡将战斗失败。")
        
        bcapi.resume_and_show_ui()
    end)
end

function MidTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("墨七", "（墨七，专注一些！一定可以想到解围之法！）")
        bcapi.talk("", "请再坚持30秒。")

        bcapi.resume_and_show_ui()
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
