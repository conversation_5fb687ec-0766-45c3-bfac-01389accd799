
---@type 漫游_雪谷_风雪神道_山路
local context = require("MapStories/漫游_雪谷/scene_漫游_雪谷_风雪神道_山路_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

---组件相关
local extensions = require("MapStories/漫游_雪谷/extension")
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type 雪谷_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 雪谷_ItemData]]


s.loadFlags(context.flags)
---@type flags_漫游_雪谷
local flags = context.flags

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.readyToStart()
    reloadAllSceneStates()
end

function reloadAllSceneStates()
    --山路积雪
    s.setActive(objs.snow,flags.sd_clean_snow == 0)
    
    --倒塌的石碑
    s.setActive(objs.steledown,flags.sd_clean_stele == 0)
    s.setActive(objs.stele,flags.sd_clean_stele == 1)

    --明慧
    s.setActive(npcs.minghui,flags.sd_clean_step ~= 4 and flags.sd_xqg_step == 2)
end

function eventCleansnow()
    if flags.sd_clean_step == 0 then
        s.aside("这里有一堆积雪阻住了去路。")
    else
        s.aside("这里有一堆积雪阻住了去路。")
        s.selectTalk(npcs.moqi, "……", {"铲除积雪"})
        
        flags.sd_clean_snow = 1
        flags.sd_clean_step = flags.sd_clean_step + 1
        s.blackScreen()
        reloadAllSceneStates()
        s.lightScreen()
    end
end

function eventCleanstele()
    if flags.sd_clean_step == 0 then
        s.aside("这里的石碑倒塌了。")
    else
        s.aside("这里的石碑倒塌了。")
        s.selectTalk(npcs.moqi, "……", {"扶起石碑"})

        flags.sd_clean_stele = 1
        flags.sd_clean_step = flags.sd_clean_step + 1
        s.blackScreen()
        reloadAllSceneStates()
        s.lightScreen()
    end
end

function eventStele()
    s.aside("这里的石碑还挺正常")
end

function talkToMinghui()
    if flags.sd_clean_step == 0 then
        talkToMinghui001()
        flags.sd_clean_step = 1
    elseif flags.sd_clean_step == 1 then
        talkToMinghui002()
    elseif flags.sd_clean_step == 3 then
        talkToMinghui003()
    end
end

function talkToMinghui001()
    s.talk(npcs.minghui,"这才没多久，山道竟已荒芜落魄成这副田地……")
    s.talk(npcs.moqi,"你以前来过这里？")
    s.talk(npcs.minghui,"嗯，许久以前这里还很热闹，有许多入山祭拜的信徒往来穿梭，放眼望去山道也整洁光鲜。")
    s.talk(npcs.moqi,"祭拜？上面有什么神明？")
    s.talk(npcs.minghui,"许多年前，有人从这雪山中发掘出一尊无名神像，据说这神像十分灵验，几乎有求必应，故而吸引了大量远道而来的信徒。")
    s.talk(npcs.minghui,"可惜……这些年山中频发雪崩，加之玄阙宫又命弟子守住山路不许外人随意出入，所以渐渐变成了这副模样。")
    s.talk(npcs.minghui,"在你到来之前，贫僧已经将这山道大略打理了一番，谁知这会儿脚伤再次发作……")
    s.talk(npcs.minghui,"“行百步者半九十”，小施主若是有空，可否替贫僧将这山道彻底清扫一番？")
    
    s.addTask(400312,1)
end

function talkToMinghui002()--todo:待补充
    s.talk(npcs.minghui,"“行百步者半九十”，小施主若是有空，可否替贫僧将这山道彻底清扫一番？")
end

function talkToMinghui003()
    s.talk(npcs.minghui,"善哉！经过小施主一番辛勤打理，山道看起来已和刚才大不相同，所谓“身是菩提树，心如明镜台，时时勤拂拭，勿使惹尘埃”，正是此意。")
    s.talk(npcs.moqi,"这些玄阙宫弟子自己都不将这山道当做一回事，你不过是个外人，为何却如此爱护？")
    --△明慧闻言，忽而出神地盯着你，仿佛想起了什么往事。明慧闻言，微微摇头叹气，仿佛想起了什么往事。
    s.talk(npcs.moqi,"阁下为何不语？")
    s.talk(npcs.minghui,"我看到小施主，似乎想起那位故人当年的模样。")
    s.talk(npcs.moqi,"哦？")
    s.talk(npcs.minghui,"那位故人与小施主一般身手了得，当年我性命垂危多亏他解围，如今故地重游，难免会有亲切之感。")
    s.talk(npcs.moqi,"那位故人究竟是谁？")
    s.talk(npcs.minghui,"他……")
    s.talk(npcs.minghui,"前方不远就是雪崖，小施主若对这故事感兴趣，你我山顶相见，再说不迟。")
    
    flags.sd_clean_step = flags.sd_clean_step + 1
    flags.sd_main_step = flags.sd_main_step + 1
    s.finishTask(400312,1)
    s.addTask(400320,1)
    s.blackScreen()
    reloadAllSceneStates()
    s.lightScreen()
end

function talkToMaohuan()
    s.talk(npcs.maohuan,"我是伥化的毛欢。")

end

function eventBell()
    if flags.dg_main_step >= 4 and flags.dg_main_mao == 0 then
        s.aside("神道小路旁，一口古老的钟。")

        local ret = s.selectTalk(npcs.moqi,"……",{"敲击","离开"})
        if ret == 0 then
            s.aside("沉闷的钟声响起。")

            ret = s.selectTalk(npcs.moqi,"……",{"再次敲击","离开"})
            if ret == 0 then
                s.aside("钟声响亮，附近的草丛中传来窸窣响动！")

                ret = s.selectTalk(npcs.moqi,"……",{"用力敲击","离开"})
                s.aside("洪亮的钟声在山道飘扬，草丛里突然有一物向你扑来！")
                s.setActive(npcs.maohuan,true)
                s.talk(npcs.moqi,"吓！")

                local isWin = s.battle("楚家庄_漫游_黑衣人") --todo:待配置
                if isWin then
                    s.setActive(npcs.maohuan,false)
                    s.aside("墨七从地上捡起一本书翻看。")
                    s.talk(npcs.moqi,"刚才打斗时，此物由这伥怪身上掉落——")
                    s.talk(npcs.moqi,"《疯猫爪功》？莫非此人就是毛欢？")

                    itemWidget:addItem(itemData.fengmao,1)

                    flags.dg_main_mao = 1
                    flags.dg_main_step = flags.dg_main_step + 1
                else
                    s.blackScreen()
                    s.talk(npcs.moqi,"（看来现在还不是时候……）")
                    s.lightScreen()
                end
            end
        end
    else
        s.aside("神道小路旁，一口古老的钟。")

        local ret = s.selectTalk(npcs.moqi,"……",{"敲击","离开"})
        if ret == 0 then
            s.aside("沉闷的钟声响起。")
        end
    end
    
end

function transportShanjiao()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_风雪神道_山脚.unity", "位置/Shanlu")
end

function transportCliff()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_风雪神道_悬崖.unity", "位置/Shanlu")
end
