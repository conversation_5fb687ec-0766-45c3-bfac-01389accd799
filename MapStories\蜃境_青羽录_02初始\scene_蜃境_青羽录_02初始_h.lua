--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_青羽录_02初始/flags_蜃境_青羽录_02初始")


---@class 角色_蜃境_青羽录_02初始
local npcs = {
    zhujue = "角色/zhujue",
    jingcheng = "角色/jingcheng",
    yunwusheng = "角色/yunwusheng",
    qyl_02 = "角色/qyl_02",
    qyl_03 = "角色/qyl_03",
    qyl_04 = "角色/qyl_04",
    qyl2_battle_101 = "角色/qyl2_battle_101",
    qyl_05 = "角色/qyl_05",
    qyl_06 = "角色/qyl_06",
    qyl_07 = "角色/qyl_07",
    zhuomicang_01 = "角色/zhuomicang_01",
    zhuomicang_02 = "角色/zhuomicang_02",
    zhuomicang_03 = "角色/zhuomicang_03",
    mozhiyou = "角色/mozhiyou",
    mozhiyou_show = "角色/mozhiyou_show",
    enemy_01 = "角色/enemy_01",
    enemy_02 = "角色/enemy_02",
    elite_03 = "角色/elite_03",
    chuqing_show = "角色/chuqing_show",
}

---@class 物体_蜃境_青羽录_02初始
local objs = {
    talk = "物体/talk",
    exit = "物体/exit",
    switcher1 = "物体/switcher1",
    switcher2 = "物体/switcher2",
    qyl_07_guide = "物体/qyl_07_guide",
    chest_1 = "物体/chest_1",
    --- "敌人1吓唬玩家触发器"
    enemy_02_area = "物体/enemy_02_area",
    --- "精英怪触发区域"
    elite_03_area = "物体/elite_03_area",
}

---@class 相机_蜃境_青羽录_02初始
local cameras = {
    camera1 = "相机/camera1",
    camera2 = "相机/camera2",
    hard_camera1 = "相机/hard_camera1",
    camera3 = "相机/camera3",
    camera4 = "相机/camera4",
    --- "宝箱房相机"
    camera5 = "相机/camera5",
    cam_chuqing_show = "相机/cam_chuqing_show",
    cam_chuqing_near = "相机/cam_chuqing_near",
    cam_chuqing_far = "相机/cam_chuqing_far",
    cam_main = "相机/cam_main",
    --- "神射手相机"
    cam_shensheshou = "相机/cam_shensheshou",
}

---@class 位置_蜃境_青羽录_02初始
local pos = {
    start_2 = "位置/start_2",
    start = "位置/start",
    jingcheng_2 = "位置/jingcheng_2",
    jingcheng_start = "位置/jingcheng_start",
    yunwusheng_2 = "位置/yunwusheng_2",
    yunwusheng_start = "位置/yunwusheng_start",
    zhujue_pos_2 = "位置/zhujue_pos_2",
    jingcheng_pos_2 = "位置/jingcheng_pos_2",
    yunwusheng_pos_2 = "位置/yunwusheng_pos_2",
    mozhiyou_pos1 = "位置/mozhiyou_pos1",
    mozhiyou_pos2 = "位置/mozhiyou_pos2",
    zhujue_pos3 = "位置/zhujue_pos3",
    jingcheng_pos3 = "位置/jingcheng_pos3",
    yunwusheng_pos3 = "位置/yunwusheng_pos3",
    mozhiyou_pos3 = "位置/mozhiyou_pos3",
    mozhiyou_pos4 = "位置/mozhiyou_pos4",
    --- "敌人1吓唬玩家点"
    enemy_02_pos = "位置/enemy_02_pos",
}

---@class 资产_蜃境_青羽录_02初始
local assets = {
    shensheshou = "资产/shensheshou",
}

---@class 动作_蜃境_青羽录_02初始
local animationClips = {
}

---@class 蜃境_青羽录_02初始
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
