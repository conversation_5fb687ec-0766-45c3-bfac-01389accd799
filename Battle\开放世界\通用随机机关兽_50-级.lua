local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.async_call(function()
        local level = 45
        local enemyTb = {"通用_机关兽_小怪_机关鹰_鹰攻击","通用_机关兽_小怪_浮游机_机关枪","通用_机关兽_小怪_防卫机_防卫机攻击"}

        
        --随机召唤其中6个角色，等级为level
        local index = math.random(1, #enemyTb)
        local hpRate = 1 + 0.001*level*math.random(15, 25)
        local opRate = 1 + 0.001*level*math.random(15, 25)
        local odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(4, 2), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(4, -4), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(6, 2), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(6, -4), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(4, 0), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(6, 0), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")
    end)
end

function start()
    
end
