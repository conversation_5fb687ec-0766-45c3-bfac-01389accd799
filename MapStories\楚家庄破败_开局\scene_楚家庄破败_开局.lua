---@type 楚家庄破败_开局
local context = require("MapStories/楚家庄破败_开局/scene_楚家庄破败_开局_h")
local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
local assets = context.assets
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger
---@type NewbieApi
local newbieApi = require("NewbieApi") -- 不允许改名

s.loadFlags(context.flags)
---@type flags_楚家庄破败_开局
local flags = context.flags


local nani = "序章/0.11序章"

local function playNani(tag,transitionTime,alpha)
    return s.runAvgTag(nani, tag,transitionTime,alpha)
end



--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

function testNani()
    playNani("楚家庄闪回")
end

function door_music()
    s.setActive(objs.obj_door_music,false)
    s.playMusic("ruin",2,nil,2,true)
    s.playSound("Chapter_Xinshou","cjz_amb_wind")
end

--每次载入调用
function start()
    --关闭交互
    s.setActive(objs.obj_blood,false)
    s.setActive(objs.obj_claw,false)
    s.setActive(objs.obj_evilAir,false)

    --激活照壁邪气
    s.setActive(objs.obj_evilAir,true)
    s.addGameMapGuide(objs.obj_evilAir)

    --关闭按钮
    s.setHudMenuButton(false)
    --设置楚家庄变量
    s.setRpgMapFlag("brokenCjz_status_flag",1)

    -- 播放音乐
    s.playMusic("凌雪悲伤1",2,nil,2)

    --设置主角位置
    s.setActive(npcs.zhujue,true)
    s.setPos(npcs.zhujue,pos.pos_start)

    --设置药曲老位置
    s.setPos(npcs.yaoqulao,pos.pos_yaoqulao_start)
    s.setActive(npcs.yaoqulao,true)
    

    --设置无面剑客位置
    s.setPos(npcs.swordsman,pos.pos_swordsman_start)
    --但是此时不激活无面剑客
    s.setActive(npcs.swordsman,false)
---------------------------------------------------剧情准备-----------------------------------------------------------------------

    --第一个巡游镜头
    s.camera(cameras.camera_manyou_01, true,true,blendHintEnum.Cut) --开始镜头
    s.readyToStart(true,0)
    s.cameraAsync(cameras.camera_manyou_02, true,true,blendHintEnum.Custom,15) 
    s.wait(3)

    s.blackScreen(0.5)
    --s.animState(npcs.zhujue,"BThink_Loop",true)

    --进入剧情镜头
    s.camera(cameras.cam_yaoqulao_talk_start, true,true,blendHintEnum.Cut)
    s.lightScreen(1)

    s.cameraAsync(cameras.cam_start_talk, true,true,blendHintEnum.Custom,3)

    local offset1 = CS.UnityEngine.Vector3(0,1.75,0)
    local offset2 = CS.UnityEngine.Vector3(0,1.5,0)
    s.move(npcs.yaoqulao,pos.pos_yaoqulao_end,1.5,true)
    --s.animState(npcs.zhujue,"BThink_Loop",false)

    s.lookAt(npcs.yaoqulao,offset1,npcs.zhujue,true)
    s.lookAt(npcs.zhujue,offset2,npcs.yaoqulao,true)
    s.runAvgTag("序章/0.11序章","开场_主角_1",nil,0)

   --s.animState(npcs.yaoqulao,roleAnims.Special_3,true)
    s.camera(cameras.cam_yaoqulao_talk2, true,true,blendHintEnum.Cut)
    s.runAvgTag("序章/0.11序章","开场_药曲老_1",nil,0)


    s.talk(npcs.zhujue, "什么完了？")

    -- s.runAvgTag("序章/0.11序章","开场_药曲老_2",nil,0)

    -- s.wait(0.5)
    s.runAvgTag("序章/0.11序章","开场_药曲老_3",nil,0)

    --s.camera(cameras.cam_yaoqulao_talk2, true,true,blendHintEnum.Cut)
    --s.animState(npcs.yaoqulao,roleAnims.BDeny,true)
    s.runAvgTag("序章/0.11序章","开场_药曲老_4",nil,0)


    s.cameraAsync(cameras.cam_start_3,true,true,blendHintEnum.Cut)
    s.talk(npcs.zhujue, "别担心，你在此处等着，我先过去看看。")

    s.cameraAsync(cameras.Main_camera,true,true,blendHintEnum.Custom,2)

    newbieApi.eventTrack("newbieStep", "step", "first_into_chujiazhuang")

    if flags.show_choose_cameraPanel == 0 then
    --显示选择相机模式面板
        s.showChooseCameraPanel()
        flags.show_choose_cameraPanel = 1
    end

    tutor3DMove()
--------------------------------------------------------------------------------------------------------------------------
    --引导显示
    --newbieApi.showHintDescPanel("跑图", "「探索地图」", "操控角色在地图中行走和交互。\n通过虚拟摇杆操作进行地图探索，亦可滑动屏幕进行旋转。", "知道了")
end

function tutor3DMove()
    newbieApi.showHintDescPanel("guide_3d_move", "箱庭关卡移动",
    "拖动屏幕右侧进行视角旋转，操作屏幕左侧虚拟摇杆</color>操作角色移动。","继续")
end

function door()
    --埋点
    newbieApi.eventTrack("player_start_story", "id", 72)

    --关闭门触发器和碰撞
    s.setActive(objs.trigger_door,false)
    s.setActive(objs.collide_door,false)
    s.removeGameMapGuide(objs.obj_evilAir)

    --设置主角位置
    s.cameraAsync(cameras.camera_door,true,true,blendHintEnum.Cut)
    s.setPos(npcs.zhujue,pos.Pos_moveToDoor_start)
    s.wait(0.1)
    s.moveAsync(npcs.zhujue,pos.Pos_moveToDoor_end,4,true,nil,0,true,false,false,0)

    --门口两人结束动作转向
    s.animState(npcs.qingnian,roleAnims.BThink_Loop,false)
    s.wait(1)
    s.turnToAsync(npcs.laoren, npcs.zhujue)
    s.turnTo(npcs.qingnian, npcs.zhujue)
    local offset = CS.UnityEngine.Vector3(0,1.75,0)
    s.lookAt(npcs.laoren, offset, npcs.zhujue, true)
    s.turnTo(npcs.qingnian, npcs.zhujue)
    --开启对话
    s.runAvgTag("序章/0.11序章","大门口对话_平民老人_1",nil,0)

    --s.runAvgTag("序章/0.11序章","大门口对话_主角_1",nil,0)

    s.animState(npcs.qingnian,roleAnims.BGesture,true)
    s.runAvgTag("序章/0.11序章","大门口对话_青年_1",nil,0)
    s.animState(npcs.qingnian,roleAnims.BGesture,false)

    s.animState(npcs.laoren,roleAnims.BDeny,true)
    s.runAvgTag("序章/0.11序章","大门口对话_平民老人_2",nil,0)
    s.animState(npcs.laoren,roleAnims.BDeny,false)

    s.animState(npcs.qingnian,roleAnims.BGesture,true)
    s.runAvgTag("序章/0.11序章","大门口对话_青年_2",nil,0)
    s.animState(npcs.qingnian,roleAnims.BGesture,false)
    s.wait(1)
    --继续前进
    s.moveAsync(npcs.zhujue,pos.Pos_moveon,1,true,nil,0,true,false,false,0)
    s.animState(npcs.laoren,roleAnims.BDeny,true)
    s.runAvgTag("序章/0.11序章","大门口对话_平民老人_3",nil,0)
    s.animState(npcs.laoren,roleAnims.BDeny,false)

    
    s.animState(npcs.qingnian,roleAnims.BDeny,true)
    s.runAvgTag("序章/0.11序章","大门口对话_青年_3",nil,0)
    s.animState(npcs.qingnian,roleAnims.BDeny,false)

    s.wait(0.8)
 
    --两人离开镜头
    s.moveAsync(npcs.laoren,pos.Pos_laoren_leave,2,true)
    s.moveAsync(npcs.qingnian,pos.Pos_qingnian_leave,2,true)



    s.camera(cameras.Main_camera,true,true,blendHintEnum.Custom,2)

    s.setActive(npcs.qingnian, false)
    s.setActive(npcs.laoren, false)

    --主角进入
    s.runAvgTag("序章/0.11序章","大门口对话_主角_2",nil,0)

--------------------------------------------------------------------------------------------------------------------------
    --添加交互引导
    s.addGameMapGuide(objs.obj_evilAir)

    --药曲老对话改变
    flags.yaoqulao_state = 1
end

function evilAir()
    --关闭碰撞
    s.setActive(objs.trigger_evilAir,false)

    local result = playNani("进入闪回",nil,0)
    if result == 0 then
        --临时处理，需要在nani加黑屏
        s.setPos(npcs.zhujue,pos.pos_evilAirBack)
        s.setActive(objs.trigger_evilAir,true)

        s.lightScreen()
    elseif result == 1 then
        evilAir_claw()
    end
end

function evilAir_claw()
    newbieApi.eventTrack("player_start_story", "id", 100) -- 邪气爪子

    --停止音乐
    s.playMusic("凌雪悲伤1",2,0,2)

    --思考动作
    s.animState(npcs.zhujue,roleAnims.BThink_Loop,true)

    --关闭迷雾
    --playNani("楚家庄闪回")

    --关闭邪气和主角
    s.setActive(objs.obj_evilAir,false)
    s.removeGameMapGuide(objs.obj_evilAir)

    if s.isMale() then
        s.playTimeline(assets.memory_timeline,false)
    else
        s.playTimeline(assets.memory_timeline_woman,false)
    end

    --设置主角位置
    s.setActive(npcs.zhujue,true)
    s.setPos(npcs.zhujue,pos.pos_memory)
    s.camera(cameras.cam_midddle,true,true,blendHintEnum.Cut)





    s.wait(1)
    s.playSound("sfx","风铃")
    s.wait(2)
    s.cameraAsync(cameras.cam_midddle_goOn,true,true,blendHintEnum.Custom,5)
    s.lightScreen(2)
    s.wait(3)

    s.camera(cameras.cam_memory,true,true,blendHintEnum.Cut)

    --激活无面剑客
    s.setActive(npcs.swordsman,true)
    s.animState(npcs.swordsman,roleAnims.Special_Loop_1,true)
    s.lookAt(npcs.swordsman,nil,npcs.zhujue,true)

    s.lightScreen(0.5,8)

    s.talk(npcs.zhujue, "我方才脑海里闪过的那些……是楚家庄过去的样子？")
    s.animState(npcs.zhujue,roleAnims.BThink_Loop,false)

    --播放音乐
    s.playMusic("遗忘之地",2,nil,2)

    s.camera(cameras.cam_swordsman_start,true,true,blendHintEnum.Cut)

    --埋点
    newbieApi.eventTrack("player_start_story", "id", 101)

    s.runAvgTag("序章/0.11序章","无面剑客对话_无面剑客_1",nil,0)
    s.turnToAsync(npcs.zhujue, npcs.swordsman,true)
    s.lookAt(npcs.zhujue,nil,npcs.swordsman)

    s.camera(cameras.cam_thinkSlip_start,true,true,blendHintEnum.Cut)

    s.runAvgTag("序章/0.11序章","无面剑客对话_无面剑客_2",nil ,0)

    --s.cameraAsync(cameras.cam_thinkSlip_end,true,true,blendHintEnum.Custom,10)
    s.runAvgTag("序章/0.11序章","无面剑客对话_无面剑客_3",nil ,0)

    --s.camera(cameras.cam_swordsmanSlip_start,true,true,blendHintEnum.Cut)
    --s.cameraAsync(cameras.cam_swordsmanSlip_end,true,true,blendHintEnum.Custom,6)
    s.runAvgTag("序章/0.11序章","无面剑客对话_无面剑客_4",nil ,0)

    s.setActive(npcs.swordsman,false)
    s.playTimeline(assets.timeline_swordManJump,false)
    s.blackScreen(0)
    s.setPos(npcs.swordsman,pos.pos_swordsman_fight) 

    swordsman_battle()
end

--归天人战斗
function swordsman_battle()
    newbieApi.eventTrack("player_start_story", "id", 110) -- 归天人战斗
    newbieApi.eventTrack("newbieStep", "step", "battle_wumianjianke_in")
    local isWin = s.battle("开放世界_破败楚家庄_初始战斗")
    if not isWin then
        s.setActive(npcs.swordsman,true)
        newbieApi.eventTrack("player_start_story", "id", 130,"key","lose") -- 归天人战斗失败
        newbieApi.eventTrack("newbieStep", "step", "battle_wumianjianke_out", "result", "lose") --失败
        --激活交互
        s.animState(npcs.swordsman, roleAnims.BAkimbo_Loop,true)
        s.talk(npcs.swordsman, "天外因果么……不过尔尔！")

        s.addGameMapGuide(npcs.swordsman)
        s.camera(cameras.Main_camera,true,true,blendHintEnum.Cut)
    else
        newbieApi.eventTrack("player_start_story", "id", 130,"key","win") -- 归天人战斗胜利
        newbieApi.eventTrack("newbieStep", "step", "battle_wumianjianke_out", "result", "win") --失败
        s.playMusic("凌雪悲伤2",2,nil,2)

        s.setPos(npcs.swordsmanWithSword,pos.pos_swordsman_fight)
        s.setActive(npcs.swordsman,false)
        --s.soloAnimState(npcs.swordsman, roleAnims.BHurt_Loop,true)
        s.soloAnimState(npcs.swordsmanWithSword, roleAnims.Special_Loop_2,true)
        --s.wait(1.5)

        s.removeGameMapGuide(npcs.swordsman)
        s.lookAt(npcs.yaoqulao,nil,npcs.swordsmanWithSword,true)
        s.setPos(npcs.yaoqulao,pos.pos_laoqulao_come_start)
        s.setPos(npcs.zhujue,pos.pos_memory_fight)
        yaoQuLao_to_fightEnd()
    end
end

function yaoQuLao_to_fightEnd()
    s.camera(cameras.cam_swordmanDead,true,true,blendHintEnum.Cut)
    s.wait(1.5)

    s.camera(cameras.cam_yaoqulao_run,true,true,blendHintEnum.Cut)
    s.move(npcs.yaoqulao,pos.pos_laoqulao_come_end,5,true)
    s.runAvgTag("序章/0.11序章","战斗结束_药曲老_1",nil ,0)

    s.camera(cameras.cam_swordsman_fight,true,true,blendHintEnum.Cut)
    s.runAvgTag("序章/0.11序章","战斗结束_无面剑客_1",nil ,0)

    s.camera(cameras.cam_fightEnd_yaoqulao,true,true,blendHintEnum.Cut)
    s.runAvgTag("序章/0.11序章","战斗结束_药曲老_2",nil ,0)

    s.camera(cameras.cam_swordman_dead,true,true,blendHintEnum.Cut)
    s.runAvgTag("序章/0.11序章","战斗结束_无面剑客_2",nil ,0)

    s.setActive(npcs.swordsmanWithSword,false)
    s.playTimeline(assets.timeline_swordManDie,false)
    s.setActive(npcs.swordsmanWithSword,true)
    s.setActive(objs.obj_blood,true)
    s.soloAnimState(npcs.swordsmanWithSword,roleAnims.Special_Loop_3,true)
    

    s.camera(cameras.cam_fightEnd,true,true,blendHintEnum.Cut)
    s.lightScreen()

    require("RpgMap/任务线/初入现世").crxs_leaveChuJiaZhuang()

    theEnd()
end

function theEnd()
    s.runAvgTag("序章/0.11序章","尾声_药曲老_1",nil,0)

    -- s.camera(cameras.cam_fightEnd_yaoqulao,true,true,blendHintEnum.Cut)
    --s.camera(cameras.cam_fightEnd_slipStart,true,true,blendHintEnum.Cut)
    --s.cameraAsync(cameras.cam_fightEnd_slipEnd,true,true,blendHintEnum.Custom,10)
    -- s.runAvgTag("序章/0.11序章","尾声_药曲老_3",nil,0)
    -- s.runAvgTag("序章/0.11序章","尾声_药曲老_4",nil,0)
    --s.runAvgTag("序章/0.11序章","尾声_药曲老_3",nil,0)

    s.setNewbieFlags("first_enter_rpgMap",2)
    s.setNewbieFlags("start_openworld",2)
    s.setRpgMapFlag("brokenCjz_status_flag",2)
    s.setRpgMapFlag("qingYuLu_rpgMapEnterance_unlock",1)
    
    --newbieApi.eventTrack("player_start_story", "id", 140)
    newbieApi.enterRpgMap("201011010100101")
    --s.goToMainSceneDirectly();  
end

function talkYaoqulao()
    --埋点
    newbieApi.eventTrack("player_start_story", "id", 71)

    if flags.yaoqulao_state == 0 then
        s.talk(npcs.yaoqulao, "前面有人，去问问他们吧，老朽先在这儿等你。")
    elseif flags.yaoqulao_state == 1 then
        s.talk(npcs.yaoqulao, "这楚家庄阴森可怖，老朽经不得吓，你先去瞧瞧，我随后就到。")
    end
end

--调查------------------------------------------------------------------------------------------------------------------------
function shuqinxuan()
    playNani("书琴轩",nil,0)
end

function tingzi()
    playNani("小亭",nil,0)
end

function bieyuan()
    playNani("别院",nil,0)
end

function zhaobi()
    playNani("照壁",nil,0)
end

function kefang()
    playNani("客房",nil,0)
end

function skip()
    s.setNewbieFlags("first_enter_rpgMap",2)
    s.setNewbieFlags("start_openworld",2)
    s.setRpgMapFlag("brokenCjz_status_flag",2)
    s.setRpgMapFlag("qingYuLu_rpgMapEnterance_unlock",1)

    require("RpgMap/任务线/初入现世").crxs_leaveChuJiaZhuang()

    s.goToMainSceneDirectly()
end