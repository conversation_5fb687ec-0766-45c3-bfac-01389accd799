--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_裂雾飞鹰_04玄阙地宫/flags_蜃境_裂雾飞鹰_04玄阙地宫")


---@class 角色_蜃境_裂雾飞鹰_04玄阙地宫
local npcs = {
    zhujue = "角色/zhujue",
    lwfy_08 = "角色/lwfy_08",
    lwfy_09 = "角色/lwfy_09",
    lwfy_10 = "角色/lwfy_10",
    lwfy_11 = "角色/lwfy_11",
    lwfy_12 = "角色/lwfy_12",
    lwfy_13 = "角色/lwfy_13",
    lwfy_14 = "角色/lwfy_14",
    enemy_1 = "角色/enemy_1",
    enemy_2 = "角色/enemy_2",
    enemy_3 = "角色/enemy_3",
    enemy_4 = "角色/enemy_4",
    enemy_5 = "角色/enemy_5",
    enemy_6 = "角色/enemy_6",
    elite_7 = "角色/elite_7",
    elite_8 = "角色/elite_8",
    boss_9 = "角色/boss_9",
    yuchi = "角色/yuchi",
    lwfy_10_for_show = "角色/lwfy_10_for_show",
    yuchi_e = "角色/yuchi_e",
    moqi_e = "角色/moqi_e",
    lwfy_14_rock = "角色/lwfy_14_rock",
}

---@class 物体_蜃境_裂雾飞鹰_04玄阙地宫
local objs = {
    exit = "物体/exit",
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
    chest_3 = "物体/chest_3",
    door1 = "物体/door1",
    door2 = "物体/door2",
    door1Pos = "物体/door1Pos",
    startTrigger = "物体/startTrigger",
    chest_3_trigger = "物体/chest_3_trigger",
    startTrigger2 = "物体/startTrigger2",
    chest_3_Light = "物体/chest_3_Light",
    experiment = "物体/experiment",
    lwfy_1004 = "物体/lwfy_1004",
}

---@class 相机_蜃境_裂雾飞鹰_04玄阙地宫
local cameras = {
    cam1 = "相机/cam1",
    cameraPlay1 = "相机/cameraPlay1",
    cameraPlay2 = "相机/cameraPlay2",
    question_1 = "相机/question_1",
    question_2 = "相机/question_2",
}

---@class 位置_蜃境_裂雾飞鹰_04玄阙地宫
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
    pos_1 = "位置/pos_1",
    pos_2 = "位置/pos_2",
    chest_3_pos = "位置/chest_3_pos",
    play_pos_1 = "位置/play_pos_1",
    play_pos_2 = "位置/play_pos_2",
    question_1 = "位置/question_1",
    question_2 = "位置/question_2",
    question_3 = "位置/question_3",
}

---@class 资产_蜃境_裂雾飞鹰_04玄阙地宫
local assets = {
    timeline1 = "资产/timeline1",
    timeline2 = "资产/timeline2",
}

---@class 动作_蜃境_裂雾飞鹰_04玄阙地宫
local animationClips = {
}

---@class 蜃境_裂雾飞鹰_04玄阙地宫
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
