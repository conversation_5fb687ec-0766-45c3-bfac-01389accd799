
---@type 挑战_风雪神道山洞
local context = require("MapStories/挑战_风雪神道/scene_挑战_风雪神道山洞_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_挑战_风雪神道
local flags = context.flags

-- local extensions = require 'MapStories/挑战_风雪神道/extension'
-- ---@type ItemWidget
-- local itemWidget = extensions.widgets.ItemWidgetInfo.widget
-- ---@type 挑战_风雪神道_ItemData
-- local itemData = extensions.widgets.ItemWidgetInfo.items

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    local isTeleport = false
    --如果是传送，就不能setPos
    if(args ~= nil and args:ContainsKey("IsTransport"))then
        isTeleport = true
    end

    --如果不是传送，则设置主角位置
    if(flags.firstEnterMap3 == 0)then
        flags.firstEnterMap3 = 1
    end
    if(not isTeleport)then
        s.setPos(npcs.zhujue,pos.start)
    end

    --重复演出
    s.playMusic("凌云惊雪")
    s.setPos(npcs.boss3,pos.boss_pos2)

    refreshDungeon()

    s.readyToStart()
end

--刷新副本信息
function refreshDungeon()
    -- s.setMapStatus("补给数量", flags.trash)
    -- s.setMapStatus("资源数量", flags.resource)
    -- itemWidget:setItem(itemData.TrashDone, flags.trash)
    -- itemWidget:setItem(itemData.ResourceDone, flags.resource)

    --重新载入场景物体，根据flag去处理每个gameobject是否是active
    -- for k,v in pairs(objs) do
    --     --print(k)
    --     --print(v)
    --     --处理垃圾点
    --     if(string.find(k,"trash") ~= nil)then
    --         s.setActive(v,flags[k] == 0)
    --     end
    -- end

    --本次副本内是否击败了Boss3
    if(flags.boss3Defeated == 1) then
        s.setActive(npcs.boss3,false)
    end

    --是否播放过Boss3战前演出
    if(s.getChallengeMapPersistentFlag("挑战_风雪神道","播放过Boss3战前演出") == 0 or s.getChallengeMapPersistentFlag("挑战_风雪神道","击败过Boss3") == 0)then
        s.setActive(npcs.boss1,true)
        s.setActive(npcs.boss2,true)
    end
end

--Boss3战斗
function boss3Trigger()
    --是否播放过Boss3战前演出
    if(s.getChallengeMapPersistentFlag("挑战_风雪神道","播放过Boss3战前演出") == 0)then
        --没有就播放一次性演出
        s.moveAsync(npcs.zhujue,pos.boss_pos1,1.5,true)
        s.camera(cameras.camera_boss1,true,true,blendHintEnum.EaseInOut,1,true,true)
        s.wait(1)
        s.turnTo(npcs.zhujue,npcs.boss3,true)
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss3战前对话_1", nil, 0)
        s.turnTo(npcs.boss3,npcs.zhujue,true)
        s.turnTo(npcs.boss1,npcs.zhujue,true)
        s.turnTo(npcs.boss2,npcs.zhujue,true)
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss3战前对话_2", nil, 0)
        s.camera(cameras.camera_boss1,false)
        s.setChallengeMapPersistentFlag("挑战_风雪神道","播放过Boss3战前演出",1)
    end

    --重复演出
    s.setActive(npcs.boss3,false)
    s.playTimeline(assets.Timeline_1)
    s.setActive(npcs.boss3,true)

    --local battleTb = {}
    -- battleTb["StructureFire"] = flags.StructureFire
    -- battleTb["StructureFood"] = flags.StructureFood
    -- battleTb["StructureAlcohol"] = flags.StructureAlcohol
    -- battleTb["StructureClothes"] = flags.StructureClothes
    -- battleTb["StructureKnifeStone"] = flags.StructureKnifeStone
    -- battleTb["StructureMap"] = flags.StructureMap
    -- battleTb["StructurePet"] = flags.StructurePet
    -- battleTb["StructureEnhance"] = flags.StructureEnhance

    --触发战斗
    local isWin,bResult = s.challengeMapBattle("103")
    if(isWin)then
        flags.boss3Defeated = 1

        --是否首次击败过Boss3
        if(s.getChallengeMapPersistentFlag("挑战_风雪神道","击败过Boss3") == 0)then
            s.camera(cameras.camera_boss2,true)
            s.setPos(npcs.zhujue,pos.boss_pos1)
            s.setPos(npcs.boss3,pos.boss_pos3)

            s.animState(npcs.boss3, "BHurt_Loop",true)
            s.animState(npcs.boss1, "BShock_Loop",true,false,0.5,false,false,1.5,true)
            s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss3战后对话_1", nil, 0)
            s.animState(npcs.boss1, "BShock_Loop",false,false,0.5,false,false,1.5,true)
            s.turnTo(npcs.boss1,npcs.boss2,true)
            s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss3战后对话_2", nil, 0)
            s.turnTo(npcs.boss1,npcs.boss3,true)
            s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss3战后对话_3", nil, 0)
            s.animState(npcs.boss3, "BHurt_Loop",false)

            s.blackScreen()
            s.setActive(npcs.boss1,false)
            s.setActive(npcs.boss2,false)
            s.setActive(npcs.boss3,false)
            s.camera(cameras.camera_boss2,false)
            s.lightScreen()
            s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss3战后对话_4", nil, 0)
        
            --设置天书flag，记录BOSS
            s.setGuideBookFlag("boss_liexiangshi")
        
        else
            s.setActive(npcs.boss3,false)
        end

        --刷出传送点
        s.setActive(objs.quit,true)
        s.completeChallengeMap()

        --结束副本内主线任务
        if s.hasTaskGroup("风雪神道副本内")then
            s.setTaskStep("风雪神道副本内","leaveScene")
        end

        if s.getCurrentTaskStep("风雪神道") == "finishFengXueShenDao" then
            s.setTaskStep("风雪神道","reportToPolice")
        end

    else
        s.camera(cameras.camera_boss2,false)
    end
end

--前往山路
function enterMiddle()
    s.changeMap("Assets/GameScenes/游历场景/挑战_风雪神道/挑战_风雪神道山路.unity", "位置/teleport")
end

--离开副本
function quitDungeon()
    s.showConfirmResetChallengeMap()
end

--捡垃圾
-- function catchTrash(trashKey)
--     s.setActive("物体/" .. trashKey,false)

--     --50%的概率获得补给，50%的概率获得资源
--     local rand = math.random(1,100)
--     if(rand <= 50)then
--         --30%的概率获得2份，70%的概率获得1份
--         local rand2 = math.random(1,100)
--         if(rand2 <= 30)then
--             s.popInfo("获得补给 × 2!")
--             flags.trash = flags.trash + 2
--             s.setMapStatus("补给数量", flags.trash)
--             itemWidget:addItem(itemData.TrashDone, 2)
--         else
--             s.popInfo("获得补给 × 1!")
--             flags.trash = flags.trash + 1
--             s.setMapStatus("补给数量", flags.trash)
--             itemWidget:addItem(itemData.TrashDone, 1)
--         end
--     else
--         --30%的概率获得2份，70%的概率获得1份
--         local rand2 = math.random(1,100)
--         if(rand2 <= 30)then
--             s.popInfo("获得资源 × 2!")
--             flags.resource = flags.resource + 2
--             s.setMapStatus("资源数量", flags.resource)
--             itemWidget:addItem(itemData.ResourceDone, 2)
--         else
--             s.popInfo("获得资源 × 1!")
--             flags.resource = flags.resource + 1
--             s.setMapStatus("资源数量", flags.resource)
--             itemWidget:addItem(itemData.ResourceDone, 1)
--         end
--     end
    
--     flags[trashKey] = -1
-- end

-- --通用小怪战斗
-- function enemyBattle(roleKey)
--     local battleTb = {}
--     battleTb["StructureFire"] = flags.StructureFire
--     battleTb["StructureFood"] = flags.StructureFood
--     battleTb["StructureAlcohol"] = flags.StructureAlcohol
--     battleTb["StructureClothes"] = flags.StructureClothes
--     battleTb["StructureKnifeStone"] = flags.StructureKnifeStone
--     battleTb["StructureMap"] = flags.StructureMap
--     battleTb["StructurePet"] = flags.StructurePet
--     battleTb["StructureEnhance"] = flags.StructureEnhance
--     如果有风雪神道地图(flags.StructureMap>0)，可以使用1补给跳过战斗
--     if(flags.StructureMap > 0 and flags.trash >= 1)then
--         local ret = s.selectTalk(npcs.zhujue, "是否使用1补给跳过战斗？", {"跳过", "不跳过"})
--         if ret == 0 then
--             flags.trash = flags.trash - 1
--             s.setMapStatus("补给数量", flags.trash)
--             itemWidget:removeItem(itemData.TrashDone, 1)
--             enemyBattleEnd(roleKey)
--             return
--         end
--     end
--     --否则继续进入战斗
--     local isWin,bResult = s.battle("副本_风雪神道_战斗_" .. roleKey, battleTb)
--     if(isWin)then
--         s.popInfo("获得补给 × 1,资源 × 1!")
--         flags.trash = flags.trash + 1
--         s.setMapStatus("补给数量", flags.trash)
--         flags.resource = flags.resource + 1
--         s.setMapStatus("资源数量", flags.resource)
--         itemWidget:addItem(itemData.TrashDone, 1)
--         itemWidget:addItem(itemData.ResourceDone, 1)
--         s.openChallengeMapChest(nil,"farmfield_FengXueShenDao_Enemy_Common")
--     end
-- end