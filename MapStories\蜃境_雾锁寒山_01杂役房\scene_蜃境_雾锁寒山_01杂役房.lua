
---@type 蜃境_雾锁寒山_01杂役房
local context = require("MapStories/蜃境_雾锁寒山_01杂役房/scene_蜃境_雾锁寒山_01杂役房_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local assets = context.assets

s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger
s.loadFlags(context.flags)

---@type flags_蜃境_雾锁寒山_01杂役房


local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function test()
end

function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数

    reset_all()
    --第一次进入场景
    if step == 0 then
        s.readyToStart(true)
        s.playTimeline(assets.timeline_enter,false)
        s.camera(cameras.cam_enter, true,true,blendHintEnum.Cut)

        s.setPos(npcs.zhujue,pos.pos_ready_start)
        s.lightScreen()

        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "进入杂役房")

        s.camera(cameras.cam_main, true,true,blendHintEnum.Custom,2)

        --s.animState(npcs.wshs_01,"BSit_Loop",true)
        next_level_step(true)
        s.setTaskStep("雾锁寒山杂役房","forwardToExplore")
    elseif step == 1 then
        s.setTaskStep("雾锁寒山杂役房","forwardToExplore")

        level_guide_to(npcs.wshs_01, 0.5)
        -- --设置墨七动作状态
        -- s.animState(npcs.wshs_01,"BSit_Loop",true)
    elseif step == 2 then
        s.setTaskStep("雾锁寒山杂役房","forwardToExplore")

        level_guide_to(npcs.wshs_02, 1)
    else
        s.setTaskStep("雾锁寒山杂役房","leave")
        level_guide_to(objs.exit)
    end

    s.playSound("sfx","夜间蟋蟀",nil,nil,nil,true,2)
end

function start()
    s.camera(cameras.cam_main, true,true,blendHintEnum.Cut)

    --主角位置
    s.setPos(npcs.zhujue,pos.pos_start)

    refreshMapStates()
    s.readyToStart(true)
    try_execute_newbieGuide()
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.wshs_01, false)
    s.setActive(npcs.wshs_02, false)

    --删除所有的引导点
    s.removeAllGameMapGuides()

    --删除支线任务人物
    if s.getCurrentTaskStep("雾锁寒山阿莫") == "finished" then
        s.setActive(npcs.amo,false)
    end

    --将所有的敌人动画状态机置为Idle
    s.animState(npcs.enemy_1,"Idle",true)

    --关闭图纸
    if flags.paper1 == 1 then
        s.setActive(objs.paper1,false)
    end

    if flags.paper2 == 1 then
        s.setActive(objs.paper2,false)
    end

    if flags.paper3 == 1 then
        s.setActive(objs.paper3,false)
    end

    if flags.dizi_talk == 1 then
        s.setActive(objs.trigger_baoguo,true)
    end
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("雾锁寒山")
end

-------------------地图跑图---------------------
--蜃境怪物出现
function evilAir_1(id)
    s.setActive(objs.evilAir_1,false)
    s.setActive(npcs.minion_1, true)

    s.turnTo(npcs.minion_1,npcs.zhujue)
    s.animState(npcs.minion_1,"BAssault_Loop",true)
    s.wait(2)
    enemy_1_fight(id)
end

--蜃境怪物出现
function evilAir_2(id)
    s.setActive(objs.evilAir_2,false)
    s.setActive(npcs.minion_2, true)
    s.turnTo(npcs.minion_2,npcs.zhujue)
    s.animState(npcs.minion_2,"BAssault_Loop",true)
    s.wait(2)
    enemy_2_fight(id)
end

function enemy_1_fight(id)
    if require("MapStories/MapStoryCommonTools").roamMapBattle(id) then
        return
    else
        s.animState(npcs.minion_1,"BAssault_Loop",false)
    end
end

function enemy_2_fight(id)
    if require("MapStories/MapStoryCommonTools").roamMapBattle(id) then
        return
    else
        s.animState(npcs.minion_2,"BAssault_Loop",false)
    end
end

-------------------支线和解密---------------------
function amo()
    if s.getCurrentTaskStep("雾锁寒山阿莫") == "deactive" then
        s.animState(npcs.amo,"Special_1",true)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "阿莫支线_遇见阿莫")
        s.setTaskStep("雾锁寒山阿莫","askDizi")
    elseif s.getCurrentTaskStep("雾锁寒山阿莫") == "backToAmo" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "阿莫支线_返回阿莫处")
        s.move(npcs.amo,pos.pos_amoLeave,5,true)
        s.setActive(npcs.amo,false)
        s.setTaskStep("雾锁寒山阿莫","finished")

        capi.finishMemorySlot("wshs1_sideTask_amo")
    else
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "阿莫支线_阿莫闲话")
    end
end

function dizi()
    if s.getCurrentTaskStep("雾锁寒山阿莫") == "askDizi" then
        s.turnTo(npcs.amoFriend,npcs.zhujue,false)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "阿莫支线_询问弟子")
        s.setTaskStep("雾锁寒山阿莫","backToAmo")
    else
        if flags.dizi_talk == 0 then
            flags.dizi_talk = 1
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "弟子第一次说话")
        else      
            s.animState(npcs.amoFriend,"BDeny",true)
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "阿莫支线_弟子闲话")
        end
        s.setActive(objs.trigger_baoguo,true)
    end
end

function jgs(id)
    local paper_id = tostring(id)
    if flags.first_findJiguanshu == 0 then
        s.showLoading("查看图纸……", 3)

        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "谜题_机关术")
        s.setActive("物体/"..id,false)
        flags[paper_id] = 1
        s.popInfo("脑海里知识增加了！！！")
    else
        s.showLoading("查看图纸……", 3)

        s.setActive("物体/"..id,false)
        flags[paper_id] = 1
        s.popInfo("脑海里知识增加了！！！")

        if flags.first_findJiguanshu >= 2 then
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "谜题_机关术收集完毕")
            capi.finishMemorySlot("wshs1_question_3things")
            s.popInfo("机关术知识收集完毕！")
        end
    end
    flags.first_findJiguanshu = flags.first_findJiguanshu + 1
end

function jgs_baoguo()
    s.setActive(objs.trigger_baoguo,false)
    s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "谜题_引开人")
    s.move(npcs.amoFriend,pos.pos_check_baoguo,3,true)
    s.animState(npcs.amoFriend,roleAnims.BSquat_Loop,true)
end
-------------------关卡记忆碎片和战斗实现---------------------
function wshs_01()
    s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "墨七的幻影1")
    executeMemSlot("wshs_01", function()
        require("Chapters/雾锁寒山").wshs1_01()
    end,function()
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "墨七的幻影1_结束")
        next_level_step(true)
    end)
end

function wshs_02()
    s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "墨七的幻影2")
    executeMemSlot("wshs_02", function()
        require("Chapters/雾锁寒山").wshs1_02()
    end
    ,function()
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_杂役房", "墨七的幻影2_结束")
        s.setTaskStep("雾锁寒山杂役房","leave") 
        next_level_step(true)
    end)
end