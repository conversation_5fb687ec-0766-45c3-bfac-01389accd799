
---@type 主线_青羽录10_荆成心意世界
local context = require("MapStories/主线_青羽录10_荆成心意世界/scene_主线_青羽录10_荆成心意世界_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_主线_青羽录10_荆成心意世界
local flags = context.flags --[[@as flags_主线_青羽录10_荆成心意世界]]
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
--每次载入调用
function start()
    s.readyToStart()
end

--第一次进入地图
function first_time_into_map()
    -- 填写逻辑
end

function hello()
    s.aside("你好")
end
