local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        team = bcapi.get_team(0)
        bcapi.create_join_role("剧情_诸葛鹿", 80, team, bcapi.Vector2(-7, 0), 0)
    end)
end

function start()
    bcapi.add_timer_trigger("[%c->teamate:剧情_诸葛鹿%][<]1",0,BattleWin)
    bcapi.add_condition_trigger("[%enemy:剧情_诸葛玲珑->hp_pct%][<=]0", 0, BattleWin)
    StartTalk()
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        
        bcapi.talk("诸葛玲珑", "师姐，看拳！")
        
        bcapi.resume_and_show_ui()
    end)
end


function BattleWin()
    bcapi.win()
end
