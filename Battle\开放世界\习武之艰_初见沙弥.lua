local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

local changeToOldMonkTag = 0
local changeToManyMonkTag = 0
-- 脚本内容：
-- 

function init()

end

function start()
    bcapi.async_call(function()
        bcapi.add_timer_trigger(1, startTalk)
        bcapi.add_timer_trigger(5, firstTipTalk)
        bcapi.add_timer_trigger(20, changeToOldMonk)
        bcapi.add_condition_trigger("[%enemy:世界_习武之艰_初见沙弥->hp_pct%][<=]0.8", 0.1, changeToOldMonk)
    end)
end

function startTalk(...)
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("小沙弥", "施主，请赐教！")
        bcapi.resume_and_show_ui()
    end)
end

function firstTipTalk(...)
    bcapi.async_call(function()
        bcapi.append_async_aside("小沙弥",0,"师父教我步法，说要缥缈不定才能以轻巧致胜！","","小沙弥")
    end)
end

function changeToOldMonk(...)
    bcapi.async_call(function()
        if changeToOldMonkTag == 0 then
            changeToOldMonkTag = 1
            local Actor = bcapi.get_role(1, "世界_习武之艰_初见沙弥")
            bcapi.use_skill_to_role(Actor,"世界_习武之艰_小沙弥变身老和尚",Actor,1)
            bcapi.add_timer_trigger(8, secondTipTalk)
            bcapi.add_timer_trigger(20, changeToManyMonk)
            -- bcapi.add_condition_trigger("[%enemy:世界_习武之艰_初见沙弥->hp_pct%][<=]0.5", 0.1, changeToManyMonk)
        end
    end)
end

function secondTipTalk(...)
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("主角", "这身法，仿佛看到了一个从容的老和尚……")
        bcapi.talk("主角", "小沙弥确有几分天赋。")
        bcapi.resume_and_show_ui()
        bcapi.add_condition_trigger("[%enemy:世界_习武之艰_初见沙弥->hp_pct%][<=]0.5", 0.1, changeToManyMonk)
    end)
end

function changeToManyMonk(...)
    bcapi.async_call(function()
        if changeToManyMonkTag == 0 then
            changeToManyMonkTag = 1
            local Actor = bcapi.get_role(1, "世界_习武之艰_初见沙弥")
            bcapi.use_skill_to_role(Actor,"世界_习武之艰_小沙弥变身一群和尚",Actor,1)
            bcapi.add_timer_trigger(8, thirdTipTalk)
        end
    end)
end

function thirdTipTalk(...)
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("主角", "这拳法，来势凶猛，接连不断，仿佛遭到围攻……")
        bcapi.talk("主角", "悟性极佳，但仍有些稚嫩。")
        bcapi.resume_and_show_ui()
    end)
end