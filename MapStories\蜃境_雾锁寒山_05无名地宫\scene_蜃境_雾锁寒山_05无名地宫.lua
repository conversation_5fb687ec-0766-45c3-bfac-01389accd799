
---@type 蜃境_雾锁寒山_05无名地宫
local context = require("MapStories/蜃境_雾锁寒山_05无名地宫/scene_蜃境_雾锁寒山_05无名地宫_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_雾锁寒山_05无名地宫
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function testanim()
    s.popInfo("测试动画")
    s.setGuideBookFlag("boss_shanzeilaoda")
end

--精英怪战斗
function eliteBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

--boss战斗
function bossBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数
    s.playMusic("蜃境")
    if flags.door_open == 1 then
        door_open()
    end

    for i = 1, 4 do
        if flags["enemy_" .. i .. "_battle"] == 1 then
            s.setActive(objs["enemy_" .. i .. "_collider"], false)
        end
    end

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 5) then
        taskStep = "forwardToExplore"
    elseif (step == 5) then
        taskStep = "leave"
    end
    s.setTaskStep("雾锁寒山无名地宫", taskStep)

        
    reset_all()
    --第一次进入场景
    if step == 0 then
        s.setTaskStep("雾锁寒山无名地宫","forwardToExplore")
        
        if flags.start_show == 0 then
            s.readyToStart(true)
            s.camera(cameras.cam_start,true,true,blendHintEnum.Cut)
            s.wait(1)
            s.cameraAsync(cameras.cam_start1,true,true,blendHintEnum.Custom,20)
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_无名地宫","开场进入")
            s.wait(1)
            s.blackScreen()
            s.camera(cameras.cam_main,true,true,blendHintEnum.Cut)
            flags.start_show = 1
        end

        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.wshs_13, 0.5)
        s.lightScreen()

    elseif step == 2 then
        level_guide_to(npcs.wshs_14, 0.5)
        door_open()
    elseif step == 3 then
        level_guide_to(npcs.wshs_15, 0.5)
    elseif step == 4 then
        level_guide_to(npcs.wshs_16, 0.5)
    else
        level_guide_to(objs.exit)
    end
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.wshs_13, false)
    s.setActive(npcs.wshs_14, false)
    s.setActive(npcs.wshs_15, false)
    s.setActive(npcs.wshs_16, false)

    --删除所有的引导点
    s.removeAllGameMapGuides()

    --设置被污染的宝箱的显隐状态
    if flags.open_shadowChest_1 == 1 then
        s.setActive(objs.evilAirChest_1,false)
    else
        s.setActive(objs.evilAirChest_1,true)
    end

    if s.getCurrentTaskStep("祖师爷") == "finished" then
        s.setActive(npcs.zushiye,false)
    end

    if flags.collect_1 == 1 then
        s.setActive(objs.wshs5_1007,false)
    else
        s.setActive(objs.wshs5_1007,true)
    end

    if flags.collect_2 == 1 then
        s.setActive(objs.wshs5_1008,false)
    else
        s.setActive(objs.wshs5_1008,true)
    end
    
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("雾锁寒山")
end

-------------------地图跑图---------------------
--门打开
function door_open()
    s.soloAnimState(objs.door_left, "Special_Loop_1",true)
    s.soloAnimState(objs.door_right, "Special_Loop_1",true)
    flags.door_open = 1
end

function enemy_battle(id,enemyKey)
    local colliderKey = enemyKey.."_collider"

    s.popInfo("你已被发现！！！")

    s.setActive(objs[colliderKey],false)
    s.turnTo(npcs[enemyKey],npcs.zhujue,true)
    s.turnTo(npcs.zhujue,npcs[enemyKey],true)

    s.soloAnimState(npcs[enemyKey],roleAnims.Special_1,true)
    s.wait(2)

    require("MapStories/MapStoryCommonTools").roamMapBattle(id,function()
        s.popInfo("战斗胜利！！！")
        flags[enemyKey.."_battle"] = 1
        s.setActive(objs[colliderKey],false)

        if s.getCurrentTaskStep("祖师爷") ~= "finished" then
            s.changeItemCount("蜃境_雾锁寒山_祖师爷_机关零件",1)

            --第一次收集机关零件
            if flags.jiguanlingjian == 0 then
                s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_无名地宫","支线_祖师爷_机关零件")
            end

            flags.jiguanlingjian = flags.jiguanlingjian + 1
            zsy_test_collect()
        end
    end,
    function()
        battle_lose(id,enemyKey)
    end,
    function()
        battle_lose(id,enemyKey)
    end
)
    s.wait(0.5)
    refreshMapStates()
end

function zsy_test_collect()
    if flags.jiguanlingjian >= 5 and s.getCurrentTaskStep("祖师爷") == "findJiguan" then
        s.setTaskStep("祖师爷","backToNanzi")
        return true
    else
        return false
    end
end

function battle_lose(id,enemyKey)
    local colliderKey = enemyKey.."_collider"

    s.popInfo("战斗失败！！！")

    s.blackScreen()
    --s.animState(npcs[enemyKey],"BAssault_Loop",false)
    s.setActive(objs[colliderKey],true)
    s.setPos(npcs.zhujue,pos[enemyKey],true)
    s.lightScreen()
end

function fightInBack(enemyKey)
    local colliderKey = enemyKey.."_collider"
    s.setActive(objs[colliderKey],false)

    s.animState(npcs.zhujue,"Special_2",true)
    s.wait(1)
    s.playSound("sfx","拳击3")
    s.wait(0.6)

    s.animState(npcs[enemyKey],"BDie",true)
    s.popInfo("敌人暂时被击倒！！！")
end
-------------------支线和解密---------------------
function zsy_xianzu()
    if s.getCurrentTaskStep("祖师爷") == "deactive" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_无名地宫","支线祖师爷_遇到")
        s.setTaskStep("祖师爷","findJiguan")
    elseif s.getCurrentTaskStep("祖师爷") == "backToNanzi" then
        delete_jiguanlingjian()
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_无名地宫","支线祖师爷_交付机关零件")
        s.setActive(npcs.zushiye,false)
        capi.finishMemorySlot("wshs5_sideTask_zushiye")
        s.setTaskStep("祖师爷","finished")
    elseif flags.jiguanlingjian >= 5 and s.getCurrentTaskStep("祖师爷") == "findJiguan" then
        delete_jiguanlingjian()
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_无名地宫","支线祖师爷_交付机关零件")
        s.setDeath(npcs.zushiye,3)
        capi.finishMemorySlot("wshs5_sideTask_zushiye")
        s.setTaskStep("祖师爷","finished")
    else
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_无名地宫","支线祖师爷_闲话")
        s.popInfo("<color=yellow>机关零件</color>不够！")
    end
end

function delete_jiguanlingjian()
    local ret = s.getItemCount("蜃境_雾锁寒山_祖师爷_机关零件")
    print("机关零件数量："..ret)
    if ret > 0 then
        s.changeItemCount("蜃境_雾锁寒山_祖师爷_机关零件", -ret)
    end
end

function shadowChest(id,chestKey)
    local chestKey = "open_shadowChest_"..chestKey
    if flags[chestKey] == 0 then
        s.popInfo("需击败附近镇守的蜃境怪物！！！")
    else
        require("MapStories/MapStoryCommonTools").roamMapChest(id)
    end
end

-------------------关卡记忆碎片和战斗实现---------------------

function evilAir_1(id)
    s.setActive(objs.evilAir_1,false)
    s.setActive(npcs.role_elite_7, true)

    s.turnTo(npcs.role_elite_7,npcs.zhujue)
    s.animState(npcs.role_elite_7,roleAnims.Special_1,true)
    s.wait(2)
    elite_7_fight(id)
end

function elite_7_fight(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,
    function()
        flags.open_shadowChest_1 = 1
            s.setActive(objs.evilAirChest_1,false)
            refreshMapStates()
    end)
end

function evilAir_2(id)
    s.setActive(objs.evilAir_2,false)
    s.setActive(npcs.role_elite_8, true)

    s.turnTo(npcs.role_elite_8,npcs.zhujue)
    s.animState(npcs.role_elite_8,roleAnims.Special_1,true)
    s.wait(2)
    elite_8_fight(id)
end

function elite_8_fight(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,
    function()
        flags.open_shadowChest_2 = 1
            s.setActive(objs.evilAirChest_2,false)
            refreshMapStates()
    end)
end

function wshs_13()
    executeMemSlot("wshs_13", function()
        require("Chapters/雾锁寒山").wshs5_13()
    end)
end

function wshs_14()
    executeMemSlot("wshs_14", function()
        require("Chapters/雾锁寒山").wshs5_14()
    end)
end

function wshs_15()
    executeMemSlot("wshs_15", function()
        require("Chapters/雾锁寒山").wshs5_15()
    end)
end

function wshs_16()
    executeMemSlot("wshs_16", function()
        require("Chapters/雾锁寒山").wshs5_16()
    end)
    s.setTaskStep("雾锁寒山无名地宫","leave")
end

function shudu_1007(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
    flags.collect_1 = 1
end

function huaji_1008(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
    flags.collect_2 = 1
end