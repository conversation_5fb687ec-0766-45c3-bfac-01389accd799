--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/漫游_雪谷/flags_漫游_雪谷")


---@class 角色_漫游_雪谷_荒原雪林
local npcs = {
    --- "族长多布"
    duobu = "角色/duobu",
    --- "长老兰西"
    lanxi = "角色/lanxi",
    --- "鲁鲁"
    lulu = "角色/lulu",
    --- "海雅"
    haiya = "角色/haiya",
    --- "盗猎人"
    hunter = "角色/hunter",
    --- "熊"
    bear = "角色/bear",
    --- "墨七"
    moqi = "角色/moqi",
}

---@class 物体_漫游_雪谷_荒原雪林
local objs = {
    --- "盗猎事件"
    eventDaolie = "物体/eventDaolie",
}

---@class 相机_漫游_雪谷_荒原雪林
local cameras = {
    --- "初始剧情相机"
    start = "相机/start",
}

---@class 位置_漫游_雪谷_荒原雪林
local pos = {
    --- "通往营地"
    Yingdi = "位置/Yingdi",
    --- "初始剧情位置"
    MQ_tied = "位置/MQ_tied",
}

---@class 资产_漫游_雪谷_荒原雪林
local assets = {
}

---@class 动作_漫游_雪谷_荒原雪林
local animationClips = {
}

---@class 漫游_雪谷_荒原雪林
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
