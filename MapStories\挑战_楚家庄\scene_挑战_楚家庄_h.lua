--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_楚家庄/flags_挑战_楚家庄")


---@class 角色_挑战_楚家庄
local npcs = {
    --- "荆成"
    jingcheng = "角色/jingcheng",
    --- "伥怪BOSS"
    changguaiBoss = "角色/changguaiBoss",
    --- "侍女"
    shinv = "角色/shinv",
    --- "毒女"
    dunv = "角色/dunv",
    --- "楚青"
    chuqing = "角色/chuqing",
    --- "燃烧伥怪"
    rscg = "角色/rscg",
}

---@class 物体_挑战_楚家庄
local objs = {
    boss1trigger = "物体/boss1trigger",
    boss2trigger = "物体/boss2trigger",
    boss3trigger = "物体/boss3trigger",
    boss4interc = "物体/boss4interc",
    boss2trigger_1 = "物体/boss2trigger_1",
}

---@class 相机_挑战_楚家庄
local cameras = {
    --- "俯视"
    overview = "相机/overview",
    --- "boss2"
    boss2 = "相机/boss2",
    --- "boss3近"
    boss3Near = "相机/boss3Near",
    --- "boss3侧"
    boss3Side = "相机/boss3Side",
    --- "boss1"
    boss1 = "相机/boss1",
    --- "boss4"
    boss4 = "相机/boss4",
    boss4_1 = "相机/boss4_1",
}

---@class 位置_挑战_楚家庄
local pos = {
    --- "开场位置"
    start = "位置/start",
    --- "BOSS1位置"
    boss1 = "位置/boss1",
    --- "BOSS2位置"
    boss2 = "位置/boss2",
    --- "BOSS3位置"
    boss3 = "位置/boss3",
    --- "演出位置"
    boss3Sub = "位置/boss3Sub",
    boss4 = "位置/boss4",
}

---@class 资产_挑战_楚家庄
local assets = {
}

---@class 动作_挑战_楚家庄
local animationClips = {
}

---@class 挑战_楚家庄
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
