local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle

function init()
    CreateRoles()
    battle.ForceNotEndBattle = true
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_墨七", 62, bcapi.get_team(0), bcapi.Vector2(-5, 0), 0)
        bcapi.create_join_role("剧情_机关熊", 62, bcapi.get_team(0), bcapi.Vector2(0, 0), 0)
        
        local moqi = bcapi.get_role(0, "剧情_墨七")
        moqi.Buff:AddBuff("剧情_巨能扛", 300, 1, 1, moqi)
        bcapi.change_role_hp(moqi, 50000)
    
    end)
end

function start()
    StartTalk()
    bcapi.add_timer_trigger(30, MidTalk)
    bcapi.add_timer_trigger(60, BattleWin)
    bcapi.add_condition_trigger("[%c->teammate:剧情_墨七%][<]1", 1, BattleLose)
    bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 1, BattleWin)
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("墨七", "或许，无路可逃的是你们才对？")
        bcapi.talk("", "请保护好墨七，墨七死亡将战斗失败。")
        
        bcapi.resume_and_show_ui()
    end)
end

function MidTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("墨七", "（机关熊还可以坚持一会儿......我得趁机逃出去，与阿凉大哥汇合。）")
        bcapi.talk("", "请再坚持30秒。")

        bcapi.resume_and_show_ui()
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
