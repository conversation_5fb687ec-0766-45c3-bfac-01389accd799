--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_雾锁寒山_04流亡者营地/flags_蜃境_雾锁寒山_04流亡者营地")


---@class 角色_蜃境_雾锁寒山_04流亡者营地
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "wshs_10"
    wshs_10 = "角色/wshs_10",
    --- "wshs_11"
    wshs_11 = "角色/wshs_11",
    --- "wshs_12"
    wshs_12 = "角色/wshs_12",
    --- "enemy_1"
    enemy_1 = "角色/enemy_1",
    --- "enemy_2"
    enemy_2 = "角色/enemy_2",
    --- "enemy_3"
    enemy_3 = "角色/enemy_3",
    --- "enemy_4"
    enemy_4 = "角色/enemy_4",
    --- "enemy_5"
    enemy_5 = "角色/enemy_5",
    --- "elite_6"
    elite_6 = "角色/elite_6",
    --- "elite_7"
    elite_7 = "角色/elite_7",
    --- "boss_8"
    boss_8 = "角色/boss_8",
    --- "wshs_10_f1"
    wshs_10_f1 = "角色/wshs_10_f1",
    --- "阿凉Story幻影1"
    role_aliang_1 = "角色/role_aliang_1",
    --- "流亡者青年01幻影1"
    role_qingnian1_1 = "角色/role_qingnian1_1",
    --- "流亡者青年02幻影1"
    role_qingnian2_1 = "角色/role_qingnian2_1",
    --- "流亡者青年03幻影1"
    role_qingnian3_1 = "角色/role_qingnian3_1",
    --- "机关狗幻影1"
    role_jiguangou_1 = "角色/role_jiguangou_1",
    --- "精英6"
    role_elite_6 = "角色/role_elite_6",
    --- "精英7"
    role_elite_7 = "角色/role_elite_7",
    --- "道士"
    daoshi = "角色/daoshi",
    --- "木人"
    ln_muren = "角色/ln_muren",
    --- "林契"
    ln_linqi = "角色/ln_linqi",
    ln_shencongan = "角色/ln_shencongan",
    moqi = "角色/moqi",
    daoshi_death = "角色/daoshi_death",
    boss_9 = "角色/boss_9",
    elite_10 = "角色/elite_10",
    elite_11 = "角色/elite_11",
}

---@class 物体_蜃境_雾锁寒山_04流亡者营地
local objs = {
    --- "出口"
    exit = "物体/exit",
    --- "chest_1"
    chest_1 = "物体/chest_1",
    --- "chest_2"
    chest_2 = "物体/chest_2",
    --- "敌人5身后碰撞"
    enemy_5_collider = "物体/enemy_5_collider",
    --- "敌人4身后碰撞"
    enemy_4_collider = "物体/enemy_4_collider",
    evilAir_1 = "物体/evilAir_1",
    --- "被无染的宝箱"
    evilAirChest_1 = "物体/evilAirChest_1",
    --- "敌人2身后碰撞"
    enemy_2_collider = "物体/enemy_2_collider",
    evilAir_2 = "物体/evilAir_2",
    --- "被无染的宝箱2"
    evilAirChest_2 = "物体/evilAirChest_2",
    --- "敌人3身后碰撞"
    enemy_3_collider = "物体/enemy_3_collider",
    --- "敌人1身后碰撞"
    enemy_1_collider = "物体/enemy_1_collider",
    --- "wshs4_1005"
    wshs4_1005 = "物体/wshs4_1005",
    --- "wshs4_1006"
    wshs4_1006 = "物体/wshs4_1006",
    --- "ln_jiaoyin"
    ln_jiaoyin = "物体/ln_jiaoyin",
}

---@class 相机_蜃境_雾锁寒山_04流亡者营地
local cameras = {
    cam_start_far = "相机/cam_start_far",
    cam_start_near = "相机/cam_start_near",
    cam_main = "相机/cam_main",
    cam_muren = "相机/cam_muren",
    cam_jiaoyin = "相机/cam_jiaoyin",
    cam_ln_backTosheng = "相机/cam_ln_backTosheng",
    --- "cam_ln_backTosheng1"
    cam_ln_backTosheng1 = "相机/cam_ln_backTosheng1",
}

---@class 位置_蜃境_雾锁寒山_04流亡者营地
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    --- "enemy_5返回位置"
    enemy_5 = "位置/enemy_5",
    --- "enemy_4返回位置"
    enemy_4 = "位置/enemy_4",
    --- "enemy_2返回位置"
    enemy_2 = "位置/enemy_2",
    --- "enemy_3返回位置"
    enemy_3 = "位置/enemy_3",
    --- "enemy_1返回位置"
    enemy_1 = "位置/enemy_1",
    --- "道士尸体位置"
    daoshi_die = "位置/daoshi_die",
    --- "木人失败返回"
    nl_jiaoyin_back = "位置/nl_jiaoyin_back",
    nl_linqicome = "位置/nl_linqicome",
    shengcongan_leave = "位置/shengcongan_leave",
    moqi_end = "位置/moqi_end",
    moqi_start = "位置/moqi_start",
}

---@class 资产_蜃境_雾锁寒山_04流亡者营地
local assets = {
    bossShow = "资产/bossShow",
    ln_muren = "资产/ln_muren",
}

---@class 动作_蜃境_雾锁寒山_04流亡者营地
local animationClips = {
}

---@class 蜃境_雾锁寒山_04流亡者营地
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
