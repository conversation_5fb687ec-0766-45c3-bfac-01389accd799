GameData = CS.DR22.ServerAPI.GameHostApiCtrl.Current.Root
local exp = GameData.Exp
local level = GameData.Level
local nextLevel = CS.DR22.Bean.PlayerUpgrade.GetByLevel(level+1);
local curLevel = CS.DR22.Bean.PlayerUpgrade.GetByLevel(level);
local baseExp = curLevel and curLevel.totalExp or 0
local hpRate =  CS.DR22.Bean.PlayerUpgrade.GetSoulRateByLevel(level)

--保留1位小数
local function formatFloat(num)
    return string.format("%.1f", num)
end

local pveRate = CS.DR22.Bean.PlayerUpgrade.GetPVERateByLevel(level)

PlaySound("UI", "warning")

aside("升级卡牌可以获得剑之川经验。")
if (nextLevel == nil) then
    aside("当前剑之川等级为<color=yellow>"..GameData.Level.."</color>，经验值<color=green>满级" .. "</color>\n<color=yellow>非玩家对战中，队伍魂值提升</color><color=green>".. formatFloat(hpRate) .. "%</color>\n<color=yellow>非玩家对战中，属性提升</color><color=green>" .. formatFloat(pveRate) .. "%</color>")

else
    aside("当前剑之川等级为<color=yellow>"..GameData.Level.."</color>，经验值<color=yellow>" .. (exp - baseExp) .. "/" .. nextLevel.exp .. "</color>\n<color=yellow>非玩家对战中，队伍魂值提升</color><color=green>".. formatFloat(hpRate) .. "%</color>\n<color=yellow>非玩家对战中，属性提升</color><color=green>" .. formatFloat(pveRate) .. "%</color>")
end