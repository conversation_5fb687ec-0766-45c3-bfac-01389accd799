local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local eTeam = bcapi.get_team(1)

function init()
    local time = 903
    if(bcapi.get_team_dict_has_key(0, "战斗时间"))then
        time = bcapi.get_team_dict_int_value(0, "战斗时间", time) + 3
    end
    bcapi.reset_limit_time(time)
end

function start()
    bcapi.async_call(function()
        local isSummon = 0
        local isCard = 0

        if(bcapi.get_team_dict_has_key(0, "是否召唤"))then
            isSummon = bcapi.get_team_dict_int_value(0, "是否召唤", 0)
        end
        if(bcapi.get_team_dict_has_key(0, "是否策略卡"))then
            isCard = bcapi.get_team_dict_int_value(0, "是否策略卡", 0)
        end

        if(isSummon == 1)then
            bcapi.create_and_summon_role("副本_夜战开封_燕路重", 65, bcapi.get_team(0), bcapi.Vector2(-6, 0), 0.1,-1,1,1,1,1,1,"")
        end
        if(isCard == 1)then
            bcapi.create_and_summon_role("副本_夜战开封_开封士兵", 55, bcapi.get_team(0), bcapi.Vector2(-6, 2), 0.1,-1,1,1,1,1,1,"")
            bcapi.create_and_summon_role("副本_夜战开封_开封士兵", 55, bcapi.get_team(0), bcapi.Vector2(-6, -2), 0.1,-1,1,1,1,1,1,"")
        end
        bcapi.add_timer_trigger(2, StartTalk)
    end)
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("胡老海#副本_夜战开封_胡老海", "河洛帮的弟兄们，今日就是耀我河洛门维的时刻！")
        bcapi.talk("胡老海#副本_夜战开封_胡老海", "保护盟主，拿下贼人！")
        bcapi.resume_and_show_ui()

        bcapi.add_condition_trigger("[%enemy:副本_夜战开封_胡老海->flag:阵亡小弟%][>=]7", 1, EnemyEnhance)
        bcapi.add_condition_trigger("[%enemy:副本_夜战开封_胡老海->flag:阵亡小弟%][>=]14", 1, EnemyEnhance2)
        bcapi.add_condition_trigger("[%enemy:副本_夜战开封_胡老海->flag:阵亡小弟%][>=]21", 1, EnemyEnhance2)
        bcapi.add_condition_trigger("[%enemy:副本_夜战开封_胡老海->flag:超级战士%][>]0", 1, SkillTalk)
    end)
end

function EnemyEnhance()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("胡老海#副本_夜战开封_胡老海","(看到河洛弟子受伤众多)……你，竟敢伤我如此多的兄弟！")
        bcapi.talk("胡老海#副本_夜战开封_胡老海","兄弟们！暂且退下休养，让本舵主来给他们一些教训！")

        bcapi.resume_and_show_ui()
        local Boss = bcapi.get_role(1, "副本_夜战开封_胡老海")
        Boss.Stat.BattleFlagDic:set_Item("可以变身", "1")
    end)
end

function SkillTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("燕路重#副本_夜战开封_燕路重","他的周身被真气笼罩，短时期内应该很难对他造成伤害。")
        bcapi.talk("燕路重#副本_夜战开封_燕路重","想办法撑过这段时间吧！")

        bcapi.resume_and_show_ui()
    end)
end

function EnemyEnhance2()
    bcapi.async_call(function()
        local Boss = bcapi.get_role(1, "副本_夜战开封_胡老海")
        Boss.Stat.BattleFlagDic:set_Item("可以变身", "1")
    end)
end