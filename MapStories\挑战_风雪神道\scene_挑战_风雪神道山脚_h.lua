--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_风雪神道/flags_挑战_风雪神道")


---@class 角色_挑战_风雪神道山脚
local npcs = {
    zhujue = "角色/zhujue",
    enemy_1 = "角色/enemy_1",
    enemy_2 = "角色/enemy_2",
    enemy_3 = "角色/enemy_3",
    boss1 = "角色/boss1",
    boss_2 = "角色/boss_2",
}

---@class 物体_挑战_风雪神道山脚
local objs = {
    trash1 = "物体/trash1",
    trash2 = "物体/trash2",
    trash3 = "物体/trash3",
    trash4 = "物体/trash4",
    trash5 = "物体/trash5",
    trash6 = "物体/trash6",
    trash7 = "物体/trash7",
    trash8 = "物体/trash8",
    chest1 = "物体/chest1",
    boss1Trigger = "物体/boss1Trigger",
    teleport = "物体/teleport",
    teleport1 = "物体/teleport1",
}

---@class 相机_挑战_风雪神道山脚
local cameras = {
    camera_boss1 = "相机/camera_boss1",
    overview1 = "相机/overview1",
    overview2 = "相机/overview2",
}

---@class 位置_挑战_风雪神道山脚
local pos = {
    start = "位置/start",
    boss_pos_1 = "位置/boss_pos_1",
    boss_pos_2 = "位置/boss_pos_2",
    boss_pos_3 = "位置/boss_pos_3",
    teleport = "位置/teleport",
    pre_start = "位置/pre_start",
    pos_boss_1 = "位置/pos_boss_1",
    pos_boss_defeat = "位置/pos_boss_defeat",
}

---@class 资产_挑战_风雪神道山脚
local assets = {
}

---@class 动作_挑战_风雪神道山脚
local animationClips = {
}

---@class 挑战_风雪神道山脚
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
