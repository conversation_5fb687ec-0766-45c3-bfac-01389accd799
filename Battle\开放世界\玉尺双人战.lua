local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

-- 召唤一个玉尺，玉尺死亡战斗失败

function init()
    bcapi.reset_limit_time(90)
    -- 开局创建角色
    createRoles()
end

function start()
    bcapi.add_timer_trigger(0.1, startTalk)
end

-- 取玩家队伍存的字典，后面两个函数中用到
-- local dict = bcapi.get_team(0).ValueDict

function createRoles()
    --给玩家创建玉尺
    bcapi.async_call(function()
        bcapi.create_join_role_with_card("玉尺", 60, team, bcapi.Vector2(-6, 0), 0)
    end)
end

function startTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("玉尺", "你每用一张卡牌，我就能招来一只机关鹰，一起合作吧！")
        bcapi.resume_and_show_ui()
    end)
end
