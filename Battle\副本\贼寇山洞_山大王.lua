local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
end

function start()
    bcapi.add_timer_trigger(0, TalkStart)
    bcapi.add_condition_trigger("[%enemy:副本_贼寇山洞_土老大->flag:二阶段%][>]0", 1, EnemyEnhance)
end

function TalkStart()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "惹到老子我，你们可是踢到铁板了！")
        bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "让你们见识下我这大铁锤的威力吧！哈哈哈！")
        bcapi.resume_and_show_ui()
    end)
end

function EnemyEnhance()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "你们……果然有些本事。")
        bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "不过，也就到此为止了，素衣心经——起！")
        local Boss = bcapi.get_role(1, "副本_贼寇山洞_土老大")
        bcapi.resume_and_show_ui()
        bcapi.use_skill_to_role(Boss,"副本_贼寇山洞_土老大_变身",Boss,1)
    end)
end