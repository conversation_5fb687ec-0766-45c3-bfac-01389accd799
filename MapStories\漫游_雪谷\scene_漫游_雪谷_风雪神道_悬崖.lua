
---@type 漫游_雪谷_风雪神道_悬崖
local context = require("MapStories/漫游_雪谷/scene_漫游_雪谷_风雪神道_悬崖_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

---组件相关
local extensions = require("MapStories/漫游_雪谷/extension")
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type 雪谷_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 雪谷_ItemData]]


s.loadFlags(context.flags)
---@type flags_漫游_雪谷
local flags = context.flags

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.readyToStart()
    reloadAllSceneStates()
end

function reloadAllSceneStates()
    --苍龙精
    s.setActive(objs.canglong,flags.dg_main_canglong == 0)
    
    --明慧
    s.setActive(npcs.minghui,flags.sd_clean_step == 4)
end


function wrappedReloadAllSceneStates()
    s.blackScreen()
    reloadAllSceneStates()
    s.lightScreen()
end

function eventCanglong()
    if flags.dg_main_canglong == 0 then
        s.aside("这是苍龙精。你获取了苍龙精！")

        flags.dg_main_canglong = 1
        wrappedReloadAllSceneStates()

        itemWidget:addItem(itemData.canglongjing,1)
    end

end

function eventWell()
    if flags.sd_cave_open == 0 then
        s.aside("这是一处枯井。")
        s.aside("总之你发现井里居然有道绳子似乎通向什么地方！")

        flags.sd_cave_open = 1
        s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_风雪神道_山脚.unity", "位置/Shanya")
        
    else
        s.aside("前往山脚吗？")

        local ret = s.selectTalk(npcs.moqi,"……",{"是","还是不了"})
        if ret == 0 then
            s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_风雪神道_山脚.unity", "位置/Shanya")
        end
    end
    
end

function eventBuda()
    s.aside("这是一处神像。")
end

function talkToMinghui()
    if flags.sd_main_step == 3 then
        --△雪崖，明慧立于悬崖边缘眺望远方。
        s.talk(npcs.moqi,"你在等那位故人吗？")
        s.talk(npcs.minghui,"正是，不过依贫僧所想，他应该是不会出现了。")
        s.talk(npcs.moqi,"此言何意？")
        --△你话音未落，附近突然传来几声沉闷的低吼。
        s.talk(npcs.moqi,"伥怪？正好拿来练手。")
        s.talk(npcs.minghui,"……")

        local isWin = s.battle("楚家庄_漫游_黑衣人")--todo:待调整
        if isWin then
            s.talk(npcs.moqi,"小小伥怪，不过如此。")
            --△明慧拦住墨七。
            s.talk(npcs.minghui,"小施主，不可赶尽杀绝。")
            s.talk(npcs.moqi,"这些可是伥怪，你难道不知道伥怪对于世人的危害吗？")
            s.talk(npcs.minghui,"可它们终究也是由人所化，若尚有一丝意识残存……")
            --△说话间，受伤的伥怪忽然袭来，墨七反应不及，条件反射般将其击毙。
            s.talk(npcs.minghui,"阿弥陀佛……")
            s.talk(npcs.moqi,"死就死了，对伥怪没什么好惋惜的。")
            --△明慧沉默片刻。
            s.talk(npcs.minghui,"贫僧一路走来，承蒙小施主照顾，现在便将那位故人之事一并告诉小施主，请借一步说话。")

            --todo：切镜头，演出级别
            s.talk(npcs.minghui,"说来真有些惭愧，贫僧那时年纪尚轻，皈依佛门并不算太久，却是住持与师兄弟眼中的异类。")
            s.talk(npcs.moqi,"异类？")
            s.talk(npcs.minghui,"正是，贫僧那时既不肯吃斋念佛，也不愿打坐行善，反而整天游手好闲胡作非为，闹得寺里乌烟瘴气，住持只好命我外出云游，想办法化解内心的戾气。")
            s.talk(npcs.moqi,"真想象不出……")
            s.talk(npcs.minghui,"贫僧离开住持的管辖，倒如同鱼儿游入大海般自由自在，不觉间一路来到这雪山之中，听说山顶有座神像很是灵验，便打算上去碰碰运气。")
            s.talk(npcs.minghui,"不料贫僧还未靠近这神像，却被一群凶恶的伥怪所围，贫僧哪里懂得拳脚，只好乖乖等死，危机之中一人忽然杀出，将这些伥怪尽数斩杀，救了贫僧一命。")
            s.talk(npcs.moqi,"可是那位故人？")
            s.talk(npcs.minghui,"正是，但小施主或许不会相信，那位故人……其实也是伥怪。")
            s.talk(npcs.moqi,"你被伥怪所救？！这不可能……")
            s.talk(npcs.minghui,"准确来说，他是一位即将伥化之人，但由于内力高强所以仍保持着一丝清明意识，拼尽全力救下了贫僧。")
            s.talk(npcs.minghui,"那位故人自称是某个门派的叛徒，因修习邪功走火入魔，特来此地向神明寻求宽恕与平静，对方与贫僧把酒言欢，约好来年在此相聚，随后大笑而去。")
            s.talk(npcs.moqi,"即将伥化之人，怕是……")
            s.talk(npcs.minghui,"贫僧也知道，这个约定多半是不能兑现了。")
            s.talk(npcs.minghui,"贫僧抱着一丝侥幸，次年又返回此地，当然未见到那故人的踪迹，却在佛像前见到他随身携带的那只刻有姓氏的酒葫芦，其中酒浆尚有余温……")
            s.talk(npcs.moqi,"难道，他并未化作伥怪？")
            --△明慧摇了摇头。
            s.talk(npcs.minghui,"人内心一旦被邪念主宰，必然堕为伥怪，此事谁也无法例外，但贫僧私以为他的善念感动了上苍，以致奇迹降临也未可知。")
            s.talk(npcs.moqi,"所以，你年年来此……")
            s.talk(npcs.minghui,"救命之恩，故人之约，不敢忘怀。")
            s.talk(npcs.minghui,"小施主有所不知，贫僧的爹娘半生行善，后来却不幸被伥怪所杀，所以贫僧一直如行尸走肉般游戏人生，若不是那位故人的一份善举，恐怕贫僧也不会有今日的心境。")
            s.talk(npcs.moqi,"那位故人，究竟什么来头？")
            s.talk(npcs.minghui,"他并未自报家门，只说门派擅长毒药与暗器，后来却被机关术所误，言语之中颇为忿恨。")
            s.talk(npcs.moqi,"毒药与暗器？还有机关术……")
            s.talk(npcs.moqi,"江湖之中，可有这样一个门派……")
            s.talk(npcs.minghui,"对了，贫僧这次来雪谷时，见几名高手为抢夺一物杀红了眼，最后统统倒在血泊中，贫僧心想此物必定是什么稀世宝贝，若流落世间或许会引人厮杀，造成更多惨剧，便悄悄将其收起。")
            s.talk(npcs.minghui,"贫僧本打算将此物抛入万丈深渊一了百了，如今见小施主心地良善又武功高强，必有能力保管此物，还请收下，但愿它能对你有所助益，也希望小施主将来行走江湖时，将那位故人的善念继续传递下去。")

            itemWidget:addItem(itemData.jiguanlingjian4,1)

            s.finishTask(400320,1)
            flags.sd_main_step = flags.sd_main_step + 1
        else
            s.aside("看来现在还不是时候……")
            --todo:需要重进漫游？
        end
    else
        s.talk(npcs.minghui,"你已经拿到琳琅玉键了。")

    end
    
end

function talkToXionghao()
    if flags.dg_main_step >= 4 and flags.dg_main_xiong == 0 then
        s.aside("这里有一间小屋。")

        local ret = s.selectTalk(npcs.moqi,"……",{"敲门","离开"})
        if ret == 0 then
            s.aside("屋内似乎有响动。。")

            ret = s.selectTalk(npcs.moqi,"……",{"继续敲门","离开"})
            if ret == 0 then
                s.aside("屋内的响动声似乎又大了一些。")

                ret = s.selectTalk(npcs.moqi,"……",{"用力敲门","离开"})
                s.aside("屋内忽然传来可怖的低吼，随即一伥怪破门而出，向你袭来！")
                
                s.talk(npcs.moqi,"吓！")

                local isWin = s.battle("楚家庄_漫游_黑衣人") --todo:待配置
                if isWin then
                    s.talk(npcs.moqi,"唔……好险，这里怎么会藏着一只伥怪？而且这伥怪的装束有些不同寻常，倒像个江湖人所化。")
                    
                    local opt = {}
                    local showConds = {}
                    
                    ::A::
                    if flags.dg_xiong_shangyi == 1 and flags.dg_xiong_kuzi == 1 and flags.dg_xiong_xuezi == 1 then
                        

                        flags.dg_main_xiong = 1
                        flags.dg_main_step = flags.dg_main_step + 1
                        
                        return
                    else
                        opt = {}
                        if flags.dg_xiong_shangyi == 0 then
                            opt[1] = "探查上衣"
                        end

                        if flags.dg_xiong_kuzi == 0 then
                            opt[2] = "探查裤子"
                        end

                        if flags.dg_xiong_xuezi == 0 then
                            opt[3] = "检查靴子"
                        end
                        
                        showConds = {}
                        showConds[1] = true
                        showConds[2] = true
                        showConds[3] = true

                        ret = s.selectTalkExWithCondition(npcs.moqi, "……", opt,showConds)

                        if ret == 1 then
                            s.talk(npcs.moqi,"找到一只钱袋，里面有一些铜钱……")--todo:不知道该投放什么
                            flags.dg_xiong_shangyi = 1
                        elseif ret == 2 then
                            s.talk(npcs.moqi,"一张有些褪色的令牌，上面写着“霸拳”二字。")
                            s.talk(npcs.moqi,"看来，这伥怪应该就是“霸拳”熊豪所化，不过他此前究竟遭遇了什么？老黎又为何想要杀他？")

                            itemWidget:addItem(itemData.lingpai,1)
                            flags.dg_xiong_kuzi = 1
                        elseif ret == 3 then
                            s.talk(npcs.moqi,"里面有一些金属残渣，色泽与地宫里的那些机关怪兽类似，难道这熊豪也曾去过地宫？")
                            
                            flags.dg_xiong_xuezi = 1
                        else
                            print("熊豪事件出bug了")
                        end
                        goto A
                        
                    end
                    
                else
                    s.blackScreen()
                    s.talk(npcs.moqi,"（看来现在还不是时候……）")
                    s.lightScreen()
                end
            end
        end
    else
        s.aside("这里有一间小屋。")
    end
end


function transportShanlu()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_风雪神道_山路.unity", "位置/Cliff")
end