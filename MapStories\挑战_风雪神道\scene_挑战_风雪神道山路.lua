
---@type 挑战_风雪神道山路
local context = require("MapStories/挑战_风雪神道/scene_挑战_风雪神道山路_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_挑战_风雪神道
local flags = context.flags

-- local extensions = require 'MapStories/挑战_风雪神道/extension'
-- -- ---@type StringWidget
-- -- local stringWidget = extensions.widgets.StringWidgetInfo.widget
-- -- ---@type 挑战_风雪神道_StringItemData
-- -- local stringItemData = extensions.widgets.StringWidgetInfo.items
-- ---@type ItemWidget
-- local itemWidget = extensions.widgets.ItemWidgetInfo.widget
-- ---@type 挑战_风雪神道_ItemData
-- local itemData = extensions.widgets.ItemWidgetInfo.items

--获取大世界的flag
---@type RpgMapApi
local rapi = require("RpgMapApi")
---@type RpgMapFlags
local worldFlags = require("RpgMap/flags")
rapi.loadFlags(worldFlags)

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    local isTeleport = false
    --如果是传送，就不能setPos
    if(args ~= nil and args:ContainsKey("IsTransport"))then
        isTeleport = true
    end

    if(not isTeleport)then
        s.setPos(npcs.zhujue,pos.start)
    end
    
    s.playMusic("凌云惊雪")

    --存在大世界副本支线任务：遗失的靴子 时，显示该交互点
    if(worldFlags.fxsd_ysdxz_shoe_findStatus == 1)then
        s.setActive(objs.lost_shoe,true)
    end

    refreshDungeon()

    s.readyToStart()
end

--刷新副本信息
function refreshDungeon()
    -- s.setMapStatus("补给数量", flags.trash)
    -- s.setMapStatus("资源数量", flags.resource)
    -- itemWidget:setItem(itemData.TrashDone, flags.trash)
    -- itemWidget:setItem(itemData.ResourceDone, flags.resource)

    --重新载入场景物体，根据flag去处理每个gameobject是否是active
    -- for k,v in pairs(objs) do
    --     --print(k)
    --     --print(v)
    --     --处理垃圾点
    --     if(string.find(k,"trash") ~= nil)then
    --         s.setActive(v,flags[k] == 0)
    --     end
    -- end

    --本次副本内是否击败了Boss2
    if(flags.boss2Defeated == 1) then
        s.setActive(npcs.boss2,false)
    end

    --是否播放过Boss2战前演出
    if(s.getChallengeMapPersistentFlag("挑战_风雪神道","播放过Boss2战前演出") == 0)then
        s.setPos(npcs.boss2,pos.pos_boss_1)
        s.animState(npcs.boss2, "BSquat_Loop",true)
    end

    --处理传送点
    s.setActive(objs.teleport,flags.boss2Defeated == 1)
end

--Boss2战斗
function boss2Trigger()
    --是否播放过Boss2战前演出
    if(s.getChallengeMapPersistentFlag("挑战_风雪神道","播放过Boss2战前演出") == 0)then
        --没有就播放一次性演出
        s.cameraAsync(cameras.camera_boss1,true,true,blendHintEnum.EaseInOut,1,true,true)
        s.moveAsync(npcs.zhujue,pos.pos_boss_2,4,true)
        s.animState(npcs.boss2, "BSquat_Loop",false)
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss2战前对话_1", nil, 0)
        s.turnTo(npcs.boss2,npcs.zhujue,true)
        s.turnTo(npcs.zhujue,npcs.boss2)
        s.animState(npcs.boss2, "BShock_Loop",true,false,0.5,false,false,1.5,true)
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss2战前对话_2", nil, 0)
        s.animState(npcs.boss2, "BShock_Loop",false,false,0.5,false,false,1.5,true)
        s.animState(npcs.boss2, "BDeny",true)
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss2战前对话_3", nil, 0)
        s.setChallengeMapPersistentFlag("挑战_风雪神道","播放过Boss2战前演出",1)
        s.camera(cameras.camera_boss1,false)
    end

    --local battleTb = {}
    -- battleTb["StructureFire"] = flags.StructureFire
    -- battleTb["StructureFood"] = flags.StructureFood
    -- battleTb["StructureAlcohol"] = flags.StructureAlcohol
    -- battleTb["StructureClothes"] = flags.StructureClothes
    -- battleTb["StructureKnifeStone"] = flags.StructureKnifeStone
    -- battleTb["StructureMap"] = flags.StructureMap
    -- battleTb["StructurePet"] = flags.StructurePet
    -- battleTb["StructureEnhance"] = flags.StructureEnhance

    --触发战斗
    local isWin,bResult = s.challengeMapBattle("102")
    if(isWin)then
        flags.boss2Defeated = 1

        --一次性演出
        if(s.getChallengeMapPersistentFlag("挑战_风雪神道","击败过Boss2") == 0)then
            --是则播放演出
            --演出前准备，处理首次战斗失败后，继续游戏的情况
            s.camera(cameras.camera_boss1,true)
            s.setPos(npcs.boss2,pos.pos_boss_1)
            s.setPos(npcs.zhujue,pos.pos_boss_2)
            s.turnTo(npcs.boss2,npcs.zhujue,true)
            s.turnTo(npcs.zhujue,npcs.boss2,true)

            s.animState(npcs.boss2, "BHurt_Loop",true)
            s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss2战后对话", nil, 0)
            s.animState(npcs.boss2, "BHurt_Loop",false)
            s.moveAsync(npcs.boss2,pos.pos_boss_3,4,true)
            s.wait(2)
            s.camera(cameras.camera_boss1,false,true,blendHintEnum.EaseInOut,1,true,true)
        end

        --重复演出
        s.setActive(npcs.boss2,false)
        
        --刷出传送点
        s.setActive(objs.teleport,true)
    else
        s.setPos(npcs.boss2,pos.pos_boss)
    end
end

--前往山脚
function enterBottom()
    s.changeMap("Assets/GameScenes/游历场景/挑战_风雪神道/挑战_风雪神道山脚.unity", "位置/teleport")
end

--前往山洞
function enterCave()
    s.changeMap("Assets/GameScenes/游历场景/挑战_风雪神道/挑战_风雪神道山洞.unity", "位置/start")
end

--大世界副本任务：遗失的靴子
function find_shoe()
    s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_遗失的靴子对话", nil, 0)
    s.setActive(objs.lost_shoe,false)

    --改变大世界任务：遗失的靴子 变量
    s.setRpgMapFlag("fxsd_ysdxz_shoe_findStatus",2)

    --改变大世界任务：遗失的靴子 状态
    if s.getCurrentTaskStep("遗失的靴子") == "helpPolice" then
        s.setTaskStep("遗失的靴子","submitShoesToPolice")
    end

    s.changeItemCount("火阳丹",1)
    --发放靴子
    s.changeItemCount("遗失的靴子_靴子",1)
end

-- --捡垃圾
-- function catchTrash(trashKey)
--     s.setActive("物体/" .. trashKey,false)

--     --50%的概率获得补给，50%的概率获得资源
--     local rand = math.random(1,100)
--     if(rand <= 50)then
--         --30%的概率获得2份，70%的概率获得1份
--         local rand2 = math.random(1,100)
--         if(rand2 <= 30)then
--             s.popInfo("获得补给 × 2!")
--             flags.trash = flags.trash + 2
--             s.setMapStatus("补给数量", flags.trash)
--             itemWidget:addItem(itemData.TrashDone, 2)
--         else
--             s.popInfo("获得补给 × 1!")
--             flags.trash = flags.trash + 1
--             s.setMapStatus("补给数量", flags.trash)
--             itemWidget:addItem(itemData.TrashDone, 1)
--         end
--     else
--         --30%的概率获得2份，70%的概率获得1份
--         local rand2 = math.random(1,100)
--         if(rand2 <= 30)then
--             s.popInfo("获得资源 × 2!")
--             flags.resource = flags.resource + 2
--             s.setMapStatus("资源数量", flags.resource)
--             itemWidget:addItem(itemData.ResourceDone, 2)
--         else
--             s.popInfo("获得资源 × 1!")
--             flags.resource = flags.resource + 1
--             s.setMapStatus("资源数量", flags.resource)
--             itemWidget:addItem(itemData.ResourceDone, 1)
--         end
--     end
    
--     flags[trashKey] = -1
-- end

-- --通用小怪战斗
-- function enemyBattle(roleKey)
--     local battleTb = {}
--     battleTb["StructureFire"] = flags.StructureFire
--     battleTb["StructureFood"] = flags.StructureFood
--     battleTb["StructureAlcohol"] = flags.StructureAlcohol
--     battleTb["StructureClothes"] = flags.StructureClothes
--     battleTb["StructureKnifeStone"] = flags.StructureKnifeStone
--     battleTb["StructureMap"] = flags.StructureMap
--     battleTb["StructurePet"] = flags.StructurePet
--     battleTb["StructureEnhance"] = flags.StructureEnhance
--     --如果有风雪神道地图(flags.StructureMap>0)，可以使用1补给跳过战斗
--     if(flags.StructureMap > 0 and flags.trash >= 1)then
--         local ret = s.selectTalk(npcs.zhujue, "是否使用1补给跳过战斗？", {"跳过", "不跳过"})
--         if ret == 0 then
--             flags.trash = flags.trash - 1
--             s.setMapStatus("补给数量", flags.trash)
--             itemWidget:removeItem(itemData.TrashDone, 1)
--             enemyBattleEnd(roleKey)
--             return
--         end
--     end
    
--     --否则继续进入战斗
--     local isWin,bResult = s.battle("副本_风雪神道_战斗_" .. roleKey, battleTb)
--     if(isWin)then
--         s.popInfo("获得补给 × 1,资源 × 1!")
--         flags.trash = flags.trash + 1
--         s.setMapStatus("补给数量", flags.trash)
--         flags.resource = flags.resource + 1
--         s.setMapStatus("资源数量", flags.resource)
--         itemWidget:addItem(itemData.TrashDone, 1)
--         itemWidget:addItem(itemData.ResourceDone, 1)

--         s.openChallengeMapChest(nil,"farmfield_FengXueShenDao_Enemy_Common")
--     end
-- end