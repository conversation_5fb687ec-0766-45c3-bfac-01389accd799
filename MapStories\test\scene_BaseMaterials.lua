
---@type BaseMaterials
local context = require("MapStories/test/scene_BaseMaterials_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_test
local flags = context.flags --[[@as flags_test]]
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
--每次载入调用
function start()
    --s.runOnce(first_time_into_map) --只执行一次
end

--第一次进入地图
function first_time_into_map()
    -- 填写逻辑
end

function hello()
    s.aside("你好")
end