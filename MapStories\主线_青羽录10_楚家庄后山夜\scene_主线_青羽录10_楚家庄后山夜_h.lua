--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/主线_青羽录10_楚家庄后山夜/flags_主线_青羽录10_楚家庄后山夜")


---@class 角色_主线_青羽录10_楚家庄后山夜
local npcs = {
    --- "荆成"
    zhujue = "角色/zhujue",
    --- "楚青"
    ChuQing = "角色/ChuQing",
}

---@class 物体_主线_青羽录10_楚家庄后山夜
local objs = {
    --- "触发器_播放Timeline"
    Trigger1 = "物体/Trigger1",
}

---@class 相机_主线_青羽录10_楚家庄后山夜
local cameras = {
}

---@class 位置_主线_青羽录10_楚家庄后山夜
local pos = {
    pos_start = "位置/pos_start",
}

---@class 资产_主线_青羽录10_楚家庄后山夜
local assets = {
}

---@class 动作_主线_青羽录10_楚家庄后山夜
local animationClips = {
}

---@class 主线_青羽录10_楚家庄后山夜
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
