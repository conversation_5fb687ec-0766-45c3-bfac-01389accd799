
---@type 挑战_楚家庄后山
local context = require("MapStories/挑战_楚家庄New/scene_挑战_楚家庄后山_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_挑战_楚家庄New
local flags = context.flags

---@type RpgMapApi
local rapi = require("RpgMapApi")

local rpgFlags = require("RpgMap/flags")
rapi.loadFlags(rpgFlags)

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.blackScreen()

    local isTeleport = false
    --如果是传送，就不能setPos
    if(args ~= nil and args:ContainsKey("IsTransport"))then
        isTeleport = true
    end

    --如果不是传送，设置主角到初始位置
    if(not isTeleport)then
        s.setPos(npcs.zhujue, pos.start)
    end

    --重复演出
    s.playMusic("tense")
    s.animState(npcs.npc_1, "BDie",true)
    s.animState(npcs.npc_2, "BDie",true)
    s.animState(npcs.npc_3, "BDie",true)
    s.animState(npcs.npc_4, "BDie",true)
    s.animState(npcs.npc_5, "BDie",true)
    s.animState(npcs.npc_6, "BDie",true)
    s.animState(npcs.npc_7, "BDie",true)
    s.animState(npcs.npc_8, "BDie",true)
    s.animState(npcs.npc_9, "BDie",true)
    s.animState(npcs.npc_10, "BDie",true)
    s.animState(npcs.npc_11, "BDie",true)
    s.animState(npcs.npc_12, "BDie",true)

    s.wait(2)
    s.lightScreen()
    
    --一次性内容
    if(s.getChallengeMapPersistentFlag("挑战_楚家庄New","亭子演出") == 0)then
        s.setActive(objs.tingzi, true)
    end
    
    local herb3Count = s.getRpgMapFlag("yfxf_get_medical3")

    if s.getCurrentTaskStep("药方修复") == "getMedical" and herb3Count == 0 then
        s.setActive(objs.trigger_medicinal_3,true)
        s.setActive(objs.medicinal_3,true)
    end

    --处理断线重连
    InitDungeon()

    s.readyToStart()
end

function InitDungeon()
    --处理boss3
    if(flags.boss3 == 1)then
        s.setActive(npcs.boss3,false)
        s.setActive(objs.quit, true)
    end
end

function triggerTingZiShow()
    s.setActive(objs.tingzi, false)
    s.cameraAsync(cameras.tingzi, true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.move(npcs.zhujue,pos.pos_tingzi,2,true)
    s.animState(npcs.zhujue, "BSquat_Loop",true)
    s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_贼寇山洞_亭子对话", nil, 0)
    s.cameraAsync(cameras.tingzi, false,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.animState(npcs.zhujue, "BSquat_Loop",false)
    s.setChallengeMapPersistentFlag("挑战_楚家庄New","亭子演出",1)
end

function triggerRoadShow()
    s.setActive(objs.trigger_road, false)
    s.appendAsyncAside(npcs.zhujue,0,"唉......可怜这些庄内百姓。","","默认")
end

function boss3()
    --重复演出
    s.cameraAsync(cameras.boss3_1, true,true,blendHintEnum.EaseInOut,0,true,true)
    s.turnTo(npcs.zhujue, npcs.boss3)
    s.move(npcs.zhujue,pos.pos_boss_1,4,true)
    s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_贼寇山洞_Boss3战前对话_1", nil, 0)
    s.turnTo(npcs.boss3, npcs.zhujue)
    s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_贼寇山洞_Boss3战前对话_2", nil, 0)

    s.setActive(npcs.boss3,false)
    s.setActive(npcs.npc_8,false)
    s.playTimeline(assets.Timeline_1)
    s.setActive(npcs.boss3,true)
    s.setActive(npcs.npc_8,true)

    local isWin,bResult = s.challengeMapBattle("17")
    if(isWin)then
        s.camera(cameras.boss3_1, false)
        s.setActive(npcs.boss3,false)

        --设置完成副本成就
        if(s.getChallengeMapPersistentFlag("挑战_楚家庄New","邪魔道人成就") == 0)then
            if(bResult.BattleDuration <= 60)then
                s.setChallengeMapPersistentFlag("挑战_楚家庄New","邪魔道人成就",1)
            end
        end

        flags.boss3 = 1

        --显示传送点
        s.setActive(objs.quit, true)
        s.completeChallengeMap()
        
        --清除此场景所有小怪
        enemyRunAway()
        s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_楚家庄_Boss3击败触发逃跑对话", nil, 0)
        
        --击败所有boss后对话
        if(flags.boss1 == 1 and flags.boss2 == 1 and flags.boss3 == 1)then
            s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_贼寇山洞_击败所有Boss对话", nil, 0)
        end

        --结束副本内主线任务
        if s.hasTaskGroup("楚家庄副本内")then
            s.setTaskStep("楚家庄副本内","leaveScene")
        end

        --设置天书flag，记录BOSS
        s.setGuideBookFlag("boss_xiemodaoren")
    else
        s.camera(cameras.boss3_1, false)
    end
end

function enemyRunAway()
    for k,v in pairs(npcs) do
        if(string.find(k,"enemy") ~= nil and flags[k] ~= nil)then
            --只处理未击败的敌人
            if(flags[k] == 0)then
                ---@diagnostic disable-next-line: inject-field-fail
                flags[k] = -1
                s.setActive(v,false)
            end
        end
    end
end

--传送到楚家庄中庭
function transPortToMiddle()
    if(flags.boss3 == 0) then
        s.talk(npcs.zhujue, "还没有击败盘踞在此的敌方头目，不要四处闲逛了。")
        return
    end

    s.changeMap("Assets/GameScenes/游历场景/挑战_楚家庄New/挑战_楚家庄中庭.unity", "位置/teleport1")
end

--离开副本
function quitDungeon()
    s.showConfirmResetChallengeMap()
    if s.getCurrentTaskStep("楚家庄副本内") == "leaveScene" then
        s.setTaskStep("楚家庄副本内","finished")
    end
end

--副本支线2，药方修复
function findMedicinal()
    require("RpgMap/任务线/药方修复").yfxf_getThird()
    
    local herbCount = s.getRpgMapFlag("yfxf_get_medical_count")
    herbCount = herbCount + 1
    s.setRpgMapFlag("yfxf_get_medical_count",herbCount)
    s.setRpgMapFlag("yfxf_get_medical3",1)

    if herbCount >= 3 then
        require("RpgMap/任务线/药方修复").judge_medical_count()

        if s.getCurrentTaskStep("药方修复") == "getMedical" then
            s.setTaskStep("药方修复","feedBackLaoPu")
        end
    end
    
    s.setActive(objs.trigger_medicinal_3,false)
    s.setActive(objs.medicinal_3,false)

end