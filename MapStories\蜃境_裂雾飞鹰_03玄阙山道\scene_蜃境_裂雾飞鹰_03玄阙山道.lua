---@type 蜃境_裂雾飞鹰_03玄阙山道
local context = require("MapStories/蜃境_裂雾飞鹰_03玄阙山道/scene_蜃境_裂雾飞鹰_03玄阙山道_h")

local npcs = context.npcs
local objs = context.objs
local assets = context.assets
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_裂雾飞鹰_03玄阙山道
local flags = context.flags
--不可省略，用于外部获取flags

local newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")



function getFlags(...)
    return flags
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function start()
    print("start called")
    refreshMapStates()
    s.readyToStart(true)
    try_execute_newbieGuide()
end

function try_execute_newbieGuide(...)
    s.nextStep("newbieGuideInLevel")
end


function newbieGuideInLevel()end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.lwfy_05, false)
    s.setActive(npcs.lwfy_06, false)
    s.setActive(npcs.lwfy_07, false)

    s.setActive(npcs.yuqiong, (flags.alyd_step < 3) and s.hasUnlockMemorySlot("lwfy_sideTask_alyd"))
    s.setActive(npcs.laopopo, flags.alyd_step == 1)
    
    -- 点灯谜题场景初始化
    if flags.puzzle_started == 0 then
        -- 初始状态：显示信件和NPC
        s.setActive(objs.puzzleKey_message, true)
        s.setActive(npcs.puzzleKey_oldMan, true)
        s.setActive(objs.noLight_01, true)
        s.setActive(objs.noLight_02, true)
        s.setActive(objs.noLight_03, true)
        s.setActive(objs.noLight_04, true)
        s.setActive(objs.puzzleLight_Trigger01, false)
        s.setActive(objs.puzzleLight_Trigger02, false)
        s.setActive(objs.puzzleLight_Trigger03, false)
        s.setActive(objs.puzzleLight_Trigger04, false)

    elseif flags.puzzle_completed == 1 then
        if flags.puzzle_chest_2_opened == 0 then
            s.setActive(objs.chest_2, true)
        elseif flags.puzzle_chest_2_opened == 1 then
            s.setActive(objs.chest_2, false)
            s.setActive(objs.chest_2_open, true)
            s.soloAnimState(objs.chest_2_open, objAnims.TurnOnDirect)
        end

        -- 确保所有谜题相关物体都被销毁
        s.setActive(objs.puzzleKey_message, false)
        s.setActive(npcs.puzzleKey_oldMan, false)
        s.setActive(objs.noLight_01, false)
        s.setActive(objs.noLight_02, false)
        s.setActive(objs.noLight_03, false)
        s.setActive(objs.noLight_04, false)
        s.setActive(objs.puzzleLight_Trigger01, false)
        s.setActive(objs.puzzleLight_Trigger02, false)
        s.setActive(objs.puzzleLight_Trigger03, false)
        s.setActive(objs.puzzleLight_Trigger04, false)
        -- 确保第四盏灯保持点亮状态
        s.setActive(objs.longLight_04, true)
    else
        -- 进行中状态：根据当前进度显示
        s.setActive(objs.puzzleKey_message, false)
        s.setActive(npcs.puzzleKey_oldMan, false)
        
        -- 根据当前步骤设置触发器和未点亮灯的状态
        if flags.puzzle_current_step >= 1 then
            s.setActive(objs.noLight_01, false)
            s.setActive(objs.longLight_01, true)
            s.setActive(objs.puzzleLight_Trigger01, false)
        else
            s.setActive(objs.noLight_01, true)
            s.setActive(objs.longLight_01, false)
            s.setActive(objs.puzzleLight_Trigger01, true)
        end
        
        if flags.puzzle_current_step >= 2 then
            s.setActive(objs.noLight_02, false)
            s.setActive(objs.longLight_02, true)
            s.setActive(objs.puzzleLight_Trigger02, false)
        else
            s.setActive(objs.noLight_02, true)
            s.setActive(objs.longLight_02, false)
            s.setActive(objs.puzzleLight_Trigger02, true)
        end
        
        if flags.puzzle_current_step >= 3 then
            s.setActive(objs.noLight_03, false)
            s.setActive(objs.longLight_03, true)
            s.setActive(objs.puzzleLight_Trigger03, false)
        else
            s.setActive(objs.noLight_03, true)
            s.setActive(objs.longLight_03, false)
            s.setActive(objs.puzzleLight_Trigger03, true)
        end
        
        if flags.puzzle_current_step >= 4 then
            s.setActive(objs.noLight_04, false)
            s.setActive(objs.longLight_04, true)
            s.setActive(objs.puzzleLight_Trigger04, false)
        else
            s.setActive(objs.noLight_04, true)
            s.setActive(objs.longLight_04, false)
            s.setActive(objs.puzzleLight_Trigger04, true)
        end
    end
    
    s.setActive(objs.lwfy_1003, not s.hasUnlockMemorySlot("lwfy_1003"))
    s.removeAllGameMapGuides()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 4) then
        taskStep = "forwardToExplore"
    elseif (step == 4) then
        taskStep = "leaveXuanQueShanDao"
    end
    s.setTaskStep("裂雾飞鹰玄阙山道", taskStep)


    reset_all()
    if step == 0 then
        ----第一次进入场景, 这里加入入场演出
        s.blackScreen()
        s.setActive(npcs.enemy_1,false)
        s.setPos(npcs.Player,pos.zhujue_start_pos)
        s.wait(0.5)
        s.lightScreen()
        s.moveAsync(npcs.Player,pos.zhujue_start_pos1,1,true)
        s.wait(1)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_进入")
        s.blackScreen()
        s.wait(0.5)
        s.camera(cameras.cam1,true)
        s.lightScreen()
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_进入1")
        s.blackScreen()
        s.setActive(npcs.enemy_1,true)
        s.wait(0.5)
        s.camera(cameras.cam1,false)
        s.lightScreen()
        s.setTaskStep("裂雾飞鹰玄阙山道","forwardToExplore")
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.lwfy_05, 0.8)
    elseif step == 2 then
        level_guide_to(npcs.lwfy_06, 0.8)
    elseif step == 3 then
        level_guide_to(npcs.lwfy_07, 0.8)
    else
        --do nothing
        level_guide_to(objs.exit_1, 0.8)
        level_guide_to(objs.exit, 0.8)
    end
end

----------------------OVERRIDE----------------------------------

function lwfy_01_test()
    flags.lwfy_05_playing = 0
    flags.level_step = 1
    refreshMapStates()
end

function lwfy_05()
    executeMemSlot("lwfy_05", quick_play_avg)
end

function lwfy_06()
    executeMemSlot("lwfy_06", quick_play_avg)
end

function lwfy_07()
    executeMemSlot("lwfy_07", quick_play_avg,function()
    s.setTaskStep("裂雾飞鹰玄阙山道","leaveXuanQueShanDao")
    next_level_step(true)
    end)
end

function exitScene()
    print("离开场景")
    capi.exitMemoryScene("裂雾飞鹰")
end

function alyd()
    if(flags.alyd_step == 0)then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_暗流涌动_1")
        s.setTaskStep("暗流涌动2","findGrandMa")
        flags.alyd_step = 1
        s.setActive(npcs.laopopo,true)
    elseif(flags.alyd_step == 1)then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "重复")
    else
        flags.alyd_step = 3
        s.finishTask("40052002","20")
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_暗流涌动_3")
        s.blackScreen()
        s.setActive(npcs.yuqiong,false)
        s.wait(0.5)
        s.lightScreen()
        capi.finishMemorySlot("lwfy_sideTask_alyd2")
    end
end

function test()
    s.setTaskStep("暗流涌动2","active")
end

function alyd2()
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_暗流涌动_2")
    s.blackScreen()
    s.setActive(npcs.laopopo,false)
    s.wait(0.5)
    s.lightScreen()
    s.setTaskStep("暗流涌动2","backToYuQiong")
    flags.alyd_step = 2
end

-----------------------------点灯谜题----------------------------
function puzzleKey_message()
    local r = s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_谜题part1线索")
    if r == 1 then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_谜题part1线索_拆开")
        s.blackScreen()
        s.setActive(objs.puzzleKey_message, false)
        s.setActive(npcs.puzzleKey_oldMan, false)
        s.setActive(objs.puzzleLight_Trigger01, true)
        s.setActive(objs.puzzleLight_Trigger02, true)
        s.setActive(objs.puzzleLight_Trigger03, true)
        s.setActive(objs.puzzleLight_Trigger04, true)
        flags.puzzle_started = 1
        flags.puzzle_current_step = 0  -- 确保初始步骤为1
        s.lightScreen()
    elseif r == 2 then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_谜题part1线索_不拆")
    end
end

function puzzleLight_Trigger01()
    if flags.puzzle_started == 0 then return end
    if flags.puzzle_current_step > 1 then
        resetPuzzle()
        return
    end
    s.setActive(objs.puzzleLight_Trigger01, false)
    s.setActive(objs.noLight_01, false)
    s.setActive(objs.longLight_01, true)
    flags.puzzle_current_step = 1
end

function puzzleLight_Trigger02()
    if not flags.puzzle_started then return end
    if flags.puzzle_current_step > 2 then
        resetPuzzle()
        return
    end
    s.setActive(objs.puzzleLight_Trigger02, false)
    s.setActive(objs.noLight_02, false)
    s.setActive(objs.longLight_02, true)
    flags.puzzle_current_step = 2
end

function puzzleLight_Trigger03()
    if not flags.puzzle_started then return end
    if flags.puzzle_current_step > 3 then
        resetPuzzle()
        return
    end
    s.setActive(objs.puzzleLight_Trigger03, false)
    s.setActive(objs.noLight_03, false)
    s.setActive(objs.longLight_03, true)
    flags.puzzle_current_step = 3
end

function puzzleLight_Trigger04()
    if not flags.puzzle_started then return end
    -- 添加检查，如果谜题已完成，直接返回
    if flags.puzzle_completed == 1 then return end
    
    if flags.puzzle_current_step > 4 then
        resetPuzzle()
        return
    end
    
    -- 只有当当前步骤为3时（即前三盏灯都已点亮），才允许点亮第四盏灯
    if flags.puzzle_current_step == 3 then
        -- 根据性别播放不同的动画
        if s.isMale() then                 
            s.setActive(objs.puzzleLight_Trigger04, false)
            s.setActive(objs.noLight_04, false)
            s.setActive(objs.longLight_04, true)
            flags.puzzle_current_step = 4
            s.playTimeline(assets.puzzle_complete, false)
            s.lightScreen()
        else              
            s.setActive(objs.puzzleLight_Trigger04, false)
            s.setActive(objs.noLight_04, false)
            s.setActive(objs.longLight_04, true)
            flags.puzzle_current_step = 4
            s.playTimeline(assets.puzzle_complete_Nvzhu, false)
            s.lightScreen()
        end

        completePuzzle()
    else
        resetPuzzle()
    end
end

function resetPuzzle()
    flags.puzzle_error_count = flags.puzzle_error_count + 1
    
    if flags.puzzle_error_count == 1 then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_谜题part2点错顺序_第一次错")
    elseif flags.puzzle_error_count == 2 then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_谜题part2点错顺序_第二次错")
    else
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_谜题part2点错顺序_第三次错")
    end
    
    -- 重置所有已点亮的灯
    s.setActive(objs.longLight_01, false)
    s.setActive(objs.longLight_02, false)
    s.setActive(objs.longLight_03, false)
    s.setActive(objs.longLight_04, false)
    -- 恢复所有未点亮的灯和触发器
    s.setActive(objs.noLight_01, true)
    s.setActive(objs.noLight_02, true)
    s.setActive(objs.noLight_03, true)
    s.setActive(objs.noLight_04, true)
    s.setActive(objs.puzzleLight_Trigger01, true)
    s.setActive(objs.puzzleLight_Trigger02, true)
    s.setActive(objs.puzzleLight_Trigger03, true)
    s.setActive(objs.puzzleLight_Trigger04, true)
    flags.puzzle_current_step = 1
end

function completePuzzle()
    -- 清理所有谜题相关物体
    s.setActive(objs.noLight_01, false)
    s.setActive(objs.noLight_02, false)
    s.setActive(objs.noLight_03, false)
    s.setActive(objs.noLight_04, false)
    s.setActive(objs.puzzleLight_Trigger01, false)
    s.setActive(objs.puzzleLight_Trigger02, false)
    s.setActive(objs.puzzleLight_Trigger03, false)
    s.setActive(objs.puzzleLight_Trigger04, false)
    s.setActive(npcs.puzzleKey_oldMan, false)
    s.setActive(objs.puzzleKey_message, false)
    flags.puzzle_completed = 1
    -- 生成奖励
    s.setActive(objs.chest_2, flags.puzzle_chest_2_opened == 0)
end

function lwfy3_puzzle_Box_01()
    if flags.puzzle_chest_2_opened == 0 then
        -- 调用通用的宝箱处理函数
        local commonTools = require("MapStories/MapStoryCommonTools")
        commonTools.roamMapChest("lwfy3_puzzle_Box_01")
        
        -- 更新宝箱状态
        flags.puzzle_chest_2_opened = 1
        -- 刷新地图状态
        --refreshMapStates()
    end
end

--[[function puzzleReward_oldMan()
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_03玄阙山道", "蜃境_裂雾飞鹰_03玄阙山道_点灯老人再次出现")  
end--]]

-- 添加宝箱打开的处理函数
--[[function chest_2_open()
    flags.chest_2_opened = 1
    -- 宝箱打开后，宝箱和NPC仍然保持显示
    s.setActive(objs.chest_2, true)
    -- s.setActive(npcs.puzzleReward_oldMan, true)
end--]]