---@type 挑战_楚家庄
local context = require("MapStories/挑战_楚家庄/scene_挑战_楚家庄_h")
local s = context.sapi
local flags = context.flags
s.loadFlags(flags)


---@class 挑战_楚家庄_StringItemData
local stringItemData = {
    fire = {key = "initFireLv",showName="火势等级",desc = "测试参数名1"},
    battletotalTime = {key = "initBattleTimeTotal",showName="战斗总时序",desc = "测试参数名2"},
    survivor = {key = "initSurvivor",showName="救出人数",desc = "测试参数名3"},
}

local stringWidget = StringWidget:new(stringItemData,flags)

local stringWidgetInfo = {
    title = "副本信息",
    desc = "楚家庄已经笼罩在火焰中了，战斗时序越长，火势等级越高，也会提高在战斗中我方侠客持续受到的伤害。\n",
    items = stringItemData,
    widget = stringWidget
}

extensions = {
    useWidgets = {"StringWidget","ItemWidget"},
    widgets = {
        StringWidgetInfo = stringWidgetInfo,
        ItemWidgetInfo = nil
    }
}

context.sapi.syncExtendsInfo(extensions)
return extensions