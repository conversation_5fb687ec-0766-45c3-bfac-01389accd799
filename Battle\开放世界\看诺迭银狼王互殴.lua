local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情诺迭", 45, team, bcapi.Vector2(-6, -1), 0.1)

        local sTable = {"银针返命","风露杏雪","大藏心经","吐纳法","不动如山","铁甲阵法","霸王怒吼","梧叶舞秋风",
        "豪云剑魂","血沸剑魂","如归剑魂","青莲剑魂","朔风吹雪","分身解厄","迷风剪影"}

        local index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], 6 + math.random(1, 3), team)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], 6 + math.random(1, 3), team)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], 6 + math.random(1, 3), team)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], 6 + math.random(1, 3), team)

        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], 6 + math.random(1, 3), eTeam)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], 6 + math.random(1, 3), eTeam)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], 6 + math.random(1, 3), eTeam)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], 6 + math.random(1, 3), eTeam)
    end)
end

function start()
    
end