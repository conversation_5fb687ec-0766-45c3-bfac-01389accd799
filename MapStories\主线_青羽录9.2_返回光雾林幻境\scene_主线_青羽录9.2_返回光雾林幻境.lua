
---@type 主线_青羽录9.2_返回光雾林幻境
local context = require("MapStories/主线_青羽录9.2_返回光雾林幻境/scene_主线_青羽录9.2_返回光雾林幻境_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local assets = context.assets
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

local nani = "蜃境/主线/青羽录_醒"

local function playNani(tag,transitionTime,alpha)
    return s.runAvgTag(nani, tag,transitionTime,alpha)
end


s.loadFlags(context.flags)
---@type flags_主线_青羽录9.2_返回光雾林幻境
local flags = context.flags
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
--每次载入调用
function start()
    s.setActive(npcs.chuqing,true)
    s.setPos(npcs.chuqing,pos.pos_chuqing)
    s.setActive(objs.trigger_tingzi,true)
    s.setActive(npcs.zhujue,false)
    s.setPos(npcs.zhujue,pos.pos_start)

    s.playMusic("凌雪悲伤1",0,nil,1)
    s.camera(cameras.cam_start,true,true,blendHintEnum.Cut,0,true,true)


    s.setActive(npcs.zhujue,true)
    
    s.readyToStart(false)
    s.playTimeline(assets.gwl_1,false)

    s.lightScreen(2)
    s.camera(cameras.cam_start,true,true,blendHintEnum.Cut,0,true,true)
    playNani("开始_1",nil,0)
    
    s.animState(npcs.zhujue,"BDeny",true)
    playNani("开始_2",nil,0)
    s.animState(npcs.zhujue,"BDeny",false)

    s.wait(0.5)
    s.animState(npcs.zhujue,roleAnims.BGesture,true)
    playNani("开始_3",nil,0)
    s.animState(npcs.zhujue,roleAnims.BGesture,false)  
    s.wait(0.5)
    s.camera(cameras.cam_tingzi_1,true,true,blendHintEnum.Custom,2,true,true)
    playNani("开始_4",nil,0)
    s.camera(cameras.cam_start,true,true,blendHintEnum.Custom,1.5,true,true)
    playNani("开始_5",nil,0)
    s.cameraAsync(cameras.cam_follow_1,true,true,blendHintEnum.Custom,1.5,true,true)
end

function testEndStory()
    s.setPos(npcs.zhujue,pos.pos_test)
    s.cameraAsync(cameras.cam_follow,true,true,blendHintEnum.Custom,1.5,true,true)
end


function test_timeline()
    s.camera(cameras.cam_tingzi_1,true,true,blendHintEnum.Custom,1,true,true)
end

function tingzi(arg)
    arg = tonumber(arg)
    local cam_name = "cam_tingzi_"..arg
    local cam_name = cameras[cam_name]

    local follow_name = "cam_follow_"..arg
    local follow_name = cameras[follow_name]

    s.camera(cam_name,true,true,blendHintEnum.Custom,1,true,true)
    playNani("亭子_1",nil,0)
    s.wait(0.5)
    s.camera(follow_name,true,true,blendHintEnum.Custom,1,true,true)
    playNani("亭子_2",nil,0)
    s.camera(cam_name,true,true,blendHintEnum.Custom,1,true,true)
    playNani("亭子_3",nil,0)
    s.camera(follow_name,true,true,blendHintEnum.Custom,1,true,true)
end

function tingzi_end()
    s.camera(cameras.cam_final,true,true,blendHintEnum.Custom,1,true,true)
    playNani("亭子_终",nil,0)
    s.camera(cameras.cam_follow,true,true,blendHintEnum.Custom,1,true,true)
end

function tele_l_1()--true
    s.blackScreen(1)
    s.setPos(npcs.zhujue,pos.pos_tele_2_r)
    s.camera(cameras.cam_follow_2,true,true,blendHintEnum.Custom,1,true,true)
    s.wait(0.5)
    s.lightScreen(2)

    if flags.guide_open_step==1 then
        s.removeGameMapGuide(objs.left_1)
        s.addGameMapGuide(objs.right_2)
        flags.guide_open_step=2
    end
end

function tele_r_1()--false
    s.blackScreen(1)
    s.setPos(npcs.zhujue,pos.pos_tele_1_l)
    s.camera(cameras.cam_follow_1,true,true,blendHintEnum.Custom,1,true,true)
    s.wait(0.5)
    s.lightScreen(2)
    playNani("传送错误_1",nil,0)

    wrongTime()
end

function tele_l_2()--false
    s.blackScreen(1)
    s.setPos(npcs.zhujue,pos.pos_tele_1_l)
    s.camera(cameras.cam_follow_1,true,true,blendHintEnum.Custom,1,true,true)
    s.wait(0.5)
    s.lightScreen(2)
    playNani("传送错误_1",nil,0)

    wrongTime()
end

function tele_r_2()--true
    s.blackScreen(1)
    s.setPos(npcs.zhujue,pos.pos_tele_3_l)
    s.camera(cameras.cam_follow_3,true,true,blendHintEnum.Custom,1,true,true)
    s.wait(0.5)
    s.lightScreen(2)

    if flags.guide_open_step==2 then
        s.removeGameMapGuide(objs.right_2)
        s.addGameMapGuide(objs.right_3)
        flags.guide_open_step=3
    end

    wrongTime()
end

function tele_l_3()--false
    s.blackScreen(1)
    s.setPos(npcs.zhujue,pos.pos_tele_1_l)
    s.camera(cameras.cam_follow_1,true,true,blendHintEnum.Custom,1,true,true)
    s.wait(0.5)
    s.lightScreen(2)
    playNani("传送错误_1",nil,0)

    wrongTime()
end

function tele_r_3()--true
    s.blackScreen(1)
    s.setPos(npcs.zhujue,pos.pos_tele)
    s.camera(cameras.cam_follow,true,true,blendHintEnum.Custom,1,true,true)
    s.wait(0.5)
    s.lightScreen(2)

    if flags.guide_open_step==3 then
        s.removeGameMapGuide(objs.right_3)
        flags.guide_open_step=4
    end
end

function wrongTime()
    flags.wrong_time=flags.wrong_time+1
    if flags.wrong_time == 3 then
        tipsOpen()
    end
end

function tipsOpen()
    s.wait(1)
    playNani("楚青提示",nil,0)
    s.animState(npcs.zhujue,"BShock_Loop",true)
    playNani("荆成惊吓_1",nil,0)
    s.animState(npcs.zhujue,"BShock_Loop",false)

    flags.guide_open_step=1
    s.popInfo("神秘人向你指引了方向！")
    s.addGameMapGuide(objs.left_1)
end

function final()
    s.setActive(objs.trigger_tingzi,false)
    s.camera(cameras.cam_final,true,true,blendHintEnum.Custom,2,true,true)

    s.animState(npcs.zhujue,roleAnims.BGesture,true)
    playNani("走出幻境_1",nil,0)
    s.animState(npcs.zhujue,roleAnims.BGesture,false)

    s.wait(0.5)
    playNani("走出幻境_2",nil,0)
    s.camera(cameras.cam_follow,true,true,blendHintEnum.Custom,0.5,true,true)

    s.animState(npcs.zhujue,"BShock_Loop",true)
    playNani("走出幻境_3",nil,0)
    s.animState(npcs.zhujue,"BShock_Loop",false)

    s.wait(0.5)
    s.animState(npcs.zhujue,"BDeny",true)
    playNani("走出幻境_4",nil,0)
    s.animState(npcs.zhujue,"BDeny",false)
end

function getthrough()
    s.setActive(objs.trigger_through,false)
    -- s.camera(cameras.cam_through,true,true,blendHintEnum.Custom,1,true,true)
    -- s.animState(npcs.zhujue,"BGesture",true)
    -- s.talk(npcs.zhujue,"前面应该就是来时的路了。")
    -- s.animState(npcs.zhujue,"BGesture",false)
    -- s.camera(cameras.cam_follow,true,true,blendHintEnum.Custom,1,true,true)
    -- s.wait(0.5)
    -- s.talk(npcs.zhujue,"走吧。")
    s.appendAsyncAside(npcs.jingcheng,1,"前面应该就是来时的路了。",nil,"默认")


    s.playMusic("autumn",5,nil,3)

end

function stone()
    if flags.taijiyin==0 then
        playNani("石头_1",nil,0)
        s.wait(0.5)
        s.animState(npcs.zhujue,"BDeny",true)
        --s.cameraAsync(cameras.cam_stone,true,true,blendHintEnum.Custom,8,true,true)
        playNani("石头_2",nil,0)
        s.animState(npcs.zhujue,"BDeny",false)
        --s.camera(cameras.cam_follow,true,true,blendHintEnum.Custom,1.5,true,true)

        flags.taijiyin=1
    else
        playNani("石头_3",nil,0)
        --s.camera(cameras.cam_follow,true,true,blendHintEnum.Custom,1.5,true,true)
    end
end

function pipa()
    if flags.pipa==0 then
        s.camera(cameras.cam_pipa,true,true,blendHintEnum.Custom,1.5,true,true)

        s.wait(0.5)
        playNani("枇杷_1",nil,0)
    
        s.cameraAsync(cameras.cam_follow,true,true,blendHintEnum.Custom,4,true,true)
        s.animState(npcs.zhujue,"BDeny",true)
        playNani("枇杷_2",nil,0)
        s.animState(npcs.zhujue,"BDeny",false)
        s.wait(0.5)
    
        flags.pipa=1
    else
        s.camera(cameras.cam_pipa,true,true,blendHintEnum.Custom,1.5,true,true)
        playNani("枇杷_3",nil,0)
        s.camera(cameras.cam_follow,true,true,blendHintEnum.Custom,1.5,true,true)
    end


end 

function finalend()
    s.playMusic("凌雪悲伤2",5,nil,3)
    s.setActive(objs.trigger_final,true)
    s.cameraAsync(cameras.cam_end,true,true,blendHintEnum.Custom,3,true,true)
    s.move(npcs.zhujue,pos.pos_player,1,true)
    
    s.wait(0.5)
    s.setActive(npcs.chuqing,false)
    s.playTimeline(assets.gwl_2)
    s.setNewbieFlags("chapter_qyl_16_finished",1)
    s.finishInfiniteStory(true)
end

function tree()
    playNani("树_1",nil,0)
end

function tree_1()
    playNani("树_2",nil,0)
end

function tree_2()
    playNani("树_3",nil,0)
end