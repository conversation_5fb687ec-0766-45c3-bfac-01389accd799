local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.hide_ui("疗伤")
    createRole()
end

function start()
    bcapi.add_timer_trigger(1, checkLose)
end

function createRole()
    bcapi.async_call(function()
        --给玩家创建角色
        bcapi.create_join_role("副本_玄阙地宫外层_特殊_机关犬", 50, team, bcapi.Vector2(-6, 0), 0)
            --给敌人加策略卡
        bcapi.add_strategy_card("光明圣火", 5, eTeam)
        bcapi.add_strategy_card("藏锋积锐", 5, eTeam)
        bcapi.add_strategy_card("银针返命", 5, eTeam)
        bcapi.add_strategy_card("朔风吹雪", 5, eTeam)
    end)
end

function checkLose()
    bcapi.add_condition_trigger("[%c->teammate:副本_玄阙地宫外层_特殊_机关犬%][<]1", 0, lose)
end

function lose()
    bcapi.async_call(function()
        bcapi.lose()
    end)
end