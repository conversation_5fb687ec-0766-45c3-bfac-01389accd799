local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_墨七", 61, bcapi.get_team(0), bcapi.Vector2(-5, 0), 0)
        bcapi.create_join_role("剧情_阿凉", 61, bcapi.get_team(0), bcapi.Vector2(-2, 0), 0)
    end)
end

function start()
    StartTalk()
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("墨七", "是机关兽！快躲开！")
        
        bcapi.resume_and_show_ui()
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
