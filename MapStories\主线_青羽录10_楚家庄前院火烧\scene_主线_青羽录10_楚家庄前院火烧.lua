
---@type 主线_青羽录10_楚家庄前院火烧
local context = require("MapStories/主线_青羽录10_楚家庄前院火烧/scene_主线_青羽录10_楚家庄前院火烧_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_主线_青羽录10_楚家庄前院火烧
local flags = context.flags --[[@as flags_主线_青羽录10_楚家庄前院火烧]]
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
--每次载入调用
function start()
    s.playMusic("jianbanuzhang",0,nil)
    s.setPos(npcs.zhujue,pos.Pos_start)
    s.camera(cameras.LensStart,true,true,blendHintEnum.Cut,0,true,true)
    s.readyToStart(false,0.2)

    s.talk(npcs.YunWuSheng, "好大的火！")
    s.talk(npcs.MoZhiYou, "不妙！")
    s.talk(npcs.MoZhiYou, "要尽快找到他们！")
    s.camera(cameras.LensStart,false,true,blendHintEnum.Custom,1,true,true)

    s.addTask(310020,1) -- 触发任务

    s.moveAsync(npcs.MoZhiYou,pos.Pos_MoZhiYou,5,true)
    s.wait(0.5)
    s.moveAsync(npcs.YunWuSheng,pos.Pos_YunWuSheng,5,true)
end

function PlayTimeline()
    s.setActive(objs.Trigger1,false) -- 关闭触发器
    -- s.finishTask(310020, 1) -- 完成任务
    s.blackScreen()
    s.setActive(npcs.zhujue,false)
    s.setActive(npcs.MoZhiYou,false)
    s.setActive(npcs.YunWuSheng,false)
    s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录10_楚家庄前院火烧动画.unity")
    s.setNewbieFlags("chapter_qyl_20_finished",1)
    s.finishInfiniteStory(true) -- 结束关卡
end
