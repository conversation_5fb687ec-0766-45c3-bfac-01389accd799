local s = require("StoryApi")

local extension = require 'MapStories/挑战_远古地宫/extension'
---@type flags_挑战_远古地宫
local flags = require("MapStories/挑战_远古地宫/flags_挑战_远古地宫")
s.loadFlags(flags)
---@type 挑战_远古地宫
local main = require 'MapStories/挑战_远古地宫/scene_挑战_远古地宫_h'

---@type StringWidget
local stringWidget = extension.widgets.StringWidgetInfo.widget
---@type 挑战_远古地宫_StringItemData
local stringItemData = extension.widgets.StringWidgetInfo.items
---@type ItemWidget
local itemWidget = extension.widgets.ItemWidgetInfo.widget
---@type 挑战_远古地宫_ItemData
local itemData = extension.widgets.ItemWidgetInfo.items --[[@as 挑战_远古地宫_ItemData]]

---不可改名
function get_string_item_desc(itemKey,sceneId)
    
    return "没有描述"
end

function on_use_item(itemKey,sceneId)
    

end