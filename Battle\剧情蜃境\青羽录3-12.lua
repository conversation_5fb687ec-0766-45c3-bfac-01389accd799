local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role_with_card("荆成",20, bcapi.get_team(0), bcapi.Vector2(0, 0), 0)
        local jingcheng1 = bcapi.get_role(0, "荆成")
        bcapi.wait(0.1)
        bcapi.role_add_buff(jingcheng1, jingcheng1, "晕眩", 300, 1, 1)

    end)
end

function start()
    StartTalk()
    bcapi.add_timer_trigger(30, Talk30)
    bcapi.add_condition_trigger("[%c->teammate:荆成%][<]1", 0, BattleLose)
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("旁白", "荆成正处于昏迷中，请保护好荆成一段时间。")
        
        bcapi.resume_and_show_ui()
    end)
end

function Talk30()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        
        bcapi.talk("旁白", "荆成从幻境中醒来，获得了极大的强化，将参与接下来的战斗。")
        local jingcheng1 = bcapi.get_role(0, "荆成")
        jingcheng1.Buff:MinusBuffLevel("晕眩", 999, jingcheng1)
        jingcheng1.Buff:AddBuff("魔神降临", 300, 1, 1, jingcheng1)
        jingcheng1.Buff:AddBuff("剧情_持续恢复", 300, 1, 1, jingcheng1)
        jingcheng1.Buff:AddBuff("剧情_巨力", 300, 1, 1, jingcheng1)
        bcapi.change_role_hp(jingcheng1, 10000)
        
        bcapi.resume_and_show_ui()
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
