
---@type 副本_黑刹教_地下
local context = require("MapStories/副本_黑刹教/scene_副本_黑刹教_地下_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_副本_黑刹教
local flags = context.flags

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    local isTeleport = false
    --如果是传送，就不能setPos
    if(args ~= nil and args:ContainsKey("IsTransport"))then
        isTeleport = true
    end

    --如果不是传送，则设置主角位置
    if(not isTeleport)then
        s.setPos(npcs.zhujue,pos.start)
    end

    s.readyToStart()
end

--传送到地下场景
function teleport()
    s.changeMap("Assets/GameScenes/游历场景/副本_黑刹教/副本_黑刹教_地上.unity", "位置/start")
end

--离开副本
function quitDungeon()
    s.showConfirmResetChallengeMap()
end