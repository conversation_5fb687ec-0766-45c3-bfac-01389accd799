---荆成之章
---@type ChapterApi
local capi = require("ChapterApi")

---@type NewbieApi
local newbieApi = require("NewbieApi") -- 不允许改名

---@type StageMapApi
local stageApi = require("StageMapApi")

s = require("StoryApi")

local avg = require("AVGApi")

-- 主要用于摄影棚接新手战斗
function playTimelineAndCustomBattle(timelinePath, battleKey, hideSkip)
    if battleKey == nil then battleKey = args:get_Item("battleKey") end
    innerPlayTimelineScene(timelinePath, false, hideSkip, true)
    newbieApi.battle(battleKey, true)  -- 开始战斗
    capi.setTimelineSceneActive(false) -- 隐藏Timeline场景
    --capi.stopTimelineScene(true) -- 此时真正停止timeline并切换到主场景
    capi.pass(7)
end

function playAvgAndCustomBattle()
    innerPlayAvg(args:get_Item("avgKey"))
    newbieApi.battle(args:get_Item("battleKey"), true)
    capi.pass(7)
end

function quick_play_avg()
    local avgKey = args:get_Item("avgKey")
    local avg = require("AVGApi")
    avg.run(avgKey) 
end

function quick_map_story()
    local mapStoryKey = args:get_Item("mapStoryKey")
    --s.aside(mapStoryKey .. ":changeMapStory切地点有问题，待程序修复。先为了串流程直接跳过")
    if(capi.isGameMapRunning()) then
        s.changeMapStory(mapStoryKey)
    else
        capi.mapStory(mapStoryKey,false)
    end
end

function quick_play_timeline()
    local timelineKey = args:get_Item("timelineSceneKey")
    s.playTimelineScene("Assets/GameScenes/动画演出场景/" .. timelineKey)
end

  
function on_refresh_chapter1()
    ---@type NewbieFlags
    local flags = require("Newbie/flags")
    if(s.getNewbieFlag(flags.first_enter_mirage_story) == 0) then
        innerPlayAvg("序章/0.8序章_第一幕_008")
        s.setNewbieFlag(flags.first_enter_mirage_story, 1)
    end
end

-- 从记忆碎片画廊点击执行,注意这里是纯演出,不应该包含任何值的变化
function qyl_01()
    quick_play_avg()
end

function qyl_02()
    quick_play_timeline()
    --s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录2_受困.unity")
end

function qyl_03()
    quick_play_timeline()
    --s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录2_应战.unity")
end

function qyl_04()
    quick_play_timeline()

    --设置天书flag，记录BOSS
    s.setGuideBookFlag("boss_chuqing")
end

function qyl_05()
    quick_play_timeline()
    --s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录3_傀儡.unity")
end

function qyl_06()
    quick_play_avg()
end

function qyl_07()
    quick_play_avg()
end

function qyl_08()
    quick_map_story()
end

function qyl_09()
    quick_map_story()
end

function qyl_10()
    quick_play_avg()
end

function qyl_11()
    quick_play_avg()
    quick_play_timeline()
end

function qyl_12()
    quick_play_avg()
end

function qyl_13()
    quick_play_avg()
end

function qyl_14()
    quick_play_avg()
end

function qyl_15()
    quick_map_story()
end

function qyl_16()
    quick_map_story()
end

function qyl_17()
    quick_play_avg()
end

function qyl_18()
    quick_play_avg()
end

function qyl_19()
    quick_play_avg()
end

function qyl_20()
    quick_map_story()
end

function qyl_21()
    quick_play_avg()
end

function qyl_22()
    quick_play_avg()
end

function qyl_23()
    quick_map_story()
    
end

function qyl_24()
    s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录11_告别之地.unity", false)
    s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录11_回忆之地.unity")
end

return {
    qyl1_01 = qyl_01,
    qyl2_01 = qyl_02,
    qyl3_01 = qyl_03,
    qyl4_01 = qyl_04,
    qyl5_01 = qyl_05,
    qyl6_01 = qyl_06,
    qyl7_01 = qyl_07,
    qyl8_01 = qyl_08,
    qyl9_01 = qyl_09,
    qyl10_01 = qyl_10,
    qyl11_01 = qyl_11,
    qyl12_01 = qyl_12,
    qyl13_01 = qyl_13,
    qyl14_01 = qyl_14,
    qyl15_01 = qyl_15,
    qyl16_01 = qyl_16,
    qyl17_01 = qyl_17,
    qyl18_01 = qyl_18,
    qyl19_01 = qyl_19,
    qyl20_01 = qyl_20,
    qyl21_01 = qyl_21,
    qyl22_01 = qyl_22,
    qyl23_01 = qyl_23,
    qyl24_01 = qyl_24,
}