--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/新手幻境/flags_新手幻境")


local npcs = {
    --- "荆成"
    jingcheng = "角色/jingcheng",
}

local objs = {
    --- "事件触发1"
    obj1 = "物体/obj1",
    --- "事件触发2"
    obj2 = "物体/obj2",
}

local cameras = {
}

local pos = {
    --- "开始位置"
    start = "位置/start",
}

local assets = {
}

local animationClips = {
}

---@class 新手幻境
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
