local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

--用来管理所有表演的一次性
local secondFormationFlag = 0
local thirdFormationFlag = 0
local fourthFormationFlag = 0
local fifthFormationFlag = 0

function init()

end

function start()
    bcapi.async_call(function()
        --2秒后开始第一个弹话
        bcapi.add_timer_trigger(2, firstShowTalk)
    end)
end

function firstShowTalk(...)
    bcapi.async_call(function()
        bcapi.append_async_aside("天山弟子",0,"凌霜剑阵第一阵准备！","","天山派男弟子")
        bcapi.add_timer_trigger(1, firstFormation)
    end)
end

function firstFormation(...)
    bcapi.async_call(function()
        local TianShanPupil1 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_一")
        local TianShanPupil2 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_二")
        local TianShanPupil3 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_三")
        local TianShanPupil4 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_四")
        local TianShanPupil5 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_五")

        --第一次表演落位
        bcapi.use_skill_to_role(TianShanPupil2,"世界_雪霜白乌_施展剑阵表演_左上角飞雪阵",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil3,"世界_雪霜白乌_施展剑阵表演_左下角",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil4,"世界_雪霜白乌_施展剑阵表演_右上角",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil5,"世界_雪霜白乌_施展剑阵表演_右下角",TianShanPupil2,1)
        --两种条件进入第二次表演的弹话
        bcapi.add_condition_trigger("[%enemy:世界_雪霜白乌_天山弟子_一->hp_pct%][<=]0.5", 0.1, secondShowTalk)
        bcapi.add_timer_trigger(20, secondShowTalk)

    end)
end

function secondShowTalk(...)
    bcapi.async_call(function()
        if (secondFormationFlag == 0) then
            bcapi.append_async_aside("天山弟子",0,"凌霜剑阵第二阵准备！","","天山派男弟子")
            bcapi.add_timer_trigger(1, secondFormation)
            secondFormationFlag = 1
        end
    end)
end

function secondFormation(...)
    bcapi.async_call(function()
        local TianShanPupil1 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_一")
        local TianShanPupil2 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_二")
        local TianShanPupil3 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_三")
        local TianShanPupil4 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_四")
        local TianShanPupil5 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_五")
        
        --解放弟子2进行战斗，弟子1封印
        TianShanPupil2.Buff:MinusBuffLevel("世界_雪霜白乌_状态_不移动不攻击", 300, TianShanPupil2)
        TianShanPupil2.Buff:MinusBuffLevel("世界_精英技能_强化小兵防御_3_BUFF_加双防", 300, TianShanPupil2)
        bcapi.use_skill_to_role(TianShanPupil2,"世界_通用技能_打断",TianShanPupil2,1)

        --技能里面做了打断
        bcapi.use_skill_to_role(TianShanPupil2,"世界_雪霜白乌_施展剑阵表演_锥形位1位",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil1,"世界_雪霜白乌_施展剑阵表演_锥形位2位",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil3,"世界_雪霜白乌_施展剑阵表演_锥形位3位",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil4,"世界_雪霜白乌_施展剑阵表演_锥形位4位",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil5,"世界_雪霜白乌_施展剑阵表演_锥形位5位",TianShanPupil2,1)
        --两种条件进入第三次表演的弹话
        bcapi.add_condition_trigger("[%enemy:世界_雪霜白乌_天山弟子_二->hp_pct%][<=]0.5", 0.1, thirdShowTalk)
        bcapi.add_timer_trigger(10, thirdShowTalk)

    end)
end

function thirdShowTalk(...)
    bcapi.async_call(function()
        if (thirdFormationFlag == 0) then
            bcapi.append_async_aside("天山弟子",0,"凌霜剑阵第三阵准备！","","天山派男弟子")
            bcapi.add_timer_trigger(1, thirdFormation)
            thirdFormationFlag = 1
        end
    end)
end

function thirdFormation(...)
    bcapi.async_call(function()
        local TianShanPupil1 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_一")
        local TianShanPupil2 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_二")
        local TianShanPupil3 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_三")
        local TianShanPupil4 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_四")
        local TianShanPupil5 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_五")
        --下面解放弟子3战斗，弟子2封印
        bcapi.use_skill_to_role(TianShanPupil3,"世界_通用技能_打断",TianShanPupil3,1)
        TianShanPupil3.Buff:MinusBuffLevel("世界_雪霜白乌_状态_不移动不攻击", 300, TianShanPupil3)
        TianShanPupil3.Buff:MinusBuffLevel("世界_精英技能_强化小兵防御_3_BUFF_加双防", 300, TianShanPupil3)
        TianShanPupil1.Buff:MinusBuffLevel("世界_雪霜白乌_放飞剑", 300, TianShanPupil3)
        TianShanPupil3.Buff:MinusBuffLevel("世界_雪霜白乌_放飞剑", 300, TianShanPupil3)
        TianShanPupil4.Buff:MinusBuffLevel("世界_雪霜白乌_放飞剑", 300, TianShanPupil3)
        TianShanPupil5.Buff:MinusBuffLevel("世界_雪霜白乌_放飞剑", 300, TianShanPupil3)
        --技能里面做了打断
        bcapi.use_skill_to_role(TianShanPupil1,"世界_雪霜白乌_施展剑阵表演_菱形位左中",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil4,"世界_雪霜白乌_施展剑阵表演_菱形位顶上",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil2,"世界_雪霜白乌_施展剑阵表演_菱形位底下",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil5,"世界_雪霜白乌_施展剑阵表演_菱形位右中",TianShanPupil2,1)
        --两种条件进入第四次表演的弹话
        bcapi.add_timer_trigger(1, thirdSkill)
        bcapi.add_condition_trigger("[%enemy:世界_雪霜白乌_天山弟子_三->hp_pct%][<=]0.5", 0.1, fourthShowTalk)
        bcapi.add_timer_trigger(30, fourthShowTalk)
    end)
end

--使用降低移速的技能并弹个tips
function thirdSkill(...)
    bcapi.async_call(function()
            local TianShanPupil3 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_三")
            bcapi.use_skill_to_role(TianShanPupil3,"世界_雪霜白乌_释放减攻速移速DEBUFF",0,1)
            bcapi.wait(1)
            bcapi.show_pop_info("攻速被巨幅降低了，如果不及时清除减益的话……", 2)
    end)
end

function fourthShowTalk(...)
    bcapi.async_call(function()
        if (fourthFormationFlag == 0) then
            bcapi.append_async_aside("天山弟子",0,"凌霜剑阵第四阵准备！","","天山派男弟子")
            bcapi.add_timer_trigger(1, fourthFormation)
            fourthFormationFlag = 1
        end
    end)
end

function fourthFormation(...)
    bcapi.async_call(function()
        local TianShanPupil1 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_一")
        local TianShanPupil2 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_二")
        local TianShanPupil3 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_三")
        local TianShanPupil4 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_四")
        local TianShanPupil5 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_五")

        --下面解放弟子4/5战斗，弟子3封印
        bcapi.use_skill_to_role(TianShanPupil1,"世界_通用技能_打断",TianShanPupil4,1)
        bcapi.use_skill_to_role(TianShanPupil1,"世界_通用技能_打断",TianShanPupil5,1)
        TianShanPupil4.Buff:MinusBuffLevel("世界_雪霜白乌_状态_不移动不攻击", 300, TianShanPupil4)
        TianShanPupil4.Buff:MinusBuffLevel("世界_精英技能_强化小兵防御_3_BUFF_加双防", 300, TianShanPupil4)
        TianShanPupil5.Buff:MinusBuffLevel("世界_雪霜白乌_状态_不移动不攻击", 300, TianShanPupil5)
        TianShanPupil5.Buff:MinusBuffLevel("世界_精英技能_强化小兵防御_3_BUFF_加双防", 300, TianShanPupil5)
        --技能里面做了打断
        bcapi.use_skill_to_role(TianShanPupil4,"世界_雪霜白乌_施展剑阵表演_斜十字左上",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil5,"世界_雪霜白乌_施展剑阵表演_斜十字左下",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil1,"世界_雪霜白乌_施展剑阵表演_斜十字正中",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil2,"世界_雪霜白乌_施展剑阵表演_斜十字右上",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil3,"世界_雪霜白乌_施展剑阵表演_斜十字右下",TianShanPupil2,1)
        --用来管理当前阵法的BUFF
        bcapi.add_talent(TianShanPupil1,"世界_雪霜白乌_被打断消除攻速")
        --三种条件进入最后的弹话
        bcapi.add_condition_trigger("[%enemy:世界_雪霜白乌_天山弟子_四->hp_pct%][<=]0.5", 0.1, fifthShowTalk)
        bcapi.add_condition_trigger("[%enemy:世界_雪霜白乌_天山弟子_五->hp_pct%][<=]0.5", 0.1, fifthShowTalk)
        bcapi.add_timer_trigger(30, fifthShowTalk)
    end)
end

function fifthShowTalk(...)
    bcapi.async_call(function()
        if (fifthFormationFlag == 0) then
            bcapi.append_async_aside("天山弟子",0,"凌霜剑阵第五阵，齐上阵","","天山派男弟子")
            bcapi.add_timer_trigger(1, fifthFormation)
            fifthFormationFlag = 1
        end
    end)
end

function fifthFormation(...)
    bcapi.async_call(function()
        local TianShanPupil1 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_一")
        local TianShanPupil2 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_二")
        local TianShanPupil3 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_三")
        local TianShanPupil4 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_四")
        local TianShanPupil5 = bcapi.get_role(1, "世界_雪霜白乌_天山弟子_五")

        --下面全解封印
        TianShanPupil1.Buff:MinusBuffLevel("世界_雪霜白乌_状态_不移动不攻击", 300, TianShanPupil1)
        TianShanPupil1.Buff:MinusBuffLevel("世界_精英技能_强化小兵防御_3_BUFF_加双防", 300, TianShanPupil1)
        --技能里面做了打断
        bcapi.use_skill_to_role(TianShanPupil1,"世界_雪霜白乌_施展剑阵表演_锥形位1位_第5次",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil2,"世界_雪霜白乌_施展剑阵表演_锥形位2位_第5次",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil3,"世界_雪霜白乌_施展剑阵表演_锥形位3位_第5次",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil4,"世界_雪霜白乌_施展剑阵表演_锥形位4位_第5次",TianShanPupil2,1)
        bcapi.use_skill_to_role(TianShanPupil5,"世界_雪霜白乌_施展剑阵表演_锥形位5位_第5次",TianShanPupil2,1)
        --超过30秒宣布胜利
        bcapi.show_pop_info("撑过30秒！", 2)
        bcapi.add_timer_trigger(30, finalWin)
    end)
end

function finalWin(...)
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("凌茵茵", "能完美地撑过这五剑，已经相当不容易了。")
        bcapi.talk("凌茵茵", "算你赢了！")
        bcapi.resume_and_show_ui()
        bcapi.win()
    end)
end