local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

-- 脚本内容：
-- 

function init()
    createRoles()
end

function start()
    bcapi.async_call(function()
        bcapi.add_timer_trigger(1, startTalk)
    end)
end

function createRoles(...)
    bcapi.async_call(function()
        -- bcapi.create_join_role("世界_折金枝_萧明漪", enemyLevel, team, bcapi.Vector2(-6, 0), 0)
    end)
end

function startTalk(...)
    bcapi.async_call(function()
        -- bcapi.create_join_role("世界_折金枝_萧明漪", enemyLevel, team, bcapi.Vector2(-6, 0), 0)
        bcapi.pause_and_hide_ui()
        bcapi.talk("主角", "小心！躲在我身后！")
        bcapi.talk("官差", "贼子好胆！敢来县令府捣乱！")
        bcapi.resume_and_show_ui()
    end)
end
