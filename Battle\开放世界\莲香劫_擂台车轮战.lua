local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

-- 脚本内容：
-- 三个敌人依次序上场跟玩家单挑，时不时有观众的讨论声

function init()

end

function start()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        bcapi.add_timer_trigger(5, startTalk)
        bcapi.add_condition_trigger("[%t->teammate:世界_莲香劫_擂台车轮战_第一个%][<]1", 0, secondChanllenger)
    end)
end

function startTalk(...)
    bcapi.async_call(function()
        bcapi.append_async_aside("观众",0,"哦？这新人身法不错。","","大汉")
        bcapi.wait(4)
        bcapi.append_async_aside("观众",0,"真……真是潇洒！","","村姑")
    end)
end

function secondChanllenger(...)
    bcapi.async_call(function()
        bcapi.append_async_aside("观众",0,"没见过的功法。真是长见识了！","","江湖女侠")
        bcapi.create_join_role("世界_莲香劫_擂台车轮战_第二个", 32, eTeam, bcapi.Vector2(6, 3), 0)
        bcapi.wait(4)
        bcapi.append_async_aside("观众",0,"哎呀！要……要见血了！","","贵妇")
        bcapi.wait(4)
        bcapi.append_async_aside("观众",0,"仅以蛮力，怕是难敌过这位少侠。","","道士")

        bcapi.add_condition_trigger("[%t->teammate:世界_莲香劫_擂台车轮战_第二个%][<]1", 0, finalChanllenger)
    end)
end

function finalChanllenger()
    bcapi.async_call(function()
        bcapi.append_async_aside("观众",0,"攻守兼备……这就是高手间的战斗！","","剑术学徒")
        bcapi.create_join_role("世界_莲香劫_擂台车轮战_河洛人士", 32, eTeam, bcapi.Vector2(6, 0), 0)
        bcapi.wait(4)
        bcapi.append_async_aside("观众",0,"呵！是河洛帮内第一棍才！","","中年男子")
        bcapi.wait(4)
        bcapi.append_async_aside("观众",0,"千变万化，目不暇接！","","尼姑")
        bcapi.wait(8)
        bcapi.append_async_aside("观众",0,"这一招也能接住？下盘真稳！","","青衣剑侠")

        bcapi.add_condition_trigger("[%t->teammate:世界_莲香劫_擂台车轮战_河洛人士%][<]1", 0, winTalk)
    end)
end

function winTalk()
    bcapi.async_call(function()
        bcapi.win()
    end)
end
