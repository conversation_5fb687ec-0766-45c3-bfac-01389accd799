local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()

end

function start()  
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        bcapi.add_condition_trigger("[%enemy:世界_新风拂雾开_释缘方丈->flag:当前阶段%][=]1", 0, firstStateTalk)
        bcapi.add_condition_trigger("[%enemy:世界_新风拂雾开_释缘方丈->flag:当前阶段%][=]2", 0, secondStateTalk)
        bcapi.add_condition_trigger("[%enemy:世界_新风拂雾开_释缘方丈->flag:当前阶段%][=]3", 0, thirdStateTalk)
        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0.1, winTalk)
    end)
end

function firstStateTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("释缘方丈", "众生落于苦海，沉沦于三毒之中，终是难脱轮回……")
        bcapi.talk("释缘方丈", "陷于此者，纵是武艺傍身，亦为行尸走肉，与那伥怪无异。")
        bcapi.talk("释缘方丈", "第一毒曰贪，五欲存心，而难舍离……")
        bcapi.resume_and_show_ui()
    end)
end

function secondStateTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("释缘方丈", "第二毒曰嗔，嫉贤妒才，心存毒怨，蚀空自身……")
        bcapi.resume_and_show_ui()
    end)
end

function thirdStateTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("释缘方丈", "第三毒曰痴，眼盲心空，无明无识，种种事象，皆迷拙眼……")
        bcapi.resume_and_show_ui()
    end)
end

function winTalk()
    bcapi.async_call(function()
    bcapi.pause_and_hide_ui()
    bcapi.talk("释缘方丈", "三毒俱破……少侠，你果然并非天命所能拘束之人……")
    bcapi.talk("释缘方丈", "《瀚海功》……暂且交给你了……")
    bcapi.resume_and_show_ui()  
    bcapi.win()
    end)
end
