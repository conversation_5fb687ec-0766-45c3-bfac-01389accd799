---@type 挑战_贼寇山洞
local context = require("MapStories/挑战_贼寇山洞/scene_挑战_贼寇山洞_h")
local newbieApi = require("NewbieApi")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
local s = context.sapi


---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

local capi = require("ChapterApi")

s.loadFlags(context.flags)
---@type flags_挑战_贼寇山洞
local flags = context.flags

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

local function getFlagValue(key)
    return flags[key] or 0
end

local function setFlagValue(key, value)
    flags[key] = value
end

--每次载入调用
function start()
    --各重复演出准备
    s.playMusic("ruin")
    s.animState(npcs.boss, "Special_Loop_2",true)
    s.setAnimationClip(npcs.enemy_gate_1,"Assets/BuildSource/Animations/Humanoid/Others/跑图通用状态机动画/叉腰_循环.anim")
    s.setAnimationClip(npcs.enemy_gate_2,"Assets/BuildSource/Animations/Humanoid/Others/跑图通用状态机动画/敌视.anim")
    
    --添加副本内主线任务
    if not s.hasTaskGroup("贼寇山洞副本内")then
        s.setTaskStep("贼寇山洞副本内","searchCave")
    end

    --蜀境迷踪任务Step7,进入副本完成任务，后续触发剧情
    if (s.getCurrentTaskStep("蜀境迷踪") == "exploreCave")then
        s.nextTaskStep("蜀境迷踪","beatBoss")
        newbieApi.eventTrack("newbieStep", "step", "first_enter_fuben_zeikoushandong")
    end

    if getFlagValue("canGiveUp") == 0 or getFlagValue("canGiveUp") == nil then
        if s.isChallengeCompleted() then
            setFlagValue("canGiveUp", 1)
        else
            setFlagValue("canGiveUp", -1)
        end
    end
    -- CS.UnityEngine.Debug.LogError("canGiveUp4" .. tostring(flags["canGiveUp"]))

    --是否播放过开场演出1
    if(s.getChallengeMapPersistentFlag("挑战_贼寇山洞","是否播放过开场演出") == 0)then
        --开启开场演出的触发器
        s.setActive(objs.enemy_gate_show_trigger,true)

        --设置主角到开场演出初始位置
        s.setPos(npcs.zhujue, pos.pre_start)

        --设置守门小怪到开场演出位置
        s.setPos(npcs.enemy_gate_1, pos.pos_start_show_enemy_1)
        s.setPos(npcs.enemy_gate_2, pos.pos_start_show_enemy_2)
    else
        --设置主角到初始位置
        s.setPos(npcs.zhujue, pos.start)
    end

    --是否首次击败过Boss
    if(s.getChallengeMapPersistentFlag("挑战_贼寇山洞","通关过Boss") == 0)then
        --显示Boss战前演出角色
        s.setActive(npcs.caiyaonv,true)
        s.animState(npcs.caiyaonv, "Special_Loop_1",true)
        s.turnTo(npcs.caiyaonv, npcs.boss, true)
    end
    s.readyToStart()

    --处理断线重连
    refreshGameObjects()
end

--处理断线重连
function refreshGameObjects()
    --处理BOSS
    local bossDefeated = false
    if(getFlagValue("boss") ~= nil and getFlagValue("boss") == -1)then
        bossDefeated = true
    end
    s.setActive(npcs.boss,not bossDefeated)
    s.setActive(objs.bossTrigger,not bossDefeated)
    s.setActive(objs.quit,bossDefeated)
end

--没有首次击败过Boss时，开场演出1
function enemyBattleTutorialShow()
    s.setActive(objs.enemy_gate_show_trigger,false)

    --开场演出1
    s.cameraAsync(cameras.camera_start,true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.moveAsync(npcs.zhujue,pos.pre_start_show,4,true)
    s.wait(0.5)
    s.animState(npcs.zhujue, "BSquat_Loop",true)
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_首次门口对话_1", nil, 0)
    PlayRandomIdle("enemy_gate_1")
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_首次门口对话_2", nil, 0)
    PlayRandomIdle("enemy_gate_2")
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_首次门口对话_3", nil, 0)
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_首次门口对话_4", nil, 0)
    PlayRandomIdle("enemy_gate_1")
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_首次门口对话_5", nil, 0)
    PlayRandomIdle("enemy_gate_2")
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_首次门口对话_6", nil, 0)
    s.animState(npcs.zhujue, "BSquat_Loop",false)
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_首次门口对话_7", nil, 0)
    s.camera(cameras.camera_start, false,true,blendHintEnum.EaseInOut,1.5,true,false)
end

--随机播放休闲待机动画
function PlayRandomIdle(roleKey)
    local idle = math.random(1, 5)
    if (idle == 1) then
        s.animState(npcs[roleKey], "BGesture",true)
    elseif (idle == 2) then
        s.animState(npcs[roleKey], "BScratch",true)
    elseif (idle == 3) then
        s.animState(npcs[roleKey], "BAgree",true)
    elseif (idle == 4) then
        s.animState(npcs[roleKey], "BDeny",true)
    elseif (idle == 5) then
        s.animState(npcs[roleKey], "BRubNose",true)
    end
end

--触发第1场战斗
function enemyBattleTutorial()
    --判断播放一次性演出还是重复演出
    if(s.getChallengeMapPersistentFlag("挑战_贼寇山洞","是否播放过开场演出") == 0)then
        --播放一次性演出
        s.setChallengeMapPersistentFlag("挑战_贼寇山洞","是否播放过开场演出",1)
        s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_首次门口遇敌对话_1", nil, 0)
        s.animState(npcs.enemy_gate_1, "BAkimbo_Loop",false,false,0,false,false,0,false)
        s.animState(npcs.enemy_gate_2, "BHostile",false,false,0,false,false,0,false)
        s.turnTo(npcs.enemy_gate_1, npcs.zhujue, true)
        s.turnTo(npcs.enemy_gate_2, npcs.zhujue, true)
        s.animState(npcs.enemy_gate_1, "BShock_Loop",true,false,0,false,false,0,false)
        s.wait(0.2)
        s.animState(npcs.enemy_gate_2, "BShock_Loop",true,false,0,false,false,0,false)
    else
        --播放重复演出
        s.turnTo(npcs.enemy_gate_1, npcs.zhujue, true)
        s.turnTo(npcs.enemy_gate_2, npcs.zhujue, true)
    end
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_门口遇敌对话_1", nil, 0)

    --触发战斗
    local isWin,bResult = s.challengeMapBattle("1")
    s.animState(npcs.enemy_gate_1, "BShock_Loop",false)
    s.animState(npcs.enemy_gate_2, "BShock_Loop",false)
end

function isInNewbieProgress()
    return s.getChallengeMapPersistentFlag("挑战_贼寇山洞","通关过Boss") == 0
end

--Boss战斗
function bossBattle()
    --没有击败过Boss时，播放演出
    if(s.getChallengeMapPersistentFlag("挑战_贼寇山洞","通关过Boss") == 0)then
        s.setPos(npcs.zhujue, pos.boss_pos_1)
        s.cameraAsync(cameras.camera_boss_1,true,true,blendHintEnum.EaseInOut,0.5,true,true)
        s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_Boss首次战前对话_1", nil, 0)
        s.moveAsync(npcs.zhujue,pos.boss_pos_7,4,true)
        s.wait(1)
        s.animState(npcs.caiyaonv, "Special_Loop_1",false)
        s.turnTo(npcs.caiyaonv, npcs.zhujue, true)
        s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_Boss首次战前对话_2", nil, 0)
        s.camera(cameras.camera_boss_1,false)
        s.cameraAsync(cameras.camera_boss_3,true,true,blendHintEnum.EaseInOut,0.2,true,true)
        
        if (Gender == 0) then
            s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_Boss首次战前对话_3_1", nil, 0)
        else
            s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_Boss首次战前对话_3_2", nil, 0)
        end
        s.camera(cameras.camera_boss_3,false)

        newbieApi.eventTrack("newbieStep", "step", "battle_boss_shanzeilaoda_in")
    end

    --重复演出
    s.setActive(npcs.boss,false)
    s.setActive(objs.boss_weapon,false)
    s.playTimeline(assets.Timeline_1)
    s.setActive(npcs.boss,true)


    function doWin()
        flags["boss"] = -1

        --一次性内容，没有获得过心法时
        if(isInNewbieProgress())then
            firstFinishStory()
        else
            s.setActive(npcs.boss,false)
        end

        --结束副本内主线任务
        if s.hasTaskGroup("贼寇山洞副本内")then
            s.nextTaskStep("贼寇山洞副本内","searchCave")
        end

        --显示传送点
        s.setActive(objs.quit,true)
        s.setActive(objs.bossTrigger,false)
        s.completeChallengeMap()
    end

    local battleTb = {}
    if(isInNewbieProgress())then
        battleTb["ignore_fail_move_to_start"] = 1
    end

    --进入战斗
    local isWin,bResult = s.challengeMapBattle("4", battleTb)
    if(isWin)then
        doWin()
    else
        --如果处于新手教学，且战败了。则直接弹教学战斗
        if(isInNewbieProgress())then
            local isWinBattle2 = false
            while(isWinBattle2 == false) do
                s.talk("药曲老", "初入江湖，难免水土不服，阁主莫慌，老朽来帮你应对强敌！")
                isWinBattle2 = s.battle("贼寇山洞_战斗_BOSS教学")
            end
            local setChallengeBattleFinish = CS.H2.GameMap.Scripts.GameMapLuaBridge.SetChallengeBattleFinish
            -- 教学战斗赢了也算赢，不记录时间和推荐阵容
            -- 发宝箱也在这里
            setChallengeBattleFinish("4")
            doWin()
        else
            s.setActive(objs.boss_weapon,true)
            s.animState(npcs.caiyaonv, "Special_Loop_1",true)
            s.turnTo(npcs.caiyaonv, npcs.boss, true)
        end
    end
end

--没有获得过心法时，播放演出并下发一次性奖励
function firstFinishStory()
    --演出前准备
    s.setActive(npcs.caiyaonv,false)
    s.animState(npcs.boss, "Special_Loop_2",false)
    s.animState(npcs.boss, "BSquat_Loop",true)
    s.setPos(npcs.boss, pos.boss_pos_3)
    s.setPos(npcs.zhujue, pos.boss_pos_2)
    s.camera(cameras.camera_boss_2,true)

    --播放演出1
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_Boss首次战后对话_1", nil, 0)

    --播放演出2
    s.animState(npcs.boss, "BSquat_Loop",false)
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_Boss首次战后对话_2", nil, 0)
    s.animState(npcs.boss, "Special_3",true)
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_Boss首次战后对话_3", nil, 0)
    s.moveAsync(npcs.boss,pos.boss_pos_6,4,true)
    s.camera(cameras.camera_boss_2,false)

    --添加蜀境迷踪任务Step8，击败山大王
    if (s.getCurrentTaskStep("蜀境迷踪") == "beatBoss")then
        s.nextTaskStep("蜀境迷踪","leadBoss")
    end
    
    newbieApi.eventTrack("newbieStep", "step", "battle_boss_shanzeilaoda_out")

    --设置永久flag，通关过Boss
    s.setChallengeMapPersistentFlag("挑战_贼寇山洞","通关过Boss",1)

    --设置天书flag，记录BOSS
    s.setGuideBookFlag("boss_shanzeilaoda")
end

--没有首次击败过Boss时，战后和Boss对话演出
function bossFirstDefeatDialogue()
    s.runAvgTag("副本/副本_贼寇山洞/副本_贼寇山洞", "副本_贼寇山洞_Boss首次战后对话_4", nil, 0)
end

function quitDungeon()
    s.showConfirmResetChallengeMap(getFlagValue("canGiveUp") == 1)
end

function debugFinish()
    s.finishTask(110003,90,100)
    s.setChallengeMapPersistentFlag("挑战_贼寇山洞","获得成就任务",1)
    s.setChallengeMapPersistentFlag("挑战_贼寇山洞","是否播放过开场演出",1)
    s.addTask(110003,90,true)
    s.setChallengeMapPersistentFlag("挑战_贼寇山洞","通关过Boss",1)
end

--从UI退出副本教学
-- function quitTutorial()
--     if(s.getChallengeMapPersistentFlag("挑战_贼寇山洞","通关") == 0)then
--         s.completeChallengeMap()
--         s.setChallengeMapPersistentFlag("挑战_贼寇山洞","通关",1)
--         s.aside("已经成功通关副本，现在通过<color=red>结算</color>按钮来离开副本吧！")
--         local n = require("NewbieApi")
--         n.showGameMapHud()
--         --- 非常重要 ---
--         --- 此函数会强制提交上述的所有指令改动 ---
--         --- 只允许经过程序来添加这种操作 ---
--         s.checkSubmit(flags)
--         s.wait(0.5)
--         n.waitUntilGameObject("UIRoot/GameMapHUD/GameObject/Buttons/MenuButton")
--         n.guideButton("UIRoot/GameMapHUD", "UIRoot/GameMapHUD/GameObject/Buttons/MenuButton", "点击菜单按钮", 100, -250)
--         n.waitUntilGameObject("UIRoot/GameMapHUD/GameObject/MenuWidget/LobbyButton")
--         n.guideButton("UIRoot/GameMapHUD", "UIRoot/GameMapHUD/GameObject/MenuWidget/LobbyButton", "点击退出", 120, -150)
--         n.waitUntilGameObject("UIRoot/MessageBox")
--         n.guideButton("UIRoot/MessageBox", "UIRoot/MessageBox/Buttons/ComButton_Blue", "点击结算", 170, -150)
--         s.wait(0.2)
--         n.waitUntilGameObject("UIRoot/MessageBox")
--         n.guideButton("UIRoot/MessageBox", "UIRoot/MessageBox/Buttons/ComButton_Orange", "点击确定", 170, -150)
--         local panel = CS.H2Engine.Runtime.UIModule.UIManager.Instance:GetUIWindowByType("GameMapSpeedPanelUI");
--         if (panel ~= nil) then
--             panel:Close()
--         end
--     end
-- end

function test()
    s.completeChallengeMap()
end