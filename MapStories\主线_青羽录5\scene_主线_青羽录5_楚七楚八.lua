
---@type 主线_青羽录5_楚七楚八
local context = require("MapStories/主线_青羽录5/scene_主线_青羽录5_楚七楚八_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger


s.loadFlags(context.flags)
---@type flags_主线_青羽录5
local flags = context.flags --[[@as flags_主线_青羽录5]]
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
--每次载入调用
function start()
    s.setPos(npcs.zhujue, pos.JC_init)
    s.setActive(npcs.zhujue,false)
    s.playMusic("七时桃源")
    s.readyToStart(false,0.2)
    
    s.playTimeline(assets.qyl1_5_1,false)

    s.addTask(310000, 1)
    --增加引导
    s.addGameMapGuide(npcs.guide_huwei)
    --莫知酉、主角激活
    s.setActive(npcs.mozhiyou,true)
    s.setActive(npcs.zhujue,true)
    s.lightScreen()
end

--与莫知酉交互
function talkToMozhiyou()
    s.talk(npcs.mozhiyou, "还是赶紧先去拜见楚庄主吧！")
end

--与石碑交互
function eventShibei()
    s.turnTo(npcs.zhujue, objs.shibei, true)
    s.animState(npcs.zhujue,roleAnims.BSquat_Loop)
    s.talk(npcs.zhujue, "上面刻的是“沉舟侧畔千帆过，病树前头万木春”。")
    s.talk(npcs.mozhiyou, "怎么刻这句？我以为是“欢迎来到楚家庄”呢。")
    s.soloAnimState(npcs.zhujue,roleAnims.BScratch)
    s.wait(1)
    s.triggerReset(npcs.zhujue)
    s.talk(npcs.mozhiyou, "不说笑了，阿成，还是先不管这个了，拜访庄主要紧。")
    s.wait(0.5)
end

--与护卫交互
function eventNpcHuwei()
    --清除引导
    s.removeGameMapGuide(npcs.guide_huwei)
    local offset = CS.UnityEngine.Vector3(0,1.75,0)
    s.turnTo(npcs.zhujue,npcs.huwei)
    s.lookAt(npcs.zhujue,offset,npcs.huwei,true)
    s.lookAt(npcs.huwei,offset,npcs.zhujue,true)
    s.cameraAsync(cameras.cam_door,true,true,blendHintEnum.Custom,3,true,true)
    s.setActive(npcs.mozhiyou2,true)
    s.moveAsync(npcs.mozhiyou2,pos.pos_mzy_door,2,true)
    s.wait(1)
    s.lookAt(npcs.huwei,offset,npcs.mozhiyou2,true)
    s.lookAt(npcs.mozhiyou2,offset,npcs.huwei,true)
    s.wait(1)

    s.talk(npcs.mozhiyou, "麻烦通知一下楚老爷，说是莫知酉携小友前来拜见。有重要事情要面呈楚庄主。")
    s.animState(npcs.huwei,roleAnims.BGreet)
    s.talk(npcs.huwei, "老爷有交代过了，莫大师可直接入内，请随我来。")

    s.setActive(npcs.mozhiyou2,false)
    s.setActive(npcs.mozhiyou,false)
    s.playTimeline(assets.qyl1_5_2)
    
    --s.finishTask(310000, 1)
    s.wait(0.1)
    s.setNewbieFlags("chapter_qyl_08_finished",1)
    s.finishInfiniteStory(true)
end

function testPass()
    s.setNewbieFlags("chapter_qyl_08_finished",1)
    s.finishInfiniteStory(true)
end