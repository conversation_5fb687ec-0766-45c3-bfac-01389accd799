local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()

end

function start()
    -- 每十秒，生成一个怪物
    for i=0, 9 do
        bcapi.add_timer_trigger(8*(i+1), createMoster)
    end
end

-- 随机怪物池
local monsterTable = {"通用_猎人_小怪_青年男_斧","通用_猎人_小怪_青年男_弓","通用_偷猎者_小怪_偷猎者男_斧","通用_偷猎者_小怪_偷猎者男_弓","通用_流氓_小怪_男小弟_拳",
"通用_流氓_小怪_男小弟_脚","通用_乞丐_小怪_乞丐男_拳","通用_乞丐_小怪_乞丐男_脚","通用_打手_小怪_护院男_掌","通用_打手_小怪_护院男_长棍",
"通用_和尚_小怪_和尚_掌","通用_和尚_小怪_和尚_长棍","通用_恶僧_小怪_恶僧_脚","通用_恶僧_小怪_恶僧_刀","通用_江湖武人_小怪_门派弟子男_暗器",
"通用_江湖武人_小怪_门派弟子女_暗器","通用_刺客_小怪_男杀手_弓","通用_刺客_小怪_女飞贼_弓"}

--创建一个随机的怪物
function createMoster()
    bcapi.async_call(function()
        --创建怪物前检查场上怪物数量
        if eTeam.FightingRoleCount < 6 then
            local index = math.random(1, #monsterTable) 
            local  createX = math.random(1, 7)
            local  createY = math.random(-3, 3)
            bcapi.create_join_role(monsterTable[index], 42, eTeam, bcapi.Vector2(createX, createY), 0.1) 
        end
    end)
end


