--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/大熔炉/flags_大熔炉")


local npcs = {
    --- "荆成"
    jingcheng = "角色/jingcheng",
    --- "测试NPC"
    npc1 = "角色/npc1",
    --- "熊"
    bear = "角色/bear",
    --- "莫只有"
    mozhiyou = "角色/mozhiyou",
    --- "荆成2"
    jingcheng2 = "角色/jingcheng2",
}

local objs = {
    --- "物体A"
    objA = "物体/objA",
    --- "物体B"
    objB = "物体/objB",
    --- "宝箱A"
    chest = "物体/chest",
    --- "场景物体A"
    testObj1 = "物体/testObj1",
}

local cameras = {
    --- "镜头1"
    vcam1 = "相机/vcam1",
    --- "镜头2"
    vcam2 = "相机/vcam2",
}

local pos = {
    --- "点位1"
    pos1 = "位置/pos1",
    --- "点位2"
    pos2 = "位置/pos2",
    --- "点位3"
    pos3 = "位置/pos3",
    --- "点位4"
    pos4 = "位置/pos4",
}

local assets = {
    --- "测试演出"
    timeline1 = "资产/timeline1",
}

local animationClips = {
}

---@class 大熔炉
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
