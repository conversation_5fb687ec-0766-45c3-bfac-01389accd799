local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
end

function start()
    bcapi.add_timer_trigger(0, TalkStart)
    bcapi.add_timer_trigger(60, SecondEnemys)
end

function TalkStart()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_沙匪营地_李司统#副本_沙匪营地_李司统", "我乃本地族长，诸位少侠定要信我！")
        bcapi.talk("副本_沙匪营地_李司统#副本_沙匪营地_李司统", "......")
        bcapi.talk("副本_沙匪营地_李司统#副本_沙匪营地_李司统", "既如此，休怨我手下不留情！待我擒下诸位再作解释！")
        bcapi.resume_and_show_ui()
    end)
end

function SecondEnemys()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_沙匪营地_李司统#副本_沙匪_李司统", "你们.....")
        bcapi.talk("副本_沙匪营地_李司统#副本_沙匪_李司统", "也罢！弟兄们，不要藏着掖着了！")
        bcapi.talk("副本_沙匪营地_李司统#副本_沙匪_李司统", "不要留活口！")
        bcapi.create_join_role("副本_沙匪营地_沙匪_刀", 45, bcapi.get_team(1), bcapi.Vector2(4, 2), 0.1)
        bcapi.create_join_role("副本_沙匪营地_沙匪_刀", 45, bcapi.get_team(1), bcapi.Vector2(4, -2), 0.1)
        bcapi.create_join_role("副本_沙匪营地_沙匪_弓箭", 45, bcapi.get_team(1), bcapi.Vector2(6, 2), 0.1)
        bcapi.create_join_role("副本_沙匪营地_沙匪_弓箭", 45, bcapi.get_team(1), bcapi.Vector2(6, -2), 0.1)
        bcapi.resume_and_show_ui()
    end)
end