--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_沙匪营地/flags_挑战_沙匪营地")


---@class 角色_挑战_沙匪营地
local npcs = {
    zhujue = "角色/zhujue",
    enemy_1 = "角色/enemy_1",
    enemy_2 = "角色/enemy_2",
    enemy_2_1 = "角色/enemy_2_1",
    enemy_3 = "角色/enemy_3",
    enemy_4 = "角色/enemy_4",
    boss_1 = "角色/boss_1",
    boss_2 = "角色/boss_2",
    boss_3 = "角色/boss_3",
    boss_4 = "角色/boss_4",
}

---@class 物体_挑战_沙匪营地
local objs = {
    Trigger_Camera_Start = "物体/Trigger_Camera_Start",
    Trigger_Battle_Boss1 = "物体/Trigger_Battle_Boss1",
    Trigger_Battle_Boss2 = "物体/Trigger_Battle_Boss2",
    Trigger_Music_Mount = "物体/Trigger_Music_Mount",
    Trigger_Battle_Boss3 = "物体/Trigger_Battle_Boss3",
    Trigger_Well_Enter = "物体/Trigger_Well_Enter",
    Trigger_Well_Discover = "物体/Trigger_Well_Discover",
    Trigger_Well_Down = "物体/Trigger_Well_Down",
    Trigger_Well_Up = "物体/Trigger_Well_Up",
    Trigger_Well_Dialogue_1 = "物体/Trigger_Well_Dialogue_1",
    Trigger_Well_Dialogue_2 = "物体/Trigger_Well_Dialogue_2",
    Trigger_Battle_Boss4 = "物体/Trigger_Battle_Boss4",
    Trigger_Leave_2 = "物体/Trigger_Leave_2",
    teleport1 = "物体/teleport1",
    teleport2 = "物体/teleport2",
    teleport3 = "物体/teleport3",
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
    chest_3 = "物体/chest_3",
}

---@class 相机_挑战_沙匪营地
local cameras = {
    camera_enter = "相机/camera_enter",
    camera_boss1 = "相机/camera_boss1",
    camera_boss2 = "相机/camera_boss2",
    camera_boss3 = "相机/camera_boss3",
    camera_boss4 = "相机/camera_boss4",
}

---@class 位置_挑战_沙匪营地
local pos = {
    Pos_Well_Roof = "位置/Pos_Well_Roof",
    Pos_Well_Bottom = "位置/Pos_Well_Bottom",
    Pos_Defeat_Enemy = "位置/Pos_Defeat_Enemy",
    Pos_Defeat_Boss_1 = "位置/Pos_Defeat_Boss_1",
    Pos_Defeat_Boss_2 = "位置/Pos_Defeat_Boss_2",
    Pos_Defeat_Boss_3 = "位置/Pos_Defeat_Boss_3",
    Pos_Defeat_Boss_4 = "位置/Pos_Defeat_Boss_4",
    telePos_boss1 = "位置/telePos_boss1",
    telePos_boss2 = "位置/telePos_boss2",
    telePos_boss3 = "位置/telePos_boss3",
    start = "位置/start",
    pre_start = "位置/pre_start",
}

---@class 资产_挑战_沙匪营地
local assets = {
}

---@class 动作_挑战_沙匪营地
local animationClips = {
}

---@class 挑战_沙匪营地
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
