local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
--玩家队伍
local team = bcapi.get_team(0)
--敌人队伍
local eTeam = bcapi.get_team(1)

function init()
    battle.ForceNotEndBattle = true
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("玉尺", 68, team, bcapi.Vector2(-4, 0), 0)
    end)
end

function start()
    bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 1, TalkEnd)
    StartTalk()
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        team = bcapi.get_team(0)
        bcapi.talk("玉尺", "老鼠和鹰，难道说的墨七和我？")
        bcapi.talk("玉尺", "看样子你是收钱办事的，你的雇主是谁？")
        bcapi.talk("求道者（？）", "我没问他的名字，要不你随我去亲眼见见呗~")
        bcapi.resume_and_show_ui()
    end)
end

function TalkEnd()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("求道者（？）", "怎么……可能……")
        bcapi.talk("求道者（？）", "机关……机关不会比人快……")
        bcapi.resume_and_show_ui()
        battle.ForceNotEndBattle = false
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
