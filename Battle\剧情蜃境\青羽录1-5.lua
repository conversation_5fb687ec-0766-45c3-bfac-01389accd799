local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)
local hasStartTalk = 0
local zhujue, <PERSON><PERSON>heng, MoZhiyou

function init()
    bcapi.ta_event_track("PlayerStartStory", "Step", "BattleTutor_Step6")
    battle.HideCardLevel = true
    
    bcapi.disable_auto_battle()
    bcapi.disable_speed_up()
    bcapi.disable_dart()
    
    bcapi.hide_ui("自动按钮")
    bcapi.hide_ui("身法按钮")

    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        -- 创建玩家角色
        bcapi.create_join_new_player_with_card(1, team, bcapi.Vector2(-1, -1.1), 0)
        bcapi.create_join_role_with_card("莫知酉", 1, team, bcapi.Vector2(-5, 3), 0)
        bcapi.create_join_role_with_card("荆成", 1, team, bcapi.Vector2(-5, -4.5), 0)
        zhujue = bcapi.get_player_role("主角")
        Mo<PERSON>hiyou = bcapi.get_player_role("莫知酉")
        JingCheng = bcapi.get_player_role("荆成")
        --由于登场时间修改了，这里加个阻塞，保证演出效果和之前一致
        MoZhiyou.Buff:AddBuff("通用_阻塞", 1.3, 1, 1, MoZhiyou)
        JingCheng.Buff:AddBuff("通用_阻塞", 1.3, 1, 1, JingCheng)
        bcapi.add_strategy_card("梧叶舞秋风", 1, team)
        bcapi.add_strategy_card("血沸剑魂", 1, team)
        bcapi.add_strategy_card("光明圣火", 1, team)
        bcapi.add_strategy_card("大藏心经", 1, team)

        -- 创建敌人
        bcapi.create_join_role("蜃境_护院_地", 4, eTeam, bcapi.Vector2(3, -0.9), 0)
        bcapi.create_join_role("蜃境_门派高等弟子_地", 4, eTeam, bcapi.Vector2(5, 3), 0)
        bcapi.create_join_role("蜃境_江湖盗匪老大_地", 4, eTeam, bcapi.Vector2(5, -4.5), 0)
        bcapi.create_join_role("蜃境_江湖高手女_地", 4, eTeam, bcapi.Vector2(7, -1), 0)
        bcapi.wait(0.1)
    end)
end

function start()
    bcapi.add_timer_trigger(5, StartTalk)
    bcapi.add_condition_trigger("[%teammate:主角->hp%][<]400", 0, StartTalk)
end

function StartTalk()
    if hasStartTalk == 1 then
        return
    end
    hasStartTalk = 1
        bcapi.async_call(function()
        bcapi.create_join_role_with_card_await("燕路重", 3, team, bcapi.Vector2(-7, -1), 0.1)  
        bcapi.wait(0.5)      
        bcapi.pause_and_hide_ui()
        bcapi.talk("旁白", "你正受到蜃境怪物的围攻，情况危急。")
        bcapi.talk("燕路重#燕路重", "少侠勿慌，我来助你！")
        bcapi.resume_and_show_ui()
        bcapi.show_unique_skill_guide("燕路重", "主角", "使用燕路重的绝招，保护自己。")

        bcapi.add_timer_trigger(5, Replace)
    end)
end

function Replace()
    bcapi.async_call(function()
        bcapi.add_role_card("云舞笙", 3, 0, 0, team)
        bcapi.wait(0.1)
        
        bcapi.pause_and_hide_ui()
        bcapi.show_hint_desc_panel("换人", "换人", "当场上已有4名侠客时，可以通过换人使替补的侠客加入战斗。拖拽右侧未登场侠客至场上任意侠客的位置，即可将其替换下场。在场下的侠客，将持续恢复生命。", "知道了")
        bcapi.resume_and_show_ui()

        --有合法登场牌才出现按头教学
        bcapi.show_replace_role_guide("云舞笙", "主角", "将云舞笙拖至场上，将会换下选中的侠客，建议选择受伤较重的侠客！")
    end)
end


function ForceGetUniqueCard(roleKey, i)
    local index = 0
    while (HasUniqueCard(roleKey) == false and index <= 20) do
        ShuffleHandPile(i)
        index = index + 1
    end
end

function HasUniqueCard(roleKey)
    local card = bcapi.get_hand_pile_by_key(roleKey)
    return card ~= nil
end

function ShuffleHandPile(i)
    -- 弃掉对应牌
    local lastCard = bcapi.get_hand_pile(i);
    team.CardSystem:ForceDiscardCard(lastCard)
    team.CardSystem:DrawCard()
end