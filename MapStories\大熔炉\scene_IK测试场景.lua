
---@type IK测试场景
local context = require("MapStories/大熔炉/scene_IK测试场景_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_大熔炉
local flags = context.flags --[[@as flags_大熔炉]]

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    
end

--第一次进入地图
function first_time_into_map()
    -- 填写逻辑
end

function hello()
    s.aside("你好")
end

function TestTurn()
    s.wait(2)
end