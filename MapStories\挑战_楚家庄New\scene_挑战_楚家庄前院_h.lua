--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_楚家庄New/flags_挑战_楚家庄New")


---@class 角色_挑战_楚家庄前院
local npcs = {
    zhujue = "角色/zhujue",
    enemy_1 = "角色/enemy_1",
    enemy_2 = "角色/enemy_2",
    enemy_3 = "角色/enemy_3",
    enemy_4 = "角色/enemy_4",
    boss1 = "角色/boss1",
    shadow = "角色/shadow",
    npc_1 = "角色/npc_1",
    npc_2 = "角色/npc_2",
    npc_3 = "角色/npc_3",
    npc_4 = "角色/npc_4",
    npc_5 = "角色/npc_5",
    npc_6 = "角色/npc_6",
    npc_7 = "角色/npc_7",
    npc_em = "角色/npc_em",
}

---@class 物体_挑战_楚家庄前院
local objs = {
    enterHint1 = "物体/enterHint1",
    boss1 = "物体/boss1",
    teleport1 = "物体/teleport1",
    teleport2 = "物体/teleport2",
    trigger_em = "物体/trigger_em",
    trigger_medicinal_1 = "物体/trigger_medicinal_1",
    medicinal_1 = "物体/medicinal_1",
    chest1 = "物体/chest1",
}

---@class 相机_挑战_楚家庄前院
local cameras = {
    overall = "相机/overall",
    boss1_1 = "相机/boss1_1",
    boss1_2 = "相机/boss1_2",
}

---@class 位置_挑战_楚家庄前院
local pos = {
    start = "位置/start",
    boss1Pos = "位置/boss1Pos",
    pos_npc_1 = "位置/pos_npc_1",
    pos_npc_2 = "位置/pos_npc_2",
    pos_npc_3 = "位置/pos_npc_3",
    pre_start = "位置/pre_start",
    telePos1 = "位置/telePos1",
    pos_boss1 = "位置/pos_boss1",
}

---@class 资产_挑战_楚家庄前院
local assets = {
}

---@class 动作_挑战_楚家庄前院
local animationClips = {
}

---@class 挑战_楚家庄前院
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
