local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
--玩家队伍
local team = bcapi.get_team(0)
--敌人队伍
local eTeam = bcapi.get_team(1)

function init()
    battle.ForceNotEndBattle = true
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("玉尺", 71, team, bcapi.Vector2(-4, 0), 0)
        bcapi.create_join_role("剧情_程夫子", 71, team, bcapi.Vector2(-4, 0), 0)
    end)
end

function start()
    bcapi.add_condition_trigger("[%enemy:剧情_玄阙地宫_机关泰坦->hp_pct%][<]0.5", 1, Talk50)
    StartTalk()
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        team = bcapi.get_team(0)
        bcapi.talk("玉尺", "夫子！你的伤——")
        bcapi.talk("程夫子", "无碍。")
        bcapi.talk("玉尺", "它是什么时候出现的？")
        bcapi.talk("程夫子", "在中央石室陷阱启动的时候。")
        bcapi.talk("玉尺", "少说过去了一个半时辰，它竟能与您缠斗如此之久！")
        bcapi.talk("程夫子", "是啊，恐怕它的体内，也有烬砂。")
        bcapi.talk("机关兽", "吼————")
        bcapi.resume_and_show_ui()
    end)
end

function Talk50()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("玉尺", "夫子，这个机关兽攻击方式一向如此吗？")
        bcapi.talk("程夫子", "是，有进有退，时攻时守。")
        bcapi.talk("玉尺", "【喃喃自语】中心。")
        bcapi.talk("程夫子", "你发现了什么？")
        bcapi.talk("玉尺", "它运作一段时间后就会在场地中心处积蓄力量，并且进入防御姿态，之前也是这样吗？")
        bcapi.talk("程夫子", "等我想一下，我的脑袋才挨了他一锤。")
        bcapi.talk("程夫子", "想起来了，的确如此。")
        bcapi.talk("玉尺", "那么，夫子，现在和我一起击破它的防御，可能就找到了它的弱点。")
        bcapi.talk("程夫子", "哈哈！得令！")
        bcapi.resume_and_show_ui()
        battle.ForceNotEndBattle = false
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
