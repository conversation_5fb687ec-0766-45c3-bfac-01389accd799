
---@type 蜃境_雾锁寒山_03天琅山
local context = require("MapStories/蜃境_雾锁寒山_03天琅山/scene_蜃境_雾锁寒山_03天琅山_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_雾锁寒山_03天琅山
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")

function testanim()
    --s.animState(npcs.boss_6, "Special_Loop_1",true,false,0,true,true,0)
    s.soloAnimState(npcs.boss_6, "Special_1",true)
end

function testCam()

end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function test()
   s.setTaskStep("雾锁寒山阿莫2","finished")
   refreshMapStates()
end

function start()
    if flags.amo_task_finished == 1 then
        s.setActive(npcs.amo,false)
    end

    refreshMapStates()
    s.readyToStart(true)
    try_execute_newbieGuide()
end


--精英怪战斗
function eliteBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

--boss战斗
function bossBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数
    s.playMusic("蜃境")
    if flags.boss_6_battle == 0 and flags.enemy_1_battle == 1 and flags.enemy_2_battle == 1 and flags.enemy_3_battle == 1 and flags.enemy_4_battle == 1 then
        s.popInfo("蜃境守卫出现！！！")
        showBoss()
    end

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 4) then
        taskStep = "forwardToExplore"
    elseif (step == 4) then
        taskStep = "leave"
    end
    s.setTaskStep("雾锁寒山天琅山", taskStep)

    reset_all()
    --第一次进入场景
    if step == 0 then
        s.setTaskStep("雾锁寒山天琅山","forwardToExplore")
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.wshs_07, 1)

        --进场演出
        if flags.start_play == 0 then
            s.readyToStart(true)
            s.cameraAsync(cameras.cam_tl_far,true,true,blendHintEnum.Cut)
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_进入天琅山")
            s.cameraAsync(cameras.cam_tl_near,true,true,blendHintEnum.Custom,5,true,true)
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_进入天琅山1")
            s.cameraAsync(cameras.cam_main,true,true,blendHintEnum.Custom,2,true,true)
            flags.start_play = 1
        end

    elseif step == 2 then
        level_guide_to(npcs.wshs_08, 0.5)
    elseif step == 3 then
        level_guide_to(npcs.wshs_09, 1)
    else
        level_guide_to(objs.exit)
    end
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.wshs_07, false)
    s.setActive(npcs.wshs_08, false)
    s.setActive(npcs.wshs_09, false)

    if flags.open_shadowChest_1 == 1 then
        s.setActive(objs.evilAirChest_1,false)
    else
        s.setActive(objs.evilAirChest_1,true)
    end

    for i = 1, 4 do
        if flags["enemy_" .. i .. "_battle"] == 1 then
            s.setActive(objs["enemy_" .. i .. "_collider"], false)
        end
    end

    if flags.boss_6_battle == 1 then
        s.setActive(npcs.boss_6,true)
    else
        s.setActive(npcs.boss_6,false)
    end

    if s.getCurrentTaskStep("雾锁寒山阿莫3") == "deactive" and s.getCurrentTaskStep("雾锁寒山阿莫2") == "finished" then
        s.setActive(npcs.amo,true)
        s.setActive(npcs.sisi,true)

        s.setActive(objs.trigger_amo,true)
    elseif s.getCurrentTaskStep("雾锁寒山阿莫3") == "findSisi" then
        s.setActive(npcs.amo,true)
        s.setActive(npcs.sisi,true)

        s.setActive(objs.trigger_amo,false)
    elseif s.getCurrentTaskStep("雾锁寒山阿莫3") == "backToAmo" then
        s.setActive(npcs.amo,true)
        s.setActive(npcs.sisi,true)

        s.setActive(objs.trigger_amo,false)
    elseif s.getCurrentTaskStep("雾锁寒山阿莫3") == "findBag" then
        s.setActive(npcs.amo,true)
        s.setActive(npcs.sisi,true)

        s.setActive(objs.trigger_amo,false)

        if flags.amo_bag_finished == 1 then
            s.readyToStart(true)
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_阿莫支线_提前拿到荷包")
            s.setTaskStep("雾锁寒山阿莫3","giveBagToAmo")
        end
    elseif s.getCurrentTaskStep("雾锁寒山阿莫3") == "giveBagToAmo" then
        s.setActive(npcs.amo,true)
        s.setActive(npcs.sisi,true)
        s.setActive(objs.trigger_amo,false)
    end

    --收集物显示
    if flags.collect_1000 == 1 then
        s.setActive(objs.wshs3_1000,true)
    else
        s.setActive(objs.wshs3_1000,false)
    end

    if flags.collect_1004 == 1 then
        s.setActive(objs.wshs3_1004,false)
    else
        s.setActive(objs.wshs3_1004,true)
    end

    --删除所有的引导点
    s.removeAllGameMapGuides()
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("雾锁寒山")
end
-------------------地图跑图---------------------


-------------------支线和解密---------------------
function amo()
    if s.getCurrentTaskStep("雾锁寒山阿莫3") == "findSisi" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_阿莫支线_找思思阿莫闲话")
    elseif s.getCurrentTaskStep("雾锁寒山阿莫3") == "backToAmo" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_阿莫支线_返回阿莫处对话")
        s.setTaskStep("雾锁寒山阿莫3","findBag")
        s.nextStep("refreshMapStates")
    elseif (s.getCurrentTaskStep("雾锁寒山阿莫3") == "giveBagToAmo") or (s.getCurrentTaskStep("雾锁寒山阿莫3") == "findBag") then

        local submit = s.submitItem("阿莫的荷包")
        if submit ~= nil and submit[0].Key == "蜃境_雾锁寒山_阿莫_荷包"  then
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_阿莫支线_归还金沙")
            s.setTaskStep("雾锁寒山阿莫3","finished")
    
            s.changeItemCount("蜃境_雾锁寒山_阿莫_荷包",-1)

            flags.amo_task_finished = 1
            --开启画记
            flags.collect_1000 = 1
    
            capi.finishMemorySlot("wshs3_sideTask_amo3")
        else
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_阿莫支线_找荷包阿莫闲话") 
        end
    elseif s.getCurrentTaskStep("雾锁寒山阿莫3") == "finished" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_阿莫支线_结束")
    end
end

function amo_meet()
    s.setActive(objs.trigger_amo,false)
    s.turnTo(npcs.amo,npcs.zhujue)
    s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_阿莫支线_遇见阿莫")
    s.setTaskStep("雾锁寒山阿莫3","findSisi")
    s.nextStep("refreshMapStates")
end

function sisi()
    if s.getCurrentTaskStep("雾锁寒山阿莫3") == "findSisi" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_阿莫支线_思思对话")
        s.setTaskStep("雾锁寒山阿莫3","backToAmo")
    else
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_阿莫支线_思思闲话")
    end
end

function huaji_1000(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
    flags.collect_1000 = 2
end

function shudu_1004(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
    flags.collect_1004 = 1
end

function shadowChest(id)
    if flags.open_shadowChest_1 == 0 then
        s.popInfo("需击败附近镇守的蜃境怪物！！！")
    else
        require("MapStories/MapStoryCommonTools").roamMapChest(id)
    end
end

-------------------和战斗实现---------------------
--蜃境怪物出现
function evilAir_1(id)
    s.setActive(objs.evilAir_1,false)
    s.setActive(npcs.role_elite_5, true)
    s.turnTo(npcs.role_elite_5,npcs.zhujue)
    s.animState(npcs.role_elite_5,"BAssault_Loop",true)
    s.wait(2)
    elite_5_fight(id)
end

function elite_5_fight(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,
    function()
        flags.open_shadowChest_1 = 1
        s.setActive(objs.evilAirChest_1,false)
        refreshMapStates()
    end,
    function()
        s.animState(npcs.role_elite_5,"BAssault_Loop",false)    
    end,
    function()
        s.animState(npcs.role_elite_5,"BAssault_Loop",false)
    end
    )
end

function enemy_battle(id,enemyKey)
    print("敌人战斗")
    local colliderKey = enemyKey.."_collider"

    s.popInfo("你已被发现！！！")

    s.turnTo(npcs[enemyKey],npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs[enemyKey])

    s.animState(npcs[enemyKey],"BAssault_Loop",true)
    s.wait(2)

    require("MapStories/MapStoryCommonTools").roamMapBattle(id,function()
        s.popInfo("战斗胜利！！！")
        flags[enemyKey.."_battle"] = 1
        s.setActive(objs[colliderKey],false)
    end,
    function()
        battle_quit(id,enemyKey)
    end,
    function()
        battle_lose(id,enemyKey)
    end
)

    s.wait(0.5)
    refreshMapStates()
end

function fightInBack(enemyKey)
    local colliderKey = enemyKey.."_collider"
    s.setActive(objs[colliderKey],false)

    s.animState(npcs.zhujue,"Special_2",true)
    s.wait(1)
    s.playSound("sfx","拳击3")
    s.wait(0.6)

    s.animState(npcs[enemyKey],"BDie",true)
    s.setDeath(npcs[enemyKey],0.5,false)
    s.popInfo("敌人暂时被击倒！！！")
end

function battle_lose(id,enemyKey)
    local colliderKey = enemyKey.."_collider"

    s.popInfo("战斗失败！！！")

    s.blackScreen()
    s.animState(npcs[enemyKey],"BAssault_Loop",false)
    s.setActive(objs[colliderKey],true)
    s.setPos(npcs.zhujue,pos[enemyKey],true)
    s.lightScreen()
end

function battle_quit(id,enemyKey)
    local colliderKey = enemyKey.."_collider"

    s.blackScreen()
    s.animState(npcs[enemyKey],"BAssault_Loop",false)
    s.setActive(objs[colliderKey],true)
    s.setPos(npcs.zhujue,pos[enemyKey],true)
    s.lightScreen() 
end

function showBoss()
    s.blackScreen()
    s.setActive(npcs.boss_6,true)
    s.camera(cameras.cam_boss_far,true,true,blendHintEnum.Cut,0.5,true,true)
    s.animState(npcs.boss_6, "Special_1",true)
    s.wait(0.1)
    s.soloAnimState(npcs.boss_6, "BAssault_Loop",true)

    s.lightScreen()

    s.cameraAsync(cameras.cam_boss_near,true,true,blendHintEnum.Custom,3,true,true)
    s.wait(3)
    s.blackScreen()
    s.soloAnimState(npcs.boss_6, "BAssault_Loop",false)
    s.camera(cameras.cam_main,true,true,blendHintEnum.Cut,1,true,true)
    s.lightScreen()

    s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_涂舟BOSS")

    s.popInfo("击败<color=yellow>蜃境涂舟</color>！！！")
    --设置boss战斗状态
    flags.boss_6_battle = 1
end

function fightWithBoss(id)
    s.turnTo(npcs.boss_6,npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs.boss_6)
    s.soloAnimState(npcs.boss_6, "BAssault_Loop",true)

    require("MapStories/MapStoryCommonTools").roamMapBattle(id,function()
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_阿莫支线_打败BOSS")

        s.changeItemCount("蜃境_雾锁寒山_阿莫_荷包",1)
        flags.amo_bag_finished = 1
        flags.boss_6_battle = 2
    end,
    function()
        s.setPos(npcs.zhujue,pos.tuzhou_back)
        s.soloAnimState(npcs.boss_6, "BAssault_Loop",false)
    end,
    function()
        s.setPos(npcs.zhujue,pos.tuzhou_back)
        s.soloAnimState(npcs.boss_6, "BAssault_Loop",false)
    end
)
end

-----------------------关卡记忆碎片-----------------------

function wshs_07()
    s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_天琅山","蜃境_雾锁寒山_墨七虚影1")
    executeMemSlot("wshs_07", function()
        require("Chapters/雾锁寒山").wshs3_07()
    end)
end

function wshs_08()
    executeMemSlot("wshs_08", function()
        require("Chapters/雾锁寒山").wshs3_08()
    end)
end

function wshs_09()
    executeMemSlot("wshs_09", function() 
        require("Chapters/雾锁寒山").wshs3_09()
        s.setTaskStep("雾锁寒山天琅山","leave")
    end)
end
