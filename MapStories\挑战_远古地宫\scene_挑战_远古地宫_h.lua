--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_远古地宫/flags_挑战_远古地宫")


---@class 角色_挑战_远古地宫
local npcs = {
    --- "墨七"
    zhujue = "角色/zhujue",
    enemy1 = "角色/enemy1",
    enemy_1 = "角色/enemy_1",
    enemy_2 = "角色/enemy_2",
    enemy_3 = "角色/enemy_3",
    enemy_4 = "角色/enemy_4",
    enemy_6 = "角色/enemy_6",
    enemy_8 = "角色/enemy_8",
    enemy_9 = "角色/enemy_9",
    enemy_10 = "角色/enemy_10",
    enemy_11 = "角色/enemy_11",
    boss_1 = "角色/boss_1",
    boss_2 = "角色/boss_2",
    boss_3 = "角色/boss_3",
    boss_4 = "角色/boss_4",
    boss_5 = "角色/boss_5",
    boss_6 = "角色/boss_6",
    bear = "角色/bear",
    role1 = "角色/role1",
}

---@class 物体_挑战_远古地宫
local objs = {
    --- "机关石门0"
    stoneDoor0 = "物体/stoneDoor0",
    --- "机关石门1"
    stoneDoor1 = "物体/stoneDoor1",
    --- "机关石门2"
    stoneDoor2 = "物体/stoneDoor2",
    storyStartTrigger = "物体/storyStartTrigger",
    testOpenDoor = "物体/testOpenDoor",
    BOSS_Door = "物体/BOSS_Door",
    boss_1_trigger1 = "物体/boss_1_trigger1",
    boss_1_trigger2 = "物体/boss_1_trigger2",
    middle_trigger1 = "物体/middle_trigger1",
    middle_trigger2 = "物体/middle_trigger2",
    middle_trigger3 = "物体/middle_trigger3",
    exboss_door1 = "物体/exboss_door1",
    boss_5_trigger = "物体/boss_5_trigger",
    exboss_trigger = "物体/exboss_trigger",
    mech_light1 = "物体/mech_light1",
    mech_light2 = "物体/mech_light2",
    mech_light3 = "物体/mech_light3",
    mech_light_main = "物体/mech_light_main",
    mech_light_trigger = "物体/mech_light_trigger",
    boss_2_trigger = "物体/boss_2_trigger",
    boss_3_trigger = "物体/boss_3_trigger",
    boss_4_trigger = "物体/boss_4_trigger",
    bear_eye = "物体/bear_eye",
    exboss_1_trigger = "物体/exboss_1_trigger",
}

---@class 相机_挑战_远古地宫
local cameras = {
    --- "俯视"
    overview = "相机/overview",
    start_camera1 = "相机/start_camera1",
    start_camera1_1 = "相机/start_camera1_1",
    start_camera2 = "相机/start_camera2",
    start_camera2_1 = "相机/start_camera2_1",
    start_camera3 = "相机/start_camera3",
    start_camera3_1 = "相机/start_camera3_1",
    start_enemy_camera_pos = "相机/start_enemy_camera_pos",
    start_enemy_camera_pos_1 = "相机/start_enemy_camera_pos_1",
    start_enemy_camera_pos_2 = "相机/start_enemy_camera_pos_2",
    start_enemy_camera_pos_3 = "相机/start_enemy_camera_pos_3",
    boss_1_camera_1 = "相机/boss_1_camera_1",
    middle_camera_1 = "相机/middle_camera_1",
    middle_camera_2 = "相机/middle_camera_2",
    middle_camera_3 = "相机/middle_camera_3",
    middle_camera_4 = "相机/middle_camera_4",
    boss_5_camera = "相机/boss_5_camera",
    mid_camera_d = "相机/mid_camera_d",
    mid_camera_1 = "相机/mid_camera_1",
    mid_camera_2 = "相机/mid_camera_2",
    mid_camera_3 = "相机/mid_camera_3",
    mid_camera_f = "相机/mid_camera_f",
    boss_2_camera = "相机/boss_2_camera",
    boss_3_camera = "相机/boss_3_camera",
    boss_4_camera_1 = "相机/boss_4_camera_1",
    boss_4_camera_2 = "相机/boss_4_camera_2",
    boss_4_camera_3 = "相机/boss_4_camera_3",
    boss_4_camera_4 = "相机/boss_4_camera_4",
    boss_4_camera_5 = "相机/boss_4_camera_5",
    exboss_camera = "相机/exboss_camera",
    exboss_camera_1 = "相机/exboss_camera_1",
    exboss_camera_2 = "相机/exboss_camera_2",
}

---@class 位置_挑战_远古地宫
local pos = {
    --- "开场位置"
    start = "位置/start",
    door_pos = "位置/door_pos",
    secret_telepot = "位置/secret_telepot",
    secret_start = "位置/secret_start",
    battle_pos_1 = "位置/battle_pos_1",
    boss1_pos1 = "位置/boss1_pos1",
    boss1_pos2 = "位置/boss1_pos2",
    middle_place_pos = "位置/middle_place_pos",
    boss5_pos1 = "位置/boss5_pos1",
    boss5_pos2 = "位置/boss5_pos2",
    start1 = "位置/start1",
    door2_pos = "位置/door2_pos",
    door3_pos = "位置/door3_pos",
    boss2_pos = "位置/boss2_pos",
    boss3_pos = "位置/boss3_pos",
    boss2_pos_move = "位置/boss2_pos_move",
    boss4_pos1 = "位置/boss4_pos1",
    boss4_pos2 = "位置/boss4_pos2",
    boss4_test_pos = "位置/boss4_test_pos",
    boss4_bear_pos = "位置/boss4_bear_pos",
    exboss_pos_1 = "位置/exboss_pos_1",
    exboss_pos_2 = "位置/exboss_pos_2",
}

---@class 资产_挑战_远古地宫
local assets = {
}

---@class 动作_挑战_远古地宫
local animationClips = {
}

---@class 挑战_远古地宫
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
