local s = require("StoryApi")

local extension = require 'MapStories/大熔炉/extension'
---@type flags_大熔炉
local flags = require("MapStories/大熔炉/flags_大熔炉") --[[@as flags_大熔炉]]
s.loadFlags(flags)
---@type 大熔炉
local daronglu = require 'MapStories/大熔炉/scene_大熔炉_h'
---@type 小熔炉
local xiaoronglu = require 'MapStories/大熔炉//scene_小熔炉_h'

local mozhiyou = "角色/mozhiyou"

---@type ItemWidget
local itemWidget = extension.widgets.ItemWidgetInfo.widget
---@type 大熔炉_ItemData
local itemData = extension.widgets.ItemWidgetInfo.items --[[@as 大熔炉_ItemData]]
---@type StringWidget
local stringWidget = extension.widgets.StringWidgetInfo.widget
---@type 大熔炉_StringItemData
local stringItemData = extension.widgets.StringWidgetInfo.items

function on_use_item(itemKey,sceneId)
    if itemKey == itemData.test1.key then
        on_use_testItem1()
    end
end

---不可改名
function get_string_item_desc(itemKey,sceneId)
    if itemKey == stringItemData.test1.key then
        return "测试物品1的描述123"
    elseif itemKey == stringItemData.test2.key then
        return "测试物品2的描述"
    end
    return "没有描述"
end

function on_use_testItem1()
    s.talk(mozhiyou, "物品1的数量是" .. tostring(itemWidget:getItemCount(itemData.test1)))
    s.talk(mozhiyou, "使用了物品1")
    itemWidget:removeItem(itemData.test1, 1)
    s.talk(mozhiyou, "物品1的数量是" .. tostring(itemWidget:getItemCount(itemData.test1)))
end