
---@type 漫游_楚家庄_别院
local context = require("MapStories/漫游_楚家庄/scene_漫游_楚家庄_别院_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi

s.loadFlags(context.flags)

---@type flags_漫游_楚家庄
local flags = context.flags

---组件相关
local extensions = require("MapStories/漫游_楚家庄/extension")
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type 楚家庄_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 楚家庄_ItemData]]

---@type RoleTrigger
local roleAnims = RoleTrigger

---@type ObjectTrigger
local objAnims = ObjTrigger

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.playMusic("huanxing")
    s.readyToStart()
    reloadAllSceneStates()
end

function reloadAllSceneStates()
    --竹节人老头
    s.setActive(npcs.laopu, flags.zhujiexia_newbie_win > 0)
    print("竹节人老头:"..flags.zhujiexia_newbie_win)
    s.setActive(objs.suggest_zhujiexia, flags.zhujiexia_step == 0)

    --小石头
    s.setActive(objs.xiaoshitou, flags.stone_in_mud < 2)
    s.setActive(objs.xiaoshitoucaheng, flags.stone_in_mud == 2)

    --宝箱2
    s.setActive(objs.triggerChest2, flags.chest2 == 0)
    if(flags.chest2 == 1) then
        s.soloAnimState(objs.chest2, objAnims.TurnOnDirect)
    end

    --宝箱3
    s.setActive(objs.triggerChest3, flags.chest3 == 0)
    if(flags.chest3 == 1) then
        s.soloAnimState(objs.chest3, objAnims.TurnOnDirect)
    end

    --宝箱4
    s.setActive(objs.triggerChest4, flags.chest4 == 0)
    if(flags.chest4 == 1) then
        s.soloAnimState(objs.chest4, objAnims.TurnOnDirect)
    end

    --宝箱5
    s.setActive(objs.triggerChest5, flags.chest5 == 0)
    if(flags.chest5 == 1) then
        s.soloAnimState(objs.chest5, objAnims.TurnOnDirect)
    end
end

function wrappedReloadAllSceneStates()
    s.blackScreen()
    reloadAllSceneStates()
    s.lightScreen()
end


function transportQianyuan()
    s.changeMap("Assets/GameScenes/游历场景/漫游_楚家庄/漫游_楚家庄_前院.unity", "位置/transportHoushan")
end

---@gameEvent 竹节侠
function eventZhujiexia()
    if (flags.zhujiexia_newbie_win == 1) then
        s.aside("桌上摆着竹节侠玩具。")
    elseif(flags.zhujiexia_step == 0) then
        zhujiexia001()
        flags.zhujiexia_step = 1
    elseif(flags.zhujiexia_step == 1) then
        askForZhujiexia()
    end
end

---@gameEvent 竹节侠老仆
function eventNpcLaopu()
    zhujiexiaLaopu()
    
end


function zhujiexiaLaopu()
    if(flags.zhujiexia_laopu_win == 0) then
        s.blackScreen()
        s.setActive(npcs.mozhiyou,true)
        s.camera(cameras.zhujiexia2,true)
        s.setPos(npcs.jingcheng,pos.JC_zhujiexia2)
        s.setPos(npcs.mozhiyou,pos.MZY_zhujiexia2)
        s.turnToAsync(npcs.laopu, npcs.jingcheng)
        s.turnToAsync(npcs.jingcheng, npcs.laopu)
        s.turnToAsync(npcs.mozhiyou, npcs.laopu)
        s.wait(0.5)
        s.lightScreen()
        
        s.talk(npcs.laopu, "这位没有白发的小兄弟，准备好了就过来吧。")

        local ret = s.selectTalk(npcs.jingcheng, "要与看上去很厉害的老头来一把竹节侠么？", {"好啊", "不了"})
        if(ret == 1) then
            return
        end

        --是否竹节侠胜利
        ret = s.selectTalk(npcs.jingcheng, "竹节侠游戏的结果是:", {"胜利", "失败"})
        if(ret == 0) then
            flags.zhujiexia_laopu_win = 1
            zhujiexia003()
        else
            s.talk(npcs.laopu, "哈哈哈，年轻人，还是多练练吧。")
        end
        
        s.blackScreen()
        s.setActive(npcs.mozhiyou,false)
        s.camera(cameras.zhujiexia2,false)
        s.wait(0.5)
        s.lightScreen()
    else
        s.talk(npcs.laopu, "不错不错，后生可畏。")
    end
end

function zhujiexia001()
    s.blackScreen()
    s.setActive(npcs.mozhiyou,true)
    s.camera(cameras.zhujiexia,true)
    s.setPos(npcs.jingcheng,pos.JC_zhujiexia)
    s.setPos(npcs.mozhiyou,pos.MZY_zhujiexia)
    s.wait(0.5)
    s.lightScreen()
    
    s.talk(npcs.mozhiyou, "啊——是竹节侠。")
    s.talk(npcs.jingcheng, "竹节侠？")
    s.animState(npcs.mozhiyou,roleAnims.BAkimbo_Loop,true)
    s.talk(npcs.mozhiyou, "我小时候玩这个，可是打遍周围三村三镇无敌手！")
    s.animState(npcs.mozhiyou,roleAnims.BAkimbo_Loop,false)
    s.talk(npcs.jingcheng, "用这个……怎么打？")
    s.animState(npcs.mozhiyou,roleAnims.BScratch)
    s.talk(npcs.mozhiyou, "难道你们那边不玩这个？")
    s.animState(npcs.jingcheng, roleAnims.BDeny)
    s.talk(npcs.jingcheng, "我不知道我们那边小孩子玩什么……我多数时间都在跟师父习武。")
    s.talk(npcs.mozhiyou, "天天习武，木桩子都搓出火星子了，有啥意思。")
    s.talk(npcs.jingcheng, "也不只有习武……")
    s.animState(npcs.jingcheng, roleAnims.BScratch)
    s.talk(npcs.jingcheng, "有时候师兄会带我念书。")
    s.talkAmazed(npcs.mozhiyou, "念书？！")
    s.animState(npcs.mozhiyou,roleAnims.BShock_Loop,true)
    s.wait(1)
    s.animState(npcs.mozhiyou,roleAnims.BShock_Loop,false)
    s.talk(npcs.mozhiyou, "年纪轻轻，念甚鸟书！")
    s.talk(npcs.mozhiyou, "来来来，莫大师今日就带你玩玩年轻人该玩的！")

    s.addTask(300040, 1)
    s.setActive(npcs.mozhiyou,false)
    s.camera(cameras.zhujiexia,false)
end

function askForZhujiexia()
    s.blackScreen()
    s.setActive(npcs.mozhiyou,true)
    s.camera(cameras.zhujiexia,true)
    
    s.setPos(npcs.jingcheng,pos.JC_zhujiexia)
    s.setPos(npcs.mozhiyou,pos.MZY_zhujiexia)
    s.wait(0.5)
    s.lightScreen()
    
    s.talk(npcs.mozhiyou, "怎么样？荆少侠，咱们来一把吧？")
    local ret = s.selectTalk(npcs.jingcheng, "要来一把竹节侠么？", {"好啊", "不了"})
    
    if(ret == 1) then
        s.blackScreen()
        s.setActive(npcs.mozhiyou,false)
        s.camera(cameras.zhujiexia,false)
        s.wait(0.5)
        s.lightScreen()
        return
    end
    
    --是否竹节侠胜利
    ret = s.selectTalk(npcs.jingcheng, "竹节侠游戏的结果是:", {"胜利", "失败"})
    if(ret == 0) then

        --首次胜利
        if(flags.zhujiexia_newbie_win == 0) then
            flags.zhujiexia_newbie_win = 1
            zhujiexia002()
        end

        --其他胜利投放
    else
        s.blackScreen()
        s.setActive(npcs.mozhiyou,false)
        s.camera(cameras.zhujiexia,false)
        s.wait(0.5)
        s.lightScreen()
    end


end


function zhujiexia002()
    s.talk(npcs.mozhiyou, "哎哟，厉害厉害。")

    wrappedReloadAllSceneStates()
    s.camera(cameras.zhujiexia2,true)

    s.turnTo(npcs.laopu, npcs.jingcheng)
    s.talk(npcs.laopu, "不错不错，小兄弟天资甚好啊。")
    s.turnToAsync(npcs.jingcheng, npcs.laopu)
    s.turnToAsync(npcs.mozhiyou, npcs.laopu)
    s.wait(1)
    s.animState(npcs.jingcheng, roleAnims.BScratch)
    s.talk(npcs.laopu, "不过老身宝刀未老，要是你能战胜我，老身就给你个好东西。")
    s.talk(npcs.mozhiyou, "老伯，那我来。")
    s.turnTo(npcs.laopu, npcs.mozhiyou)
    s.animState(npcs.laopu,roleAnims.BAkimbo_Loop,true)
    s.talkAmazed(npcs.laopu, "你都满头白发了，还叫我老伯！")
    s.animState(npcs.laopu,roleAnims.BAkimbo_Loop,false)
    s.wait(1)
    s.turnTo(npcs.laopu, npcs.jingcheng)
    s.talk(npcs.laopu, "这位小兄弟面善，看起来就尊老爱幼，我还是跟他玩吧。")
    s.talkThink(npcs.mozhiyou, "（我看你就是欺软怕硬，想玩得久一点。）")
    s.talkThink(npcs.mozhiyou, "（罢了罢了，看在你白头发比我多的份上……）")
    s.talk(npcs.laopu, "这位没有白发的小兄弟，准备好了就过来吧。")

    s.finishTask(300040, 1,2)

    s.blackScreen()
    s.setActive(npcs.mozhiyou,false)
    s.camera(cameras.zhujiexia2,false)
    s.wait(0.5)
    s.lightScreen()
    
    --[[——开启任务
    任务名：竹节侠
    任务描述：楚家庄后山的树墩上有名为竹节侠的玩具，楚家庄老仆邀荆成对决一局。]]
end

function zhujiexia003()

    s.talk(npcs.laopu, "不错不错，后生可畏。")
    s.animState(npcs.laopu,roleAnims.BGive_Loop,true)
    s.talk(npcs.laopu, "这是我上次见到的虎纹布头，就送给二位。")
    s.animState(npcs.jingcheng,roleAnims.BGive_Loop,true)
    s.animState(npcs.laopu,roleAnims.BGive_Loop,false)
    s.talk(npcs.mozhiyou, "这是——")
    s.animState(npcs.jingcheng,roleAnims.BGive_Loop,false)
    s.wait(1)
    s.turnTo(npcs.jingcheng,npcs.mozhiyou)
    s.animState(npcs.jingcheng,roleAnims.BGive_Loop,true)
    s.animState(npcs.mozhiyou,roleAnims.BThink_Loop,true)
    s.talkThink(npcs.mozhiyou, "（真是老花眼了，这哪是虎纹布头，分明是一片画花了的碎布。）")
    s.talkThink(npcs.mozhiyou, "（不过这纹路……藏宝图？）")
    s.animState(npcs.mozhiyou,roleAnims.BThink_Loop,false)
    s.animState(npcs.jingcheng,roleAnims.BGive_Loop,false)
    
    s.aside("【获得藏宝图碎片之三】")
    itemWidget:addItem(itemData.cangbaotu3,1)

    s.finishTask(300040, 2)
    
    flags.main_story_step = flags.main_story_step + 1
end

---@gameEvent 泥地里的小石头
function eventStoneInMud()
    if(flags.stone_in_mud == 0) then
        s.aside("泥地嵌着一块小石子。")
        local ret = s.selectTalk(npcs.jingcheng,"...", {"踢飞", "不予理会"})
        if(ret == 0) then
            s.aside("你将石子一脚踢飞，鞋头沾了些泥。")

            flags.stone_in_mud = 2
            wrappedReloadAllSceneStates()
        elseif (ret == 1)then
            flags.stone_in_mud = 1
            --wrappedReloadAllSceneStates()
        end
    elseif(flags.stone_in_mud == 2) then
        s.aside("泥地上有一处小小的凹槽，代表这里曾经有一颗小石子。")
        s.aside("旁边有些擦痕，代表这里曾经被人踢了一脚。")
        
    elseif(flags.stone_in_mud == 1) then
        s.aside("一块安安静静地躺在泥地中的小石子。")
    end
end

---@gameEvent 宝箱
function chest2()
    if(flags.chest2 == 0) then
        s.openChest(objs.chest2, "TravelChest_1cjz_2")--todo:需调整投放配置
        s.wait(0.5)

        flags.chest2 = 1
        wrappedReloadAllSceneStates()
    end
end

---@gameEvent 宝箱
function chest3()
    if(flags.chest3 == 0) then
        s.openChest(objs.chest3, "TravelChest_1cjz_3")--todo:需调整投放配置
        s.wait(0.5)

        flags.chest3 = 1
        wrappedReloadAllSceneStates()
    end
end

---@gameEvent 宝箱
function chest4()
    if(flags.chest4 == 0) then
        s.openChest(objs.chest4, "TravelChest_1cjz_4")--todo:需调整投放配置
        s.wait(0.5)

        flags.chest4 = 1
        wrappedReloadAllSceneStates()
    end
end

---@gameEvent 宝箱
function chest5()
    if(flags.chest5 == 0) then
        s.openChest(objs.chest5, "TravelChest_1cjz_5")--todo:需调整投放配置
        s.wait(0.5)

        flags.chest5 = 1
        wrappedReloadAllSceneStates()
    end
end