local bcapi = require("BattleClientA<PERSON>")
local battle = args["battle"]
bcapi.battle = battle

function init()
end

function start()
    bcapi.add_timer_trigger(0, TalkStart)
    bcapi.add_condition_trigger("[%c->enemy:副本_醉花荫_琴女%][<]1", 0, BattleWin)
end

function TalkStart()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_醉花荫_琴女#副本_醉花荫_琴女", "我素来喜好音律，便以琴声挑战，请！")
        bcapi.resume_and_show_ui()
    end)
end

function BattleWin()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_醉花荫_琴女#醉花荫_琴女", "很好很好，恭喜几位！")
        bcapi.resume_and_show_ui()
    end)
end