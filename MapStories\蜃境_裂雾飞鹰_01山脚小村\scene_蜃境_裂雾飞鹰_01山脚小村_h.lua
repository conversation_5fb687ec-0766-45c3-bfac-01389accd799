--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_裂雾飞鹰_01山脚小村/flags_蜃境_裂雾飞鹰_01山脚小村")


---@class 角色_蜃境_裂雾飞鹰_01山脚小村
local npcs = {
    zhujue = "角色/zhujue",
    lwfy_01 = "角色/lwfy_01",
    lwfy_02 = "角色/lwfy_02",
    enemy_1 = "角色/enemy_1",
    enemy_2 = "角色/enemy_2",
    dsyy_gril = "角色/dsyy_gril",
    eyu = "角色/eyu",
}

---@class 物体_蜃境_裂雾飞鹰_01山脚小村
local objs = {
    exit = "物体/exit",
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
    dsyy_item = "物体/dsyy_item",
    dsyy_trigger = "物体/dsyy_trigger",
}

---@class 相机_蜃境_裂雾飞鹰_01山脚小村
local cameras = {
    start_cam = "相机/start_cam",
    show_cam_1 = "相机/show_cam_1",
    show_cam_2 = "相机/show_cam_2",
    timeline_cam = "相机/timeline_cam",
    timeline_cam2 = "相机/timeline_cam2",
}

---@class 位置_蜃境_裂雾飞鹰_01山脚小村
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
    zhujue_start_pos_1 = "位置/zhujue_start_pos_1",
    timeline_pos_1 = "位置/timeline_pos_1",
    timeline_pos_2 = "位置/timeline_pos_2",
}

---@class 资产_蜃境_裂雾飞鹰_01山脚小村
local assets = {
    exit = "资产/exit",
}

---@class 动作_蜃境_裂雾飞鹰_01山脚小村
local animationClips = {
}

---@class 蜃境_裂雾飞鹰_01山脚小村
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
