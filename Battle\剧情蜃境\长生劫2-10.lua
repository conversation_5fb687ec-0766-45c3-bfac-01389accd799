local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    CreateRoles()
end

function start()
    Talk()
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("椿岁", 78, team, bcapi.Vector2(-2, 0), 0)
    end)
end

function Talk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("神秘杀手", "地仙大人已经将你驱逐了……")
        bcapi.talk("神秘杀手", "你别想再踏入无忧村一步！")
        bcapi.resume_and_show_ui()
    end)
end
