local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.hide_ui("疗伤")
    bcapi.reset_limit_time(300)
    -- 开局创建角色
    createRoles()
end

function start()
    -- 开局提示
    bcapi.add_timer_trigger(0.1,startTalk)
    -- 每20秒，生成官差队友
    for i=0, 50 do
        bcapi.add_timer_trigger(20*(i+1), createNextRoles)
    end
    -- 当山贼栅栏被破坏任意一个时，提示可以召唤友军
    bcapi.add_timer_trigger(1, checkFenceNum)
end

-- 取玩家队伍存的字典，后面两个函数中用到
local dict = bcapi.get_team(0).ValueDict

function createRoles()
    bcapi.async_call(function()
        --给玩家创建四个官差
        bcapi.create_join_role("世界_攻坚战_官差", 30, team, bcapi.Vector2(-7, 3), 0)
        bcapi.create_join_role("世界_攻坚战_官差", 30, team, bcapi.Vector2(-7, 1), 0)
        bcapi.create_join_role("世界_攻坚战_官差", 30, team, bcapi.Vector2(-7, -1), 0)
        bcapi.create_join_role("世界_攻坚战_官差", 30, team, bcapi.Vector2(-7, -3), 0)
    end)
end

function createNextRoles()
    bcapi.async_call(function()
        --给玩家创建官差
        bcapi.create_join_role("世界_攻坚战_官差", 30, team, bcapi.Vector2(-7, 0), 0)
    end)
end

function checkFenceNum()
    bcapi.add_condition_trigger("[%enemy:世界_攻坚战_指挥->flag:有陷阱%][=]0",0,breakthroughTalk)
end

function startTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("攻坚战_官差", "侠士，来得正是时候，请助我们一臂之力，攻下山贼！")
        bcapi.talk("主角", "绵薄之力，愿意效劳！")
        bcapi.talk("攻坚战_官差", "山贼的营地内陷阱重重，在敌方阵营使用召唤技能很危险，请谨慎。")
        bcapi.resume_and_show_ui()
    end)
end

function breakthroughTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("攻坚战_官差", "攻破敌人防线了！")
        bcapi.talk("攻坚战_官差", "现在我们可以在敌方阵营召唤友军，战术夹击了。")
        bcapi.resume_and_show_ui()
    end)
end