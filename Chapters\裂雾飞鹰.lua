---@type ChapterApi
local capi = require("ChapterApi")

local newbieApi = require("NewbieApi") -- 不允许改名

---@type StageMapApi
local stageApi = require("StageMapApi")


s = require("StoryApi")

local avg = require("AVGApi")

-- 主要用于摄影棚接新手战斗
function playTimelineAndCustomBattle(timelinePath, battleKey, hideSkip)
    if battleKey == nil then battleKey = args:get_Item("battleKey") end
    innerPlayTimelineScene(timelinePath, false, hideSkip, true)
    newbieApi.battle(battleKey, true)  -- 开始战斗
    capi.setTimelineSceneActive(false) -- 隐藏Timeline场景
    --capi.stopTimelineScene(true) -- 此时真正停止timeline并切换到主场景
    capi.pass(7)
end

function playAvgAndCustomBattle()
    innerPlayAvg(args:get_Item("avgKey"))
    newbieApi.battle(args:get_Item("battleKey"), true)
    capi.pass(7)
end

function quick_play_avg()
    local avgKey = args:get_Item("avgKey")
    local avg = require("AVGApi")
    avg.run(avgKey) 
end

function quick_map_story()
    local mapStoryKey = args:get_Item("mapStoryKey")
    --s.aside(mapStoryKey .. ":changeMapStory切地点有问题，待程序修复。先为了串流程直接跳过")
    if(capi.isGameMapRunning()) then
        s.changeMapStory(mapStoryKey)
    else
        capi.mapStory(mapStoryKey,false)
    end
end

function quick_play_timeline()
    local timelineKey = args:get_Item("timelineSceneKey")
    s.playTimelineScene("Assets/GameScenes/动画演出场景/" .. timelineKey)
end

function lwfy_01()
    quick_play_avg()
end

function lwfy_02()
    quick_play_avg()
end

function lwfy_03()
    quick_play_avg()
end

function lwfy_04()
    quick_play_avg()
end

function lwfy_05()
    quick_play_avg()
end

function lwfy_06()
    quick_play_avg()
end

function lwfy_07()
    quick_play_avg()
end

function lwfy_08()
    quick_play_avg()
end

function lwfy_09()
    quick_play_avg()
end

function lwfy_10()
    quick_play_avg()
end

function lwfy_11()
    quick_play_avg()
end

function lwfy_12()
    quick_play_avg()
end

function lwfy_13()
    quick_play_avg()
end

function lwfy_14()
    quick_play_avg()
end

function lwfy_15()
    quick_play_avg()
end

function lwfy_16()
    quick_play_avg()
end

function lwfy_17()
    quick_play_avg()
end

function lwfy_18()
    quick_play_avg()
end

function lwfy_19()
    quick_play_avg()
end

function lwfy_20()
    quick_play_avg()
end

function lwfy_21()
    quick_play_avg()
end

function lwfy_22()
    quick_play_avg()
end

function lwfy_23()
    quick_play_avg()
end

function lwfy_24()
    quick_play_avg()
end

return {
    --把所有的函数注册进去
    lwfy_01 = lwfy_01,
    lwfy_02 = lwfy_02,
    lwfy_03 = lwfy_03,
    lwfy_04 = lwfy_04,
    lwfy_05 = lwfy_05,
    lwfy_06 = lwfy_06,
    lwfy_07 = lwfy_07,
    lwfy_08 = lwfy_08,
    lwfy_09 = lwfy_09,
    lwfy_10 = lwfy_10,
    lwfy_11 = lwfy_11,
    lwfy_12 = lwfy_12,
    lwfy_13 = lwfy_13,
    lwfy_14 = lwfy_14,
    lwfy_15 = lwfy_15,
    lwfy_16 = lwfy_16,
    lwfy_17 = lwfy_17,
    lwfy_18 = lwfy_18,
    lwfy_19 = lwfy_19,
    lwfy_20 = lwfy_20,
    lwfy_21 = lwfy_21,
    lwfy_22 = lwfy_22,
    lwfy_23 = lwfy_23,
    lwfy_24 = lwfy_24

}