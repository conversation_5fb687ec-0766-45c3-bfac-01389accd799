--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_雾锁寒山_01杂役房/flags_蜃境_雾锁寒山_01杂役房")


---@class 角色_蜃境_雾锁寒山_01杂役房
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "wshs_01"
    wshs_01 = "角色/wshs_01",
    --- "wshs_02"
    wshs_02 = "角色/wshs_02",
    --- "enemy_1"
    enemy_1 = "角色/enemy_1",
    --- "enemy_2"
    enemy_2 = "角色/enemy_2",
    --- "敌人1"
    minion_1 = "角色/minion_1",
    --- "敌人2"
    minion_2 = "角色/minion_2",
    --- "阿莫朋友"
    amoFriend = "角色/amoFriend",
    --- "阿莫"
    amo = "角色/amo",
    --- "elite_3"
    elite_3 = "角色/elite_3",
}

---@class 物体_蜃境_雾锁寒山_01杂役房
local objs = {
    --- "出口"
    exit = "物体/exit",
    --- "房顶"
    obj_roofs = "物体/obj_roofs",
    --- "宝箱1"
    chest_1 = "物体/chest_1",
    --- "邪祟之气1"
    evilAir_1 = "物体/evilAir_1",
    --- "邪祟之气2"
    evilAir_2 = "物体/evilAir_2",
    --- "包裹触发器"
    trigger_baoguo = "物体/trigger_baoguo",
    --- "paper1"
    paper1 = "物体/paper1",
    --- "paper2"
    paper2 = "物体/paper2",
    --- "paper3"
    paper3 = "物体/paper3",
}

---@class 相机_蜃境_雾锁寒山_01杂役房
local cameras = {
    --- "屋内跟随相机"
    cam_insideRoomFollow = "相机/cam_insideRoomFollow",
    --- "主相机"
    cam_main = "相机/cam_main",
    --- "屋内跟随相机1"
    cam_insideRoomFollow1 = "相机/cam_insideRoomFollow1",
    --- "进入相机"
    cam_enter = "相机/cam_enter",
}

---@class 位置_蜃境_雾锁寒山_01杂役房
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    --- "屋内位置"
    pos_insideRoom = "位置/pos_insideRoom",
    --- "屋外位置"
    pos_outsideRoom = "位置/pos_outsideRoom",
    --- "出口返回"
    pos_back = "位置/pos_back",
    --- "小怪返回位置_1"
    pos_enemyBack_1 = "位置/pos_enemyBack_1",
    --- "小怪返回位置_2"
    pos_enemyBack_2 = "位置/pos_enemyBack_2",
    --- "阿莫跑走位置"
    pos_amoLeave = "位置/pos_amoLeave",
    --- "包裹检查位置"
    pos_check_baoguo = "位置/pos_check_baoguo",
    --- "pos_ready_start"
    pos_ready_start = "位置/pos_ready_start",
}

---@class 资产_蜃境_雾锁寒山_01杂役房
local assets = {
    --- "进入房间"
    timeline_enter = "资产/timeline_enter",
}

---@class 动作_蜃境_雾锁寒山_01杂役房
local animationClips = {
}

---@class 蜃境_雾锁寒山_01杂役房
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
