---@type 蜃境_鹿鸣天下_02鄂州南市
local context = require("MapStories/蜃境_鹿鸣天下_02鄂州南市/scene_蜃境_鹿鸣天下_02鄂州南市_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_鹿鸣天下_02鄂州南市
local flags = context.flags
--不可省略，用于外部获取flags

local newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")

function getFlags(...)
    return flags
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

local memoryKey = "lmtx_06"

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function start()
    print("start called")
    s.readyToStart(true)
    refreshMapStates()
    try_execute_newbieGuide()
end

function try_execute_newbieGuide(...)
    s.nextStep("newbieGuideInLevel")
end

function newbieGuideInLevel()end

--默认初始化的地图显隐状态
function reset_all()
    --初始相机
    if flags.camera_2L_finished == 0 then
        s.camera(cameras.cam_2L,true, true, blendHintEnum.EaseInOut, 1, true, true)
    else
        s.camera(cameras.cam_2L,false, true, blendHintEnum.EaseInOut, 1, true, true)
    end

    --隐藏所有的剧情节点
    s.setActive(npcs.lmtx_06, false)
    s.setActive(npcs.lmtx_07, false)
    s.setActive(npcs.lmtx_08, false)
    s.setActive(npcs.lmtx_09, false)
    s.setActive(npcs.lmtx_10, false)
    s.setActive(npcs.lmtx_11, false)

    --隐藏所有碎片氛围模型节点
    s.setActive(npcs.lmtx_09_ZhuGeLu, false)
    s.setActive(npcs.lmtx_09_ZhuLingLong, false)
    s.setActive(npcs.lmtx_10_ZhuGeLu, false)
    s.setActive(npcs.lmtx_10_ChuWanJun, false)
    s.setActive(npcs.lmtx_11_ZhuLingLong, false)
    s.setActive(npcs.lmtx_11_ChuWanJun, false)

    if flags.activeBranchTask == 0 then
        s.setActive(npcs.branchTask_Qing_S0,true)
        s.setActive(npcs.branchTask_Qing_S1,false)
        s.setActive(npcs.branchTask_Dizi_S2,false)
        s.setActive(npcs.branchTask_Dizi_S2_after,false)
    end

    if flags.branchTaskStep2_Trriger_finished == 1 or flags.activeBranchTask == 0 then
        s.setActive(objs.branchTaskStep2_Trriger,false)
    elseif flags.branchTaskStep2_Trriger_finished == 0 and flags.activeBranchTask == 1 then
        s.setActive(objs.branchTaskStep2_Trriger,true)
    end

    if flags.activeBranchTask == 1 then
        s.setActive(npcs.branchTask_Qing_S0,false)
        s.setActive(npcs.branchTask_Qing_S1,true)
        s.setActive(npcs.branchTask_Dizi_S2,true)
        s.setActive(npcs.branchTask_Dizi_S2_after,false)
    end

    if flags.branchTaskStep2_finished == 1 then
        s.setActive(npcs.branchTask_Qing_S1,false)
        s.setActive(npcs.branchTask_Dizi_S2,false)
        s.setActive(npcs.branchTask_Dizi_S2_after,true)
    end

    if flags.puzzle_checkItem < 3 then
        -- 谜题触发器隐藏
        s.setActive(objs.puzzleDoor_shuihu,false)
        s.setActive(objs.puzzleDoor_beizi,false)
        s.setActive(objs.puzzleDoor_dengzi,false)
    end

    if flags.puzzle_dengzi_finished == 0 then
        s.setActive(objs.putDown_dengzi,false)
    else
        s.setActive(objs.putDown_dengzi,true)
    end

    if flags.puzzle_beizi_finished == 0 then
        s.setActive(objs.putDown_beizi,false)
    else
        s.setActive(objs.putDown_beizi,true)
    end

    if flags.puzzle_shuihu_finished == 0 then
        s.setActive(objs.putDown_shuihu,false)
    else
        s.setActive(objs.putDown_shuihu,true)
    end

    if flags.puzzle_chapuBoss_talk == 0 then
        -- 未开始对话时隐藏所有道具
        s.setActive(objs.puzzleKey_dengzi, false)
        s.setActive(objs.puzzleKey_shuihu, false)
        s.setActive(objs.puzzleKey_beizi, false)
    else
        -- 已开始对话，根据收集状态显示道具
        s.setActive(objs.puzzleKey_dengzi, flags.checkKey_dengzi == 0)
        s.setActive(objs.puzzleKey_shuihu, flags.checkKey_shuihu == 0)
        s.setActive(objs.puzzleKey_beizi, flags.checkKey_beizi == 0)
    end

    s.removeAllGameMapGuides()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 7) then
        taskStep = "forwardToExplore"
    elseif (step == 7) then
        taskStep = "leaveEzhouNanShi"
    end
    s.setTaskStep("鹿鸣天下鄂州南市", taskStep)


    print("refreshMapStates level step = " .. tostring(step))
    reset_all()
    
    s.setActive(objs.lmtx_1003,not s.hasUnlockMemorySlot("lmtx_1003"))

    
    if step == 0 then
        ----第一次进入场景, 初始化主角位置
        s.setPos(npcs.Player,pos.zhujue_start_pos)
        s.setActive(npcs.lmtx_06,true)
        s.setTaskStep("鹿鸣天下鄂州南市","forwardToExplore")

        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.lmtx_06, 0.8)
    elseif step == 2 then
        level_guide_to(npcs.lmtx_07, 0.8)
    elseif step == 3 then
        level_guide_to(npcs.lmtx_08, 0.8)
    elseif step == 4 then
        level_guide_to(npcs.lmtx_09, 0.8)
        -- 添加碎片相关氛围NPC的显示
        s.setActive(npcs.lmtx_09_ZhuGeLu, true)
        s.setActive(npcs.lmtx_09_ZhuLingLong, true)
    elseif step == 5 then
        level_guide_to(npcs.lmtx_10, 0.8)
        -- 添加碎片相关氛围NPC的显示
        s.setActive(npcs.lmtx_10_ZhuGeLu, true)
        s.setActive(npcs.lmtx_10_ChuWanJun, true)
    elseif step == 6 then
        level_guide_to(npcs.lmtx_11, 0.8)
        -- 添加碎片相关氛围NPC的显示
        s.setActive(npcs.lmtx_11_ZhuLingLong, true)
        s.setActive(npcs.lmtx_11_ChuWanJun, true)
    else
        --do nothing
        level_guide_to(objs.exit)
    end
end

----------------------OVERRIDE----------------------------------

function lmtx_06()
    executeMemSlot("lmtx_06", quick_play_avg)
    s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
    s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
    s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
end


function lmtx_07()
    executeMemSlot("lmtx_07", quick_play_avg)
    s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
    s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
    s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
end

function lmtx_08()
    executeMemSlot("lmtx_08", quick_play_avg)
    s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
    s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
    s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
end

function lmtx_09()
    executeMemSlot("lmtx_09", quick_play_avg)
    s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
    s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
    s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
end

function lmtx_10()
    executeMemSlot("lmtx_10", quick_play_avg)
    s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
    s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
    s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
end

function lmtx_11()
    executeMemSlot("lmtx_11", quick_play_avg,function()
    s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
    s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
    s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
    s.setTaskStep("鹿鸣天下鄂州南市","leaveEzhouNanShi")
    next_level_step(true)
    end)
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("鹿鸣天下")
end


function activeBranchTask()
    if s.getCurrentTaskStep("病机之病") == "deactive" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_激活任务")
        --此处建议AVG换成Timeline演出，可以在演出中让晴姑娘轻功离开。
        s.blackScreen()
        s.setActive(npcs.branchTask_Qing_S0,false)
        s.lightScreen()
        --s.aside("她轻笑一声，便从眼前离去。")
        s.setActive(npcs.branchTask_Qing_S1,true)
        s.setActive(npcs.branchTask_Dizi_S2,false)
        s.setActive(objs.branchTaskStep2_Trriger,true)
        s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
        s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
        s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
        flags.activeBranchTask = 1

        s.setTaskStep("病机之病","findQing")
    end
end

function branchTaskStep2_Trriger()

    if s.isMale() then
        s.setActive(npcs.branchTask_Qing_S1,false)
        s.setActive(npcs.branchTask_Dizi_S2,false)
        s.playTimeline(assets.Timeline_01,false)
        s.setPos(npcs.Player,pos.zhujue_branchTask_S2)
        s.lightScreen()
    else
        s.setActive(npcs.branchTask_Qing_S1,false)
        s.setActive(npcs.branchTask_Dizi_S2,false)
        s.playTimeline(assets.Timeline_01_Nv,false)
        s.setPos(npcs.Player,pos.zhujue_branchTask_S2)
        s.lightScreen()
    end

    s.setActive(objs.branchTaskStep2_Trriger,false)
    s.setActive(npcs.branchTask_Dizi_S2,true)
    s.setActive(npcs.branchTask_Qing_S1,true)
    s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
    s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
    s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
    s.setTaskStep("病机之病","touchDizi")
    flags.branchTaskStep2_Trriger_finished = 1
end

function branchTaskStep1()
    if s.getCurrentTaskStep("病机之病") == "touchDizi" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_晴催促行动闲话")
        s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
        s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
        s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
    end
end

function branchTaskStep2()
    if s.getCurrentTaskStep("病机之病") == "touchDizi" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_弟子暴起")
        s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
        s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
        s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
        local lmtx02_battleWin = s.battle("蜃境_鹿鸣天下2_支线与伥怪战斗")
        if lmtx02_battleWin then
            s.setActive(npcs.branchTask_Qing_S1,false)
            s.setActive(npcs.branchTask_Dizi_S2,false)
            s.setActive(npcs.branchTask_Dizi_S2_after,true)

            if s.isMale() then
                s.playTimeline(assets.Timeline_02,false)
            else
                s.playTimeline(assets.Timeline_02_Nv,false)
            end

            flags.branchTaskStep2_finished = 1 
            s.lightScreen()
            s.setTaskStep("病机之病","finished")
            local capi = require("ChapterApi")
            capi.finishMemorySlot("lmtx_branchTask_bjzb")
            s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
            s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
            s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
        end
    end
end

function puzzleKey_dengzi()
    s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_拾取凳子")
    s.setActive(objs.puzzleKey_dengzi,false)
    flags.puzzle_checkItem = flags.puzzle_checkItem + 1
    flags.checkKey_dengzi = 1

    if flags.puzzle_checkItem == 3 then
        s.setActive(objs.puzzleDoor_shuihu,true)
        s.setActive(objs.puzzleDoor_beizi,true)
        s.setActive(objs.puzzleDoor_dengzi,true)
    end
end

function puzzleKey_beizi()
    s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_拾取杯子")
    s.setActive(objs.puzzleKey_beizi,false)
    flags.puzzle_checkItem = flags.puzzle_checkItem + 1
    flags.checkKey_beizi = 1

    if flags.puzzle_checkItem == 3 then
        s.setActive(objs.puzzleDoor_shuihu,true)
        s.setActive(objs.puzzleDoor_beizi,true)
        s.setActive(objs.puzzleDoor_dengzi,true)
    end
end

function puzzleKey_shuihu()
    s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_拾取水壶")
    s.setActive(objs.puzzleKey_shuihu,false)
    flags.puzzle_checkItem = flags.puzzle_checkItem + 1
    flags.checkKey_shuihu = 1

    if flags.puzzle_checkItem == 3 then
        s.setActive(objs.puzzleDoor_shuihu,true)
        s.setActive(objs.puzzleDoor_beizi,true)
        s.setActive(objs.puzzleDoor_dengzi,true)
    end
end

function puzzleNpc_chapuBoss()
    if flags.puzzle_chapuBoss_talk == 0 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_茶摊老板接谜题")
        s.setActive(objs.puzzleKey_dengzi,true)
        s.setActive(objs.puzzleKey_beizi,true)
        s.setActive(objs.puzzleKey_shuihu,true)
        flags.puzzle_chapuBoss_talk = 1
    elseif flags.puzzle_complete >= 3 then
        -- #8913 修复完成解密后，茶摊老板不能对话的BUG 
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_茶摊老板感谢")
    elseif flags.puzzle_checkItem < 3 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_茶摊老板对话")
    elseif flags.puzzle_checkItem == 3 and flags.puzzle_chapuBoss_talk == 1 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_茶摊老板线索")
    end
end

function puzzleDoor_dengzi()
    local r = s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_选择要的交付道具")
    if r == 1 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_凳子正确")
        s.setActive(objs.puzzleDoor_dengzi,false)
        s.setActive(objs.putDown_dengzi,true)
        flags.puzzle_complete = flags.puzzle_complete + 1
        flags.puzzle_dengzi_finished = 1
        if flags.puzzle_complete == 3 then
            local capi = require("ChapterApi")
            capi.finishMemorySlot("lmtx2_puzzle_openShop")
        end
    elseif r == 2 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_交付错误道具")
    elseif r == 3 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_交付错误道具")
    end
end

function puzzleDoor_beizi()
    local r = s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_选择要的交付道具")
    if r == 1 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_交付错误道具")
    elseif r == 2 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_茶杯正确")
        s.setActive(objs.puzzleDoor_beizi,false)
        s.setActive(objs.putDown_beizi,true)
        flags.puzzle_complete = flags.puzzle_complete + 1
        flags.puzzle_beizi_finished = 1
        if flags.puzzle_complete == 3 then
            local capi = require("ChapterApi")
            capi.finishMemorySlot("lmtx2_puzzle_openShop")
        end
    elseif r == 3 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_交付错误道具")
    end
end

function puzzleDoor_shuihu()
    local r = s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_选择要的交付道具")
    if r == 1 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_交付错误道具")
    elseif r == 2 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_交付错误道具")
    elseif r == 3 then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_水壶正确")
        s.setActive(objs.puzzleDoor_shuihu,false)
        s.setActive(objs.putDown_shuihu,true)
        flags.puzzle_complete = flags.puzzle_complete + 1
        flags.puzzle_shuihu_finished = 1
        if flags.puzzle_complete == 3 then
            local capi = require("ChapterApi")
            capi.finishMemorySlot("lmtx2_puzzle_openShop")
        end
    end
end

function puzzle_box_L1()
    s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_鄂州南市", "蜃境_鹿鸣天下_02鄂州南市_2楼宝箱谜题引导")
    local rapi = require("RpgMapApi")
    local ret = rapi.submitStrategyCard("请选择要使用的策略卡")
    
    if (ret ~= nil) then
        -- 检查是否使用了正确的策略卡
        if (ret.getSubmitStrategyCardItem(0).Key == "养精蓄锐" or 
            ret.getSubmitStrategyCardItem(0).Key == "分身解厄" or 
            ret.getSubmitStrategyCardItem(0).Key == "千面无常") then
            s.blackScreen()
            s.setPos(npcs.Player,pos.L2_pos)
            s.camera(cameras.cam_2L,true, true, blendHintEnum.EaseInOut, 1, true, true)
            flags.camera_2L_finished = 0
            s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
            s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
            s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
            s.lightScreen()
        else
            s.aside("好像没什么用，如果是效果中有【撤步】或者【远遁】的话，应该可以。")
        end
    else
        s.aside("没做好准备，稍后再来也不迟。")
    end
end

function puzzle_box_L2()
    s.blackScreen()
    s.setPos(npcs.Player,pos.L1_pos)
    s.camera(cameras.cam_2L,false, true, blendHintEnum.EaseInOut, 1, true, true)
    flags.camera_2L_finished = 1
    s.setPos(npcs.enemy_01,pos.enemy_1_start_pos)
    s.setPos(npcs.enemy_02,pos.enemy_2_start_pos)
    s.setPos(npcs.enemy_04,pos.enemy_4_start_pos)
    s.lightScreen()
end

function lmtx_1003()
    s.setActive(objs.lmtx_1003,false)
end