local chapterApi = require("ChapterApi")
local sapi = require("StoryApi")

local function playTimelineScene(scenePath)
    local timelineSceneKey = args:get_Item("timelineSceneKey")
    local isFromGameMap = args:get_Item("isFromGameMap")
    if isFromGameMap then
        -- 从跑图场景播放
        sapi.playTimelineScene(scenePath)
    else
        -- 从RpgMap播放,播放完返回该界面
        chapterApi.playTimelineSceneNew("Assets/GameScenes/动画演出场景/" .. timelineSceneKey .. ".unity",true,true,false,false,false)
    end
end

local function playAvg(avgPath)
    local avgApi = require("AVGApi")
    return avgApi.runAlpha(path, 0)
end

local function testTimeline()
    playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录10_楚家庄前院火烧动画.unity")
end

local function testAvg()
    local avgKey = args:get_Item("avgKey")
    local avgApi = require("AVGApi")
    avgApi.run(avgKey)
end

local function testMapStory()
    local sapi = require("StoryApi")
    local mapStoryKey = args:get_Item("mapStoryKey")
    sapi.changeMapStory(mapStoryKey)
end

local function testBattle()
    local newbieApi = require("NewbieApi")
    local battleKey = args:get_Item("battleKey")
    newbieApi.battle(battleKey, false)
end

local function testNewbieBattle()
    local newbieApi = require("NewbieApi")
    local battleKey = args:get_Item("battleKey")
    newbieApi.battle(battleKey, false)
end

-- 组合不同的效果
local function testCombo()
    local sapi = require("StoryApi")
    local mapStoryKey = args:get_Item("mapStoryKey")
    sapi.changeMapStory(mapStoryKey)
    local newbieApi = require("NewbieApi")
    local battleKey = args:get_Item("battleKey")
    newbieApi.battle(battleKey, false)
end

return {
    testTimeline = testTimeline,
    testAvg = testAvg,
    testMapStory = testMapStory,
    testBattle = testBattle,
    testCombo = testCombo,
    testNewbieBattle = testNewbieBattle,
}