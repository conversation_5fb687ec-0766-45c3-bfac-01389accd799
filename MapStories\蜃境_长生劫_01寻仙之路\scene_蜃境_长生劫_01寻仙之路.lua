
---@type 蜃境_长生劫_01寻仙之路
local context = require("MapStories/蜃境_长生劫_01寻仙之路/scene_蜃境_长生劫_01寻仙之路_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger
s.loadFlags(context.flags)

s.loadFlags(context.flags)
---@type flags_蜃境_长生劫_01寻仙之路

local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")


----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

--测试效果
function test()
    s.camera(cameras.cam_start_near,true,true,blendHintEnum.Cut)
    s.readyToStart(true)
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_寻仙之路","蜃境_寻仙之路_进入小径")
    s.wait(0.5)
    s.cameraAsync(cameras.cam_start_far,true,true,blendHintEnum.Custom,15)
end


--小怪通用战斗指令
function battle(id)
    s.roamMapBattle(id)       
end

function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数
    reset_all()

    --第一次进入场景
    if step == 0 then

        --临时屏蔽掉最后的出口

        if flags.enter_show == 0 then
            s.setActive(objs.exit1,false)

            s.readyToStart(true)
            s.cameraAsync(cameras.cam_start_near,true,true,blendHintEnum.Cut)
            s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_寻仙之路","蜃境_寻仙之路_进入小径")
            s.cameraAsync(cameras.cam_start_far,true,true,blendHintEnum.Custom,15)
            s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_寻仙之路","蜃境_寻仙之路_进入小径_开场",nil,0)
            s.cameraAsync(cameras.cam_main,true,true,blendHintEnum.Custom,2)

            --打开最后的出口
            s.setActive(objs.exit1,true)
            flags.enter_show = 1
        end

        s.setTaskStep("长生劫寻仙之路","forwardToExplore")
    elseif step == 1 then
        level_guide_to(npcs.csj_01, 1)
    elseif step == 2 then
        level_guide_to(npcs.csj_02, 1)
    elseif step == 3 then
        level_guide_to(npcs.csj_03, 1)
        s.soloAnimState(npcs.chunsui_shadow_3,roleAnims.BSquat_Loop,true)
    elseif step == 4 then
        level_guide_to(npcs.csj_04, 1)
        s.soloAnimState(npcs.chunsui_shadow_4,"BHurt_Loop",true)
    else
        level_guide_to(objs.exit)
    end
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.csj_01, false)
    s.setActive(npcs.csj_02, false)
    s.setActive(npcs.csj_03, false)
    s.setActive(npcs.csj_04, false)
    --删除所有的引导点
    s.removeAllGameMapGuides()

    --支线何以为家NPC
    s.setActive(npcs.XSZL_NPC, s.getCurrentTaskStep("长生劫支线何以为家") == "deactive")

    --收集物开启
    s.setActive(objs.csj_1001,not s.hasUnlockMemorySlot("csj_1001"))
    s.setActive(objs.csj_1002,not s.hasUnlockMemorySlot("csj_1002"))

    --解谜物体
    if flags.open_shadowChest_2 == 1 then
        s.setActive(objs.csj_01_Puzzle_zhanlishikuai,false)
        s.setActive(objs.csj_01_Puzzle_tuidongshikuai,false)
        s.setActive(objs.csj_01_Puzzle_miwu,false)
        s.setActive(objs.csj_01_Puzzle_miwuchufaqi,false)
    end
    
    --如果关卡步数不为0，则隐藏开场碰撞
    if flags.level_step ~= 0 then
        s.setActive(objs.triggger_chunsui_start,false)
    end

    if flags.show_start == 1 then
        s.setActive(objs.triggger_chunsui_start,false)
    else
        s.setActive(objs.triggger_chunsui_start,true)
    end

    if flags.chunsui_show == 1 then
        s.setActive(npcs.chunsui,true)
    else
        s.setActive(npcs.chunsui,false)
    end
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("长生劫")
end

-------------------=跑图-------------------  
function chunsui_start()
    s.setActive(objs.triggger_chunsui_start,false)
    s.playTimeline(assets.chunsui_start)

    --设置椿岁位置
    s.setPos(npcs.chunsui,pos.chunsui_start)
    s.setActive(npcs.chunsui,true)

    flags.show_start = 1
    flags.chunsui_show = 1
    s.lightScreen()
end

function chunsui_1()
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_寻仙之路","长生劫_寻仙之路_开场",nil,0)
    s.setActive(npcs.chunsui,false)
    s.playTimeline(assets.chunsui_jump)
    flags.chunsui_show = 2
    --开启蜃影子
    next_level_step(true)
    s.lightScreen(0.2)
end

----------------------------------支线剧情 何以为家 场景一剧情----------------------------------
--第一段
function HYWJ_01()
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_寻仙之路","蜃境_寻仙之路_支线_何以为家")
    s.setTaskStep("长生劫支线何以为家","MeetAtWuyoucun")
    --黑屏特效
    s.blackScreen(0.7)
    s.setActive(npcs.XSZL_NPC,false)
    s.lightScreen(0.7)
end


-------------------解谜-----------------------

--石头调查剧情
function csj_01_01()
    s.aside("这块石头上好像刻有字……它似乎在吸收着周遭的浊气")
    s.aside("想办法将这块石头取出来试试")
    local rapi = require("RpgMapApi")
    local ret = rapi.submitStrategyCard("请选择要使用的策略卡")
    
    if (ret ~= nil) then
        -- 检查是否使用了正确的策略卡
        if (ret.getSubmitStrategyCardItem(0).Key == "摧山掌") then
            csj_01_02()
            s.setActive(objs.csj_01_Puzzle_zhanlishikuai,false)
            s.setActive(objs.csj_01_Puzzle_tuidongshikuai,true)
        else
            s.aside("好像没什么用，如果是能劈山摧石的招式，应该可以。")
        end
    else
        s.aside("没做好准备，稍后再来也不迟。")
    end
end

--石头复位
function csj_01_02()
    s.blackScreen()
    s.setActive(objs.csj_01_Puzzle_zhanlishikuai,false)
    s.setPos(objs.csj_01_Puzzle_tuidongshikuai,pos.csj_01_Puzzle_tuidongshikuai)
    s.lightScreen(0.5)
end

--石头推动到迷雾中剧情
function csj_01_03()
    flags.open_shadowChest_2 = 1
    --s.cameraAsync(cameras.cam_puzzle,true,true,blendHintEnum.EaseIn,1,true,true)
    s.setActive(objs.csj_01_Puzzle_miwu,false)
    s.setActive(objs.csj_01_Puzzle_miwuchufaqi,false)
    s.setActive(objs.csj_01_Puzzle_tuidongshikuai,false)
    refreshMapStates()
    s.talkThink(npcs.zhujue,"迷雾被驱散了")
    s.wait(1)
    s.blackScreen() 
    --s.camera(cameras.cam_puzzle,true,true,blendHintEnum.Cut,1,true,false)
    s.lightScreen(0.5)
end
-------------------战斗实现-------------------  
-- function enemy_battle(id,enemyKey)
--     print("敌人战斗")
--     local colliderKey = enemyKey.."_collider"

--     s.popInfo("你已被发现！！！")

--     --s.setActive(objs[colliderKey],false)
--     s.turnTo(npcs[enemyKey],npcs.zhujue,true)
--     s.turnTo(npcs.zhujue,npcs[enemyKey],true)

--     s.animState(npcs[enemyKey],"BAssault_Loop",true)
--     s.wait(2)

--     require("MapStories/MapStoryCommonTools").roamMapBattle(id,function()
--         s.popInfo("战斗胜利！！！")
--         flags[enemyKey.."_battle"] = 1
--         s.setActive(objs[colliderKey],false)
--         s.setActive(npcs[enemyKey],false)
--     end,
--     function()
--         battle_cancle(id,enemyKey)
--     end,
--     function()
--         battle_lose(id,enemyKey)
--     end
-- )
--     s.wait(0.5)
--     refreshMapStates()
-- end

-- function battle_lose(id,enemyKey)
--     local colliderKey = enemyKey.."_collider"
--     print("战斗失败")
--     s.popInfo("战斗失败！！！")

--     s.blackScreen()
--     s.animState(npcs[enemyKey],"BAssault_Loop",false)
--     s.setActive(objs[colliderKey],true)
--     s.setPos(npcs.zhujue,pos[enemyKey])
--     s.lightScreen()
-- end
-- function battle_cancle(id,enemyKey)
--     local colliderKey = enemyKey.."_collider"

--     s.blackScreen()
--     s.animState(npcs[enemyKey],"BAssault_Loop",false)
--     s.setActive(objs[colliderKey],true)
--     s.setPos(npcs.zhujue,pos[enemyKey])
--     s.lightScreen()
-- end

-- function fightInBack(enemyKey)
--     local colliderKey = enemyKey.."_collider"
--     s.setActive(objs[colliderKey],false)
    
--     s.animState(npcs.zhujue,"Special_2",true)
--     s.wait(1)
--     s.playSound("sfx","拳击3")
--     s.wait(0.6)

--     s.animState(npcs[enemyKey],"BDie",true)
--     s.setDeath(npcs[enemyKey],0.5,false)

--     s.popInfo("敌人暂时被击倒！！！")

--     refreshMapStates()
-- end

function csj1_battle_101(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,
function()
        flags.open_shadowChest_1 = 1
        s.setActive(objs.evilAirChest_1,false)
        refreshMapStates()
    end)
end

-------------------蜃境宝箱实现---------------------
function shadowChest(id)
    if flags.open_shadowChest_1 == 0 then
        s.popInfo("需击败附近镇守的蜃境怪物！！！")
    else
        require("MapStories/MapStoryCommonTools").roamMapChest(id)
    end
end

function shadowChest2(id)
    if flags.open_shadowChest_2 == 0 then
        s.popInfo("这里的雾太浓了，根本过不去")
    else
        require("MapStories/MapStoryCommonTools").roamMapChest("csj1_chest_02")
    end
end
-------------------关卡记忆碎片和战斗实现---------------------
function csj_01()
    executeMemSlot("csj_01", 
    function()
        s.turnTo(npcs.zhujue,npcs.csj_01,true)
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_寻仙之路","长生劫_寻仙之路_蜃影出现",nil,0)
        require("Chapters/长生劫").csj_01()
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_寻仙之路","长生劫_寻仙之路_蜃影1结束",nil,0)
    end,
function()
    next_level_step(true)
    end)
end

function csj_02()
    executeMemSlot("csj_02", function()
        require("Chapters/长生劫").csj_02()
        next_level_step(true)
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_寻仙之路","长生劫_寻仙之路_蜃影2结束",nil,0)
    end)
end

function csj_03()
    executeMemSlot("csj_03", function()
        require("Chapters/长生劫").csj_03()
    end)
end

function csj_04()
    executeMemSlot("csj_04", function()
        require("Chapters/长生劫").csj_04()
    end,function()
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_寻仙之路","长生劫_寻仙之路_蜃影4结束",nil,0)
        s.setTaskStep("长生劫寻仙之路","leave")
        next_level_step(true)
    end)    
end

