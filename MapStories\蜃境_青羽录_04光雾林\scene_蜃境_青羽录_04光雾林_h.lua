--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_青羽录_04光雾林/flags_蜃境_青羽录_04光雾林")


---@class 角色_蜃境_青羽录_04光雾林
local npcs = {
    zhujue = "角色/zhujue",
    qyl_15 = "角色/qyl_15",
    qyl_16 = "角色/qyl_16",
    qyl_17 = "角色/qyl_17",
    qyl_18 = "角色/qyl_18",
    enemy_01 = "角色/enemy_01",
    enemy_02 = "角色/enemy_02",
    enemy_03 = "角色/enemy_03",
    enemy_04 = "角色/enemy_04",
    enemy_05 = "角色/enemy_05",
    elite_06 = "角色/elite_06",
    boss_07 = "角色/boss_07",
    npc_01 = "角色/npc_01",
    soul = "角色/soul",
}

---@class 物体_蜃境_青羽录_04光雾林
local objs = {
    exit = "物体/exit",
    qyl4_1005 = "物体/qyl4_1005",
    qyl4_1006 = "物体/qyl4_1006",
    qyl4_1007 = "物体/qyl4_1007",
    chanzi = "物体/chanzi",
    stone_1 = "物体/stone_1",
    stone_2 = "物体/stone_2",
    stone_3 = "物体/stone_3",
    stone_4 = "物体/stone_4",
    stone_5 = "物体/stone_5",
    stone_1Trigger = "物体/stone_1Trigger",
    stone_2Trigger = "物体/stone_2Trigger",
    stone_3Trigger = "物体/stone_3Trigger",
    stone_4Trigger = "物体/stone_4Trigger",
    stone_5Trigger = "物体/stone_5Trigger",
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
}

---@class 相机_蜃境_青羽录_04光雾林
local cameras = {
    --- "跟随相机"
    cam_follow = "相机/cam_follow",
    --- "测试相机"
    cam_test = "相机/cam_test",
}

---@class 位置_蜃境_青羽录_04光雾林
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
    zhujue_start_pos2 = "位置/zhujue_start_pos2",
}

---@class 资产_蜃境_青羽录_04光雾林
local assets = {
}

---@class 动作_蜃境_青羽录_04光雾林
local animationClips = {
}

---@class 蜃境_青羽录_04光雾林
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
