local bcapi = require("BattleClientA<PERSON>")
local battle = args["battle"]
bcapi.battle = battle

function init()
end

function start()
    bcapi.async_call(function()
        local bossLv = 30
        if(bcapi.get_team_dict_has_key(0, "BossLv"))then
            bossLv = bcapi.get_team_dict_int_value(0, "BossLv", bossLv) + 25
            if(bossLv <= 30)then
                bossLv = 30
            end
        end
        print(bossLv)
        local role1 = bcapi.create_join_role("副本_楚家庄_邪魔道人", bossLv, bcapi.get_team(1), bcapi.Vector2(6, 0), 0.1)
    end)
end


