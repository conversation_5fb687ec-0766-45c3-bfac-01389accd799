---@type 蜃境_青羽录_01芦苇地
local context = require("MapStories/蜃境_青羽录_01芦苇地/scene_蜃境_青羽录_01芦苇地_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_青羽录_芦苇地
local flags = context.flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

local capi = require("ChapterApi")

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")
require("MapStories/MapStoryCommonTools")

function test()
    s.startGhostDissolve(npcs.shenjingjingcheng)
end

local memoryKey = "qyl_01"

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

--每次载入调用
function start()
    local hasFinishMemorySlot = s.hasUnlockMemorySlot(memoryKey)
    if (hasFinishMemorySlot == false) then
        --填写逻辑
        s.setPos(npcs.zhujue, pos.start)
        --s.turnTo(npcs.zhujue,npcs.shenjingjingcheng,false)
        s.camera(cameras.camera1, true)
        s.wait(0.5)
        s.readyToStart(true)

        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_芦苇地", "蜃境_青羽录_芦苇地_入口", nil, 0)

        s.camera(cameras.camera2, true, true, blendHintEnum.EaseInOut, 2.5, true, true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_芦苇地", "蜃境_青羽录_芦苇地_渲染", nil, 0)

        --任务状态变化
        s.unTraceTask(10000001,10)
        s.setTaskStep("青羽录芦苇地", "talkToJingCheng")
        --添加交互引导
        s.addGameMapGuide(objs.trigger, 0.1)
        s.camera(cameras.camera2, false, true, blendHintEnum.EaseInOut, 2.5, true, true)

        newbieApi.eventTrack("newbieStep", "step", "enter_scene_qingyulu01")
    else
        refreshMapStates()
    end
end

---初始化地图状态
function refreshMapStates()
    s.playMusic("韵道天成")
    s.setActive(objs.trigger, not s.hasUnlockMemorySlot(memoryKey))
    s.setActive(npcs.shenjingjingcheng, not s.hasUnlockMemorySlot(memoryKey))
    s.setActive(npcs.jingcheng, not s.hasUnlockMemorySlot(memoryKey))
    s.setActive(npcs.yunwusheng, not s.hasUnlockMemorySlot(memoryKey))
    s.setActive(objs.qyl1_1000,not s.hasUnlockMemorySlot("qyl1_1000"))
    s.setActive(objs.qyl1_1001,not s.hasUnlockMemorySlot("qyl1_1001"))
    --s.setActive(npcs.elite_02, s.hasUnlockMemorySlot("qyl_24"))
    --s.setActive(npcs.elite_02_PreShow, s.hasUnlockMemorySlot("qyl_24"))
    s.setActive(objs.elite_02_Interact, not  s.hasUnlockMemorySlot("qyl1_battle_201"))
    s.setActive(objs.elite_02_muren, flags.elite_02_pre_show == 0)
end

--第一次进入地图
function first_time_into_map()
    -- 填写逻辑
end

function touchMemoryPieces1()
    --第一个记忆碎片特殊处理，不弹出界面，也不消耗体力
    -- if (s.ShowMemorySlot(memoryKey) ~= true) then
    --     return
    -- end

    local capi = require("ChapterApi")
    capi.startMemorySlot(memoryKey)

    s.blackScreen()
    s.removeGameMapGuide(objs.trigger)
    s.setActive(objs.trigger, false)
    s.camera(cameras.camera3, true)
    s.setPos(npcs.zhujue, pos.zhujue_play_pos)
    s.setPos(npcs.jingcheng, pos.jingcheng_play_pos)
    s.setPos(npcs.yunwusheng, pos.yunwusheng_play_pos)
    s.turnTo(npcs.zhujue, npcs.shenjingjingcheng)
    s.turnTo(npcs.shenjingjingcheng, npcs.zhujue)
    s.lightScreen()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_芦苇地", "蜃境_青羽录_芦苇地_交互", nil, 0)
    s.soloAnimState(npcs.shenjingjingcheng, "BGive_Loop", true)
    s.wait(1)
    s.setActive(objs.xiesui, true)
    s.wait(0.5)
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_芦苇地", "蜃境_青羽录_芦苇地_交互2", nil, 0)
    s.soloAnimState(npcs.zhujue, "BSquat_Loop", true)
    s.wait(1)
    s.cameraEffect(2)
    s.wait(1)

    -- 调用演出
    require("Chapters/青羽录").qyl1_01()

    s.blackScreen()
    s.setActive(objs.xiesui, false)
    --s.setActive(npcs.shenjingjingcheng, false)

    s.turnTo(npcs.jingcheng, npcs.zhujue)
    s.turnTo(npcs.yunwusheng, npcs.zhujue)
    s.lightScreen()
    s.startGhostDissolve(npcs.shenjingjingcheng)
    s.soloAnimState(npcs.zhujue, "BSquat_Loop", false)
    --s.cameraEffect(2)
    s.wait(1)
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_芦苇地", "蜃境_青羽录_芦苇地_交互结束", nil, 0)
    s.camera(cameras.camera3, false, true, blendHintEnum.EaseInOut, 1.5, true, true)
    
    s.setTaskStep("青羽录芦苇地","leaveShenJing")
    --正式配置，但api有问题，等解决，先用上面的临时方法苟。

    
    capi.finishMemorySlot(memoryKey)
    newbieApi.eventTrack("newbieStep", "step", "finish_memoryslot_qyl_01")
    
    s.setRpgMapFlag("qyl_luWeiDi_chapter_finished",1)
    s.nextStep("guideToNextStep", nil)
end

function exitScene()
    print("离开场景")
    s.setTaskStep("青羽录芦苇地","leaveShenJing")
    --正式配置，但api有问题，等解决，先用上面的临时方法苟。
    local capi = require("ChapterApi")
    capi.exitMemoryScene()
end

function guideToNextStep()
    local function exceptionFunc()
        print("用户执行退出蜃境异常")
    end

    s.removeAllGameMapGuides()
    s.addGameMapGuide(objs.exit)

    -- newbieApi.waitUntilGameObjectV2("UIRoot/GameMapHUD", "exitBtn", 5)

    -- if (not newbieApi.guideButton("UIRoot/GameMapHUD", "GameMapHUD/GameObject/StoryRoot/RpgMenuWidget/Root/exitBtn", "点击此处，离开蜃境", -10, -150)) then
    --     return exceptionFunc()
    -- end

    -- newbieApi.waitUntilGameObjectV2("UIRoot/CommonQuestionPanel", "SubmitButton", 5)

    -- if (not newbieApi.guideButton("UIRoot/CommonQuestionPanel", "CommonQuestionPanel/Root/SubmitButton", "", -10, -150)) then
    --     return exceptionFunc()
    -- end
    newbieApi.setNewbieFlag(newbieFlags.finish_chapter_1_qingYuLu, 1)
end

--收集品虚影消散
function collectableDisappear(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
    s.startGhostDissolve("物体/" ..id)
end

--精英怪前置出场
function elitePreShow()
    local rapi = require("RpgMapApi")
    local ret = rapi.submitRoleCardWithInstanceReturn("尝试派出具有相同招式的侠客出战",nil,nil)
    if (ret ~= nil) then
        -- 检查是否使用了正确的侠客卡
        if (ret.Key == "荆成") then
            s.setActive(npcs.elite_02, false)
            s.setActive(npcs.elite_02_PreShow, false)
            s.setActive(npcs.jingcheng, false)
            s.setActive(npcs.yunwusheng, false)
            s.playTimeline(assets.elitePreShow)
            flags.elite_02_pre_show = 1
            s.setActive(npcs.jingcheng, true)
            s.setActive(npcs.yunwusheng, true)
            s.setPos(npcs.elite_02, pos.elite_show)
            s.setActive(npcs.elite_02, true)
            s.setActive(npcs.elite_02_PreShow, true)
            s.setActive(objs.elite_02_battle, true)
            s.setActive(objs.elite_02_Interact, false)
            s.setActive(objs.elite_02_muren, false)
        else
            s.aside("好像没什么用。")
        end
    else
        s.aside("没做好准备，稍后再来也不迟。")
    end
end

--精英怪交互
function eliteInteract()
    s.aside("他正在打木桩，这招式好熟悉。")
end






