--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_雾锁寒山_06荒原雪林/flags_蜃境_雾锁寒山_06荒原雪林")


---@class 角色_蜃境_雾锁寒山_06荒原雪林
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "wshs_17"
    wshs_17 = "角色/wshs_17",
    --- "wshs_18"
    wshs_18 = "角色/wshs_18",
    --- "wshs_19"
    wshs_19 = "角色/wshs_19",
    --- "wshs_20"
    wshs_20 = "角色/wshs_20",
    --- "enemy_1"
    enemy_1 = "角色/enemy_1",
    --- "enemy_2"
    enemy_2 = "角色/enemy_2",
    --- "enemy_3"
    enemy_3 = "角色/enemy_3",
    --- "enemy_4"
    enemy_4 = "角色/enemy_4",
    --- "enemy_5"
    enemy_5 = "角色/enemy_5",
    --- "enemy_6"
    enemy_6 = "角色/enemy_6",
    --- "elite_7"
    elite_7 = "角色/elite_7",
    --- "boss_8"
    boss_8 = "角色/boss_8",
    --- "elite_9"
    elite_9 = "角色/elite_9",
    --- "elite_10"
    elite_10 = "角色/elite_10",
    role_elite_10 = "角色/role_elite_10",
    --- "阿凉幻影"
    aliang_shadow = "角色/aliang_shadow",
    --- "诸葛鹿幻影"
    zhugelu_shadow = "角色/zhugelu_shadow",
    --- "墨七幻影1"
    moqi_shadow1 = "角色/moqi_shadow1",
    --- "书牍1"
    shudu_1 = "角色/shudu_1",
}

---@class 物体_蜃境_雾锁寒山_06荒原雪林
local objs = {
    --- "出口"
    exit = "物体/exit",
    --- "chest_1"
    chest_1 = "物体/chest_1",
    --- "chest_2"
    chest_2 = "物体/chest_2",
    --- "wshs6_1009"
    wshs6_1009 = "物体/wshs6_1009",
    enemy_1_collider = "物体/enemy_1_collider",
    enemy_2_colliderenemy_1_collider = "物体/enemy_2_colliderenemy_1_collider",
    enemy_3_collider = "物体/enemy_3_collider",
    enemy_4_collider = "物体/enemy_4_collider",
    enemy_5_collider = "物体/enemy_5_collider",
    enemy_6_collider = "物体/enemy_6_collider",
    enemy_7_collider = "物体/enemy_7_collider",
    enemy_8_collider = "物体/enemy_8_collider",
    evilAir_2 = "物体/evilAir_2",
    bossRoom_enter = "物体/bossRoom_enter",
}

---@class 相机_蜃境_雾锁寒山_06荒原雪林
local cameras = {
    cam_portal = "相机/cam_portal",
    cam_main = "相机/cam_main",
    cam_start_far = "相机/cam_start_far",
    cam_start_near = "相机/cam_start_near",
    cam_start_3 = "相机/cam_start_3",
}

---@class 位置_蜃境_雾锁寒山_06荒原雪林
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    enemy_1 = "位置/enemy_1",
    enemy_2 = "位置/enemy_2",
    enemy_3 = "位置/enemy_3",
    enemy_4 = "位置/enemy_4",
    enemy_5 = "位置/enemy_5",
    elite_9 = "位置/elite_9",
    enemy_6 = "位置/enemy_6",
    enemy_7 = "位置/enemy_7",
    enemy_8 = "位置/enemy_8",
}

---@class 资产_蜃境_雾锁寒山_06荒原雪林
local assets = {
    timeline_wolfFight = "资产/timeline_wolfFight",
}

---@class 动作_蜃境_雾锁寒山_06荒原雪林
local animationClips = {
}

---@class 蜃境_雾锁寒山_06荒原雪林
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
