local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)
local <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, role1, role2, role3, role4, role
local hasWin = 0

function init()
    ---- 禁用卡牌等级显示
    --battle.HideCardLevel = true
    -- 禁用自动战斗按钮
    bcapi.disable_auto_battle()
    ---- 禁用加速
    --bcapi.disable_speed_up()
    -- 禁用身法按钮
    bcapi.disable_dart()
    ---- 禁用左侧登场牌
    --bcapi.disable_join_card_expand_mode()
    ---- 禁用战场UI
    bcapi.hide_ui("技能区")
    bcapi.hide_ui("怒气条")
    bcapi.hide_ui("角色技区")
    bcapi.hide_ui("策略技区")
    --创建角色加入战斗
    createRoles()
end

function createRoles()
    bcapi.async_call(function()
        -- 创建玩家角色
        bcapi.create_join_new_player_with_card_newbie(2, team, bcapi.Vector2(1, -0.5), 0)
        -- 创建荆成和云舞笙
        bcapi.create_join_role_with_card("荆成", 2, team, bcapi.Vector2(-1, 1.5), 0)
        bcapi.create_join_role_with_card("云舞笙", 2, team, bcapi.Vector2(-1, -2), 0)
        -- 设置云舞笙、荆成绝招CD
        local card2 = bcapi.get_player_unique_card("云舞笙")
        local card3 = bcapi.get_player_unique_card("荆成")
        card2.CDTimer = 999
        card3.CDTimer = 999
        --  给云舞笙、荆成加点属性，并且给主角加个阻塞使索敌正确按照剧本来
         zhujue = bcapi.get_player_role("主角_新手战斗")
         YunWusheng = bcapi.get_player_role("云舞笙")
         JingCheng = bcapi.get_player_role("荆成")
         zhujue.Buff:AddBuff("通用_阻塞", 1.3, 1, 1, zhujue)
         YunWusheng.Buff:AddBuff("配角_珍娘战斗_属性提升", 300, 1, 1, YunWusheng)
         JingCheng.Buff:AddBuff("配角_珍娘战斗_属性提升", 300, 1, 1, JingCheng)

    end)
end

function start()
    --添加触发器，开场演出
    talk1()

    --添加触发器，主角绝招教学
    bcapi.add_timer_trigger(3, talk2)
    bcapi.add_condition_trigger("[%enemy:剧情_珍娘->flag:剧情_珍娘_被绝招打%][=]1", 0, SetCD)
    bcapi.add_condition_trigger("[%enemy:剧情_珍娘->flag:剧情_珍娘_主角被晕%][=]1", 0, YunWushengCD)
    bcapi.add_condition_trigger("[%enemy:剧情_珍娘->flag:剧情_珍娘_只打断一次%][=]1", 0, ZhuJueCD)
    bcapi.add_condition_trigger("[%enemy:剧情_珍娘->flag:剧情_珍娘_重置荆成CD%][=]1", 0, JingChengCD)
    --添加触发器，珍娘退场
    bcapi.add_condition_trigger("[%enemy:剧情_珍娘->hp_pct%][<=]0.10", 0, triggerWin)
end

function talk1()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        -- 此处的延时是为了等待战场镜头
        bcapi.wait(1)
        bcapi.show_hint_desc_panel("自动战斗", "自动战斗", "欢迎来到《对决！剑之川》，角色在场上会自动攻击，击败敌人获得胜利！", "知道了")
        --show这个会取消暂停，需再次暂停一次
        bcapi.pause_and_hide_ui()
        --开场演出对话

        --埋点
        bcapi.ta_event_track("player_start_story", "id", "41")

        --bcapi.playVoice("Dub","荆成，我的名字……荆成。")

        bcapi.talk("云舞笙#云舞笙#开心", "是[PlayerName]！我就知道什么都困不住你!")
        bcapi.talk("荆成#荆成#微笑","太好了，有你在，这些<color=yellow>蜃境幻影</color>不足为惧！")
        bcapi.talk("主角#主角_新手战斗","嗯，一鼓作气击败她！")
        bcapi.talk("剧情_珍娘#剧情_珍娘","哈哈哈！既然来了，就不要再走了！")

        bcapi.resume_and_show_ui()
    end)
end

function talk2()
    bcapi.async_call(function()
        zhujue = bcapi.get_player_role("主角_新手战斗")
        zhujue.Buff:AddBuff("主角_珍娘战斗_属性提升", 300, 1, 1, zhujue)    
        --演出对话
        bcapi.pause_and_hide_ui()
        --埋点
        bcapi.ta_event_track("player_start_story", "id", "42")
        bcapi.talk("荆成#荆成", "阁主！你的力量已经恢复，找准时机将对手一举歼灭！")
        --短暂恢复游戏
        bcapi.resume_and_show_ui()
        bcapi.pause()
        bcapi.show_ui("技能区")
        bcapi.show_ui("角色技区")
        bcapi.show_ui("怒气条")
        bcapi.show_unique_skill_guide("主角_新手战斗", "剧情_珍娘", "拖拽绝招按钮至指定区域，释放绝招「百念所归」")

        --将策略牌加入牌堆
        --bcapi.add_strategy_card("血沸剑魂", 5, team)
        --bcapi.add_strategy_card("藏锋积锐", 5, team)
        --bcapi.add_strategy_card("大藏心经", 5, team)
        --bcapi.add_strategy_card("吐纳法", 5, team)

    end)
end

function SetCD()
    bcapi.async_call(function()
    -- bcapi.wait(3)
    --设置荆成和云舞笙CD
    local card2 = bcapi.get_player_unique_card("云舞笙")
    local card3 = bcapi.get_player_unique_card("荆成")
    card2.CDTimer = 0
    card3.CDTimer = 0
    end)
end

function YunWushengCD()
    bcapi.async_call(function()
    local card2 = bcapi.get_player_unique_card("云舞笙")
        if team.NuqiSystem.CurNuqi < 4 then
        team.NuqiSystem.CurNuqi = 4
            -- 设置云舞笙绝招CD
            if card2.CDTimer > 0 then
            ---@diagnostic disable-next-line: inject-field-fail
            card2.CDTimer = 0
            end
        else
            if card2.CDTimer > 0 then
                ---@diagnostic disable-next-line: inject-field-fail
                card2.CDTimer = 0
            end            
        end
    end)
end

function ZhuJueCD()
    bcapi.async_call(function()
    local card1 = bcapi.get_player_unique_card("主角_新手战斗")
        if team.NuqiSystem.CurNuqi < 3 then
        team.NuqiSystem.CurNuqi = 3
            --设置主角绝招CD
            if card1.CDTimer > 0 then
            ---@diagnostic disable-next-line: inject-field-fail
            card1.CDTimer = 0
            end
        else
            if card1.CDTimer > 0 then
                ---@diagnostic disable-next-line: inject-field-fail
                card1.CDTimer = 0
            end
        end
    end)
end

function JingChengCD()
    bcapi.async_call(function()
    local card3 = bcapi.get_player_unique_card("荆成")
        if team.NuqiSystem.CurNuqi < 3 then
        team.NuqiSystem.CurNuqi = 3
            --设置荆成绝招CD
            if card3.CDTimer > 0 then
            ---@diagnostic disable-next-line: inject-field-fail
            card3.CDTimer = 0
            end
        else
            if card3.CDTimer > 0 then
                ---@diagnostic disable-next-line: inject-field-fail
                card3.CDTimer = 0
            end
        end
    end)
end

function triggerWin()
    if hasWin == 1 then 
        return;
    end
    hasWin = 1
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        --埋点
        bcapi.ta_event_track("player_start_story", "id", "43")
        bcapi.talk("剧情_珍娘#剧情_珍娘","执念不散，蜃境长存……剑川之主，来日再战！")
        bcapi.resume()

        local Boss = bcapi.get_role(1, "剧情_珍娘")
        bcapi.use_skill_to_role(Boss, "剧情_珍娘_退场", Boss, 1)
        
        bcapi.wait(0.5)

        bcapi.pause()
        bcapi.talk("云舞笙#云舞笙#生气", "咳咳……又是这怪雾。烦人，让她逃了！")
        bcapi.talk("荆成#荆成#思索","等等，她好像掉了什么东西？")
        bcapi.talk("云舞笙#云舞笙#开心", "是残卷！我们要找的东西果然在她手上。")
        bcapi.talk("主角#主角_新手战斗","既然残卷到手，我们先离开这里。")
        bcapi.win()
        bcapi.resume_and_show_ui()
    end)
end

