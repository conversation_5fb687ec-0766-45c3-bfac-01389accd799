local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle

function init()
    battle.ForceNotEndBattle = true
    bcapi.reset_limit_time(90)
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        team = bcapi.get_team(0)
        bcapi.create_join_role("诸葛鹿", 45, team, bcapi.Vector2(-5, 0), 0)
        bcapi.add_strategy_card("吐纳法", 5, team)
        bcapi.add_strategy_card("天罡真气", 5, team)
        bcapi.add_strategy_card("梧叶舞秋风", 5, team)
        bcapi.add_strategy_card("藏锋积锐", 5, team)
        bcapi.add_unique_card("诸葛鹿",team)
    end)
end

function start()
    --战斗中诸葛鹿心魔第五次阵亡后不存在这个物体，因此只有将flag挂在诸葛鹿身上,这里的c指的是玩家方
    bcapi.add_condition_trigger("[%c->flag:诸葛鹿_心魔_剩余复活次数%][=]-1",3,BattleWin)
    bcapi.add_condition_trigger("[%c->teammate:诸葛鹿%][<]1",2,BattleLose)
    bcapi.add_timer_trigger(89,BattleLose)
    StartTalk()
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        
        bcapi.talk("诸葛鹿", "心魔虚影，看拳！")
        
        bcapi.resume_and_show_ui()
    end)
end


function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
