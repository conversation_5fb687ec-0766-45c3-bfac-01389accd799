local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

local level = 30

function init()
    createRoles()
end

function start()  
    bcapi.add_timer_trigger(0.1, Talk)
end

function createRoles()
    bcapi.async_call(function()
        --创建角色
        bcapi.create_join_role("世界_愿作风宁_官员", level, team, bcapi.Vector2(-5, -1), 0)
    end)
end

function Talk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("神秘刺客", "多管闲事，连你一起宰了！")
        bcapi.talk("官员", "哎哟，大侠，救救我，我可什么都没做啊！")
        bcapi.talk("神秘刺客", "呵，什么都没做？那你就什么都别做了，下地狱吧！")
        bcapi.resume_and_show_ui()
    end)
end