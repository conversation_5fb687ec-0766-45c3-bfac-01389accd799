--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/漫游_雪谷/flags_漫游_雪谷")


---@class 角色_漫游_雪谷_流亡者营地
local npcs = {
    --- "胖管家"
    pangguanjia = "角色/pangguanjia",
    --- "流亡者甲"
    lwz_01 = "角色/lwz_01",
    --- "流亡者乙"
    lwz_02 = "角色/lwz_02",
    --- "吴铁手"
    wutieshou = "角色/wutieshou",
    --- "陆九"
    lujiu = "角色/lujiu",
    --- "墨七"
    moqi = "角色/moqi",
    --- "大牛"
    daniu = "角色/daniu",
    --- "黑娃"
    heiwa = "角色/heiwa",
    --- "守卫"
    shouwei = "角色/shouwei",
    --- "流亡者女性"
    lwz_nv = "角色/lwz_nv",
    --- "失落女性"
    shiluo = "角色/shiluo",
    --- "酿酒老人"
    niangjiu = "角色/niangjiu",
    --- "博学老人"
    boxue = "角色/boxue",
    --- "豆沙"
    dousha = "角色/dousha",
    --- "李大锤"
    lidachui = "角色/lidachui",
    --- "老猎户"
    laoliehu = "角色/laoliehu",
    --- "汪虎"
    wanghu = "角色/wanghu",
}

---@class 物体_漫游_雪谷_流亡者营地
local objs = {
    --- "脚印"
    footstep = "物体/footstep",
    --- "脚印1"
    footstep_1 = "物体/footstep_1",
    --- "脚印2"
    footstep_2 = "物体/footstep_2",
    --- "南门"
    southgate = "物体/southgate",
    --- "北门"
    northgate = "物体/northgate",
    --- "东门"
    eastgate = "物体/eastgate",
    --- "齿轮"
    gear = "物体/gear",
    --- "白虎骨"
    baihu = "物体/baihu",
}

---@class 相机_漫游_雪谷_流亡者营地
local cameras = {
    --- "开场剧情相机"
    start = "相机/start",
    --- "开场剧情看向胖管家相机"
    start_pgj = "相机/start_pgj",
}

---@class 位置_漫游_雪谷_流亡者营地
local pos = {
    --- "通往山脚"
    Shanjiao = "位置/Shanjiao",
    --- "通往雪林"
    Forest = "位置/Forest",
    --- "通往古渡"
    Gudu = "位置/Gudu",
    --- "通往地宫"
    Digong = "位置/Digong",
    --- "北门"
    NG = "位置/NG",
    --- "南门"
    SG = "位置/SG",
    --- "东门"
    EG = "位置/EG",
    --- "墨七初始演出位置"
    MQ_init = "位置/MQ_init",
}

---@class 资产_漫游_雪谷_流亡者营地
local assets = {
}

---@class 动作_漫游_雪谷_流亡者营地
local animationClips = {
}

---@class 漫游_雪谷_流亡者营地
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
