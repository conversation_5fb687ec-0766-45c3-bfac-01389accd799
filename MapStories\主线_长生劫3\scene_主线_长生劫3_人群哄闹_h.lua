--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/主线_长生劫3/flags_主线_长生劫3")


---@class 角色_主线_长生劫3_人群哄闹
local npcs = {
    --- "椿岁"
    zhujue = "角色/zhujue",
}

---@class 物体_主线_长生劫3_人群哄闹
local objs = {
    --- "触发器_播放Timeline"
    Trigger1 = "物体/Trigger1",
    --- "任务点提示"
    effects_task = "物体/effects_task",
    role = "物体/role",
}

---@class 相机_主线_长生劫3_人群哄闹
local cameras = {
    camera_start = "相机/camera_start",
    cam_chest = "相机/cam_chest",
    cam_main = "相机/cam_main",
}

---@class 位置_主线_长生劫3_人群哄闹
local pos = {
    --- "椿岁初始位置"
    Pos_start = "位置/Pos_start",
}

---@class 资产_主线_长生劫3_人群哄闹
local assets = {
    --- "Timeline1"
    Timeline1 = "资产/Timeline1",
}

---@class 动作_主线_长生劫3_人群哄闹
local animationClips = {
}

---@class 主线_长生劫3_人群哄闹
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
