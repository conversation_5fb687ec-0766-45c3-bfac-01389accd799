
---@type 蜃境_雾锁寒山_04流亡者营地
local context = require("MapStories/蜃境_雾锁寒山_04流亡者营地/scene_蜃境_雾锁寒山_04流亡者营地_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_雾锁寒山_04流亡者营地
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end
local capi = require("ChapterApi")

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function test()
    s.setActive(npcs.daoshi,false)
end

--精英怪战斗
function eliteBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

--boss战斗
function bossBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function start()
    if flags.daoshi_jiemi >= 3 and flags.boss_8_battle == 1 then

        s.setPos(npcs.daoshi_death,pos.daoshi_die)
        s.setActive(npcs.daoshi_death,true)
        s.setActive(npcs.daoshi,false)
    elseif flags.daoshi_jiemi >= 3 then
        s.setActive(npcs.daoshi,false)
    else
        --如果道士任务未完成，重新进入地图便重新开始
        flags.daoshi_jiemi_hint_done = 0
        flags.daoshi_jiemi = 0
        flags.daoshi_first_down = 0
    end

    --清除所有倒下的敌人flag
    flags.enemy_1_down = 0
    flags.enemy_2_down = 0
    flags.enemy_3_down = 0
    flags.enemy_4_down = 0
    flags.enemy_5_down = 0


    --boss战斗状态

    if flags.boss_8_show == 1 and flags.boss_8_battle == 0 then
        s.setActive(npcs.boss_8,true)
    end

    refreshMapStates()
    
    s.readyToStart(true)
    try_execute_newbieGuide()
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数
    s.playMusic("蜃境")
    --设置被污染的宝箱的显隐状态
    if flags.open_shadowChest_1 == 1 then
        s.setActive(objs.evilAirChest_1,false)
    else
        s.setActive(objs.evilAirChest_1,true)
    end

    if flags.open_shadowChest_2 == 1 then
        s.setActive(objs.evilAirChest_2,false)
    else
        s.setActive(objs.evilAirChest_2,true)
    end

    if flags.boss_8_show == 0 and flags.boss_8_battle == 0 and flags.enemy_1_battle == 1 and flags.enemy_2_battle == 1 and flags.enemy_3_battle == 1 and flags.enemy_4_battle == 1 and flags.enemy_5_battle == 1 then
        s.popInfo("蜃境守卫出现！！！")
        showBoss()
    end

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 5) then
        taskStep = "forwardToExplore"
    elseif (step == 5) then
        taskStep = "talkToMoqi"
    elseif (step == 6) then
        taskStep = "leave"
    else
        taskStep = "finished"
    end

    s.setTaskStep("雾锁寒山流亡者营地", taskStep)

    reset_all()
    --第一次进入场景
    if step == 0 then
        s.setPos(npcs.zhujue,pos.pos_start)
        s.setTaskStep("雾锁寒山流亡者营地","forwardToExplore")
        next_level_step(true)
    elseif step == 1 then
        --开场演出
        if flags.start_show == 0 then
            s.camera(cameras.cam_start_far,true,true,blendHintEnum.Cut)
            s.readyToStart(true)
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_流亡者营地开场进入")
            s.wait(0.5)
            s.cameraAsync(cameras.cam_start_near,true,true,blendHintEnum.Custom,15)
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_开场演出闲话")

            s.blackScreen()
            s.camera(cameras.cam_main,true,true,blendHintEnum.Cut)
            s.lightScreen()

            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_开场演出闲话1")
            flags.start_show = 1
        end

        level_guide_to(npcs.wshs_10_f1, 0.5)
    elseif step == 2 then
        level_guide_to(npcs.wshs_10, 0.5)
    elseif step == 3 then
        level_guide_to(npcs.wshs_11, 1)
    elseif step == 4 then
        level_guide_to(npcs.wshs_12, 1)
    elseif step == 5 then
        level_guide_to(npcs.moqi, 1)
    else
        level_guide_to(objs.exit)
    end
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.wshs_10_f1, false)
    s.setActive(npcs.wshs_10, false)
    s.setActive(npcs.wshs_11, false)
    s.setActive(npcs.wshs_12, false)
    --删除所有的引导点
    s.removeAllGameMapGuides()

    --道士任务
    daoshiReactMessage() 

    --道士任务完成，且敌人全部击败过一次，让敌人消失
    if flags.daoshi_jiemi >= 3 and flags.enemy_1_battle == 1 and flags.enemy_2_battle == 1 and flags.enemy_3_battle == 1 and flags.enemy_4_battle == 1 and flags.enemy_5_battle == 1 then
        s.setActive(npcs.enemy_1, false)
        s.setActive(npcs.enemy_2, false)
        s.setActive(npcs.enemy_3, false)
        s.setActive(npcs.enemy_4, false)
        s.setActive(npcs.enemy_5, false)
    end
 
    --灵鸟任务
    if s.getCurrentTaskStep("灵鸟2") == "deactive" and s.getCurrentTaskStep("灵鸟") == "finished" then
        s.setActive(npcs.ln_shencongan,true)
    elseif s.getCurrentTaskStep("灵鸟2") == "findsomeone" then
        s.setActive(objs.ln_jiaoyin,true)
        s.setActive(npcs.ln_shencongan,true)
        s.setActive(npcs.ln_muren,true)
    elseif s.getCurrentTaskStep("灵鸟2") == "backToShencongan" then
        s.setActive(npcs.ln_shencongan,true)
        s.setActive(objs.ln_jiaoyin,false)
        s.setActive(npcs.ln_muren,false)
    elseif s.getCurrentTaskStep("灵鸟2") == "askForlinqi" then
        s.setActive(npcs.ln_linqi,true)
        s.setPos(npcs.ln_linqi,pos.nl_linqicome)
    elseif s.getCurrentTaskStep("灵鸟2") == "finished" then
        s.setActive(npcs.ln_linqi,false)
    end

    --收集物显隐
    if flags.collect_1 == 1 then
        s.setActive(objs.wshs4_1005,false)
    else
        s.setActive(objs.wshs4_1005,true)
    end

    if flags.collect_2 == 1 then
        s.setActive(objs.wshs4_1006,false)
    else
        s.setActive(objs.wshs4_1006,true)
    end

    --墨七显隐
    if flags.moqi_talk == 0 or flags.moqi_talk == 1 then
        s.setPos(npcs.moqi,pos.moqi_start,true)
        s.setActive(npcs.moqi,true)
    elseif flags.moqi_talk == 2 then
        s.setPos(npcs.moqi,pos.moqi_end,true)
        s.setActive(npcs.moqi,true)
    end
end

function exitScene()
    if s.getCurrentTaskStep("雾锁寒山流亡者营地") == "leave" then
        s.setTaskStep("雾锁寒山流亡者营地","finished")
    end

    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("雾锁寒山")
end
-------------------地图跑图---------------------

function moqi()
    if flags.moqi_talk == 0 then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_墨七_1")
        flags.moqi_talk = 1
    elseif flags.moqi_talk == 1 then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_墨七_2")
    elseif flags.moqi_talk == 2 then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_墨七_3")
        flags.moqi_talk = 3
        s.setTaskStep("雾锁寒山流亡者营地","leave")
        next_level_step(true)
    elseif flags.moqi_talk == 3 then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_墨七_4")
    end
end

function shadowChest(id,chestKey)
    local chestKey = "open_shadowChest_"..chestKey
    if flags[chestKey] == 0 then
        s.popInfo("需击败附近镇守的蜃境怪物！！！")
    else
        require("MapStories/MapStoryCommonTools").roamMapChest(id)
    end
end

---显示boss
function showBoss()
    s.playTimeline(assets.bossShow,false)
    s.setActive(npcs.boss_8,true)
    s.lightScreen()
    flags.boss_8_show = 1
end

function boss_8_battle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,function()
        flags.boss_8_battle = 1
        --s.setActive(npcs.daoshi,false)
    end
)
end
-------------------支线和解密---------------------
function daoshi()
    if flags.daoshi_jiemi == 0 then
        local ret = s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_疯癫的道士遇见")
        if ret == 1 then
            flags.daoshi_jiemi = 1
        else
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_疯癫的道士遇见【不予理睬】")
            return
        end
    elseif flags.daoshi_jiemi == 1 then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_疯癫的道士闲话")
    elseif flags.daoshi_jiemi == 2 and flags.daoshi_jiemi_hint_done == 1 then
        --任务完成
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_疯癫的道士任务完成")

        capi.finishMemorySlot("wshs4_question_daoshi")
        flags.daoshi_jiemi = 3
    elseif flags.daoshi_jiemi == 3 then
        if flags.boss_8_battle == 1 then
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_疯癫的道士_死亡")
        else
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_疯癫的道士任务完成_闲话")
        end
    end
end

function daoshi_death()
    s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_疯癫的道士_死亡")
end

function daoshiReactMessage() 
    if downAllEnemy() then
        if flags.daoshi_jiemi_hint_done == 0 then
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_疯癫的道士提前完成_提示")
            flags.daoshi_jiemi = 2
            flags.daoshi_jiemi_hint_done = 1
        end
    end
end
function downAllEnemy()
    if flags.enemy_1_down == 1 and flags.enemy_2_down == 1 and flags.enemy_3_down == 1 and flags.enemy_4_down == 1 and flags.enemy_5_down == 1 then
        return true
    else
        return false
    end
end

function ln_shencongan()
    if s.getCurrentTaskStep("灵鸟2") == "deactive" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_流亡者营地_灵鸟支线_再次遇见沈聪安")
        s.setTaskStep("灵鸟2","findsomeone")

        --设置脚印状态
        s.setActive(objs.ln_jiaoyin,true)
        s.setActive(npcs.ln_muren,true)

    elseif s.getCurrentTaskStep("灵鸟2") == "findsomeone" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_流亡者营地_灵鸟支线_沈聪安闲话")
    elseif s.getCurrentTaskStep("灵鸟2") == "backToShencongan" then
        s.camera(cameras.cam_ln_backTosheng,true,true,blendHintEnum.Cut)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_流亡者营地_灵鸟支线_回复沈聪安")
        s.setActive(npcs.ln_linqi,true)
        s.move(npcs.ln_linqi,pos.nl_linqicome,2,true)
        s.cameraAsync(cameras.cam_ln_backTosheng1,true,true,blendHintEnum.Cut)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_流亡者营地_灵鸟支线_回复沈聪安_林契赶到")
        s.move(npcs.ln_shencongan,pos.shengcongan_leave,3,true)
        s.setActive(npcs.ln_shencongan,false)

        s.camera(cameras.cam_main,true,true,blendHintEnum.Custom,2)
        s.setTaskStep("灵鸟2","askForlinqi")
    end
end

function ln_linqi()
    if s.getCurrentTaskStep("灵鸟2") == "askForlinqi" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_流亡者营地_灵鸟支线_对话林契")
        capi.finishMemorySlot("wshs4_sideTask_lingniao2")
        s.setTaskStep("灵鸟2","finished")
    elseif s.getCurrentTaskStep("灵鸟2") == "finished" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_流亡者营地_灵鸟支线_对话林契_闲话")
    end
end

function ln_muren()
    s.setActive(objs.ln_jiaoyin,false)
    if s.getCurrentTaskStep("灵鸟2") == "findsomeone" then
        s.animState(npcs.zhujue,roleAnims.BSquat_Loop,true)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_流亡者营地_灵鸟支线_发现木人")
        s.camera(cameras.cam_muren,true,true,blendHintEnum.Custom,2)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_流亡者营地_灵鸟支线_木人声音")

        s.camera(cameras.cam_jiaoyin,true,true,blendHintEnum.Cut)
        s.animState(npcs.zhujue,roleAnims.BSquat_Loop,false)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_流亡者营地_灵鸟支线_主角站起")

        s.setActive(npcs.ln_muren,false)
        s.playTimeline(assets.ln_muren,false)
        battleWithMuren()
    end
end

function battleWithMuren()
    local ret = s.battle("蜃境_雾锁寒山4_灵鸟_与木人筑战斗")
    if ret then
        s.camera(cameras.cam_main,true,true,blendHintEnum.Cut)
        s.setActive(npcs.ln_muren,false)
        s.setPos(npcs.zhujue,pos.nl_jiaoyin_back)
        s.lightScreen()

        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_流亡者营地_灵鸟支线_打败木人")
        s.setTaskStep("灵鸟2","backToShencongan")
    else
        s.setPos(npcs.zhujue,pos.nl_jiaoyin_back)
        s.setActive(objs.ln_jiaoyin,true)
        s.setActive(npcs.ln_muren,true)
        s.camera(cameras.cam_main,true,true,blendHintEnum.Cut)
        s.lightScreen()
    end
    s.nextStep("refreshMapStates")
end


---------------------战斗实现---------------------

--战斗选择
function enemy_battle_choose(id,enemyKey)
    if flags[enemyKey.."_battle"] == 1 then
        enemy_normal_battle(id,enemyKey)
    else
        enemy_battle(id,enemyKey)
    end
end

--不消耗体力的战斗
function enemy_normal_battle(id,enemyKey)

    print("敌人战斗!!!!!!")
    local colliderKey = enemyKey.."_collider"

    s.popInfo("你已被发现！！！")

    s.turnTo(npcs[enemyKey],npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs[enemyKey])

    s.animState(npcs[enemyKey],"BAssault_Loop",true)
    s.wait(2)

    local isWin = s.battle(id,nil,true)
    if isWin then
        flags[enemyKey.."_battle"] = 1
        s.setActive(objs[colliderKey],false)
        s.setActive(npcs[enemyKey],false)
    else
        battle_lose(id,enemyKey)
    end

    s.wait(0.5)
    refreshMapStates()
end

function enemy_battle(id,enemyKey)
    print("敌人战斗")
    local colliderKey = enemyKey.."_collider"

    s.popInfo("你已被发现！！！")

    --s.setActive(objs[colliderKey],false)
    s.turnTo(npcs[enemyKey],npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs[enemyKey])

    s.animState(npcs[enemyKey],"BAssault_Loop",true)
    s.wait(2)

    require("MapStories/MapStoryCommonTools").roamMapBattle(id,function()
        s.popInfo("战斗胜利！！！")
        flags[enemyKey.."_battle"] = 1
        s.setActive(objs[colliderKey],false)
        s.setActive(npcs[enemyKey],false)
    end,
    function()
        battle_cancle(id,enemyKey)
    end,
    function()
        battle_lose(id,enemyKey)
    end
)

    s.wait(0.5)
    refreshMapStates()
end

function battle_lose(id,enemyKey)
    local colliderKey = enemyKey.."_collider"
    print("战斗失败")
    s.popInfo("战斗失败！！！")

    s.blackScreen()
    s.animState(npcs[enemyKey],"BAssault_Loop",false)
    s.setActive(objs[colliderKey],true)
    s.setPos(npcs.zhujue,pos[enemyKey],true)
    s.lightScreen()
end
function battle_cancle(id,enemyKey)
    local colliderKey = enemyKey.."_collider"

    s.blackScreen()
    s.animState(npcs[enemyKey],"BAssault_Loop",false)
    s.setActive(objs[colliderKey],true)
    s.setPos(npcs.zhujue,pos[enemyKey],true)
    s.lightScreen()
end

function fightInBack(enemyKey)
    local colliderKey = enemyKey.."_collider"
    local enemyDownKey = enemyKey.."_down"
    s.setActive(objs[colliderKey],false)

    s.animState(npcs.zhujue,"Special_2",true)
    s.wait(1)
    s.playSound("sfx","拳击3")
    s.wait(0.6)
    
    s.animState(npcs[enemyKey],"BDie",true)
    s.setDeath(npcs[enemyKey],0.5,false)

    flags[enemyDownKey] = 1
    s.popInfo("敌人暂时被击倒！！！")

    if flags.daoshi_first_down == 0 then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_疯癫的道士_第一次击倒敌人")
        flags.daoshi_first_down = 1
    end

    refreshMapStates()
end

function evilAir_1(id)
    s.setActive(objs.evilAir_1,false)
    s.setActive(npcs.role_elite_6, true)

    s.turnTo(npcs.role_elite_6,npcs.zhujue)
    s.animState(npcs.role_elite_6,"BAssault_Loop",true)
    s.wait(2)
    elite_6_fight(id)
end

function evilAir_2(id)
    s.setActive(objs.evilAir_2,false)
    s.setActive(npcs.role_elite_7, true)

    s.turnTo(npcs.role_elite_7,npcs.zhujue)
    s.animState(npcs.role_elite_7,"BAssault_Loop",true)
    s.wait(2)
    elite_7_fight(id)
end

function elite_6_fight(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,
    function()
        flags.open_shadowChest_1 = 1
            s.setActive(objs.evilAirChest_1,false)
            refreshMapStates()
    end,
    function()
        s.animState(npcs.role_elite_6,"BAssault_Loop",false)
    end,
    function()
        s.animState(npcs.role_elite_6,"BAssault_Loop",false)
    end
    )
end

function elite_7_fight(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,
    function()
        flags.open_shadowChest_2 = 1
        s.setActive(objs.evilAirChest_2,false)
        refreshMapStates()
    end,
    function()
        s.animState(npcs.role_elite_7,"BAssault_Loop",false) 
    end,
    function()
        s.animState(npcs.role_elite_7,"BAssault_Loop",false)
    end
    )
end

-------------------关卡记忆碎片----------------------------     
function wshs_10_f1()
    executeMemSlot("wshs_10_f1", function()
        require("Chapters/雾锁寒山").wshs4_10_f1()
    end)
end

function wshs_10()
    executeMemSlot("wshs_10", function()
        require("Chapters/雾锁寒山").wshs4_10()
    end)
end

function wshs_11()
    executeMemSlot("wshs_11", function()
        require("Chapters/雾锁寒山").wshs4_11()
    end)
end

function wshs_12()
    executeMemSlot("wshs_12", function()
        require("Chapters/雾锁寒山").wshs4_12()
    end,function()
        s.setPos(npcs.moqi,pos.moqi_end,true)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_记忆碎片结束")
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_流亡者营地","蜃境_雾锁寒山_墨七发现")
        flags.moqi_talk = 2
        next_level_step(true)
    end)
end

function shudu_1005(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
    flags.collect_1 = 1
end

function shudu_1006(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
    flags.collect_2 = 1
end