--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/序章/flags_序章")


---@class 角色_主线_破冰而出_蜃境
local npcs = {
    jingcheng = "角色/jingcheng",
    yunwusheng = "角色/yunwusheng",
    zhujue = "角色/zhujue",
}

---@class 物体_主线_破冰而出_蜃境
local objs = {
    SpeakTrigger = "物体/SpeakTrigger",
    EndTrigger = "物体/EndTrigger",
}

---@class 相机_主线_破冰而出_蜃境
local cameras = {
    DoorCamera = "相机/DoorCamera",
    MainCamera = "相机/MainCamera",
}

---@class 位置_主线_破冰而出_蜃境
local pos = {
    --- "开场位置"
    startpos = "位置/startpos",
    --- "离场位置"
    EndPos = "位置/EndPos",
    NpcPos = "位置/NpcPos",
    EndPos_jinchen = "位置/EndPos_jinchen",
    EndPos_yunwusheng = "位置/EndPos_yunwusheng",
    RunPos_jinchen = "位置/RunPos_jinchen",
    RunPos_yunwusheng = "位置/RunPos_yunwusheng",
    RunPos_zhujue = "位置/RunPos_zhujue",
    SetEndPos_jinchen = "位置/SetEndPos_jinchen",
    SetEndPos_yunwusheng = "位置/SetEndPos_yunwusheng",
}

---@class 资产_主线_破冰而出_蜃境
local assets = {
}

---@class 动作_主线_破冰而出_蜃境
local animationClips = {
}

---@class 主线_破冰而出_蜃境
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
