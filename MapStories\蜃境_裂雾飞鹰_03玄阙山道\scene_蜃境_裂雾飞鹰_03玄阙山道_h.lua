--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_裂雾飞鹰_03玄阙山道/flags_蜃境_裂雾飞鹰_03玄阙山道")


---@class 角色_蜃境_裂雾飞鹰_03玄阙山道
local npcs = {
    Player = "角色/Player",
    lwfy_05 = "角色/lwfy_05",
    lwfy_06 = "角色/lwfy_06",
    lwfy_07 = "角色/lwfy_07",
    enemy_1 = "角色/enemy_1",
    enemy_2 = "角色/enemy_2",
    enemy_3 = "角色/enemy_3",
    enemy_4 = "角色/enemy_4",
    enemy_5 = "角色/enemy_5",
    elite_6 = "角色/elite_6",
    yuqiong = "角色/yuqiong",
    laopopo = "角色/laopopo",
    puzzleKey_oldMan = "角色/puzzleKey_oldMan",
    puzzleReward_oldMan = "角色/puzzleReward_oldMan",
}

---@class 物体_蜃境_裂雾飞鹰_03玄阙山道
local objs = {
    exit = "物体/exit",
    chest_1 = "物体/chest_1",
    lwfy_1003 = "物体/lwfy_1003",
    puzzleLight_Trigger01 = "物体/puzzleLight_Trigger01",
    puzzleLight_Trigger02 = "物体/puzzleLight_Trigger02",
    puzzleLight_Trigger03 = "物体/puzzleLight_Trigger03",
    puzzleLight_Trigger04 = "物体/puzzleLight_Trigger04",
    noLight_01 = "物体/noLight_01",
    noLight_02 = "物体/noLight_02",
    noLight_03 = "物体/noLight_03",
    noLight_04 = "物体/noLight_04",
    longLight_01 = "物体/longLight_01",
    longLight_02 = "物体/longLight_02",
    longLight_03 = "物体/longLight_03",
    longLight_04 = "物体/longLight_04",
    puzzleKey_message = "物体/puzzleKey_message",
    chest_2 = "物体/chest_2",
    exit_1 = "物体/exit_1",
    chest_2_open = "物体/chest_2_open",
}

---@class 相机_蜃境_裂雾飞鹰_03玄阙山道
local cameras = {
    cam1 = "相机/cam1",
}

---@class 位置_蜃境_裂雾飞鹰_03玄阙山道
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
    zhujue_start_pos1 = "位置/zhujue_start_pos1",
}

---@class 资产_蜃境_裂雾飞鹰_03玄阙山道
local assets = {
    puzzle_complete = "资产/puzzle_complete",
    puzzle_complete_Nvzhu = "资产/puzzle_complete_Nvzhu",
}

---@class 动作_蜃境_裂雾飞鹰_03玄阙山道
local animationClips = {
}

---@class 蜃境_裂雾飞鹰_03玄阙山道
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
