local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    Effects()
end

function start()
    bcapi.add_timer_trigger(1, Effects)
    --local teamValues = bcapi.get_team(0).ValueDict
end

function Effects()
    bcapi.async_call(function()
        local team = bcapi.get_team(0)

        local isActive = false
        local desc = "效果"
        ---@type number?
        local initNuqiAdd = 0
        ---@type number?
        local recover = 0
        ---@type number?
        local nuqiAddSpeed = 0
        ---@type number?
        local wudi = 0
        ---@type number?
        local baoji = 0
        ---@type number?
        local summon = 0
        ---@type number?
        local lvAdd = 0
    
        local dict = bcapi.get_team(0).ValueDict
        -- dict是c#的dict,遍历dict的方法 用 ipairs
        for k,v in pairs(dict) do
            if(k == "StructureFire")then
                initNuqiAdd = tonumber(v)
            end
            if(k == "StructureFood")then
                recover = tonumber(v)
            end
            if(k == "StructureAlcohol")then
                nuqiAddSpeed = tonumber(v)
            end
            if(k == "StructureClothes")then
                wudi = tonumber(v)
            end
            if(k == "StructureKnifeStone")then
                baoji = tonumber(v)
            end
            if(k == "StructurePet")then
                summon = tonumber(v)
            end
            if(k == "StructureEnhance")then
                lvAdd = tonumber(v)
            end
        end
    
        --初始怒气增加，如果值>0，则增加初始怒气5点
        if(initNuqiAdd > 0)then
            isActive = true
            desc = desc .. "「营地篝火」"
            team.NuqiSystem.CurNuqi = team.NuqiSystem.CurNuqi + 5
        end
    
        local cnt = 0
        local lv = 0
        if(recover > 0)then
            isActive = true
            desc = desc .. "「烤肉架子」"
        end
        if(wudi > 0)then
            isActive = true
            desc = desc .. "「缝纫机」"
        end
        if(baoji > 0)then
            isActive = true
            desc = desc .. "「磨刀石」"
        end

        for i = 0, team.RoleList.Count - 1 do
            local role = team.RoleList[i]
            cnt = cnt + 1
            lv = lv + role.Level
            --持续治疗，如果值>0，给team里的所有角色都增加一个"风雪神道_持续治疗"的buff 
            if(recover > 0)then
                role.Buff:AddBuff("风雪神道_持续治疗", 9999, 1, 1, role)
            end
            --无敌，如果值>0，给team里的所有角色都增加一个"无敌"的buff，1级，持续15秒
            if(wudi > 0)then
                role.Buff:AddBuff("无敌", 15, 1, 1, role)
            end
            --暴击，如果值>0，给team里的所有角色都增加一个"魔神降临"的buff，1级，持续20秒
            if(baoji > 0)then
                role.Buff:AddBuff("魔神降临", 20, 1, 1, role)
            end
        end
    
        --如果nuqiAddSpeed的值>0，则将team的ValueDict中"teamNuqiSpeedUp"的值设置为0.3
        if(nuqiAddSpeed > 0)then
            isActive = true
            desc = desc .. "「酿酒桶」"
            if(team.ValueDict:ContainsKey("teamNuqiSpeedUp"))then
                team.ValueDict["teamNuqiSpeedUp"] = 0.3
            else
                team.ValueDict:Add("teamNuqiSpeedUp", 0.3)
            end
            --team.ValueDict["teamNuqiSpeedUp"] = 0.3
        end
    
        --如果lvAdd的值>0，则遍历team的CardSystem的CardPool，找到其中所有的BattleCardType.StrategyCard的卡片，令其Level+2
        if(lvAdd > 0)then
            isActive = true
            desc = desc .. "「锻造台」"
            for i = 0, team.CardSystem.CardPool.Count - 1 do
                local card = team.CardSystem.CardPool[i]
                if(card.Type == CS.H2.BattleCardType.StrategyCard)then
                    --print("卡牌等级" .. card.Level)
                    ---@diagnostic disable-next-line:inject-field-fail
                    card.Level = card.Level + 2
                    --print("卡牌等级2" .. card.Level)
                end
            end
        end
    
        --如果summon的值>0，则将进行额外召唤
        if(summon > 0)then
            isActive = true
            desc = desc .. "「宠物蛋」"
            --rolekey是挑战_风雪神道_猎人斧或者挑战_风雪神道_猎人弓或者挑战_风雪神道_猎人拳或者挑战_风雪神道_猎人枪中，等级是我方RoleList里所有角色的平均等级，属性系数都是2倍
            local k = math.random(1, 4)
            local rolekey = ""
            if(k == 1)then
                rolekey = "挑战_风雪神道_猎人斧"
            elseif(k == 2)then
                rolekey = "挑战_风雪神道_猎人弓"
            elseif(k == 3)then
                rolekey = "挑战_风雪神道_猎人拳"
            else
                rolekey = "挑战_风雪神道_猎人枪"
            end
    
            local hpRate = 1.5
            local opRate = 2
            local odRate = 1.5
    
            bcapi.create_and_summon_role(rolekey, lv/cnt, bcapi.get_team(0), bcapi.Vector2(-6, 0), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")
        end

        if(isActive)then
            bcapi.pause_and_hide_ui()
            bcapi.aside(desc .. "已生效!")
            bcapi.resume_and_show_ui()
        end
    end)
end
