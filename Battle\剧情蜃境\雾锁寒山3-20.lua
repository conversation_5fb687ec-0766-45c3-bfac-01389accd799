local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_墨七", 65, bcapi.get_team(0), bcapi.Vector2(-5, 0), 0)
        bcapi.create_join_role("剧情_诸葛鹿", 65, bcapi.get_team(0), bcapi.Vector2(0, 0), 0)
    
        local moqi = bcapi.get_role(0, "剧情_墨七")
        moqi.Buff:AddBuff("剧情_持续恢复", 300, 1, 1, moqi)
        moqi.Buff:AddBuff("剧情_巨能扛", 300, 1, 1, moqi)
        bcapi.change_role_hp(moqi, 50000)
    
        local zhugelu = bcapi.get_role(0, "剧情_诸葛鹿")
        zhugelu.Buff:AddBuff("剧情_持续恢复", 300, 1, 1, zhugel<PERSON>)
        zhugelu.Buff:AddBuff("剧情_巨能扛", 300, 1, 1, zhugelu)
        bcapi.change_role_hp(zhugelu, 50000)
    end)
end

function start()
    bcapi.add_timer_trigger(30, StartTalk)
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("墨七", "少侠！我们跑到那峭壁底下去！")
        bcapi.talk("诸葛鹿_名字问号", "这是为何......不过，现下也只能听你的了！")
        
        bcapi.resume_and_show_ui()
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
