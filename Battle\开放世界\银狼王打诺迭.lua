local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()

end

function start()
    bcapi.async_call(function()
        bcapi.create_and_summon_role("世界_银狼王_友方版", 40, team, bcapi.Vector2(-7, 0), 0)
        bcapi.add_timer_trigger(0.1, firstTalk)
        bcapi.add_condition_trigger("[%enemy:世界_诺迭->flag:世界_诺迭_已死亡标签%][=]1",1,win) 
    end)
end

function firstTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
            bcapi.talk("诺迭", "你是江湖门派，你应当去干涉人与人之间的厮杀，管一个人和一头畜生的恩怨做什么？")
            bcapi.talk("灵湫", "诺迭大叔，之前您说过，因狼群掠夺家畜，县里布悬赏杀狼，一夜之间，猎人武者齐聚于此。")
            bcapi.talk("灵湫", "但此地本就是狼群住所，甚至狼比人来得还要早，只是它们常年远居山野，互不干扰。")
            bcapi.talk("灵湫", "直至野味健体传言一出，偷猎者盯上了山上的羊兔鹿狐，开始肆意捕猎，与豹狼夺食，狼群无以为生，才冒险下山，捕杀家畜。")
            bcapi.talk("灵湫", "这一切的开始，究竟是人错了，还是狼错了？")
            bcapi.talk("诺迭", "仇恨哪有先来后到！")
            bcapi.talk("银狼王", "嗷呜——")
            bcapi.talk("灵湫", "我知道了，出手吧。")
        bcapi.resume_and_show_ui() 
    end)
end

function win()
    bcapi.async_call(function()
        bcapi.win()
    end)
end