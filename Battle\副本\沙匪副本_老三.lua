local bcapi = require("BattleClientA<PERSON>")
local battle = args["battle"]
bcapi.battle = battle

function start()
    bcapi.add_timer_trigger(0, TalkStart)
    bcapi.add_timer_trigger(70, SecondEnemys)
end

function init()
end

function TalkStart()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_沙匪营地_老三#副本_沙匪营地_老三", "哼！你们怕是活腻歪了，竟敢闯入老子这里！")
        bcapi.talk("副本_沙匪营地_老三#副本_沙匪营地_老三", "老子要用你们下酒！")
        bcapi.resume_and_show_ui()
    end)
end

function SecondEnemys()
    bcapi.async_call(function()
        bcapi.create_join_role_with_card("副本_沙匪营地_沙匪_狼牙棒", 35, bcapi.get_team(1), bcapi.Vector2(-5, 2), 0.1)
        bcapi.create_join_role_with_card("副本_沙匪营地_沙匪_狼牙棒", 35, bcapi.get_team(1), bcapi.Vector2(-5, -2), 0.1)
    end)
end