local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
--玩家队伍
local team = bcapi.get_team(0)
--敌人队伍
local eTeam = bcapi.get_team(1)

function init()
    battle.ForceNotEndBattle = true
    CreateRoles()
    local Boss = bcapi.get_role(1, "剧情_幻箫罗刹")
    Boss.Stat.BattleFlagDic:set_Item("是否变身", "1")       --1是不变身，这关不变身s
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("玉尺", 75, team, bcapi.Vector2(-4, 0), 0)
    end)
end

function start()
    bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 1, TalkEnd)
    StartTalk()
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        team = bcapi.get_team(0)
        bcapi.talk("玉尺", "你是谁！你把她怎么样了？")
        bcapi.talk("幻箫罗刹", "你猜？")
        bcapi.resume_and_show_ui()
    end)
end

function TalkEnd()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("幻箫罗刹", "好了，不陪你玩了，姐姐给你吹奏一曲。")
        bcapi.talk("玉尺", "呃啊……")
        bcapi.talk("玉尺", "【头痛欲裂，几欲昏厥】")
        bcapi.talk("玉尺", "【呼唤机关鹰】小羽，攻她……呃……唔……【晕倒】")
        bcapi.talk("幻箫罗刹", "你以为我会再给你发号施令的时间？")
        bcapi.resume_and_show_ui()
        battle.ForceNotEndBattle = false
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
