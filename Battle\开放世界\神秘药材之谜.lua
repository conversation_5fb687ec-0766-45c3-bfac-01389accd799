local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.reset_limit_time(60)
    -- battle.ForceNotEndBattle = true
end

function start()
    -- bcapi.add_timer_trigger(60, lose)
    bcapi.add_condition_trigger("[%t->teammate:世界_神秘药材之谜_青年男子%][<]1", 0, win)
end

function win()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        -- battle.ForceNotEndBattle = false
        bcapi.win()
    end)
end

-- function lose()
--     bcapi.async_call(function()
--         bcapi.pause_and_hide_ui()
--         bcapi.talk("市井百姓男", "时间不等人，在下就先溜之大吉了！")
--         bcapi.talk("主角", "……")
--         bcapi.resume_and_show_ui()
--         battle.ForceNotEndBattle = false
--         bcapi.lose()
--     end)
-- end
