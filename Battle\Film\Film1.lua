local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function start()
    battle.ForceNotEndBattle = true
    battle.HideCardLevel = true

    bcapi.disable_auto_battle()
    bcapi.disable_speed_up()
    bcapi.disable_dart()
    bcapi.hide_ui("画布")
    bcapi.hide_ui("技能区")
    bcapi.hide_ui("怒气条")
    bcapi.hide_ui("愈伤")
    bcapi.hide_ui("战斗时间")
    bcapi.hide_ui("暂停按钮")
    bcapi.hide_ui("加速按钮")
    bcapi.hide_ui("身法按钮")
    bcapi.hide_ui("角色技区")
    
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        -- 创建玩家角色
        bcapi.create_join_role_with_card("椿岁", 15, team, bcapi.Vector2(0, 0), 0.1)

        -- 创建敌人
        bcapi.create_join_role("东瀛狂刀", 15, eTeam, bcapi.Vector2(6, -2), 0.1)
    end)
end