local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)
local hasStartTalk = 0
local flag = 1
local reviveFlag = 0
local JinYanTao
function init()
    bcapi.ta_event_track("PlayerStartStory", "Step", "BattleTutor_Step6")
    battle.HideCardLevel = true
    
    bcapi.disable_auto_battle()
    bcapi.disable_speed_up()
    
    bcapi.hide_ui("自动按钮")
end

function start()
    bcapi.add_condition_trigger("[%enemy:新手_猛鼠帮_二当家->enemy:主角%][<]1", 1, Zhu<PERSON><PERSON><PERSON>ie)
    bcapi.add_timer_trigger(2, <PERSON>YanTaoTeach)
end
function JinYanTaoTeach()
    bcapi.async_call(function()
    JinYanTao = bcapi.get_player_role("金燕桃")
    JinYanTao.Buff:AddBuff("通用_教学关_回血", 300, 1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON>)
    bcapi.show_unique_skill_guide("金燕桃", "新手_猛鼠帮_小弟_拳", "拖拽绝招按钮至指定目标，释放绝招「金香愈灵」")
    end)
end

function ZhuJueDie()
    bcapi.async_call(function()
        bcapi.wait(1)
        bcapi.pause_and_hide_ui()
        flag = 2
        bcapi.talk("药曲老#药曲老", "阁主，无妨，先下去休息一阵。")
        bcapi.talk("云舞笙#云舞笙#严肃", "阁主，让我上场吧！")
        bcapi.show_hint_desc_panel("复活", "疗伤与退场", "当角色被击倒时，会消耗<color=red>疗伤</color>次数，退场进行休息。当角色在场下时，会快速恢复生命值，并在5秒后可以再次登场。", "知道了")
        bcapi.resume_and_show_ui()
        bcapi.show_join_role_guide("云舞笙", "将云舞笙拖至场上，令其加入战斗吧！")
        bcapi.wait(1)
        bcapi.pause()
        bcapi.show_ui_guide("疗伤", "单场战斗中，疗伤的次数有限，千万注意不要让侠客被击倒过多次了！", 300, -300)
        bcapi.wait(5)
        if (battle ~= nil) then
            RoleRevive()
        end
    end)
end

function RoleRevive()
    bcapi.async_call(function()
    if (flag == 2) then
            bcapi.pause_and_hide_ui()
            bcapi.talk("主角#主角", "(差不多了，是时候重返战斗了。)")
            bcapi.show_hint_desc_panel("换人", "换人", "当场上已有4名侠客时，可以通过换人使替补的侠客加入战斗。拖拽右侧未登场侠客至场上任意侠客的位置，即可将其替换下场。在场下的侠客，将持续恢复生命。", "知道了") 
            bcapi.resume_and_show_ui()
            bcapi.show_replace_role_guide("主角", "荆成", "将自己拖至场上，将会换下选中的侠客，建议选择受伤较重的侠客！")
            bcapi.wait(1)
            bcapi.add_condition_trigger("[%enemy:新手_猛鼠帮_二当家->enemy:主角%][>=]1", 0, RoleReviveTalk)
    end
    end)
end

function RoleReviveTalk()
    bcapi.async_call(function()
        local ZhuJue = bcapi.get_role(0, "主角")
        if (ZhuJue.Stat.RoleState == CS.H2.BattleRoleState.IsFighting and reviveFlag == 0) then
            reviveFlag = 1
            bcapi.wait(1)
            bcapi.pause_and_hide_ui()
            bcapi.talk("新手_猛鼠帮_二当家#新手_猛鼠帮_二当家", "可恶！你是什么人,刚刚伤势这么快就恢复好了！")
            bcapi.talk("主角#主角", "少废话，看招！")
            bcapi.resume_and_show_ui()
        end
    end)
end