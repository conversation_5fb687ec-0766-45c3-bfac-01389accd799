
---@type 漫游_雪谷_流亡者营地
local context = require("MapStories/漫游_雪谷/scene_漫游_雪谷_流亡者营地_h")
local s = context.sapi
local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local flags = context.flags
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

---组件相关
local extensions = require("MapStories/漫游_雪谷/extension")
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type StringWidget
local stringWidget = extensions.widgets.StringWidgetInfo.widget
---@type 雪谷_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 雪谷_ItemData]]
---@type 雪谷_StringItemData
local stringItemData = extensions.widgets.StringWidgetInfo.items

---@type flags_漫游_雪谷
s.loadFlags(context.flags)

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    --开场剧情
    if(flags.open_scene_flag == 0) then
        openScene()
        flags.open_scene_flag = 1
    end
    s.readyToStart()
    reloadAllSceneStates()
end

function testAddItem()
    itemWidget:addItem(itemData.zhuquexin,1)
end

function reloadAllSceneStates()
    --北门
    s.setActive(objs.northgate, flags.yd_main_step == 0)

    --南门
    s.setActive(objs.southgate, flags.yd_main_step == 0)

    --东门
    s.setActive(objs.eastgate, flags.yd_main_step == 0)
    
    --脚印
    s.setActive(objs.footstep,flags.yd_buyierfei_step == 1)
    s.setActive(objs.footstep_1,flags.yd_buyierfei_step == 2)
    s.setActive(objs.footstep_2,flags.yd_buyierfei_step == 3)

    --大牛
    s.setActive(npcs.daniu, flags.yd_buyierfei_step >= 5)
    
    --齿轮
    s.setActive(objs.gear,flags.yd_gear_collected == 0)
    
    --白虎骨
    s.setActive(objs.baihu,flags.dg_main_baihu == 0)
    
    --汪虎
    s.setActive(npcs.wanghu,flags.dg_main_hu == 0)
end

function wrappedReloadAllSceneStates()
    s.blackScreen()
    reloadAllSceneStates()
    s.lightScreen()
end

--第一次进入地图
function openScene()
    s.blackScreen()
    s.camera(cameras.start,true)
    s.setActive(npcs.lwz_01,true)
    s.setActive(npcs.lwz_02,true)
    s.setPos(npcs.moqi,pos.MQ_init)
    s.lightScreen()
    
    s.animState(npcs.lwz_01,roleAnims.BScratch)
    s.wait(2)
    s.turnToAsync(npcs.lwz_01,npcs.moqi)
    s.turnToAsync(npcs.lwz_02,npcs.moqi)
    s.talk(npcs.lwz_01,"墨七？你来得正好，过来评评理。")
    s.talk(npcs.lwz_01,"他一口咬定，任谁也拿这上锁的宝箱没办法，照我看，你墨七就打得开！")
    s.animState(npcs.lwz_02,roleAnims.BGesture)
    s.talk(npcs.lwz_02,"少来这套！刚才只有咱俩的时候，你可不是这么说的。")
    s.talk(npcs.moqi,"……所以，这箱子有什么特殊之处？")
    s.talk(npcs.lwz_01,"这可不是普通箱子！阿青姑娘亲口说了，谁要是有能耐打开，不仅可以取走她藏在里面的宝贝，还能跟随她学习全天下最最最厉害的机关术！")
    s.talk(npcs.lwz_02,"得了吧！你以为白送啊？大字不识几个还学机关术呢，这机关义肢你都用不明白！")
    s.talk(npcs.lwz_01,"我不明白，你明白！墨七，你虽然入伙不久，大伙对你的实力却有目共睹，这稳赚不赔的买卖倒是可以试它一试！")

    s.talk("???","有目共睹？我这个二当家的怎么没瞧见？")
    
    s.setActive(npcs.wutieshou,true)
    s.setActive(npcs.lujiu,true)

    s.turnToAsync(npcs.lwz_01,npcs.wutieshou)
    s.turnToAsync(npcs.lwz_02,npcs.wutieshou)
    s.turnToAsync(npcs.moqi,npcs.wutieshou)
    s.wait(0.5)
    s.animState(npcs.lwz_01,roleAnims.BShock_Loop,true)
    s.animState(npcs.lwz_02,roleAnims.BShock_Loop,true)
    
    s.animState(npcs.lujiu,roleAnims.BGesture)
    s.talk(npcs.lujiu,"既是无中生有的事，大哥就算再长十双眼睛，也是瞧不见的。")
    s.talk(npcs.wutieshou,"我要那么多眼睛干嘛？当我是苍蝇？")
    s.animState(npcs.lwz_01,roleAnims.BShock_Loop,false)
    s.animState(npcs.lwz_02,roleAnims.BShock_Loop,false)
    s.talk(npcs.wutieshou,"废话少说，你就是墨七？我听阿凉提起过你，原来是个乳臭未干的白毛小屁孩，倒是吹上天了。")
    s.talk(npcs.lujiu,"吹得越高，摔得越狠！")

    local ret = s.selectTalk(npcs.moqi, "……", {"你们是谁？", "哪来的跳梁小丑？"})
    if (ret == 0) then
        s.talk(npcs.lujiu,"这是我大哥吴铁手，堂堂二当家，江湖人称“雪山飞鹰”！至于我陆九，嘿嘿，绰号“独眼黑龙”，你小子记住没？")
        s.talk(npcs.moqi,"哦，原来是白斩鸡和它的跟屁虫。")
        s.animState(npcs.lujiu,roleAnims.BFight_Loop,true)
        s.talk(npcs.lujiu,"放肆！活腻歪了是吧？")
    else
        s.talk(npcs.wutieshou,"好狂的口气！看来阿凉还没教过你这里的规矩。")
        s.animState(npcs.lujiu,roleAnims.BFight_Loop,true)
        s.talk(npcs.lujiu,"大哥息怒，这小子就是个愣头青，还需咱们多敲打敲打。")
    end
    s.animState(npcs.lujiu,roleAnims.BFight_Loop,false)
    s.talk(npcs.wutieshou,"墨七，这宝箱已是我吴铁手囊中之物，你若是敢打什么歪主意，当心性命不保！")
    s.talk(npcs.moqi,"那也需有本事打开才行。")
    s.talk(npcs.wutieshou,"口无遮拦，狠话倒是张口就来，可惜这雪谷危机四伏，可不是个任你随意玩耍的地方！")
    s.talk(npcs.wutieshou,"你听好了，等我吴铁手抢先开启这宝箱之日，便是你墨七认输求饶之时，哈哈哈哈，我们走！")
    s.talk(npcs.moqi,"……")

    s.setActive(npcs.wutieshou,false)
    s.setActive(npcs.lujiu,false)
    s.setActive(npcs.lwz_01,false)
    s.setActive(npcs.lwz_02,false)

    s.talkThink(npcs.moqi,"（这宝箱是阿青留下的，连二当家也在打它的主意，莫非其中真的藏有什么奇珍异宝？）")
    s.talkThink(npcs.moqi,"（既然好不容易潜入营地，不如趁机调查一番。）")

    s.addTask(400000, 1)
    s.camera(cameras.start,false)

    s.camera(cameras.start_pgj,true,true,blendHintEnum.EaseInOut,1)
    s.turnTo(npcs.pangguanjia,npcs.moqi)
    s.talkAmazed(npcs.pangguanjia,"喂！新来的！")
    
    s.talkThink(npcs.moqi,"（那边似乎有人在叫我，去看看）")
    
    s.addTask(400010, 1)
    stringWidget:showItem(stringItemData.yd_rep)
    stringWidget:showItem(stringItemData.xl_rep)
    stringWidget:showItem(stringItemData.gd_rep)
    
    s.camera(cameras.start_pgj,false)
end


--region 胖管家

function talkToPangguanjia()
    if(flags.yd_main_step == 0) then
        talkToPangguanjia001()
        flags.yd_main_step = 1
        --刷新所有场景状态
        wrappedReloadAllSceneStates()
    elseif (flags.yd_main_step == 6) then
        talkToPangguanjia003()
    else
        s.talk(npcs.pangguanjia,"新来的，清单上的东西收集得如何了？")
        talkToPangguanjia002()
    end
end

function talkToPangguanjia001()
    s.animState(npcs.pangguanjia,roleAnims.BAkimbo_Loop,true)
    s.talk(npcs.pangguanjia,"新来的！见到小爷也不主动打声招呼，阿凉没跟你讲过这里的规矩吗？")
    local ret = s.selectTalk(npcs.moqi, "……", {"我不认识你", "久仰大名"})
    if (ret == 0) then
        s.talk(npcs.pangguanjia,"连我都不认识，亏得阿凉还那么赏识你。")
        s.talk(npcs.pangguanjia,"小子你记住了，我是这里的管家，跟我搞好关系对你没坏处！")
    else
        s.talk(npcs.pangguanjia,"连我是谁都不知道，少整这些虚情假意的，懂吗？")
        s.talk(npcs.pangguanjia,"别看我拳脚功夫虽不怎么样，这营地里的一砖一瓦，粮油米面，我可比任何人都要清楚！")
    end
    s.animState(npcs.pangguanjia,roleAnims.BAkimbo_Loop,false)
    s.talk(npcs.pangguanjia,"话说回来，自从你加入营地，我的糟心事又多了一件。")
    s.talk(npcs.moqi,"此话何意？")
    s.talk(npcs.pangguanjia,"多了一个人，可不只是多了一张嘴那么简单！你的吃喝拉撒，衣食住行，我都要操心！")
    s.animState(npcs.pangguanjia,roleAnims.BGive_Loop,true)
    s.talk(npcs.pangguanjia,"清单上的东西若是凑不齐，咱们都得喝西北风，等再下几场大雪，这仇也不用报啦！")
    s.talk(npcs.pangguanjia,"小子，大伙都夸你聪明能干，你若能替我找齐这清单上的物资，我自有好处给你。")
    s.animState(npcs.moqi,roleAnims.BGive_Loop,true)
    s.animState(npcs.pangguanjia,roleAnims.BGive_Loop,false)
    
    s.talk(npcs.moqi,"肉、大米、动物毛皮、烧酒……")
    s.talk(npcs.moqi,"这些都是稀松平常之物，为何营地却不曾预备？")
    s.animState(npcs.moqi,roleAnims.BGive_Loop,false)

    s.talk(npcs.pangguanjia,"唉，说来话长！今年自入冬以后，鹅毛大雪是一场接着一场，谷中鲜有野兽出没，自然猎不到用来御寒的毛皮。")
    s.talk(npcs.pangguanjia,"最可气的是，居然连囤积许久的米袋也不翼而飞了许多！")
    s.talk(npcs.moqi,"莫非，营地出了窃贼？")
    s.talk(npcs.pangguanjia,"谁知道呢！唉，你是不当家不知过日子的艰难，阿凉昨天还问我这事呢，现在知道我这臭脾气是怎么来的了吧？")
    s.talk(npcs.moqi,"……")
    s.talk(npcs.pangguanjia,"我说完了，还有什么不清楚的吗？")
    
    s.finishTask(400010,1,2)

    talkToPangguanjia002()
end

function talkToPangguanjia002()--todo:中间剧情文案待调
    local ret = 0
    local opt = {}
    local showConds = {}
    ::A::
    showConds = {}
    showConds[1] = true
    showConds[2] = true
    showConds[3] = true
    showConds[4] = true
    showConds[5] = true
    
    if flags.yd_main_step == 5 then
        
        s.talk(npcs.pangguanjia,"唔，清单上的东西找齐了……")
        s.talk(npcs.pangguanjia,"阿凉说你是个人才，起初我还不信呢，现在算是开了眼了。")
        s.talk(npcs.pangguanjia,"我说话算话，这玩意是我偶然在营地发现的，想着兴许是个宝贝本打算自己留着，既然你小子帮了大忙，就便宜你啦！")
        s.talk(npcs.moqi,"这是机关零件，似乎可以组成某种东西。")
        
        --todo:激活吴铁手零件夺取剧情的触发
        itemWidget:addItem(itemData.jiguanlingjian1,1)
        flags.yd_main_step = 6

        s.finishTask(400010,2)
    else
        opt = {}
        if flags.yd_lurou_step < 3 then
            opt[1] = "关于肉"
        end
        if flags.yd_buyierfei_step < 6 then
            opt[2] = "关于米袋"
        end
        if flags.yd_lengchexinfei_step < 3 then
            opt[3] = "关于毛皮"
        end
        if flags.yd_meijiu_step < 5 then
            opt[4] = "关于烧酒"
        end
        opt[5] = "没事了"
        
        ret = s.selectTalkExWithCondition(npcs.moqi, "……", opt,showConds)
        if (ret == 1) then
            s.talk(npcs.pangguanjia,"鹿是咱们的主要食物来源之一，但雪谷里的鹿格外机警敏捷，寻常人很难将其捕获，你若感到棘手，可向营地里的猎户打听。")

            opt = {}
            opt[1] = "已经找到肉了"
            opt[2] = "知道了"
            showConds = {}
            showConds[1] = itemWidget:getItemCount(itemData.rou) ~= 0
            showConds[2] = true
            ret = s.selectTalkExWithCondition(npcs.moqi, "……", opt,showConds)

            if ret == 1 then
                local submitItem = s.submitTempItem("请提交肉！")
                if (submitItem ~= nil) then
                    if (submitItem[0].Key == itemData.rou.key) then
                        itemWidget:removeItem(itemData.rou,1)
                        s.talk(npcs.pangguanjia,"哦？肉被你找到了！很好！")
                        s.talk(npcs.pangguanjia,"这个xxx就送给你了！")--todo：待投放

                        s.aside("营地声望提高了！")

                        stringItemData.yd_rep.value = stringItemData.yd_rep.value + 5
                        flags.yd_main_step = flags.yd_main_step + 1
                        flags.yd_lurou_step = 3

                    else
                        s.aside("你提交错了！这不是肉。")
                    end
                else
                    s.aside("你没有提交任何东西！")
                end
            end
            goto A
        elseif (ret == 2) then
            s.talk(npcs.pangguanjia,"说起失窃的米袋，真叫人气恼，我到现在也没个头绪，不过我相信窃贼必定就出在营地里，你可就近找一找蛛丝马迹。")

            opt = {}
            opt[1] = "我已经找到米袋了"
            opt[2] = "知道了"
            showConds = {}
            showConds[1] = itemWidget:getItemCount(itemData.midai) ~= 0
            showConds[2] = true
            ret = s.selectTalkExWithCondition(npcs.moqi, "……", opt,showConds)

            if ret == 1 then
                local submitItem = s.submitTempItem("请提交米袋！")
                if (submitItem ~= nil) then
                    if (submitItem[0].Key == itemData.midai.key) then
                        itemWidget:removeItem(itemData.midai,1)

                        s.talk(npcs.pangguanjia,"哦？米袋被你找到了！很好！")
                        s.talk(npcs.pangguanjia,"这个xxx就送给你了！")--todo：待投放
                        
                        s.aside("营地声望提高了！")

                        stringItemData.yd_rep.value = stringItemData.yd_rep.value + 5
                        flags.yd_main_step = flags.yd_main_step + 1
                        flags.yd_buyierfei_step = 6
                    else
                        s.aside("你提交错了！这不是米袋。")
                    end
                else
                    s.aside("你没有提交任何东西！")
                end
            end

            goto A
        elseif (ret == 3) then
            s.talk(npcs.pangguanjia,"陆九负责为大家供应毛皮，不过这家伙最近神出鬼没的，你先去找守卫打听下吧。")

            opt = {}
            opt[1] = "我已经找到毛皮了"
            opt[2] = "知道了"
            showConds = {}
            showConds[1] = itemWidget:getItemCount(itemData.maopi) ~= 0
            showConds[2] = true
            ret = s.selectTalkExWithCondition(npcs.moqi, "……", opt,showConds)

            if ret == 1 then
                local submitItem = s.submitTempItem("请提交毛皮！")
                if (submitItem ~= nil) then
                    if (submitItem[0].Key == itemData.maopi.key) then
                        itemWidget:removeItem(itemData.maopi,1)
                        s.talk(npcs.pangguanjia,"哦？毛皮被你找到了！很好！")
                        s.talk(npcs.pangguanjia,"这个xxx就送给你了！")--todo：待投放

                        s.aside("营地声望提高了！")

                        stringItemData.yd_rep.value = stringItemData.yd_rep.value + 5
                        flags.yd_main_step = flags.yd_main_step + 1
                        flags.yd_lengchexinfei_step = 3
                    else
                        s.aside("你提交错了！这不是毛皮。")
                    end
                else
                    s.aside("你没有提交任何东西！")
                end
            end

            goto A
        elseif (ret == 4) then
            s.talk(npcs.pangguanjia,"这种鬼天气，不喝点烧酒真是冷得睡不着觉！营地里有位老人擅长酿造烧酒，不过他最近好像有些烦恼，你去找他聊一聊吧。")

            opt = {}
            opt[1] = "我已经找到烧酒了"
            opt[2] = "知道了"
            showConds = {}
            showConds[1] = itemWidget:getItemCount(itemData.shaojiu) ~= 0
            showConds[2] = true
            ret = s.selectTalkExWithCondition(npcs.moqi, "……", opt,showConds)

            if ret == 1 then
                local submitItem = s.submitTempItem("请提交烧酒！")
                if (submitItem ~= nil) then
                    if (submitItem[0].Key == itemData.shaojiu.key) then
                        itemWidget:removeItem(itemData.shaojiu,1)
                        s.talk(npcs.pangguanjia,"哦？烧酒被你找到了！很好！")
                        s.talk(npcs.pangguanjia,"这个xxx就送给你了！")--todo：待投放

                        s.aside("营地声望提高了！")

                        stringItemData.yd_rep.value = stringItemData.yd_rep.value + 5
                        flags.yd_main_step = flags.yd_main_step + 1
                        flags.yd_meijiu_step = 5
                    else
                        s.aside("你提交错了！这不是烧酒。")
                    end
                else
                    s.aside("你没有提交任何东西！")
                end
            end

            goto A
        elseif (ret == 5) then
            s.talk(npcs.pangguanjia,"抓紧些！")
        end
    end
    
end

function talkToPangguanjia003()--todo:待增加玩法向投放向的东西
    s.talk(npcs.pangguanjia,"阿凉老大真是慧眼识才，你小子是个好帮手，做事够麻利！")
end
--endregion


--region 黑娃与大牛
function talkToHeiwa()
    if (flags.yd_main_step == 0) then
        talkToHeiwa000()
    elseif (flags.yd_main_step ~= 0)and(flags.yd_buyierfei_step == 0) then
        talkToHeiwa001()
    elseif (flags.yd_buyierfei_step <= 4) then
        talkToHeiwa002()
    else
        talkToHeiwa003()
    end
    --刷新所有场景状态
    --reloadAllSceneStates()
end

function talkToHeiwa000()
    s.talk(npcs.heiwa,"小哥哥，你是从雪谷外面来的吗？我将来也想去外面看看，最好是一个既不寒冷也不会挨饿的地方。")
    
end

function talkToHeiwa001()
    s.talk(npcs.heiwa,"……")
    local ret = s.selectTalk(npcs.moqi, "……", {"你怎么了", "默默离开"})

    if ret == 0 then
        s.talk(npcs.heiwa,"都怪我，是我连累了爹。")
        s.talk(npcs.heiwa,"爹本就腿脚不好，眼下犯了大错，若是被逐出营地，可该怎么办……")
        s.talk(npcs.moqi,"你爹他怎么了？")
        s.talk(npcs.heiwa,"你是……新来的小哥？")
        s.talk(npcs.heiwa,"就在昨夜，阿凉老大让爹值夜看守这些米袋，爹因为受了风寒卧病在床，便让我帮忙看着。")
        s.talk(npcs.heiwa,"谁知我竟不争气地睡着了，醒来后好几袋米已不翼而飞……")
        s.talk(npcs.heiwa,"阿凉老大说过，这可是救命的粮食，若是有谁看守不利，将被他逐出营地，是、是我害了爹……")
        s.talk(npcs.moqi,"先别哭，关于丢失的米袋，你有什么线索吗？")

        s.talk(npcs.heiwa,"我、我睡得沉，什么也没听见。")
        s.talk(npcs.heiwa,"哦对了，天亮后我似乎见一些脚印往营地北边去了，还纳闷会是谁那么早出门呢？")
        
        s.addTask(400011, 1)
        flags.yd_buyierfei_step = 1
        wrappedReloadAllSceneStates()
        
    end
end

function talkToHeiwa002()
    s.talk(npcs.heiwa,"爹去哪里了呢……")
end

function talkToHeiwa003()
    s.talk(npcs.heiwa,"小哥哥，我爹他、他不是有意的，念在他只是初犯，你们不要把他赶走，好不好？")
end

function talkToDaniu()
    s.talk(npcs.daniu,"既然人赃俱获，我无话可说，等阿凉老大发落吧。")
end

function eventFootstep1()
    s.aside("一串脚印，由于营地里人们来来往往，所以看得不甚分明。")
    s.talkThink(npcs.moqi,"（这脚印看起来是今天留下的，而且步履似乎有些匆忙，跟上去看看吧。）")
    flags.yd_buyierfei_step = 2

    wrappedReloadAllSceneStates()
end

function eventFootstep2()
    s.aside("脚印的痕迹看起来清晰了不少，似乎通往营地外面。")
    s.talkThink(npcs.moqi,"（唔……脚印有些深，这人似乎携带着什么重物。）")
    flags.yd_buyierfei_step = 3

    wrappedReloadAllSceneStates()
end

function eventFootstep3()
    s.aside("看起来是一串脚印，通往古渡方向去了。")
    s.talk(npcs.moqi,"（看来此人往古渡方向去了）")

    s.finishTask(400011, 1,2)
    flags.yd_buyierfei_step = 4

    wrappedReloadAllSceneStates()
end

--endregion


--region 守卫
function talkToShouwei()
    if (flags.yd_main_step == 0) then
        talkToShouwei000()
    elseif (flags.yd_main_step ~= 0)and(flags.yd_lengchexinfei_step == 0) then
        talkToShouwei001()
    elseif (flags.yd_lengchexinfei_step == 1) then
        talkToShouwei002()
    else
        talkToShouwei003()
    end
    --刷新所有场景状态
    --reloadAllSceneStates()
end

function talkToShouwei000()
    s.talk(npcs.shouwei,"这天气真是冷得不像话，若身上没点动物毛皮御寒，晚上可就遭罪咯！")
end

function talkToShouwei001()
    s.talk(npcs.shouwei,"这鬼天气，陆九之前说好预备给咱们的毛皮大衣，如今却连个影子都没有！")
    s.talk(npcs.shouwei,"若再下一场大雪，恐怕仇还没报，人就先已冻成冰块咯！")
    s.talk(npcs.moqi,"毛皮大衣？")
    s.talk(npcs.shouwei,"没错，雪谷的冬天可不是一般寒冷，普通毛皮根本抵挡不住寒气，非得是北边雪林的熊皮才行。")
    s.talk(npcs.moqi,"哦？")
    s.talk(npcs.shouwei,"北边那林子位于雪山背后，阴冷无比，其中生长的动物极为耐寒。")
    s.talk(npcs.shouwei,"不过，那林子可不是谁都能去的，有好几个兄弟为了猎熊，已经交待在里面了……")

    flags.yd_lengchexinfei_step = 1
    s.addTask(400012, 1)
end

function talkToShouwei002()
    s.talk(npcs.shouwei,"那雪林可不是个太平地界，记住快去快回，量力而行。")
end

function talkToShouwei003()
    s.talk(npcs.shouwei,"小兄弟，你可比陆九那家伙靠谱多了！")
end

--endregion

function talkToLwznv()
    s.talk(npcs.lwz_nv,"我是流亡者。")
    s.addTask(400020, 1)
end

function talkToShiluo()
    s.talk(npcs.shiluo,"我在寻找豆沙。")
    s.addTask(400030, 1)
end

--region 酿酒人

function talkToNiangjiu()
    if (flags.yd_main_step == 0) then
        talkToNiangjiu000()
    elseif (flags.yd_main_step ~= 0)and(flags.yd_meijiu_step == 0) then
        talkToNiangjiu001()
    elseif (flags.yd_meijiu_step < 3) then
        talkToNiangjiu002()
    elseif (flags.yd_meijiu_step == 3) then
        talkToNiangjiu003()
    else
        talkToNiangjiu004()
    end
    --刷新所有场景状态
    --reloadAllSceneStates()
   
end

function talkToNiangjiu000()--todo:待补充
    s.talk(npcs.niangjiu,"我是营地酿酒人，现在没有任务呢。")
   
end

function talkToNiangjiu001()
    s.talk(npcs.niangjiu,"寒冬已至，大伙整日辛苦忙碌，入夜后聚在篝火前畅饮美酒，这原是件美事，可惜……")
    s.talk(npcs.moqi,"老伯，怎么了？")
    s.talk(npcs.niangjiu,"我祖上擅长酿酒，但凡喝过的无人不拍手称赞，其中有一味重要酒材，名为“回香草。”")
    s.talk(npcs.niangjiu,"这草只生长在风霜刺骨的雪山悬崖上，添加之后不仅酒香袭人，耐寒暖身，而且这草十分神奇，".."<color=red>每八个时辰成熟一次</color>".."，世间罕有。")
    s.talk(npcs.niangjiu,"无奈营地如今腾不开人手，看来，这美酒恐怕是酿不成了……")
    
    s.addTask(400013, 1)
    flags.yd_meijiu_step = 1
end

function talkToNiangjiu002()--todo:待补充
    -- s.talk(npcs.niangjiu,"少年，你找我有事吗？")
    -- local ret = s.selectTalk(npcs.moqi, "……", {"我找到了回香草", "离去"})

    -- if ret == 0 then
    --     s.talk(npcs.niangjiu,"什么？你一个人去了雪崖？！")
    --     local submitItem = s.submitTempItem("请提交回香草！")
    --     if (submitItem[0].Key ~= nil) then
    --         if (submitItem == itemData.huixiangcao.key) then
    --             s.talk(npcs.moqi, "选中的道具是" .. submitItem)
    --             s.aside("你提交正确啦！现在就把回香草从你的背包移除咯。")
    --             itemWidget:removeItem(itemData.huixiangcao,1)

    --             s.talk(npcs.niangjiu,"我看看……没错，就是这种小草，你可帮了大忙了！")
    --             s.talk(npcs.niangjiu,"我会尽快酿造美酒，到时候你可要记得品尝啊。")
                
    --             --todo：添加计时器
    --             s.finishTask(400013, 2,3)
    --             flags.yd_meijiu_step = 3
    --         else
    --             s.talk(npcs.moqi, "选中的道具是" .. submitItem)
    --             s.aside("你提交错了！这不是回香草。")
    --         end
    --     else
    --         s.aside("你没有提交任何东西！")
    --     end
    -- end
    
end

function talkToNiangjiu003()--todo:待补充
    --todo:判断计时器是否
    s.talk(npcs.niangjiu,"还在酿酒中……")

    
    s.talk(npcs.niangjiu,"酒酿好了！")
    itemWidget:addItem(itemData.shaojiu,1)
    flags.yd_meijiu_step = 4

    s.finishTask(400013, 3)
end

function talkToNiangjiu004()--todo:待补充
    s.talk(npcs.niangjiu,"我自己酿的酒是不是很不错哈哈哈哈。")
end

--endregion

function talkToBoxue()
    s.talk(npcs.boxue,"我是博学老人。")
end

function talkToDousha()
    s.talk(npcs.dousha,"汪汪汪")
end

function talkToLidachui()
    s.talk(npcs.lidachui,"我是营地铁匠。")
    s.addTask(400040, 1)
end

--region 老猎户

function talkToLaoliehu()
    if (flags.yd_main_step == 0) then
        talkToLaoliehu000()
    elseif (flags.yd_main_step ~= 0)and(flags.yd_lurou_step == 0) then
        talkToLaoliehu001()
    elseif (flags.yd_lurou_step == 1) then
        talkToLaoliehu002()
    else
        talkToLaoliehu003()
    end
    --刷新所有场景状态
    --reloadAllSceneStates()
   
end

function talkToLaoliehu000()--todo:待补充
    s.talk(npcs.laoliehu,"我是老猎户，我暂时没什么任务，你为什么还不去找胖管家。")
end

function talkToLaoliehu001()--todo:待补充
    s.talk(npcs.laoliehu,"今早我的捕兽夹抓到一只鹿，但回来营地的路上给它逃走了。")
    s.talk(npcs.laoliehu,"挣扎中还让我伤了腿，人老了不行了……")
    s.talk(npcs.laoliehu,"你若是能把那只鹿抓回来blablabla")

    flags.yd_lurou_step = 1
    s.addTask(400014, 1)
end

function talkToLaoliehu002()--todo:待补充
    s.talk(npcs.laoliehu,"怎么样，你找到那只鹿了吗？")
end

function talkToLaoliehu003()--todo:待补充
    s.talk(npcs.laoliehu,"人老了……")
end

--endregion

function talkToWanghu()
    if flags.dg_main_step >= 4 and flags.dg_main_hu == 0 then
        s.aside("一名江湖人冷漠地望着你。")
        s.talk(npcs.moqi,"你是谁？")
        s.talk("???","滚开，除非你活腻了。")
        s.talk(npcs.moqi,"回答我。")
        s.talk("???","我很久没杀人了，你在挑战我的耐性。")
        s.talk(npcs.moqi,"我偏要问个清楚。")
        s.talk("???","那么，只好送你去见阎王了。")

        local isWin = s.battle("楚家庄_漫游_黑衣人") --todo:待配置
        if isWin then
            s.talk("???","你赢了，刚才……都是我有眼无珠。")
            s.talk(npcs.moqi,"所以，你到底是谁？")
            s.talk(npcs.wanghu,"我叫汪虎，江湖人称“屠刀”，现在被你打败，这个绰号也变得一文不值了。")
            s.talk(npcs.moqi,"你跟老黎有什么过节？")
            s.talk(npcs.wanghu,"原来你是老黎派来的杀手，真没想到老黎还能请到你这样的高人。")
            s.talk(npcs.wanghu,"我……曾经背叛了老黎，害他生不如死，甚至家破人亡。")
            s.talk(npcs.wanghu,"你见到他时，他是否时而正常时而疯癫？这里面就有我的一份“功劳”，当然，另外两人也脱不了干系。")
            s.talk(npcs.moqi,"你为何要害他？")
            s.talk(npcs.wanghu,"一言难尽……你得自己去找他问个清楚。")
            s.talk(npcs.wanghu,"不管你是否相信，此事我并非主犯，你能否放我一马？我保证从此消失。")

            local ret = s.selectTalk(npcs.moqi,"……",{"放","不放"})
            if ret == 0 then
                s.talk(npcs.wanghu,"多谢……我本以为自己死定了，我将隐姓埋名，自此退出江湖。")
                s.talk(npcs.wanghu,"这把刀，你拿去给老黎吧。")
            else
                s.talk(npcs.wanghu,"我知道你们做杀手的都不会心软，不过我也活不了多久，早晚也是一个死。")
                s.talk(npcs.moqi,"你得了什么病？")
                s.talk(npcs.wanghu,"实不相瞒，我修炼了邪功，很快将要化为伥怪，你动手给我一个解脱吧。")
                s.talk(npcs.moqi,"你走，把你的刀留下。")
            end
            itemWidget:addItem(itemData.tudao,1)

            flags.dg_main_hu = 1
            flags.dg_main_step = flags.dg_main_step + 1
            wrappedReloadAllSceneStates()
        else
            s.blackScreen()
            s.talk(npcs.moqi,"（看来现在还不是时候……）")
            s.lightScreen()
        end
    elseif flags.dg_main_hu == 1 then
        s.talk(npcs.wanghu,"对不起，我现在不想说话。")
    else
        s.talk("???","对不起，我现在不想说话。")
    end
    
end

--interactEvent

function eventNorthgate()
    s.talkThink(npcs.moqi,"（胖管家还在找我，这个时候还是不要离开营地的好）")
    s.blackScreen()
    s.setPos(npcs.moqi,pos.NG)
    s.lightScreen()
end

function eventSouthgate()
    s.talkThink(npcs.moqi,"（胖管家还在找我，这个时候还是不要离开营地的好）")
    s.blackScreen()
    s.setPos(npcs.moqi,pos.SG)
    s.lightScreen()
end

function eventEastgate()
    s.talkThink(npcs.moqi,"（胖管家还在找我，这个时候还是不要离开营地的好）")
    s.blackScreen()
    s.setPos(npcs.moqi,pos.EG)
    s.lightScreen()
end

function eventWoodpile()
    s.aside("这里是一个柴堆")
end

function eventWell()
    s.aside("这是一口井")
end

function eventChest()
    --计算已获取的玉键数量
    local num = 0
    if itemWidget:getItemCount(itemData.jiguanlingjian1) ~= 0 then
        num = num + 1
    end
    if itemWidget:getItemCount(itemData.jiguanlingjian2) ~= 0 then
        num = num + 1
    end
    if itemWidget:getItemCount(itemData.jiguanlingjian3) ~= 0 then
        num = num + 1
    end
    if itemWidget:getItemCount(itemData.jiguanlingjian4) ~= 0 then
        num = num + 1
    end
    if itemWidget:getItemCount(itemData.jiguanlingjian5) ~= 0 then
        num = num + 1
    end
    
    if num == 0 then
        s.aside("这里是一个神秘宝箱！")
    elseif num == 1 then
        s.talkThink(npcs.moqi,"（这个宝箱的纹饰似乎跟我手中的玉键很是相似……）")
        s.talkThink(npcs.moqi,"（不知它们之间是否有什么关联？）")
    elseif num <= 3 then
        s.talkThink(npcs.moqi,"（这玉键之间似乎有某种关联，不知道与这宝箱有什么关系？）")
    elseif num == 4 then
        s.talkThink(npcs.moqi,"（这玉键显然可以拼合在一起，好像是钥匙的形状！这里的拼图好像只缺一块了！）")
    elseif num == 5 then
        s.talkThink(npcs.moqi,"（这玉键显然可以拼合在一起！）")
        s.aside("五把玉键拼合成了一把钥匙！")

        s.talk(npcs.moqi,"原来是一把钥匙……")
        s.talk(npcs.moqi,"让我来开启宝箱，看看里面究竟藏着什么。")
        --todo:演出级别
        s.aside("墨七正要开启宝箱，忽然一声暗器破空的尖啸声响起，墨七敏捷地闪向一旁，一名黑衣杀手已持剑立于宝箱前。")

        s.talk("黑衣人","好身手！难怪你能在这雪谷里行走自如，确有几分本事。")
        s.talk(npcs.moqi,"你是谁？")
        s.talk("黑衣人","取你性命之人。")
        s.talk(npcs.moqi,"你与我有仇？")
        s.talk("黑衣人","我们并不相识。")
        s.talk("黑衣人","怪只怪你这番雪谷之行，撞见了太多本不该知道的事，所以——")
        s.talk("黑衣人","决不可留你！")
        
        local isWin = s.battle("楚家庄_漫游_黑衣人") --todo:战斗
        if isWin then
            s.talk("黑衣人","咳咳……")
            s.talk("黑衣人","你、你这小子，竟比我预想中还要强悍……")
            s.talk(npcs.moqi,"怎么？刚才那股嚣张劲头哪儿去了？")
            s.talk("黑衣人","哼，你得意的怕是有些太早了。")
            s.talk("黑衣人","你已惹上一个天大的麻烦，下次相见，我家主子自会亲手取你性命！")
            s.talk(npcs.moqi,"你家主子究竟是谁？")
            s.aside("黑衣人冷冷一笑，掷出一枚烟雾弹，消失在烟尘中。")
            s.talk(npcs.moqi,"慢着——")
            s.aside("烟尘渐渐散去。")
            s.talk(npcs.moqi,"似乎有人在雪谷中密谋着什么……")
            s.talk(npcs.moqi,"想必不会再有阻碍了，现在就去开启宝箱吧。")
            
            --todo；结束了！
            itemWidget:removeItem(itemData.jiguanlingjian1,1)
            itemWidget:removeItem(itemData.jiguanlingjian2,1)
            itemWidget:removeItem(itemData.jiguanlingjian3,1)
            itemWidget:removeItem(itemData.jiguanlingjian4,1)
            itemWidget:removeItem(itemData.jiguanlingjian5,1)
            
            s.finishTask(400000,1)
        else
            s.aside("战斗失败了！重新来过吧！")
        end
    end
end

function eventGear()
    s.aside("这里有一个机关零件。")
    
    local opt = {}
    local showConds = {}
    local ret = 0
    
    opt = {}
    opt[1] = "[营地声望>=35]取走"
    opt[2] = "离开"
    showConds = {}
    showConds[1] = stringItemData.yd_rep.value >= 35
    showConds[2] = true
    ret = s.selectTalkExWithCondition(npcs.moqi, "……", opt,showConds)

    if ret == 1 then
        itemWidget:addItem(itemData.digongkey2,1)
        flags.yd_gear_collected = 1
        wrappedReloadAllSceneStates()
    end
end


function eventBaihu()
    if flags.dg_main_baihu == 0 then
        s.aside("这是白虎骨。")

        flags.dg_main_baihu = 1

        itemWidget:addItem(itemData.baihugu,1)
        wrappedReloadAllSceneStates()
    end

end

--transport

function transportShanjiao()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_风雪神道_山脚.unity", "位置/Yingdi")
end

function transportForest()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_荒原雪林.unity", "位置/Yingdi")
end

function transportGudu()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_古渡人家.unity", "位置/Yingdi")
end

function transportDigong()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_远古地宫_入口.unity", "位置/Yingdi")
end