local bcapi = require("BattleClientA<PERSON>")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    
end

function start()
    bcapi.add_timer_trigger(0.1, firstTalk)
end

function firstTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("青年猎人", "你们不准我好好活，还不准我死吗？！")
        bcapi.talk("灵湫", "我师父说，眼前关难过，过后皆非难。")
        bcapi.talk("青年猎人", "你在叽里呱啦什么！")
        bcapi.talk("灵湫", "意思是，很多一时间让你生不如死的坎坷，一旦成了回忆，就会变得轻如鸿毛。")
        bcapi.talk("青年猎人", "我这条命才是和毛一样轻！红的毛，灰的毛，都不是值钱的白毛！")
        bcapi.talk("灵湫", "唉，看来人在气头上是什么也听不进去，大侠，麻烦帮忙先让他冷静下来。")
        bcapi.resume_and_show_ui() 
    end)
end