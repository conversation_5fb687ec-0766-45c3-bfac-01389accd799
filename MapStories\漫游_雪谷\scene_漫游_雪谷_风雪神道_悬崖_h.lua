--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/漫游_雪谷/flags_漫游_雪谷")


---@class 角色_漫游_雪谷_风雪神道_悬崖
local npcs = {
    --- "明慧"
    minghui = "角色/minghui",
    --- "墨七"
    moqi = "角色/moqi",
}

---@class 物体_漫游_雪谷_风雪神道_悬崖
local objs = {
    --- "苍龙精"
    canglong = "物体/canglong",
}

---@class 相机_漫游_雪谷_风雪神道_悬崖
local cameras = {
}

---@class 位置_漫游_雪谷_风雪神道_悬崖
local pos = {
    --- "通往山路"
    Shanlu = "位置/Shanlu",
    --- "通往山脚"
    Shanjiao = "位置/Shanjiao",
}

---@class 资产_漫游_雪谷_风雪神道_悬崖
local assets = {
}

---@class 动作_漫游_雪谷_风雪神道_悬崖
local animationClips = {
}

---@class 漫游_雪谷_风雪神道_悬崖
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
