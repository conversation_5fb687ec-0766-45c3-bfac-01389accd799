--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/主线_雾锁寒山1_4/flags_主线_雾锁寒山1_4")


---@class 角色_主线_雾锁寒山1_4
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "玉尺"
    yuchi = "角色/yuchi",
    --- "尹晗"
    yinhan = "角色/yinhan",
    --- "涂舟"
    tuzhou = "角色/tuzhou",
    --- "尹雪音"
    yinxueyin = "角色/yinxueyin",
    --- "男弟子_三人对话"
    dizi = "角色/dizi",
    --- "玉尺行走"
    ycmove = "角色/ycmove",
    --- "尹晗行走"
    yhmove = "角色/yhmove",
    --- "涂舟行走"
    tzmove = "角色/tzmove",
    --- "尹雪音行走"
    yxymove = "角色/yxymove",
    --- "弟子行走"
    dzmove = "角色/dzmove",
    --- "夫子"
    fuzhi = "角色/fuzhi",
    --- "弟子女乙_三人对话"
    diziny = "角色/diziny",
    --- "弟子女丙_三人对话"
    dizinb = "角色/dizinb",
    --- "男弟子_右"
    r_man = "角色/r_man",
    --- "村民"
    l_cunmin = "角色/l_cunmin",
    --- "林契"
    linqi = "角色/linqi",
}

---@class 物体_主线_雾锁寒山1_4
local objs = {
    --- "弟子对话触发"
    trigger_dizi = "物体/trigger_dizi",
    --- "涂舟对话触发"
    trigger_tuzhou = "物体/trigger_tuzhou",
    --- "赛场"
    trigger_saichang = "物体/trigger_saichang",
    --- "高亮地点"
    trigger_com = "物体/trigger_com",
    --- "玉尺对话触发"
    trigger_yuchi = "物体/trigger_yuchi",
    --- "尹晗和尹雪音对话"
    trigger_yinhan = "物体/trigger_yinhan",
    guide_dizi = "物体/guide_dizi",
    guide_yuchi = "物体/guide_yuchi",
    guide_tuzhou = "物体/guide_tuzhou",
    guide_yinhan = "物体/guide_yinhan",
}

---@class 相机_主线_雾锁寒山1_4
local cameras = {
    --- "玉尺聊天镜头"
    ycTalk = "相机/ycTalk",
    --- "尹晗聊天镜头"
    yhTalk = "相机/yhTalk",
    --- "开场镜头1"
    texie01 = "相机/texie01",
    --- "开场镜头2"
    texie02 = "相机/texie02",
    --- "开场镜头3"
    texie03 = "相机/texie03",
    --- "赛场"
    cam_saichang = "相机/cam_saichang",
    --- "夫子"
    cam_fuzi = "相机/cam_fuzi",
    --- "玉尺镜头"
    cam_yuchi = "相机/cam_yuchi",
    --- "墨七镜头1"
    cam_moqi = "相机/cam_moqi",
    --- "墨七镜头2"
    cam_moqi1 = "相机/cam_moqi1",
    main = "相机/main",
}

---@class 位置_主线_雾锁寒山1_4
local pos = {
    --- "主角初始位置"
    pos_zj = "位置/pos_zj",
    --- "玉尺离开位置"
    pos_ycGo = "位置/pos_ycGo",
    --- "对话玉尺位置"
    pos_ycTalk = "位置/pos_ycTalk",
    --- "对话尹晗位置"
    pos_yhTalk = "位置/pos_yhTalk",
    --- "尹晗离开位置"
    pos_yhGo = "位置/pos_yhGo",
    --- "玉尺位置"
    pos_yc = "位置/pos_yc",
    --- "尹晗位置"
    pos_yh = "位置/pos_yh",
    --- "涂舟位置"
    pos_tz = "位置/pos_tz",
    --- "尹雪音位置"
    pos_yxy = "位置/pos_yxy",
    --- "弟子位置"
    pos_dz = "位置/pos_dz",
    --- "夫子位置"
    pos_fuzi  = "位置/pos_fuzi ",
    --- "返回位置"
    pos_back = "位置/pos_back",
}

---@class 资产_主线_雾锁寒山1_4
local assets = {
}

---@class 动作_主线_雾锁寒山1_4
local animationClips = {
}

---@class 主线_雾锁寒山1_4
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
