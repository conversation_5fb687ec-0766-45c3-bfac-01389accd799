local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle


function init()
    bcapi.reset_limit_time(60)
    CreateRoles()
end

function start()
    bcapi.add_timer_trigger(8, firstAttackTalk)
    bcapi.add_timer_trigger(19, secondAttackTalk)
    bcapi.add_timer_trigger(35, thirdAttackTalk)
    bcapi.add_timer_trigger(55, BattleWin)
    bcapi.add_condition_trigger("[%c->teammate:剧情_诸葛鹿%][<]1", 0, BattleLose)
    startTalk()
end


function CreateRoles()
    bcapi.async_call(function()
        team = bcapi.get_team(0)
        bcapi.create_join_role("剧情_诸葛鹿", 80, team, bcapi.Vector2(-2, 0), 0)
    end)
end

--开始对话
function startTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()    
        bcapi.talk("诸葛鹿", "我得防下神秘人三招")     
        bcapi.resume_and_show_ui()
    end)
end

--第一次攻击对话
function firstAttackTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("诸葛鹿","第一招来了！")
        local Boss = bcapi.get_role(1, "剧情_玄阙宫戒律长老_神秘人")
        Boss.Stat.BattleFlagDic:set_Item("第一招", "1")
        bcapi.resume_and_show_ui()
    end)
end

--第二次攻击对话
function secondAttackTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("诸葛鹿","第二招来势汹汹！")
        local Boss = bcapi.get_role(1, "剧情_玄阙宫戒律长老_神秘人")
        Boss.Stat.BattleFlagDic:set_Item("第二招", "1")
        bcapi.resume_and_show_ui()
    end)
end

--第三次攻击分为对话和攻击两个部分，用于随机时刻出招
function thirdAttackTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("诸葛鹿","这第三招为何毫无预兆？")
        bcapi.add_timer_trigger(math.random(3,15), setThirdAttackTrigger)
        bcapi.resume_and_show_ui()
    end)
end

function setThirdAttackTrigger()
    bcapi.async_call(function()
        local Boss = bcapi.get_role(1, "剧情_玄阙宫戒律长老_神秘人")
        Boss.Stat.BattleFlagDic:set_Item("第三招", "1")
    end)
end



function BattleWin()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("诸葛鹿","师父，三招已过！")
        bcapi.win()
    end)
end



function BattleLose()
    bcapi.lose()
end

