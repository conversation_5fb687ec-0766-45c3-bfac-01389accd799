local bcapi = require("BattleClient<PERSON><PERSON>")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    
end
function start()
    bcapi.add_timer_trigger(0.1, Talk)
end

function Talk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("芙蕖望月_老全", "黑刹教追捕叛徒，为何要把不相干的人卷进来？")
        bcapi.talk("芙蕖望月_玛亚思莉", "若是要说，这还是你们四个的功劳，自你们走后，教内便有了一条新的规矩，若是哪一户敢收留叛徒，就要杀了别人满门，哪个村敢收留叛徒，就要屠了别人全村。")
        bcapi.talk("芙蕖望月_玛亚思莉", "自那之后鲜少有出逃者，还有自愿回门派受罚的逃难者。这俩人既然在谷里，那就都别走了！")
        bcapi.resume_and_show_ui()
    end)
end