local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)
local <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YaoQulao

function init()
    -- 将策略牌加入牌堆
    -- bcapi.add_strategy_card("铁甲阵法", 1, team, true)
    -- bcapi.add_strategy_card("藏锋积锐", 1, team, true)
    -- bcapi.add_strategy_card("摧山掌", 1, team, true)
    -- bcapi.add_strategy_card("金钟罩", 1, team, true)
    -- 禁用自动战斗按钮
    bcapi.disable_auto_battle()
    createRoles()
end
function start()
    bcapi.add_timer_trigger(0, TalkStart)
    bcapi.add_condition_trigger("[%enemy:副本_贼寇山洞_土老大->flag:二阶段%][>]0", 1, EnemyEnhance)
    --添加触发器，一系列教学触发器
    bcapi.add_condition_trigger("[%enemy:副本_贼寇山洞_土老大->flag:土老大技能1教学%][=]1", 0,Skill01JiaoXue)
    bcapi.add_condition_trigger("[%enemy:副本_贼寇山洞_土老大->flag:土老大技能2教学%][=]1", 0, Skill02JiaoXue)
    bcapi.add_condition_trigger("[%enemy:副本_贼寇山洞_土老大->flag:土老大技能3教学1%][=]1", 0, Skill03JiaoXue1)
    bcapi.add_condition_trigger("[%enemy:副本_贼寇山洞_土老大->flag:土老大技能3教学2%][=]1", 0, Skill03JiaoXue2)
    bcapi.add_condition_trigger("[%enemy:副本_贼寇山洞_土老大->flag:土老大技能3教学4%][=]1", 0, Skill03JiaoXue4)
end

function TalkStart()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "惹到老子我，你们可是踢到铁板了！")
        bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "让你们见识下我这大铁锤的威力吧！哈哈哈！")
        bcapi.talk("药曲老#药曲老", "咳咳，阁主，老夫又来了，此人蛮力不可硬莽，需要以巧取胜！")
        bcapi.talk("药曲老#药曲老", "给队伍中拙侠客使用「大藏心经」，为其添加御伤，提高其抗伤能力！")
        bcapi.resume_and_show_ui()
        local Boss = bcapi.get_role(1, "副本_贼寇山洞_土老大")
        Boss.Stat.BattleFlagDic:set_Item("副本教学", "1")
        zhujue = bcapi.get_player_role("主角_新手战斗")
    -- YanLuchong = bcapi.get_player_role("燕路重")
        YunWusheng = bcapi.get_player_role("云舞笙")
        JingCheng = bcapi.get_player_role("荆成")
        zhujue.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, zhujue)
        -- YanLuchong.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, YanLuchong)
        YunWusheng.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, YunWusheng)
        JingCheng.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, JingCheng)
        bcapi.show_strategy_guide("大藏心经", "金燕桃", "拖拽策略技到金燕桃位置，然后松手施放", 1)
        bcapi.add_strategy_card("大藏心经", 1, team, true)
        --     -- 策略牌耗怒以及做怒气不够时做回怒保底
        -- if team.NuqiSystem.CurNuqi < 2 then
        --     team.NuqiSystem.CurNuqi = 2
        --     team.NuqiSystem.CurNuqi = team.NuqiSystem.CurNuqi - 2
        --     else
        --     team.NuqiSystem.CurNuqi = team.NuqiSystem.CurNuqi - 2        
        --     end
    end)
end

function EnemyEnhance()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "你们……果然有些本事。")
        bcapi.talk("副本_贼寇山洞_土老大#副本_贼寇山洞_土老大", "不过，也就到此为止了，素衣心经——起！")
        local Boss = bcapi.get_role(1, "副本_贼寇山洞_土老大")
        bcapi.resume_and_show_ui()
        bcapi.use_skill_to_role(Boss,"副本_贼寇山洞_土老大_变身",Boss,1)
        --可以放砸人技能
        Boss.Stat.BattleFlagDic:set_Item("副本教学", "0")
    end)
end
function createRoles()
    bcapi.async_call(function()
        -- 创建玩家角色=
        bcapi.create_join_role_with_card("金燕桃", 5, team, bcapi.Vector2(-3, -1), 0)
        bcapi.create_join_new_player_with_card_newbie(5, team, bcapi.Vector2(-5, 3), 0)
        bcapi.create_join_role_with_card("荆成", 5, team, bcapi.Vector2(-5, -4.5), 0)
        bcapi.create_join_role_with_card("云舞笙", 5, team, bcapi.Vector2(-7, -1), 0)
        local card1 = bcapi.get_player_unique_card("荆成")
        local card2 = bcapi.get_player_unique_card("云舞笙")
        card1.CDTimer = 20
        card2.CDTimer = 20
        -- bcapi.add_role_card("荆成", 10, 0, 0, team)
        -- JingCheng = bcapi.get_player_role("荆成")
        -- JingCheng.Buff:AddBuff("通用_阻塞", 2, 1, 1, JingCheng)
        -- zhujue = bcapi.get_player_role("主角") 
        -- zhujue.Buff.AddBuff("通用_阻塞", 2, 1, 1, zhujue)
        -- YunWusheng = bcapi.get_player_role("云舞笙") 
        -- YunWusheng.Buff.AddBuff("通用_阻塞", 2, 1, 1, YunWusheng)
        -- YanLuchong = bcapi.get_player_role("燕路重") 
        -- YanLuchong.Buff.AddBuff("通用_阻塞", 2, 1, 1, YanLuchong)
        -- MoZhiyou = bcapi.get_player_role("莫知酉") 
        -- MoZhiyou.Buff.AddBuff("通用_阻塞", 2, 1, 1, MoZhiyou)
    end)
end
function Skill01JiaoXue()
    bcapi.async_call(function()
    bcapi.pause_and_hide_ui()
    bcapi.talk("药曲老#药曲老", "当心！阁主，土老大招式力大无比，还需拙侠客前来抗伤！")
    bcapi.resume_and_show_ui()
    -- zhujue = bcapi.get_player_role("主角")
    -- YanLuchong = bcapi.get_player_role("燕路重")
    YunWusheng = bcapi.get_player_role("云舞笙")
    JingCheng = bcapi.get_player_role("荆成")
    -- zhujue.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, zhujue)
    -- YanLuchong.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, YanLuchong)
    YunWusheng.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, YunWusheng)
    JingCheng.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, JingCheng)
    bcapi.show_unique_skill_guide("金燕桃", "副本_贼寇山洞_土老大", "使用金燕桃绝招，远程将其嘲讽并保护友方")
    bcapi.wait(1.5)
    bcapi.show_dart_guide("金燕桃", "主角_新手战斗", "点击身法按钮，进入身法模式", "拖拽金燕桃至自己身旁，承担伤害")
    end)
end

function Skill02JiaoXue()
    bcapi.async_call(function()
    -- bcapi.wait(0.5)
    bcapi.pause_and_hide_ui()
    bcapi.talk("药曲老#药曲老", "土匪人多势众！阁主，配合「藏锋积锐」使出那一招！将其喽啰诛之")
    bcapi.resume_and_show_ui()
    --让土老大放砸人的技能
    local Boss = bcapi.get_role(1, "副本_贼寇山洞_土老大")
    Boss.Stat.BattleFlagDic:set_Item("副本教学", "0")
    -- zhujue = bcapi.get_player_role("主角")
    JinYantao = bcapi.get_player_role("金燕桃")
    YunWusheng = bcapi.get_player_role("云舞笙")
    JingCheng = bcapi.get_player_role("荆成")
    -- zhujue.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, zhujue)
    JinYantao.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, JinYantao)
    YunWusheng.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, YunWusheng)
    JingCheng.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, JingCheng)
    bcapi.wait(0.1)
    bcapi.show_strategy_guide("藏锋积锐", "主角_新手战斗", "拖拽策略技到自己位置，然后松手施放", 1)
    bcapi.add_strategy_card("藏锋积锐", 1, team, true)
            -- 策略牌耗怒以及做怒气不够时做回怒保底
    -- if team.NuqiSystem.CurNuqi < 2 then
    --     team.NuqiSystem.CurNuqi = 2
    --     team.NuqiSystem.CurNuqi = team.NuqiSystem.CurNuqi - 2
    --     else
    --     team.NuqiSystem.CurNuqi = team.NuqiSystem.CurNuqi - 2        
    --     end
    bcapi.block_wait(0.5)
    bcapi.show_unique_skill_guide("主角_新手战斗", "副本_贼寇山洞_土老大", "拖拽绝招按钮至指定区域，释放绝招「百念所归」")
    bcapi.block_wait(0.1)
    end)
end

function Skill03JiaoXue1()
    bcapi.async_call(function()
    bcapi.pause_and_hide_ui()
    bcapi.talk("药曲老#药曲老", "土老大该招式需冷静应对，切不可硬接，仍需操控「身法技」，巧妙化解此招！")
    bcapi.resume_and_show_ui()
    bcapi.show_dart_guide("云舞笙", "荆成", "点击身法按钮，进入身法模式", "拖拽云舞笙至荆成身边，躲避掉土老大攻击")
    --砸人技能放了后一阶段不放了，保证教学顺利
    local Boss = bcapi.get_role(1, "副本_贼寇山洞_土老大")
    Boss.Stat.BattleFlagDic:set_Item("副本教学", "1")
    end)
end

function Skill03JiaoXue2()
    bcapi.async_call(function()
    bcapi.pause_and_hide_ui()
    bcapi.talk("药曲老#药曲老", "土老大该招式来势汹汹，来不及躲避！此时需要我们用出铁甲阵法，一起抗下这一招！")
    bcapi.resume_and_show_ui()
    bcapi.show_strategy_guide("铁甲阵法", "主角_新手战斗", "拖拽策略技到任意侠客位置，然后松手施放", 1)
    bcapi.add_strategy_card("铁甲阵法", 1, team, true)
    --             -- 策略牌耗怒以及做怒气不够时做回怒保底
    -- if team.NuqiSystem.CurNuqi < 4 then
    --     team.NuqiSystem.CurNuqi = 4
    --     team.NuqiSystem.CurNuqi = team.NuqiSystem.CurNuqi - 4
    --     else
    --     team.NuqiSystem.CurNuqi = team.NuqiSystem.CurNuqi - 4        
    --     end
    bcapi.add_timer_trigger(6, Skill03JiaoXue3)
    end)
end

function Skill03JiaoXue3()
    bcapi.async_call(function()

    bcapi.pause_and_hide_ui()
    bcapi.talk("药曲老#药曲老", "干的漂亮！阁主，下面让云舞笙配合荆成乘胜追击！")
    bcapi.resume_and_show_ui()
    bcapi.show_unique_skill_guide("云舞笙", "副本_贼寇山洞_土老大", "拖拽绝招按钮至敌人身上，释放绝招「定魂天歌」")
    bcapi.wait(1)
    bcapi.pause_and_hide_ui() 
    bcapi.talk("药曲老#药曲老", "就是现在！")
    bcapi.resume_and_show_ui()
    bcapi.show_strategy_guide("藏锋积锐", "荆成", "拖拽策略技到荆成位置，然后松手施放", 1)
    bcapi.wait(0.5)
    bcapi.show_unique_skill_guide("荆成", "副本_贼寇山洞_土老大", "拖拽绝招按钮至敌人身上，释放绝招「阴阳无极」")
    bcapi.wait(3)
    bcapi.add_role_card("药曲老", 5, 0, 0, team)
    zhujue = bcapi.get_player_role("主角_新手战斗")
    JingCheng = bcapi.get_player_role("荆成")
    YunWusheng = bcapi.get_player_role("云舞笙")
    zhujue.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, zhujue)
    JingCheng.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, JingCheng)
    YunWusheng.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, YunWusheng)
    bcapi.wait(0.1)
    bcapi.pause_and_hide_ui()
    bcapi.talk("药曲老#药曲老", "金燕桃状态不佳，阁主让老夫上场，为队伍进行治疗！")
    bcapi.resume_and_show_ui()
    bcapi.show_replace_role_guide("药曲老", "金燕桃", "将药曲老拖至场上，换下金燕桃，稳住队伍血线！")
    bcapi.wait(0.5)
    bcapi.show_unique_skill_guide("药曲老", "主角_新手战斗", "拖拽绝招按钮至友方身上，释放绝招「来一剂猛药」")
    end)
end

function Skill03JiaoXue4()
    bcapi.async_call(function()
    bcapi.pause_and_hide_ui()
    bcapi.talk("药曲老#药曲老", "又是同一招！勿需慌张，该招式老夫还有解法！")
    bcapi.talk("药曲老#药曲老", "土老大该招式极度消耗内力，上一次砸空已经让他经脉不畅，此次再次化解，定能找到其弱点！")
    bcapi.talk("药曲老#药曲老", "就是现在！阁主，请使用独门秘技——「摧山掌」！")
    bcapi.resume_and_show_ui()
    bcapi.show_strategy_guide("摧山掌", "副本_贼寇山洞_土老大", "拖拽策略技到敌方位置，然后松手施放", 1)
    bcapi.add_strategy_card("摧山掌", 1, team, true)
    --             -- 策略牌耗怒以及做怒气不够时做回怒保底
    -- if team.NuqiSystem.CurNuqi < 2 then
    --     team.NuqiSystem.CurNuqi = 2
    --     team.NuqiSystem.CurNuqi = team.NuqiSystem.CurNuqi - 2
    --     else
    --     team.NuqiSystem.CurNuqi = team.NuqiSystem.CurNuqi - 2        
    --     end
    end)
end