--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_雾锁寒山_06最终房/flags_蜃境_雾锁寒山_06最终房")


---@class 角色_蜃境_雾锁寒山_06最终房
local npcs = {
    zhujue = "角色/zhujue",
    --- "finalBoss_1"
    finalBoss_1 = "角色/finalBoss_1",
    moqi = "角色/moqi",
}

---@class 物体_蜃境_雾锁寒山_06最终房
local objs = {
    exit = "物体/exit",
    heibaijianshi_air = "物体/heibaijianshi_air",
    jianzhen_active = "物体/jianzhen_active",
}

---@class 相机_蜃境_雾锁寒山_06最终房
local cameras = {
    cam_main = "相机/cam_main",
    cam_start_left = "相机/cam_start_left",
    cam_start_right = "相机/cam_start_right",
    cam_start_walk = "相机/cam_start_walk",
    cam_boss = "相机/cam_boss",
    cam_moqiTalk = "相机/cam_moqiTalk",
    cam_start = "相机/cam_start",
}

---@class 位置_蜃境_雾锁寒山_06最终房
local pos = {
    pos_bossBack = "位置/pos_bossBack",
    pos_start_walk = "位置/pos_start_walk",
    pos_start_walk_1 = "位置/pos_start_walk_1",
    moqiPos1 = "位置/moqiPos1",
}

---@class 资产_蜃境_雾锁寒山_06最终房
local assets = {
    timeline_heibaijianshi = "资产/timeline_heibaijianshi",
    timeline_shijian = "资产/timeline_shijian",
}

---@class 动作_蜃境_雾锁寒山_06最终房
local animationClips = {
}

---@class 蜃境_雾锁寒山_06最终房
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
