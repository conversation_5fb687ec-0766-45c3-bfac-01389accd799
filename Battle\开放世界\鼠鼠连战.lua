local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

-- 松鼠（玩家）和熊（敌人）先进场。
-- 熊死后，狼（敌人）进场。
-- 狼死后，鳄鱼（敌人）进场。
-- 鳄鱼死后，战斗胜利。

function init()
    bcapi.hide_ui("疗伤")
    bcapi.reset_limit_time(300)
    -- 强制不结束战斗,后面用条件来给出胜利失败
    battle.ForceNotEndBattle = true
    -- 开局创建角色
    createRoles()
end

function start()
    -- 1秒后开始检查熊死没死，并执行熊死亡后的逻辑
    bcapi.add_condition_trigger("[%t->teammate:世界_鼠鼠连战_熊%][<]1", 0, bearDead)
    -- 1秒后检查失败条件
    bcapi.add_timer_trigger(1, checkLose)
end

-- 取玩家队伍存的字典，后面两个函数中用到
-- local dict = bcapi.get_team(0).ValueDict

function createRoles()
    --给玩家创建松鼠哥
    bcapi.async_call(function()
        bcapi.create_join_role("世界_鼠鼠连战_鼠鼠", 50, team, bcapi.Vector2(-6, 0), 0)
    end)
end

-- 熊死后招狼，并检查狼死没死
function bearDead()
    bcapi.async_call(function()
        bcapi.create_join_role("世界_鼠鼠连战_雪狼", 50, eTeam, bcapi.Vector2(6, 0), 0)
        bcapi.wait(1)
        bcapi.add_condition_trigger("[%t->teammate:世界_鼠鼠连战_雪狼%][<]1", 0, wolfDead)
    end)
end

-- 狼死后招鳄鱼，并关闭战斗强制不结束
function wolfDead()
    bcapi.async_call(function()
        bcapi.create_join_role("世界_鼠鼠连战_鳄鱼", 50, eTeam, bcapi.Vector2(6, 0), 0)
        battle.ForceNotEndBattle = false
    end)
end

-- 检查鼠鼠死没死，死了就寄了
function checkLose()
    bcapi.async_call(function()
        bcapi.add_condition_trigger("[%c->teammate:世界_鼠鼠连战_鼠鼠%][<]1", 0, lose)
    end)
end

-- 寄了
function lose()
    bcapi.async_call(function()
        battle.ForceNotEndBattle = false
        bcapi.lose()
    end)
end