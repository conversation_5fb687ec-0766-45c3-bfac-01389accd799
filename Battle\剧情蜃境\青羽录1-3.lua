local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.ta_event_track("PlayerStartStory", "Step", "BattleTutor_Step4")

    battle.HideCardLevel = true

    bcapi.disable_auto_battle()
    bcapi.disable_speed_up()
    -- bcapi.disable_dart()
    
    bcapi.hide_ui("疗伤")
    -- bcapi.hide_ui("身法按钮")
    bcapi.hide_ui("自动按钮")

    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        -- 创建玩家角色
        bcapi.create_join_role_with_card("荆成", 8, team, bcapi.Vector2(-3, 0), 0)
                --给荆成加点攻速
        JingCheng = bcapi.get_player_role("荆成")
        ---@diagnostic disable-next-line
        JingCheng.Buff:AddBuff("通用_教学关_加攻速", 300, 1, 1, Jing<PERSON>heng)
        bcapi.add_strategy_card("梧叶舞秋风", 1, team)
        bcapi.add_strategy_card("大藏心经", 1, team)
        bcapi.add_strategy_card("血沸剑魂", 1, team)
        -- 创建敌人
        bcapi.create_join_role("剧情_恶徒", 5, eTeam, bcapi.Vector2(3, 0), 0)
        bcapi.create_join_role("剧情_喽啰", 3, eTeam, bcapi.Vector2(5, -3), 0)
        bcapi.create_join_role("剧情_喽啰", 3, eTeam, bcapi.Vector2(5, 3), 0)
        bcapi.create_join_role("剧情_泼皮", 5, eTeam, bcapi.Vector2(7, 0), 0)
    end)
end

function start()
    EnemyTalk()
    bcapi.add_timer_trigger(6, Talk1)
end

function EnemyTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("剧情_泼皮#剧情_泼皮", "哼！看来你还没尝够苦头啊！")
        bcapi.talk("荆成#荆成#严肃", "多说无益，看拳！")
        bcapi.resume_and_show_ui()
    end)
end

function Talk1()
    bcapi.async_call(function()

        bcapi.pause()
        bcapi.talk("荆成#荆成#严肃", "敌人人多势众，要速战速决了。")
        bcapi.talk("旁白", "使用光明圣火和血沸剑魂，加强荆成的群攻和续航能力！")
        bcapi.wait(0.1)
        bcapi.show_strategy_guide("光明圣火", "荆成", "拖拽卡牌到荆成位置，然后松手释放", 13)
        bcapi.add_strategy_card("光明圣火", 13, team, true)
        -- bcapi.wait(2)
        --bcapi.set_card_cd(team, "血沸剑魂", 20)
        -- bcapi.show_strategy_guide("血沸剑魂", "荆成", "拖拽卡牌到荆成位置，然后松手释放", 1)
        -- bcapi.add_strategy_card("血沸剑魂", 1, team, true)
        --bcapi.set_card_cd(team, "藏锋积锐", 20)

    end)
end