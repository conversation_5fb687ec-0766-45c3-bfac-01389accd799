local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

-- 脚本内容：
-- 四波敌人依次序上场，会有对话出现

function init()

end

function start()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0, secondChanllenger)
    end)
end

function secondChanllenger(...)
    bcapi.async_call(function()

        bcapi.create_join_role("世界_河洛人士_刀", 25, eTeam, bcapi.Vector2(-5.5, 3.5), 0)
        bcapi.create_join_role("世界_河洛人士_刀", 25, eTeam, bcapi.Vector2(-5.5, -3.5), 0)
        bcapi.create_join_role("世界_河洛人士_脚", 25, eTeam, bcapi.Vector2(0, 3.5), 0)
        bcapi.create_join_role("世界_河洛人士_脚", 25, eTeam, bcapi.Vector2(0, -3.5), 0)
        bcapi.create_join_role("世界_河洛人士_长枪", 25, eTeam, bcapi.Vector2(5.5, 3.5), 0)
        bcapi.create_join_role("世界_河洛人士_长枪", 25, eTeam, bcapi.Vector2(5.5, -3.5), 0)

        bcapi.wait(1)
        bcapi.pause_and_hide_ui()
        bcapi.talk("河洛人", "有几分实力！我们来！")
        bcapi.resume_and_show_ui()

        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0, thirdChanllenger)
    end)
end

function thirdChanllenger(...)
    bcapi.async_call(function()

        bcapi.create_join_role("通用_刺客_小怪_男杀手_双匕", 25, eTeam, bcapi.Vector2(-5.5, 3.5), 0)
        bcapi.create_join_role("通用_刺客_小怪_男杀手_双匕", 25, eTeam, bcapi.Vector2(-5.5, -3.5), 0)
        bcapi.create_join_role("通用_刺客_小怪_男杀手_双匕", 25, eTeam, bcapi.Vector2(0, 3.5), 0)
        bcapi.create_join_role("通用_刺客_小怪_男杀手_双匕", 25, eTeam, bcapi.Vector2(0, -3.5), 0)
        bcapi.create_join_role("通用_刺客_小怪_男杀手_暗器", 25, eTeam, bcapi.Vector2(5.5, 3.5), 0)
        bcapi.create_join_role("通用_刺客_小怪_男杀手_暗器", 25, eTeam, bcapi.Vector2(5.5, -3.5), 0)

        bcapi.wait(1)
        bcapi.pause_and_hide_ui()
        bcapi.talk("刺客", "【摇头】还是造诣尚浅，这种事还是交给我们吧！")
        bcapi.resume_and_show_ui()

        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0, finalChanllenger)
    end)
end

function finalChanllenger()
    bcapi.async_call(function()

        bcapi.create_join_role("世界_黑衣教人_斧", 25, eTeam, bcapi.Vector2(-5.5, 3.5), 0)
        bcapi.create_join_role("世界_黑衣教人_斧", 25, eTeam, bcapi.Vector2(-5.5, -3.5), 0)
        bcapi.create_join_role("世界_黑衣教人_双剑", 25, eTeam, bcapi.Vector2(0, 3.5), 0)
        bcapi.create_join_role("世界_黑衣教人_双剑", 25, eTeam, bcapi.Vector2(0, -3.5), 0)
        bcapi.create_join_role("世界_黑衣教人_弓", 25, eTeam, bcapi.Vector2(5.5, 3.5), 0)
        bcapi.create_join_role("世界_黑衣教人_弓", 25, eTeam, bcapi.Vector2(5.5, -3.5), 0)

        bcapi.wait(1)
        bcapi.pause_and_hide_ui()
        bcapi.talk("黑衣教人", "小子算我轻看了你！那就接招吧！")
        bcapi.resume_and_show_ui()

        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0, win)
    end)
end

function win()
    bcapi.async_call(function()
        bcapi.win()
    end)
end
