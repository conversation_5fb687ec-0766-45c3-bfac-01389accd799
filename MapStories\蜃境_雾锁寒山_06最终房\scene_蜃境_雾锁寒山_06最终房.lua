
---@type 蜃境_雾锁寒山_06最终房
local context = require("MapStories/蜃境_雾锁寒山_06最终房/scene_蜃境_雾锁寒山_06最终房_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_雾锁寒山_06最终房
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end
local rapi = require("RpgMapApi")

local rpgFlags = require("RpgMap/flags")

local capi = require("ChapterApi")

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")
function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

--每次载入调用
function start()
    refreshMapStates()
    s.readyToStart(true)
    s.blackScreen(0)

    if flags.start_show == 0 then
        --开场演出
        s.camera(cameras.cam_start_walk,true,true,blendHintEnum.Cut)
        s.setPos(npcs.zhujue,pos.pos_start_walk)
        s.lightScreen()
        s.move(npcs.zhujue,pos.pos_start_walk_1,1,true)
        s.camera(cameras.cam_start_left,true,true,blendHintEnum.Cut)
        s.wait(0.5)
        s.cameraAsync(cameras.cam_start_right,true,true,blendHintEnum.Custom,10)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_荒原雪林","荒原雪林_最终房间_开场")

        s.setActive(npcs.moqi,true)
        s.moveAsync(npcs.moqi,pos.moqiPos1,1,true)

        s.cameraAsync(cameras.cam_moqiTalk,true,true,blendHintEnum.Cut,1)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_荒原雪林","荒原雪林_最终房间_开场2")

        s.cameraAsync(cameras.cam_main,true,true,blendHintEnum.Custom,2)

        flags.start_show = 1

        s.setTaskStep("雾锁寒山最终房","checkForward")
    else
        s.setPos(npcs.zhujue,pos.pos_start_walk_1)
        s.lightScreen()
    end

    try_execute_newbieGuide()
end


function refreshMapStates()
    local step = get_level_step() --当前关卡步数
    s.playMusic("势归")
    reset_all()
    if flags.boss_battle == 0 then
        s.setActive(objs.jianzhen_active,false)
        --s.setActive(objs.heibaijianshi_air,true)
        level_guide_to(objs.heibaijianshi_air)
    else
        s.setPos(npcs.moqi,pos.moqiPos1)
        s.setActive(objs.heibaijianshi_air,false)
        s.setActive(objs.jianzhen_active,true)
    end

    --引导点
    if s.getCurrentTaskStep("雾锁寒山最终房") == "checkForward" then
        s.addGameMapGuide(objs.heibaijianshi_air)
    elseif s.getCurrentTaskStep("雾锁寒山最终房") == "talkToMoqi" then
        level_guide_to(npcs.moqi,1) 
    elseif s.getCurrentTaskStep("雾锁寒山最终房") == "leave" then
        level_guide_to(objs.exit)
    end
end

function reset_all()
    if (flags.start_show == 1) and (flags.moqi_talk ~= 2) and (flags.boss_battle == 0) then
        --s.setActive(objs.heibaijianshi_air,true)
    else
        s.setActive(objs.heibaijianshi_air,false)
    end

    --删除所有的引导点
    s.removeAllGameMapGuides()

    --墨七交谈
    if flags.moqi_talk == 2 then
        s.setActive(npcs.moqi,false)
    elseif flags.moqi_talk == 1 then
        s.setActive(npcs.moqi,true)
    end
end

--墨七交谈
function moqi()
    if flags.boss_battle == 0 then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_荒原雪林","荒原雪林_最终房间_墨七1")
        flags.moqi_talk = 1
    elseif flags.boss_battle == 1 then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_荒原雪林","荒原雪林_最终房间_墨七2")
        s.setTaskStep("雾锁寒山最终房","leave")
        flags.moqi_talk = 2
        s.removeAllGameMapGuides()
        level_guide_to(objs.exit)
    elseif flags.moqi_talk == 2 then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_荒原雪林","荒原雪林_最终房间_墨七3")
    end
end

function enemy_battle()
    executeMemSlot("wshs6_battle_401",
    function()
        s.camera(cameras.cam_boss,true,true,blendHintEnum.Custom,2)
        s.blackScreen()
        s.setActive(objs.heibaijianshi_air,false)
        s.setActive(npcs.zhujue,false)
        s.playTimeline(assets.timeline_heibaijianshi,false)
    end,
    function()

        --消除播放timeline时候的穿帮问题
        s.setActive(npcs.moqi,false)

        s.playTimeline(assets.timeline_shijian,false)

        s.setActive(objs.heibaijianshi_air,false)
        s.setActive(objs.jianzhen_active,true)
        
        s.lightScreen()
        s.cameraAsync(cameras.cam_main,true,true,blendHintEnum.Custom,2)
        
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_荒原雪林","荒原雪林_最终房间_结束")
        flags.boss_battle = 1

        --解锁关卡完毕
        s.setRpgMapFlag("lieWuFeiYing_rpgMapEnterance_unlock",1)
        s.setRpgMapFlag("moQi_chapter_finish",1)
        s.setTaskStep("雾锁寒山最终房","talkToMoqi")




        --还原播放timeline时候的穿帮问题
        s.setActive(npcs.moqi,true)

        level_guide_to(npcs.moqi,1)
    end,
    function()
        s.blackScreen()
        s.setActive(objs.heibaijianshi_air,true)
        s.camera(cameras.cam_main,true,true,blendHintEnum.Cut)
        s.setPos(npcs.zhujue,pos.pos_bossBack)
        s.lightScreen()
    end,
    function()
        s.blackScreen()
        s.setActive(objs.heibaijianshi_air,true)
        s.camera(cameras.cam_main,true,true,blendHintEnum.Cut)
        s.setPos(npcs.zhujue,pos.pos_bossBack)
        s.lightScreen()
    end)
    s.nextStep("refreshMapStates")
end

function exitScene()
    if s.getCurrentTaskStep("雾锁寒山最终房") == "leave" then
        s.setTaskStep("雾锁寒山最终房","finished")
    end

    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("雾锁寒山")
end


function boss_finish()
    rapi.showBossSoloPanel("1003")
end
