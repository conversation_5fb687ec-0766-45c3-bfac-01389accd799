--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/副本_黑刹教/flags_副本_黑刹教")


---@class 角色_副本_黑刹教_地上
local npcs = {
    zhujue = "角色/zhujue",
}

---@class 物体_副本_黑刹教_地上
local objs = {
}

---@class 相机_副本_黑刹教_地上
local cameras = {
}

---@class 位置_副本_黑刹教_地上
local pos = {
    start = "位置/start",
}

---@class 资产_副本_黑刹教_地上
local assets = {
}

---@class 动作_副本_黑刹教_地上
local animationClips = {
}

---@class 副本_黑刹教_地上
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
