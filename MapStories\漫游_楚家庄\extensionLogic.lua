local s = require("StoryApi")

local extension = require 'MapStories/漫游_楚家庄/extension'
---@type flags_漫游_楚家庄
local flags = require("MapStories/漫游_楚家庄/flags_漫游_楚家庄")
s.loadFlags(flags)

---@type 漫游_楚家庄_前院
local qianyuan = require 'MapStories/漫游_楚家庄/scene_漫游_楚家庄_前院_h'
local npcs = qianyuan.npcs
local pos = qianyuan.pos

---@type 漫游_楚家庄_别院
local bieyuan = require 'MapStories/漫游_楚家庄/scene_漫游_楚家庄_别院_h'

---@type RoleTrigger
local roleAnims = RoleTrigger

---@type ItemWidget
local itemWidget = extension.widgets.ItemWidgetInfo.widget
---@type 楚家庄_ItemData
local itemData = extension.widgets.ItemWidgetInfo.items --[[@as 楚家庄_ItemData]]

function on_use_item(itemKey,sceneName)
    if itemKey == itemData.cangbaotu1.key or itemKey == itemData.cangbaotu2.key or itemKey == itemData.cangbaotu3.key or itemKey == itemData.cangbaotu4.key then
        on_use_cangbaotu(sceneName)
    end
end

---不可改名
function get_string_item_desc(itemKey)
    return ""
end

function on_use_cangbaotu(sceneName)
    if flags.main_story_step >= 4 then
        s.blackScreen()
        
        s.talk(npcs.jingcheng, "好像已经凑齐了，莫大哥呢？")
        s.talk(npcs.mozhiyou, "在这儿~")
        s.talk(npcs.jingcheng, "莫大哥，碎片已经集齐了。")
        s.talk(npcs.mozhiyou, "竟然这么顺利？！")

        s.talk(npcs.mozhiyou, "走，我们去那看，那里没人。")
        
        s.aside("你们找了一处僻静角落，对着藏宝图研究了一阵。")
        s.talk(npcs.mozhiyou, "小孩儿画的图真是难以理解，但还好有几样标志物能对上。")
        s.talk(npcs.mozhiyou, "你瞧瞧，是不是就在这里？")
        s.talk(npcs.jingcheng, "莫大哥，你捂得太紧了，我看不清。")
        s.talk(npcs.mozhiyou, "哈哈，我知道了！")
        s.talk(npcs.jingcheng, "那地方离得远吗？")
        s.talk(npcs.mozhiyou, "看起来就在前山附近……")
        s.talk(npcs.mozhiyou, "阿成，我先去前山看看，你若是准备好了，便来找我。")
        s.lightScreen()
        

        flags.main_story_step = flags.main_story_step + 1
        if sceneName == "漫游_楚家庄_前院" then
            s.setActive(npcs.mozhiyou,true)
            s.setPos(npcs.mozhiyou, pos.MZY_enter)--改变莫知酉的位置
        end
        
        s.finishTask(300010, 1,2)
        
        itemWidget:removeItem(itemData.cangbaotu1,1)
        itemWidget:removeItem(itemData.cangbaotu2,1)
        itemWidget:removeItem(itemData.cangbaotu3,1)
        itemWidget:removeItem(itemData.cangbaotu4,1)
        
    else
        s.aside("藏宝图还没凑齐。")
    end
end