
---@type 蜃境_青羽录_06最终房
local context = require("MapStories/蜃境_青羽录_06最终房/scene_蜃境_青羽录_06最终房_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local assets = context.assets
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_青羽录_06最终房
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")
local rapi = require("RpgMapApi")

local rpgFlags = require("RpgMap/flags")
rapi.loadFlags(rpgFlags)

function getFlags(...)
    return flags
end


----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")
require("MapStories/MapStoryCommonTools")

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function newbieGuideInLevel()

end

function start()
    s.blackScreen()
    print("start called")
    refreshMapStates()
    --s.setPos(npcs.zhujue, pos.zhujue_start_pos)
    s.readyToStart(true)
    try_execute_newbieGuide()
end


--默认初始化的地图显隐状态
function reset_all()
    local step = get_level_step()
    s.removeAllGameMapGuides()
    s.setActive(objs.door_start,false)
    s.setActive(objs.door_start,step == 1)
    s.setActive(npcs.boss,step < 2)
    s.setActive(npcs.boss2,step >= 2)
    s.setActive(npcs.chuqing,false)
end

function testAvg()
    s.soloAnimState(npcs.boss,"Special_1")
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()
    s.playMusic("势归")
    print("refreshMapStates level step = " .. tostring(step))
    reset_all()

    if step == 0 then
        s.blackScreen()
        s.camera(cameras.camera1,true)
        s.animState(npcs.boss,"Special_Loop_2",true)
        s.setPos(npcs.zhujue,pos.start)
        s.setActive(npcs.zhujue,false)
        s.lightScreen()
        s.readyToStart(true)
        s.setActive(objs.door_start,true)
        s.wait(2.5)
        --s.setActive(objs.door,true)
        s.setActive(npcs.zhujue,true)
        s.cameraAsync(cameras.camera2,true,true, blendHintEnum.EaseInOut,2,true,true)
        s.moveAsync(npcs.zhujue,pos.start_1,1,true)
        s.wait(2)
        s.blackScreen()
        s.camera(cameras.camera1,false)
        s.camera(cameras.camera2,false)
        s.cameraAsync(cameras.camera3,true,true, blendHintEnum.EaseInOut,1,true,true)
        s.wait(1)
        s.setActive(objs.door_start,false)
        s.setActive(objs.door,false)
        s.turnTo(npcs.zhujue,npcs.boss)
        --s.turnTo(npcs.boss,npcs.zhujue)
        s.lightScreen()
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_最终房","蜃境_青羽录_最终房_入口",nil,0)
        s.camera(cameras.camera4,true,true, blendHintEnum.EaseInOut,1.5,true,true)
        --s.wait(1)
        s.soloAnimState(npcs.boss,"Special_1",true)
        s.wait(2)
        s.triggerReset(npcs.boss)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_最终房","蜃境_青羽录_最终房_入口1",nil,0)
        s.camera(cameras.camera3,true,true, blendHintEnum.EaseInOut,2,true,true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_最终房","蜃境_青羽录_最终房_入口2",nil,0)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_最终房","蜃境_青羽录_最终房_入口3_1",nil,0)
        -- s.soloAnimState(npcs.zhujue,"Special_2")
        --s.wait(1.5)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_最终房","蜃境_青羽录_最终房_入口3_2",nil,0)
        s.blackScreen()
        s.camera(cameras.camera5,true)
        s.setActive(npcs.chuqing,true)
        s.setPos(npcs.chuqing,pos.chuqing)
        s.moveAsync(npcs.chuqing,pos.chuqing2,1,true)
        s.wait(0.3)
        s.lightScreen()
        s.wait(1.4)
        s.camera(cameras.camera5,false,true, blendHintEnum.EaseInOut,2,true,true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_最终房","蜃境_青羽录_最终房_入口4",nil,0)
        s.camera(cameras.camera3,true,true, blendHintEnum.EaseInOut,1.5,true,true)
        s.soloAnimState(npcs.boss,"Special_2")
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_最终房","清除",nil,0)
        --关闭camera3和4
        s.camera(cameras.camera3,false)
        s.camera(cameras.camera4,false)
        s.setActive(npcs.chuqing,false)
        next_level_step(true)
        s.blackScreen()
        s.wait(0.5)
        refreshMapStates()
        s.lightScreen()
    elseif(step == 1)then
        s.setTaskStep("青羽录蜃楼之主","beatChuQingXieZi")
        level_guide_to(npcs.boss)
    else
        --do nothing
        level_guide_to(objs.door_start)
    end

end

function try_execute_newbieGuide(...)

end

function boss()
    
    executeMemSlot("qyl6_battle_301",
    nil,
    function()
        s.setActive(npcs.boss,false)
        --剑阵落下
        s.playTimeline(assets.shijian,false)
        s.setActive(npcs.boss2,true)
        s.camera(cameras.cam_main,true,true,blendHintEnum.Cut)
        s.lightScreen()
        --剑阵落下
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_最终房","剑阵落下",nil,0)
        s.blackScreen()

        --参数变量设置
        s.setTaskStep("青羽录蜃楼之主","leaveLuWeiDiShenJing")  
        s.setRpgMapFlag("wuSuoHanShan_rpgMapEnterance_unlock",1)  
        s.setRpgMapFlag("finish_chapter_1_qingYuLu",1)
        s.setActive(npcs.boss,false)
        s.unlockSystem("sys_unlock_xingtu",false) --解锁大月卡系统
        s.unlockSystem("sys_unlock_tiaozhan",false) --解锁BOSS挑战系统


        s.wait(1)
        s.playTimeline(assets.songshu,false)

        s.setActive(npcs.songshu,true)
        s.lightScreen()
        newbieApi.eventTrack("newbieStep", "step", "qiu_hao_ping")
        local ret = s.runAvgTag("蜃境/通用对白","蜃境_通用_求好评",nil,0)
        if(ret == 1)then
            newbieApi.eventTrack("newbieStep", "step", "qiu_hao_ping_jump_to")
            s.openCommentPage()
            s.setActive(npcs.songshu,false)
            s.lightScreen()
        else
            s.setActive(npcs.songshu,false)
            s.lightScreen()
            s.runAvgTag("蜃境/通用对白","还是不给好评_结束",nil,0)
        end


        next_level_step(true)
    end,function()
            
    end,function()
        defaultLose()
    end,nil,true)
end

function test()
    s.blackScreen()
    s.setActive(npcs.boss,false)
    s.setActive(npcs.boss2,false)
    s.setActive(npcs.songshu,true)
    s.lightScreen()
    s.wait(1)
    s.soloAnimState(npcs.songshu,"Special_1",true)
end

function boss2()
    rapi.showBossSoloPanel("1001")
end
----------------------OVERRIDE----------------------------------
function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("青羽录")
end