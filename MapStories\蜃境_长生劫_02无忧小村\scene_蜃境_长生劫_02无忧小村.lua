
---@type 蜃境_长生劫_02无忧小村
local context = require("MapStories/蜃境_长生劫_02无忧小村/scene_蜃境_长生劫_02无忧小村_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger
s.loadFlags(context.flags)

s.loadFlags(context.flags)
---@type flags_蜃境_长生劫_02无忧小村

local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")


----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")


--精英怪战斗
function eliteBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end


function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function start()
    print("start called")
    refreshMapStates()
    s.readyToStart(true)

    -- 防止跑图中强退导致流程跳过
    map_story_guide()
end

function map_story_guide()
    -- 如果未解锁，且已通关，则finish
    if (not s.hasUnlockMemorySlot("csj_10") and newbieApi.getNewbieFlag(newbieFlags.chapter_csj_10_finished) == 1) then
        capi.finishMemorySlot("csj_10")
        s.setTaskStep("长生劫无忧小村","talkToChunSui2")
        flags.chunsui_state = 2
        next_level_step(true)
    end
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数

    --任务状态激活
    if step < 3 then
        s.setTaskStep("长生劫无忧小村","forwardToExplore1")
    elseif step == 3 then
        s.setTaskStep("长生劫无忧小村","talkToChunSui1")
    elseif step > 3 and step < 8 then
        s.setTaskStep("长生劫无忧小村","forwardToExplore2")
    elseif step == 8 then
        s.setTaskStep("长生劫无忧小村","talkToChunSui2")
    else
        s.setTaskStep("长生劫无忧小村","leave")
    end

    reset_all()

    --第一次进入场景
    if step == 0 then
        --进场演出
        if flags.enter_show == 0 then
            s.readyToStart(true)
            s.cameraAsync(cameras.cam_start_near,true,true,blendHintEnum.Cut)
            s.wait(0.5)
            s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_寻仙之路_无忧小村_进入")
            s.cameraAsync(cameras.cam_start_far,true,true,blendHintEnum.Custom,10)
            flags.enter_show = 1
            
            s.wait(2)
            s.blackScreen() 
            s.camera(cameras.cam_main,true,true,blendHintEnum.Cut)
            s.lightScreen()
        end
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.csj_05, 1)
    elseif step == 2 then
        level_guide_to(npcs.csj_06, 1)
    elseif step == 3 then
        level_guide_to(npcs.chunsui, 1)
    elseif step == 4 then
        level_guide_to(npcs.csj_07, 1)
    elseif step == 5 then
        level_guide_to(npcs.csj_08, 0)
    elseif step == 6 then
        level_guide_to(npcs.csj_09, 1)
    elseif step == 7 then
        level_guide_to(npcs.csj_10, 0)
    elseif step == 8 then
        level_guide_to(npcs.chunsui, 1)
    else
        level_guide_to(objs.exit)
    end
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.csj_05, false)
    s.setActive(npcs.csj_06, false)
    s.setActive(npcs.csj_07, false)
    s.setActive(npcs.csj_08, false)
    s.setActive(npcs.csj_09, false)
    s.setActive(npcs.csj_10, false)

    --支线何以为家NPC显隐
    s.setActive(npcs.HYWJ_NPC_02,  s.getCurrentTaskStep("长生劫支线何以为家") ~= "meetAtXianwaishanjing")

    s.setActive(objs.HYWJ_object_02_Medicine, s.getCurrentTaskStep("长生劫支线何以为家") == "findMedicine")

    --支线清浊理论NPC显隐
    s.setActive(npcs.QZLL_NPC,  s.getCurrentTaskStep("长生劫支线清浊理论") ~= "finished")

    --删除所有的引导点
    s.removeAllGameMapGuides()

        --收集物开启
    s.setActive(objs.csj_1003,not s.hasUnlockMemorySlot("csj_1003"))
    s.setActive(objs.csj_1004,not s.hasUnlockMemorySlot("csj_1004"))

    --触发器状态
    if flags.chunsui_show == 0 then
        s.setActive(npcs.chunsui,false)
    else
        s.setActive(npcs.chunsui,true)
    end

end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("长生劫")
end

---------------------------跑图实现--------------------------------
function chunsui()
    if flags.chunsui_state == 0 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_寻仙之路_椿岁")
        flags.chunsui_state = 1
        next_level_step(true)
    elseif flags.chunsui_state == 1 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_寻仙之路_椿岁闲话")
    elseif flags.chunsui_state == 2 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_寻仙之路_椿岁结束")
        flags.chunsui_state = 3
        next_level_step(true)
    elseif flags.chunsui_state == 3 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_寻仙之路_椿岁结束_闲话")
    end
end

----------------------------------支线剧情 何以为家 场景二----------------------------------
--测试效果
function test()
    s.setTaskStep("长生劫支线何以为家","MeetAtWuyoucun")
end

--第一段
function HYWJ_02()
    if s.getCurrentTaskStep("长生劫支线何以为家") == "MeetAtWuyoucun" then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_无忧小村_支线_何以为家2")
        s.setTaskStep("长生劫支线何以为家","findMedicine")
        s.setEventTargetType(npcs.HYWJ_NPC_02_oldman, 0)
        s.setActive(objs.HYWJ_object_02_Medicine,true)
        s.setEventTargetType(objs.HYWJ_object_02_Medicine, 1)
    elseif s.getCurrentTaskStep("长生劫支线何以为家") == "gotoOldman" then
        HYWJ_04()
        s.setEventTargetType(npcs.HYWJ_NPC_02_oldman, 0)
        s.setEventTargetType(npcs.HYWJ_NPC_02_child_2, 1 | 2)
        s.setEventTargetType(npcs.HYWJ_NPC_02_child_3, 1 | 2)
        s.setTaskStep("长生劫支线何以为家","findChild")
    else
        s.talk(npcs.HYWJ_NPC_02_oldman,"有什么事吗？")
    end
end

--第二段
function HYWJ_03()
    if s.getCurrentTaskStep("长生劫支线何以为家") == "findMedicine" then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_无忧小村_支线_何以为家3")
        s.setTaskStep("长生劫支线何以为家","gotoOldman")
        s.setActive(objs.HYWJ_object_02_Medicine,false)
        s.setEventTargetType(npcs.HYWJ_NPC_02_oldman, 1 | 2)
    else
        s.talkThink(npcs.zhujue,"未经允许还是不要乱动别人的东西为好")
    end
end

--第三段
function HYWJ_04()
    --设置主角位置
    --s.cameraAsync(cameras.cam_QZLL,true,true,blendHintEnum.EaseIn,1,true,true)
    --s.move(npcs.zhujue,pos.HYWJ_Player_story_location,5,true)
    --s.wait(0.5)
    s.turnToAsync(npcs.zhujue,npcs.HYWJ_NPC_02_oldman)
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_无忧小村_支线_何以为家4")
    s.setActive(npcs.HYWJ_NPC_02_child_1,true)
    --NPC移动演出
    s.agentMoveToAsync(npcs.HYWJ_NPC_02_child_1,pos.HYWJ_NPC_02_child_1_aim,5,5)
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_无忧小村_支线_何以为家4演出后续")
end

--第四段
function HYWJ_05()
    if s.getCurrentTaskStep("长生劫支线何以为家") == "findChild" then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_无忧小村_支线_何以为家5")
        --NPC移动演出
        s.turnToAsync(npcs.zhujue,npcs.HYWJ_NPC_02_child_3)
        s.move(npcs.HYWJ_NPC_02_child_2,pos.HYWJ_NPC_02_child_2_aim,8,true)
        s.setActive(npcs.HYWJ_NPC_02_child_2,false)
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_无忧小村_支线_何以为家5演出后续")
        s.setTaskStep("长生劫支线何以为家","meetAtXianwaishanjing")
        s.setEventTargetType(npcs.HYWJ_NPC_02_child_3, 0,true)
    else
        if s.isMale then            
            s.talk(npcs.HYWJ_NPC_02_child_3,"大哥哥，要跟我们一起玩吗？")
        else
            s.talk(npcs.HYWJ_NPC_02_child_3,"大姐姐，要跟我们一起玩吗？")
        end
    end
end

----------------------------------支线剧情 清浊理论-----------------------------------
--第一段
function QZLL_01()
    if s.getCurrentTaskStep("长生劫支线清浊理论") == "deactive" then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_无忧小村_支线_清浊理论")
        s.setTaskStep("长生劫支线清浊理论","joinArgument")
    elseif s.getCurrentTaskStep("长生劫支线清浊理论") == "joinArgument" then
        QZLL_02()
    else
        s.talk(npcs.QZLL_NPC_qingnian,"少侠可知何谓清浊？")
    end
end

--第二段
function QZLL_02()
    if s.getCurrentTaskStep("长生劫支线清浊理论") == "joinArgument" then
        s.cameraAsync(cameras.cam_QZLL,true,true,blendHintEnum.EaseIn,1,true,true)
        s.turnToAsync(npcs.QZLL_NPC_bianshou,npcs.QZLL_NPC_qingnian)
        s.turnToAsync(npcs.QZLL_NPC_qingnian,npcs.QZLL_NPC_bianshou)
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_无忧小村_支线_清浊辩论2")
        local isWin = s.battle("蜃境_长生劫_支线_清浊理论2")
        if isWin then
            QZLL_03()
            s.blackScreen(0.5)
            s.setActive(npcs.QZLL_NPC,false)
            s.lightScreen(0.5)
            s.setTaskStep("长生劫支线清浊理论","finished")
        end
    else
        s.talk(npcs.QZLL_NPC_bianshou,"去浊留清，方为正道。")
    end
end

--第三段
function QZLL_03()
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_无忧小村","蜃境_无忧小村_支线_清浊辩论3")
end

---------------------战斗实现--------------------------------
function normal_battle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

function enemy_battle(id,enemyKey)
    print("敌人战斗")
    local colliderKey = enemyKey.."_collider"

    s.popInfo("你已被发现！！！")

    --s.setActive(objs[colliderKey],false)
    s.turnTo(npcs[enemyKey],npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs[enemyKey])

    s.animState(npcs[enemyKey],"BAssault_Loop",true)
    s.wait(2)

    require("MapStories/MapStoryCommonTools").roamMapBattle(id,function()
        s.popInfo("战斗胜利！！！")
        flags[enemyKey.."_battle"] = 1
        s.setActive(objs[colliderKey],false)
        s.setActive(npcs[enemyKey],false)
    end,
    function()
        battle_cancle(id,enemyKey)
    end,
    function()
        battle_lose(id,enemyKey)
    end)

    s.wait(0.5)
    refreshMapStates()
end

function battle_lose(id,enemyKey)
    local colliderKey = enemyKey.."_collider"
    print("战斗失败")
    s.popInfo("战斗失败！！！")

    s.blackScreen()
    s.animState(npcs[enemyKey],"BAssault_Loop",false)
    s.setActive(objs[colliderKey],true)
    s.setPos(npcs.zhujue,pos[enemyKey])
    s.lightScreen()
end

function battle_cancle(id,enemyKey)
    local colliderKey = enemyKey.."_collider"

    s.blackScreen()
    s.animState(npcs[enemyKey],"BAssault_Loop",false)
    s.setActive(objs[colliderKey],true)
    s.setPos(npcs.zhujue,pos[enemyKey])
    s.lightScreen()
end

function fightInBack(enemyKey)
    local colliderKey = enemyKey.."_collider"
    local enemyDownKey = enemyKey.."_down"
    s.setActive(objs[colliderKey],false)

    s.animState(npcs.zhujue,"Special_2",true)
    s.wait(1)
    s.playSound("sfx","拳击3")
    s.wait(0.6)
    
    s.animState(npcs[enemyKey],"BDie",true)
    s.setDeath(npcs[enemyKey],0.5,false)

    flags[enemyDownKey] = 1
    s.popInfo("敌人暂时被击倒！！！")
    refreshMapStates()
end

-------------------关卡记忆碎片和战斗实现---------------------
function csj_05()
    executeMemSlot("csj_05", function()
        require("Chapters/长生劫").csj_05()
    end)
end

function csj_06()
    executeMemSlot("csj_06", function()
        require("Chapters/长生劫").csj_06()
    end,function()
        s.playTimeline(assets.chunsui_enter)
        s.setActive(npcs.chunsui,true)
        flags.chunsui_show = 1

        s.setTaskStep("长生劫无忧小村","talkToChunSui1")
        s.lightScreen()
        next_level_step(true)
    end)
end

function csj_07()
    executeMemSlot("csj_07", function()
        require("Chapters/长生劫").csj_07()
    end)
end

function csj_08()
    executeMemSlot("csj_08", function()
        require("Chapters/长生劫").csj_08()
    end)
end

function csj_09()
    executeMemSlot("csj_09", function()
        require("Chapters/长生劫").csj_09()
    end)
end

function csj_10()
    executeMemSlot("csj_10", function()
        require("Chapters/长生劫").csj_10()
    end)
end