--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/主线_青羽录9/flags_主线_青羽录9")


---@class 角色_主线_青羽录9
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "师父"
    shifu = "角色/shifu",
    --- "师兄伥化"
    shixiong = "角色/shixiong",
    --- "荆成"
    jingcheng = "角色/jingcheng",
}

---@class 物体_主线_青羽录9
local objs = {
    --- "树触发器"
    trigger_tree = "物体/trigger_tree",
    --- "石头触发器"
    trigger_stone = "物体/trigger_stone",
    --- "草地触发器"
    trigger_grass = "物体/trigger_grass",
    --- "左碰撞器"
    trigger_left = "物体/trigger_left",
    --- "镜头换轨"
    trigger_camChange = "物体/trigger_camChange",
    --- "换轨后左碰撞"
    trigger_left_c = "物体/trigger_left_c",
    --- "师父交互"
    trigger_shifu  = "物体/trigger_shifu ",
    --- "师兄交互"
    trigger_shixiong = "物体/trigger_shixiong",
    --- "更深处"
    trigger_darker = "物体/trigger_darker",
    --- "枇杷"
    trigger_pipa = "物体/trigger_pipa",
    --- "战斗"
    trigger_fight = "物体/trigger_fight",
}

---@class 相机_主线_青羽录9
local cameras = {
    --- "跟随镜头"
    camera_follow = "相机/camera_follow",
    --- "树镜头1"
    camera_tree1 = "相机/camera_tree1",
    --- "树镜头2"
    camera_tree2 = "相机/camera_tree2",
    --- "桥特写"
    camera_bridge = "相机/camera_bridge",
    --- "草地特写"
    camera_grass = "相机/camera_grass",
    --- "左侧相机"
    camera_left = "相机/camera_left",
    --- "石头特写"
    camera_stone = "相机/camera_stone",
    --- "跟随镜头1"
    camera_follow_1 = "相机/camera_follow_1",
    --- "污染桥近"
    camera_p_n_bridge = "相机/camera_p_n_bridge",
    --- "污染桥远"
    camera_p_f_bridge = "相机/camera_p_f_bridge",
    --- "师父特写"
    camera_shifu = "相机/camera_shifu",
    --- "师兄特写"
    camera_shixiong = "相机/camera_shixiong",
    --- "亭子特写"
    camera_tingzi = "相机/camera_tingzi",
    --- "师父说话"
    camera_shifu_talk = "相机/camera_shifu_talk",
    --- "荆成说话"
    camera_jingcheng_talk_shifu = "相机/camera_jingcheng_talk_shifu",
    --- "师父走路"
    camera_shifu_l = "相机/camera_shifu_l",
    --- "伥化特写"
    camera_shixiong_c = "相机/camera_shixiong_c",
    --- "魔化特写"
    camera_shixiong_d = "相机/camera_shixiong_d",
    --- "僵持"
    camera_hurt = "相机/camera_hurt",
    --- "师父进场"
    camera_shifu_in = "相机/camera_shifu_in",
    --- "枇杷"
    camera_pipa = "相机/camera_pipa",
    --- "更深处"
    camera_darker = "相机/camera_darker",
    --- "师父_结束"
    camera_shifu_end = "相机/camera_shifu_end",
}

---@class 位置_主线_青羽录9
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    --- "世界右方"
    pos_world_right = "位置/pos_world_right",
    --- "碰撞返回"
    pos_back = "位置/pos_back",
    --- "测试"
    pos_test = "位置/pos_test",
    --- "看向树"
    pos_looktree = "位置/pos_looktree",
    --- "荆成"
    pos_jingcheng = "位置/pos_jingcheng",
    --- "师父处荆成"
    pos_jingcheng_shifu = "位置/pos_jingcheng_shifu",
    --- "师父左边"
    pos_shifu_l = "位置/pos_shifu_l",
    --- "师父位置"
    pos_shifu = "位置/pos_shifu",
    --- "师父离开"
    pos_shifu_out = "位置/pos_shifu_out",
    --- "世界左方"
    pos_world_left = "位置/pos_world_left",
    --- "碰撞返回1"
    pos_back_1 = "位置/pos_back_1",
    --- "师兄位置"
    pos_shixiong = "位置/pos_shixiong",
}

---@class 资产_主线_青羽录9
local assets = {
    gwl_start = "资产/gwl_start",
    gwl_shifu = "资产/gwl_shifu",
    gwl_shixiong_change = "资产/gwl_shixiong_change",
    gwl_shijie = "资产/gwl_shijie",
    gwl_shixiong = "资产/gwl_shixiong",
}

---@class 动作_主线_青羽录9
local animationClips = {
}

---@class 主线_青羽录9
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
