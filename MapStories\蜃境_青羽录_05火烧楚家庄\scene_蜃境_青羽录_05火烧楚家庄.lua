
---@type 蜃境_青羽录_05火烧楚家庄
local context = require("MapStories/蜃境_青羽录_05火烧楚家庄/scene_蜃境_青羽录_05火烧楚家庄_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_青羽录_05火烧楚家庄
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")



function getFlags(...)
    return flags
end


----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")
require("MapStories/MapStoryCommonTools")

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function newbieGuideInLevel()

    -- CG：20250331 先关闭周常引导
    -- if s.hasUnlockMemorySlot("qyl_21") and newbieApi.getNewbieFlag(newbieFlags.tutorZhouchang) == 0 then
    --     require("Newbie/newbie_guide")
    --     switchToJianzhichuan()
    --     return
    -- end


    -- if(flags.qyl_08_playing == 1) then
    --     capi.finishMemorySlot("qyl_08") --CG：这里先临时这样写，回头要改下API
    --     next_level_step(true)
    --     flags.qyl_08_playing = 0
    -- end

    -- if(flags.qyl_09_playing == 1) then
    --     capi.finishMemorySlot("qyl_09") --CG：这里先临时这样写，回头要改下API
    --     next_level_step(true)
    --     flags.qyl_09_playing = 0
    -- end
end

function start()
    print("start called")
    refreshMapStates()
    if(flags.fire_over < 2)then
        flags.fire1 = 0
        flags.fire2 = 0
        flags.fire3 = 0
    end

    s.setActive(objs.fire1_1,flags.fire1 == 0)
    s.setActive(objs.fire1_2,flags.fire1 == 0)
    s.setActive(objs.fire2_1,flags.fire2 == 0)
    s.setActive(objs.fire3_1,flags.fire3 == 0)
    s.setActive(objs.fire3_2,flags.fire3 == 0)
    s.setActive(objs.fire3_3,flags.fire3 == 0)
    s.setPos(npcs.zhujue, pos.zhujue_start_pos)
    s.readyToStart(true)
    try_execute_newbieGuide()
end


--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点

    s.setActive(npcs.qyl_19, flags.level_step == 1)
    s.setActive(npcs.qyl_20, flags.level_step == 2)
    s.setActive(npcs.qyl_21, flags.level_step == 3)
    s.setActive(npcs.qyl_22, flags.level_step == 4)
    s.setActive(npcs.qyl_23, flags.level_step == 5)

    -- s.setActive(npcs.enemy_01, flags.enemy_01 == 0)
    -- s.setActive(npcs.enemy_02, flags.enemy_02 == 0)
    -- s.setActive(npcs.enemy_03, flags.enemy_03 == 0)
    -- s.setActive(npcs.enemy_04, flags.enemy_04 == 0)

    s.setActive(npcs.npc_01,flags.fire_over < 3)

    s.setActive(npcs.lingling,flags.llzy_step < 3)
    s.setActive(objs.lingdang,flags.llzy_step == 1)

    s.setActive(npcs.lingling2,flags.llzy_step == 3)
    s.setActive(npcs.zhenniang,flags.llzy_step == 3)
    --BOSS解谜状态
    s.setActive(objs.qinggongdian,flags.qinggongdian_over == 1)
    s.setActive(objs.qinggongdianjiemi,flags.qinggongdian_over == 0)
    --精英怪解谜状态
    s.setActive(npcs.elite_05_01,not s.hasUnlockMemorySlot("qyl5_battle_201"))

    s.removeAllGameMapGuides()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 6) then
        taskStep = "forwardToExplore"
    elseif (step == 6) then
        taskStep = "leaveFiredChuJiaZhuangShenJing"
    end
    s.setTaskStep("青羽录火烧楚家庄", taskStep)


    s.playMusic("势归")
    print("refreshMapStates level step = " .. tostring(step))
    reset_all()
    s.unTraceTask(10000001,50)
    if step == 0 then
        ----第一次进入场景, 这里加入入场演出
        s.setTaskStep("青羽录火烧楚家庄","forwardToExplore")
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.qyl_19, 0.8)
        s.removeGameMapGuide(npcs.qyl_19)
        s.addGameMapGuide(npcs.qyl_19Trigger)
    elseif step == 2 then
        level_guide_to(npcs.qyl_20, 0.8)
    elseif step == 3 then
        level_guide_to(npcs.qyl_21, 0.8)
    elseif step == 4 then
        level_guide_to(npcs.qyl_22, 0.8)
    elseif step == 5 then
        level_guide_to(npcs.qyl_23)
    else
        --do nothing
        level_guide_to(objs.exit)
    end

    s.setActive(objs.qyl5_1008,not s.hasUnlockMemorySlot("qyl5_1008"))
    s.setActive(objs.qyl5_1009,not s.hasUnlockMemorySlot("qyl5_1009"))
end

function try_execute_newbieGuide(...)
    if (not s.hasUnlockMemorySlot("qyl_20") and newbieApi.getNewbieFlag(newbieFlags.chapter_qyl_20_finished) == 1) then
        --quick_battle("青羽录2-20", "", function()
        capi.finishMemorySlot("qyl_20")
        next_level_step(true)
        --end)
    end

    if (not s.hasUnlockMemorySlot("qyl_23") and newbieApi.getNewbieFlag(newbieFlags.chapter_qyl_23_finished) == 1) then
        capi.finishMemorySlot("qyl_23")
        next_level_step(true)
    end

    s.nextStep("newbieGuideInLevel")
end

----------------------OVERRIDE----------------------------------


function qyl_19()
    executeMemSlot("qyl_19", function()
        require("Chapters/青羽录").qyl19_01()
    end,function()
        next_level_step(true)
        refreshMapStates()
        try_execute_newbieGuide()
    end)
end

function qyl_20()
    executeMemSlot("qyl_20", function()
        require("Chapters/青羽录").qyl20_01()
    end)
end

function qyl_21()
    executeMemSlot("qyl_21", function()
        require("Chapters/青羽录").qyl21_01()
    end,function()
        next_level_step(true)
        refreshMapStates()
        try_execute_newbieGuide()
    end)
end

function qyl_22()
    executeMemSlot("qyl_22", function()
        require("Chapters/青羽录").qyl22_01()
    end,function()
        next_level_step(true)
        refreshMapStates()
        try_execute_newbieGuide()
    end)
end

function qyl_23()
    executeMemSlot("qyl_23", function() 
        require("Chapters/青羽录").qyl23_01()
        s.setTaskStep("青羽录火烧楚家庄","leaveFiredChuJiaZhuangShenJing") 
    end)
end


function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("青羽录")
end

function fire1()
    s.setActive(objs.fireTrigger1,false)
    local banTb = s.getMapStatusString("fireRoles")
    local rapi = require("RpgMapApi")
    local banTable = {}
    if banTb ~= "" then
        for str in string.gmatch(banTb, "([^,]+)") do
            table.insert(banTable, str)
        end
    end

    --CS.UnityEngine.Debug.LogError(banTable)
    local ret = rapi.submitRoleCardWithInstanceReturn("请选择要派出的侠客",nil,banTable)

    if(ret == nil)then
        s.setActive(objs.fireTrigger1,true)
        return
    end

    if(ret.element ~= "1")then
        rapi.popInfo("好像没什么反应。")
        s.setActive(objs.fireTrigger1,true)
        return
    end

    s.blackScreen()
    flags.fire1 = 1
    if(banTb == "")then
        s.setMapStatus("fireRoles",ret.Key)
    else
        s.setMapStatus("fireRoles",banTb .. "," .. ret.Key)
    end

    s.playSound("sfx","泼水")

    s.setActive(objs.fire1_1,false)
    s.setActive(objs.fire1_2,false)
    s.wait(0.5)
    s.lightScreen()
    testFireOver()
end

function fire2()
    s.setActive(objs.fireTrigger2,false)
    local banTb = s.getMapStatusString("fireRoles")
    local rapi = require("RpgMapApi")
    local banTable = {}
    if banTb ~= "" then
        for str in string.gmatch(banTb, "([^,]+)") do
            table.insert(banTable, str)
        end
    end

    --CS.UnityEngine.Debug.LogError(banTable)
    local ret = rapi.submitRoleCardWithInstanceReturn("请选择要派出的侠客",nil,banTable)

    if(ret == nil)then
        s.setActive(objs.fireTrigger2,true)
        return
    end

    if(ret.element ~= "1")then
        rapi.popInfo("好像没什么反应。")
        s.setActive(objs.fireTrigger2,true)
        return
    end

    s.blackScreen()
    flags.fire2 = 1
    if(banTb == "")then
        s.setMapStatus("fireRoles",ret.Key)
    else
        s.setMapStatus("fireRoles",banTb .. "," .. ret.Key)
    end

    s.playSound("sfx","泼水")

    s.setActive(objs.fire2_1,false)
    s.wait(0.5)
    s.lightScreen()
    testFireOver()
end

function fire3()
    s.setActive(objs.fireTrigger3,false)
    local banTb = s.getMapStatusString("fireRoles")
    local rapi = require("RpgMapApi")
    local banTable = {}
    --print(banTb)
    if banTb ~= "" then
        for str in string.gmatch(banTb, "([^,]+)") do
            table.insert(banTable, str)
        end
    end

    --CS.UnityEngine.Debug.LogError(banTable)
    local ret = rapi.submitRoleCardWithInstanceReturn("请选择要派出的侠客",nil,banTable)

    if(ret == nil)then
        s.setActive(objs.fireTrigger3,true)
        return
    end

    if(ret.element ~= "1")then
        rapi.popInfo("好像没什么反应。")
        s.setActive(objs.fireTrigger3,true)
        return
    end

    s.blackScreen()
    flags.fire3 = 1
    if(banTb == "")then
        s.setMapStatus("fireRoles",ret.Key)
    else
        s.setMapStatus("fireRoles",banTb .. "," .. ret.Key)
    end
    
    s.playSound("sfx","泼水")

    s.setActive(objs.fire3_1,false)
    s.setActive(objs.fire3_2,false)
    s.setActive(objs.fire3_3,false)
    s.wait(0.5)
    s.lightScreen()
    testFireOver()
end

function testFireOver()
    if(flags.fire1 == 1 and flags.fire2 == 1 and flags.fire3 == 1)then
        s.wait(1)
        s.appendAsyncAside(npcs.zhujue,0,"蜃境中的火焰都扑灭了，去看看先前的女子是否好些了吧。","","默认")
        flags.fire_over = 2
    end
end

function fireSoul()
    if(flags.fire_over < 2)then
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_火烧楚家庄","蜃境_青羽录_火烧楚家庄_被火烧的灵魂", nil, 0)
        flags.fire_over = 1
        s.setActive(objs.fireTrigger1,flags.fire1 == 0)
        s.setActive(objs.fireTrigger2,flags.fire2 == 0)
        s.setActive(objs.fireTrigger3,flags.fire3 == 0)
    else
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_火烧楚家庄","蜃境_青羽录_火烧楚家庄_被火烧的灵魂2", nil, 0)
        flags.fire_over = 3
        local capi = require("ChapterApi")
        capi.finishMemorySlot("qyl_question_firingSpirit")
        s.blackScreen()
        s.setActive(npcs.npc_01,false)
        s.wait(0.5)
        s.lightScreen()
    end
end

function llzy1()
    if(flags.llzy_step == 0)then
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_火烧楚家庄","蜃境_青羽录_火烧楚家庄_泠泠之音1", nil, 0)
        s.setTaskStep("青羽录火烧楚家庄支线","findLingDang")
        s.setActive(objs.lingdang,true)
        flags.llzy_step = 1
    elseif(flags.llzy_step < 2)then
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_火烧楚家庄","蜃境_青羽录_火烧楚家庄_泠泠之音1_重复", nil, 0)
    else
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_火烧楚家庄","蜃境_青羽录_火烧楚家庄_泠泠之音3", nil, 0)
        flags.llzy_step = 3
        s.setTaskStep("青羽录火烧楚家庄支线","followGirl")
        s.blackScreen()
        s.setActive(npcs.lingling,false)
        s.setActive(npcs.lingling2,true)
        s.setActive(npcs.zhenniang,true)
        s.wait(0.5)
        s.lightScreen()
    end
end

function llzy2()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_火烧楚家庄","蜃境_青羽录_火烧楚家庄_泠泠之音4", nil, 0)
    local ret = s.battle("蜃境_青羽录5_打珍娘")
    if(ret)then
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_火烧楚家庄","蜃境_青羽录_火烧楚家庄_泠泠之音5", nil, 0)
        local capi = require("ChapterApi")
        flags.llzy_step = 4
        capi.finishMemorySlot("qyl_sideTask_llzy2")
        s.finishTask("40050002",30)
        s.blackScreen()
        s.setActive(npcs.lingling2,false)
        s.setActive(npcs.zhenniang,false)
        s.wait(0.5)
        s.lightScreen()
    end
end

function llzy_lingdang()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_火烧楚家庄","蜃境_青羽录_火烧楚家庄_泠泠之音2", nil, 0)
    flags.llzy_step = 2
    s.setActive(objs.lingdang,false)
    s.setTaskStep("青羽录火烧楚家庄支线","backToGirl")
end


function testBOSS()
    if s.isMale() then
    s.playTimeline(assets.BOSSshow_fly_to_location_1)
    else
        s.playTimeline(assets.BOSSshow_fly_to_location_1_female)  
    end
    s.setPos(npcs.zhujue,pos.fly_to_BOSS)
end

function testBOSS2()
    s.aside("这里似乎是个风眼，想办法打开风眼即可乘风而上")
    local rapi = require("RpgMapApi")
    local ret = rapi.submitStrategyCard("选择能打开风眼的策略卡")
    
    if (ret ~= nil) then
        -- 检查是否使用了正确的策略卡
        if (ret.getSubmitStrategyCardItem(0).Key == "梧叶舞秋风") then
            testBOSS3()
            s.setActive(objs.qinggongdian,true)
            s.setActive(objs.qinggongdianjiemi,false)
            flags.qinggongdian_over = 1
        else
            s.aside("好像没什么用。")
        end
    else
        s.aside("没做好准备，稍后再来也不迟。")
    end
end

function testBOSS3()
    s.blackScreen()
    s.setPos(npcs.zhujue,pos.qinggongdian)
    s.lightScreen()
    if s.isMale() then
        s.playTimeline(assets.BOSSshow_wuyewuqiufeng)
    else
        s.playTimeline(assets.BOSSshow_wuyewuqiufeng_female)
    end
end


function elite_05_01()
    s.aside("这里有个奇怪的冰雕，在不断吸收周围的浊气")
    local rapi = require("RpgMapApi")
    local ret = rapi.submitStrategyCard("选择能融化冰雕的策略卡")
    
    if not ret then
        s.aside("没做好准备，稍后再来也不迟。")
        return
    end

    local cardKey = ret.getSubmitStrategyCardItem(0).Key
    local validCards = {
        ["浴火涅槃"] = {
            male = assets.elite_show_yuhuoniepan,
            female = assets.elite_show_yuhuoniepan_female
        },
        ["光明圣火"] = {
            male = assets.elite_show_guangmingshenghuo,
            female = assets.elite_show_guangmingshenghuo_female
        }
    }

    if validCards[cardKey] then
        local timeline = s.isMale() and validCards[cardKey].male or validCards[cardKey].female
        s.setActive(npcs.elite_05_01, false)
        s.playTimeline(timeline)
        flags.elite_05_over = 1
        s.setActive(npcs.elite_05, true)
    else
        s.aside("好像没什么用。")
    end
end

