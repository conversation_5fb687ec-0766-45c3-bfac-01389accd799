
---@type 副本_醉花荫
local context = require("MapStories/副本_醉花荫/scene_副本_醉花荫_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_副本_醉花荫
local flags = context.flags

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

function test()
    s.changeItemCount("201031",1) -- 增加药材
    s.setTaskStep("燕归无期","deliverLetter")
end

--每次载入调用
function start()
    --各重复演出
    s.playMusic("韶岁度春")
    s.playMusic("bird")
    s.setPos(npcs.zhujue,pos.start)

    --添加燕归无期任务触发器生成判定
    if s.getCurrentTaskStep("燕归无期") == "deliverLetter" then
        s.setActive(objs.branchTask_yqwg_fq, true)
        s.setActive(objs.branchTask_yqwg_fj, false)
        -- s.setActive(objs.branchTask_yqwg_fq, false)
    elseif s.getCurrentTaskStep("燕归无期") == "talkWithFengJian" then
        s.setActive(objs.branchTask_yqwg_fq, false)
        s.setActive(objs.branchTask_yqwg_fj, true)
    end

    --添加医者自医任务触发器生成判定
    if s.getCurrentTaskStep("医者自医") == "startFindScent" then
        s.setActive(objs.branchTask_yzzy_S1, true)
        s.setActive(objs.branchTask_yzzy_S2, false)
        s.setActive(objs.branchTask_yzzy_S6, false)
    elseif s.getCurrentTaskStep("医者自医") == "clinicStinkQuest" then
        s.setActive(objs.branchTask_yzzy_S1, false)
        s.setActive(objs.branchTask_yzzy_S2, true)
        s.setActive(objs.branchTask_yzzy_S6, false)
    elseif s.getCurrentTaskStep("医者自医") == "goBacktoZuihuayin" or s.getCurrentTaskStep("医者自医") == "applyFertilizer" then
        s.setActive(objs.branchTask_yzzy_S1, false)
        s.setActive(objs.branchTask_yzzy_S2, false)
        s.setActive(objs.branchTask_yzzy_S6, true)
        s.setTaskStep("医者自医","applyFertilizer")
    end

    --添加副本内主线任务
    if not s.hasTaskGroup("醉花荫副本内")then
        s.setTaskStep("醉花荫副本内","beatBoss")
    end
    
    --s.readyToStart()

    --如果此次门票已触发过开始对话，则不再触发。
    if(s.getChallengeMapPersistentFlag("副本_醉花荫","触发过开始对话") == 0)then
         s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_首次门口对话", nil, 0)
         s.setChallengeMapTicketFlag("副本_醉花荫","触发过开始对话",1)
     end
end

--触发小怪101战斗
function EnemyBattle1_1(roleKey)
    s.turnTo(npcs.enemy_101,npcs.zhujue)
    s.turnTo(npcs.enemy_101_1,npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs.enemy_101)
    s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_小怪101对话", nil, 0)
    local isWin,bResult = s.challengeMapBattle("101")
end

--触发小怪102战斗
function EnemyBattle1_2(roleKey)
    s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_小怪102对话", nil, 0)
    local isWin,bResult = s.challengeMapBattle("102")
end

--用于随机问题答案选项和记下哪个是正确答案
function shuffleTable(t)
    local randomizedTable = {}
    ---@type string[]
    local pool = {}
    for key, value in pairs(t) do
        print((#pool + 1) .. tostring(value))
        pool[#pool + 1] = value
    end

    -- 使用一个随机函数来打乱数组
    local n = #pool
    -- 如果n为0，则返回原始表，不进行任何操作
    if n == 0 then return t end
    -- 否则，创建一个新的随机顺序的表
    while n > 0 do
        local k = math.random(n)
        n = n - 1
        print("index" .. (#randomizedTable + 1))
        print(pool[k])
        ---@diagnostic disable-next-line: inject-field-fail
        randomizedTable[#randomizedTable + 1] = pool[k]
        pool[k] = pool[n]
        table.remove(pool,n)
    end
    return randomizedTable
end

--触发小怪201、202战斗
function EnemyBattle2(roleKey)
    s.setActive(objs.Trigger_Battle_Enemy_201,false)
    s.setActive(objs.Trigger_Battle_Enemy_202,false)
    s.turnTo(npcs["enemy_" .. roleKey],npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs["enemy_" .. roleKey])
    s.talk(npcs["enemy_" .. roleKey], "诸位请！我是药公弟子，师父命我在此守关。若能答对家师的问题，便可放诸位离去。")
    s.talk(npcs.zhujue, "有礼了，请讲！")

    ---@type string[]
    local answer = {}
    local question1 = math.random(1, 10)
    if(question1 == 1) then
        s.talk(npcs["enemy_" .. roleKey], "哪位药材主用于散寒，祛风，活血？")

        local options = {"党参","山姜","桂皮"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "哪位药材主用于散寒，祛风，活血？", options)
        if(answer[ret+1] == "山姜") then
            Question1Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "非也，是山姜。既如此，不得放诸位通过了！")
            Question1Wrong(roleKey)
        end
    
    elseif(question1 == 2) then
        s.talk(npcs["enemy_" .. roleKey], "哪位药材主用于通便？")

        local options = {"巴豆树根","巴豆叶","巴豆油"}
        options = shuffleTable(options)
        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "哪位药材主用于通便？", options)
        if(answer[ret+1] == "巴豆油") then
            Question1Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "非也，是巴豆油。既如此，不得放诸位通过了！")
            Question1Wrong(roleKey)
        end
    
    elseif(question1 == 3) then
        s.talk(npcs["enemy_" .. roleKey], "哪位药材主用于清热润肺？")

        local options = {"地稔根","罗汉果","粉葛"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "哪位药材主用于清热润肺？", options)
        if(answer[ret+1] == "罗汉果") then
            Question1Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "非也，是罗汉果。既如此，不得放诸位通过了！")
            Question1Wrong(roleKey)
        end
    
    elseif(question1 == 4) then
        s.talk(npcs["enemy_" .. roleKey], "张仲景、李时珍、孙思邈乃三大名医，世人对他们的尊称分别是")

        local options = {"医圣张仲景、药圣李时珍、药王孙思邈","药王张仲景、医圣李时珍、药圣孙思邈","医圣张仲景、药王李时珍、药圣孙思邈","药圣张仲景、药王李时珍、医圣孙思邈"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "张仲景、李时珍、孙思邈乃三大名医，世人对他们的尊称分别是", options)
        if(answer[ret+1] == "医圣张仲景、药圣李时珍、药王孙思邈") then
            Question1Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "非也，是医圣张仲景、药圣李时珍、药王孙思邈。既如此，不得放诸位通过了！")
            Question1Wrong(roleKey)
        end
    
    elseif(question1 == 5) then
        s.talk(npcs["enemy_" .. roleKey], "哪位药材主用于止血？")

        local options = {"艾叶","金银花","马蹄金"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "哪位药材主用于止血？", options)
        if(answer[ret+1] == "艾叶") then
            Question1Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "非也，是艾叶。既如此，不得放诸位通过了！")
            Question1Wrong(roleKey)
        end
    
    elseif(question1 == 6) then
        s.talk(npcs["enemy_" .. roleKey], "哪位药材主用于活血化瘀？")

        local options = {"车前草","五指毛","马钱子"}
        options = shuffleTable(options)
        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "哪位药材主用于活血化瘀？", options)
        if(answer[ret+1] == "马钱子") then
            Question1Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "非也，是马钱子。既如此，不得放诸位通过了！")
            Question1Wrong(roleKey)
        end
    
    elseif(question1 == 7) then
        s.talk(npcs["enemy_" .. roleKey], "哪位药材主用于清热化痰？")

        local options = {"槲寄生","川贝母","罗勒"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "哪位药材主用于清热化痰？", options)
        if(answer[ret+1] == "川贝母") then
            Question1Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "非也，是川贝母。既如此，不得放诸位通过了！")
            Question1Wrong(roleKey)
        end
    
    elseif(question1 == 8) then
        s.talk(npcs["enemy_" .. roleKey], "哪位药材主用于消食化滞？")

        local options = {"桂皮","小茴香","沙棘"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "哪位药材主用于消食化滞？", options)
        if(answer[ret+1] == "沙棘") then
            Question1Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "非也，是沙棘。既如此，不得放诸位通过了！")
            Question1Wrong(roleKey)
        end
    
    elseif(question1 == 9) then
        s.talk(npcs["enemy_" .. roleKey], "哪位药材主用于利尿通淋？")

        local options = {"灯心草","芦荟","黄荆子"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "哪位药材主用于利尿通淋？", options)
        if(answer[ret+1] == "灯心草") then
            Question1Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "非也，是灯心草。既如此，不得放诸位通过了！")
            Question1Wrong(roleKey)
        end
    
    elseif(question1 == 10) then
        s.talk(npcs["enemy_" .. roleKey], "本草纲目乃李时珍医学巨著，共五十二卷，附药图千余幅，请问书中收录药物共多少种？")

        local options = {"九百四十六种","一千两百七十八种","一千八百九十二种"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "本草纲目乃李时珍医学巨著，共五十二卷，附药图千余幅，请问书中收录药物共多少种？", options)
        if(answer[ret+1] == "一千八百九十二种") then
            Question1Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "非也，是一千八百九十二种。既如此，不得放诸位通过了！")
            Question1Wrong(roleKey)
        end
    end
end

function Question1Right(roleKey)
    s.talk(npcs["enemy_" .. roleKey], "不错不错！想不到几位还擅长药理！")
    s.talk(npcs["enemy_" .. roleKey], "几位可以去见家师了！")
end

function Question1Wrong(roleKey)
    local isWin,bResult = s.challengeMapBattle("2")
    if(isWin)then
        s.talk(npcs["enemy_" .. roleKey], "不错不错！几位可以去见家师了！")
    else
        s.setActive(objs.Trigger_Battle_Enemy_201,true)
        s.setActive(objs.Trigger_Battle_Enemy_202,true)
    end
end

--触发Boss1战斗
function BossBattle1()
    --s.camera(cameras.camera_boss1,true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_Boss1战前对话", nil, 0)
    local isWin,bResult = s.challengeMapBattle("1001")
    if(isWin)then
        --s.camera(cameras.camera_boss1,false)
        flags.boss1Defeated = 1

        -- 检查是否是第一次击败Boss1
        if s.getChallengeMapPersistentFlag("副本_醉花荫", "首次击败Boss1") == 0 then
            -- 首次播放后，开启医者自医
            s.runAvgTag("大世界区域三/支线/医者自医", "医者自医_001_战后药人剧情_开启医者自医", nil, 0)
            s.setTaskStep("医者自医","active")
            s.setActive(objs.branchTask_yzzy_S1,true)
            -- 设置标记，表示已经播放过
            s.setChallengeMapPersistentFlag("副本_醉花荫", "首次击败Boss1", 1)
        end

        FinishTask()
    end
end

function FinishTask()
    if(s.getChallengeMapTicketFlag("副本_醉花荫","击败过药公") == 1 and s.getChallengeMapTicketFlag("副本_醉花荫","击败过琴女") == 1 and s.getChallengeMapTicketFlag("副本_醉花荫","击败过诗人") == 1)then
        --s.finishTask(997001,1,2)
        s.wait(1)
        s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_击败3个Boss对话", nil, 0)
        s.setActive(objs.Trigger_Battle_Boss4,true)
    end
end

--触发小怪3战斗
function EnemyBattle3(roleKey)
    s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_小怪3对话", nil, 0)
    local isWin,bResult = s.challengeMapBattle("3")
end

--触发小怪401、402战斗
function EnemyBattle4(roleKey)
    s.setActive(objs.Trigger_Battle_Enemy_401,false)
    s.setActive(objs.Trigger_Battle_Enemy_402,false)
    s.turnTo(npcs["enemy_" .. roleKey],npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs["enemy_" .. roleKey])
    s.talk(npcs["enemy_" .. roleKey], "我是音绝夫人的侍女，几位客人若能答对一道音律之题，便可过去。")
    ---@type string[]
    local answer = {}
    local question2 = math.random(1, 9)
    if(question2 == 1) then
        s.talk(npcs["enemy_" .. roleKey], "请问哪一个为古琴谱？")

        local options = {"百鸟归巢","广陵散","十面埋伏"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "请问哪一个为古琴谱？", options)
        if(answer[ret+1] == "广陵散") then
            Question2Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "不不，应是广陵散。也罢，那请几位出招吧！")
            Question2Wrong(roleKey)
        end
    
    elseif(question2 == 2) then
        s.talk(npcs["enemy_" .. roleKey], "西域乐理，也与中原类似，请问西域中的“Do、Sol”二音，相当于")

        local options = {"宫、徵二音","宫、商二音","角、羽二音","角、徵二音"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "西域乐理，也与中原类似，请问西域中的“Do、Sol”二音，相当于", options)
        if(answer[ret+1] == "宫、徵二音") then
            Question2Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "不不，应是宫、徵二音。也罢，那请几位出招吧！")
            Question2Wrong(roleKey)
        end
    
    elseif(question2 == 3) then
        s.talk(npcs["enemy_" .. roleKey], "五音宫、商、角、徵、羽，依发音部位分为")

        local options = {"宫-齿音 商-唇音 角-舌音 徵-牙音 羽-喉音","宫-喉音 商-舌音 角-牙音 徵-齿音 羽-唇音","宫-牙音 商-喉音 角-齿音 徵-唇音 羽-舌音","宫-喉音 商-齿音 角-牙音 徵-舌音 羽-唇音"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "五音宫、商、角、徵、羽，依发音部位分为", options)
        if(answer[ret+1] == "宫-喉音 商-齿音 角-牙音 徵-舌音 羽-唇音") then
            Question2Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "不不。宫属喉音，其音极长极高极热；商属齿音，其声次长次高次热；角属牙音，其声长短高下冷热之间；徵属舌音，其声次短次下次冷；羽属唇音，其声极短极下极冷。也罢，那请几位出招吧！")
            Question2Wrong(roleKey)
        end
    
    elseif(question2 == 4) then
        s.talk(npcs["enemy_" .. roleKey], "说话乃分平、上、去、入四声，符合其顺序的是")

        local options = {"风起云涌","一马平川","曲高和寡","天子圣哲"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "说话乃分平、上、去、入四声，符合其顺序的是", options)
        if(answer[ret+1] == "天子圣哲") then
            Question2Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "不不，应是天子圣哲。也罢，那请几位出招吧！")
            Question2Wrong(roleKey)
        end
    
    elseif(question2 == 5) then
        s.talk(npcs["enemy_" .. roleKey], "琵琶共有几弦？")

        local options = {"四弦","五弦","六弦"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "琵琶共有几弦？", options)
        if(answer[ret+1] == "四弦") then
            Question2Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "不不，应是四弦。也罢，那请几位出招吧！")
            Question2Wrong(roleKey)
        end
    
    elseif(question2 == 6) then
        s.talk(npcs["enemy_" .. roleKey], "琴与筝，有何区别？")

        local options = {"琴弦数二十一，筝弦数七","琴弦下无品、柱、码","琴大筝小"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "琴与筝，有何区别？", options)
        if(answer[ret+1] == "琴弦下无品、柱、码") then
            Question2Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "不不。琴小筝大，弦数为七，弦下无品、柱、码。也罢，那请几位出招吧！")
            Question2Wrong(roleKey)
        end
    
    elseif(question2 == 7) then
        s.talk(npcs["enemy_" .. roleKey], "俞伯牙和钟子期，所作名曲为")

        local options = {"阳春白雪","高山流水","梅花三弄"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "俞伯牙和钟子期，所作名曲为", options)
        if(answer[ret+1] == "高山流水") then
            Question2Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "不不，应是高山流水。也罢，那请几位出招吧！")
            Question2Wrong(roleKey)
        end
    
    elseif(question2 == 8) then
        s.talk(npcs["enemy_" .. roleKey], "词牌名是乐曲的重要组成部分，苏轼所用最多的词牌名为？")

        local options = {"浣溪沙","定风波","江城子","西江月"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "词牌名是乐曲的重要组成部分，苏轼所用最多的词牌名为？", options)
        if(answer[ret+1] == "浣溪沙") then
            Question2Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "不不，应是浣溪沙。也罢，那请几位出招吧！")
            Question2Wrong(roleKey)
        end
    
    elseif(question2 == 9) then
        s.talk(npcs["enemy_" .. roleKey], "唐宫廷乐舞《秦王破阵乐》，乃是讲述谁的故事？")

        local options = {"嬴政","李世民","项羽"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "唐宫廷乐舞《秦王破阵乐》，乃是讲述谁的故事？", options)
        if(answer[ret+1] == "李世民") then
            Question2Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "不不，应是李世民。也罢，那请几位出招吧！")
            Question2Wrong(roleKey)
        end
    end
end

function Question2Right(roleKey)
    s.talk(npcs["enemy_" .. roleKey], "很好，不想几位音律造诣也是甚高！夫人就在前方等候！")
end

function Question2Wrong(roleKey)
    local isWin,bResult = s.challengeMapBattle("4")
    if(isWin)then
        s.talk(npcs["enemy_" .. roleKey], "很好，不想几位武学造诣甚高！夫人就在前方等候！")
    else
        s.setActive(objs.Trigger_Battle_Enemy_401,true)
        s.setActive(objs.Trigger_Battle_Enemy_402,true)
    end
end

--触发Boss2战斗
function BossBattle2()
    --s.camera(cameras.camera_boss2,true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_Boss2战前对话", nil, 0)
    local isWin,bResult = s.challengeMapBattle("1002")
    if(isWin)then
        --s.camera(cameras.camera_boss2,false)
        flags.boss2Defeated = 1
        FinishTask()
    end
end

--触发小怪501战斗
function EnemyBattle5_1(roleKey)
    s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_小怪501对话", nil, 0)
    local isWin,bResult = s.challengeMapBattle("501")
end

--触发小怪502战斗
function EnemyBattle5_2(roleKey)
    s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_小怪502对话", nil, 0)
    local isWin,bResult = s.challengeMapBattle("502")
end

--触发小怪601、602战斗
function EnemyBattle6(roleKey)
    s.setActive(objs.Trigger_Battle_Enemy_601,false)
    s.setActive(objs.Trigger_Battle_Enemy_602,false)
    s.turnTo(npcs["enemy_" .. roleKey],npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs["enemy_" .. roleKey])
    s.talk(npcs["enemy_" .. roleKey], "几位有礼了，我是先生的弟子，恩师正在前方等候。不过还需诸位答出一问！")
    ---@type string[]
    local answer = {}
    local question3 = math.random(1, 10)
    if(question3 == 1) then
        s.talk(npcs["enemy_" .. roleKey], "“辛弃疾的《破阵子·为陈同甫赋壮词以寄之》中一句，“了却君王天下事”的下半句是？")

        local options = {"何须马革裹尸还","赢得生前身后名","辞君一夜取楼兰"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "“辛弃疾的《破阵子·为陈同甫赋壮词以寄之》中一句，“了却君王天下事”的下半句是？", options)
        if(answer[ret+1] == "赢得生前身后名") then
            Question3Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "应是“了却君王天下事，赢得生前身后名”。可惜，师命在身，只有向诸位请教一番了。")
            Question3Wrong(roleKey)
        end
    
    elseif(question3 == 2) then
        s.talk(npcs["enemy_" .. roleKey], "王昌龄所作《出塞二首》中，“秦时明月汉时关，万里长征人未还。但使龙城飞将在，......”，下半句是？。")

        local options = {"不破楼兰终不还","战罢沙场月色寒","不教胡马度阴山"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "王昌龄所作《出塞二首》中，“秦时明月汉时关，万里长征人未还。但使龙城飞将在，", options)
        if(answer[ret+1] == "不教胡马度阴山") then
            Question3Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "应是“但使龙城飞将在，不教胡马度阴山”。可惜，师命在身，只有向诸位请教一番了。")
            Question3Wrong(roleKey)
        end
    
    elseif(question3 == 3) then
        s.talk(npcs["enemy_" .. roleKey], "“王师北定中原日，家祭无忘告乃翁。”中，乃翁是指？")

        local options = {"陆游","苏轼","辛弃疾"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "“王师北定中原日，家祭无忘告乃翁。”中，乃翁是指？", options)
        if(answer[ret+1] == "陆游") then
            Question3Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "应是陆游。可惜，师命在身，只有向诸位请教一番了。")
            Question3Wrong(roleKey)
        end
    
    elseif(question3 == 4) then
        s.talk(npcs["enemy_" .. roleKey], "人生自古谁无死，留取丹心照汗青”是哪位名士所作？")

        local options = {"岳飞","文天祥","范仲淹"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "人生自古谁无死，留取丹心照汗青”是哪位名士所作？", options)
        if(answer[ret+1] == "文天祥") then
            Question3Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "应是文天祥。可惜，师命在身，只有向诸位请教一番了。")
            Question3Wrong(roleKey)
        end
    
    elseif(question3 == 5) then
        s.talk(npcs["enemy_" .. roleKey], "戚继光的《韬钤深处》，“但愿海波平”的上一句是？")

        local options = {"封侯非我意","功名万里外","捐躯赴国难"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "戚继光的《韬钤深处》，“但愿海波平”的上一句是？", options)
        if(answer[ret+1] == "封侯非我意") then
            Question3Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "应是“封侯非我意，但愿海波平”。可惜，师命在身，只有向诸位请教一番了。")
            Question3Wrong(roleKey)
        end
    
    elseif(question3 == 6) then
        s.talk(npcs["enemy_" .. roleKey], "“楚虽三户，亡秦必楚”最早出自哪本史书？")

        local options = {"资治通鉴","春秋","史记"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "“楚虽三户，亡秦必楚”最早出自哪本史书？", options)
        if(answer[ret+1] == "史记") then
            Question3Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "应是《史记》。可惜，师命在身，只有向诸位请教一番了。")
            Question3Wrong(roleKey)
        end
    
    elseif(question3 == 7) then
        s.talk(npcs["enemy_" .. roleKey], "“大方无隅；大器免成；大音希声；大象无形”，这是谁的名言？")

        local options = {"老子","孔子","墨子"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "“大方无隅；大器免成；大音希声；大象无形”，这是谁的名言？", options)
        if(answer[ret+1] == "老子") then
            Question3Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "应是老子，出自《老子·德经·第四十一章》。可惜，师命在身，只有向诸位请教一番了。")
            Question3Wrong(roleKey)
        end
    
    elseif(question3 == 8) then
        s.talk(npcs["enemy_" .. roleKey], "“空穴来风”的原意是指？")

        local options = {"无中生有","班班可考","流言蜚语"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "“空穴来风”的原意是指？", options)
        if(answer[ret+1] == "班班可考") then
            Question3Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "应是“班班可考”，此语原指“开了空穴，便会有风”，诸如世俗常说“穿堂风”。可惜，师命在身，只有向诸位请教一番了。")
            Question3Wrong(roleKey)
        end
    
    elseif(question3 == 9) then
        s.talk(npcs["enemy_" .. roleKey], "“礼、乐、射、御、书、数”六艺中，“御”是指？")

        local options = {"御人之道","抵御入侵","驾驶马车"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "“礼、乐、射、御、书、数”六艺中，“御”是指？", options)
        if(answer[ret+1] == "驾驶马车") then
            Question3Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "应是“驾驶马车”。共有五御，鸣和鸾：谓行车时和鸾之声相应；逐水车：随曲岸疾驰而不坠水；过君表：经过天子的表位有礼仪；舞交衢：过通道而驱驰自如；逐禽左：行猎时追逐禽兽从左面射获。可惜，师命在身，只有向诸位请教一番了。")
            Question3Wrong(roleKey)
        end
    
    elseif(question3 == 10) then
        s.talk(npcs["enemy_" .. roleKey], "“仁义”中，“义”之一字，应作和解？")

        local options = {"义，宜也","解作义气也","与仁同义，德道也"}
        options = shuffleTable(options)

        for k,v in ipairs(options) do
            answer[k] = v
        end
        
        local ret = s.selectTalk(npcs.zhujue, "“仁义”中，“义”之一字，应作和解？", options)
        if(answer[ret+1] == "义，宜也") then
            Question3Right(roleKey)
        else
            s.talk(npcs["enemy_" .. roleKey], "义，宜也。裁制事物，使合宜也。可惜暂非同道，师命在身，只有向诸位请教一番了。")
            Question3Wrong(roleKey)
        end
    end
end

function Question3Right(roleKey)
    s.talk(npcs["enemy_" .. roleKey], "善！几位请，家师以备下茶水相迎！")
end

function Question3Wrong(roleKey)
    local isWin,bResult = s.challengeMapBattle("6")
    if(isWin)then
        s.talk(npcs["enemy_" .. roleKey], "善！几位请！")
    else
        s.setActive(objs.Trigger_Battle_Enemy_601,true)
        s.setActive(objs.Trigger_Battle_Enemy_602,true)
    end
end

--触发Boss3战斗
function BossBattle3()
    --s.camera(cameras.camera_boss3,true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_Boss3战前对话", nil, 0)
    local isWin,bResult = s.challengeMapBattle("1003")
    if(isWin)then
        --s.camera(cameras.camera_boss3,false)
        flags.boss3Defeated = 1
        FinishTask()
    end
end

--触发Boss4战斗
function BossBattle4()
    --s.camera(cameras.camera_boss4,true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_Boss4战前对话_1", nil, 0)
    s.blackScreen()
    s.wait(0.5)
    s.setActive(npcs.boss_4,true)
    s.lightScreen()
    s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_Boss4战前对话_2", nil, 0)
    local isWin,bResult = s.challengeMapBattle("1004")
    if(isWin)then
        --s.camera(cameras.camera_boss4,false)
        s.setActive(npcs.boss_4,false)
        flags.boss4Defeated = 1

        s.runAvgTag("副本/副本_醉花荫/副本_醉花荫", "副本_醉花荫_Boss4战后对话", nil, 0)

        --结束副本内主线任务
        if s.hasTaskGroup("醉花荫副本内")then
            s.setTaskStep("醉花荫副本内","leaveScene")
        end
        s.setActive(objs.Trigger_Leave,true)
        s.completeChallengeMap()
    else
        s.setActive(npcs.boss_4,false)
    end
end

--场景出口
function Leave()
    s.showConfirmResetChallengeMap()
    if s.getCurrentTaskStep("醉花荫副本内") == "leaveScene" then
        s.setTaskStep("醉花荫副本内","finished")
    end
end

-- 支线：医者自医 --author：冬牧 2025-04-22
-- 医者自医Step1 触发器播放草药剧情
function bt_yzzy_S1()
    s.setActive(objs.branchTask_yzzy_S1,false)
    s.setActive(objs.branchTask_yzzy_S2,true)
    s.turnToAsync(npcs.zhujue,objs.branchTask_yzzy_S2)
    s.soloAnimState(npcs.zhujue,"BScratch",true)
    s.runAvgTag("大世界区域三/支线/医者自医", "医者自医_002_路过紫色药田触发剧情", nil, 0)
    s.setTaskStep("医者自医","startFindScent")
end

-- 医者自医Step2 紫色药田交互剧情
function bt_yzzy_S2()
    s.setActive(objs.branchTask_yzzy_S2,false)
    s.soloAnimState(npcs.zhujue,"BSquat_Loop",true)
    s.showLoading("采摘草药", 3)
    s.soloAnimState(npcs.zhujue,"BSquat_Loop",false)
    s.blackScreen()
    s.runAvgTag("大世界区域三/支线/医者自医", "医者自医_002_别碰！", nil, 0)
    s.setActive(npcs.branchTask_S2_npc,true)
    s.lightScreen()
    s.moveAsync(npcs.branchTask_S2_npc,pos.Pos_S2npc,1,true)
    s.turnToAsync(npcs.zhujue,npcs.branchTask_S2_npc)
    s.turnToAsync(npcs.branchTask_S2_npc,npcs.zhujue)
    s.runAvgTag("大世界区域三/支线/医者自医", "医者自医_003_和紫色药田交互后剧情A", nil, 0)
    s.aside("说话间药老将指尖缓缓伸入土壤，双手小心环绕着深埋土里的根茎。")
    s.runAvgTag("大世界区域三/支线/医者自医", "医者自医_003_和紫色药田交互后剧情B", nil, 0)
    s.soloAnimState(npcs.branchTask_S2_npc,"BSquat_Loop",false)
    s.runAvgTag("大世界区域三/支线/医者自医", "医者自医_003_和紫色药田交互后剧情C", nil, 0)
    s.wait(1)
    s.moveAsync(npcs.branchTask_S2_npc,pos.Pos_S2npc_1,1,true)
    s.blackScreen()
    s.setActive(npcs.branchTask_S2_npc,false)
    s.lightScreen()
    s.runAvgTag("大世界区域三/支线/医者自医", "医者自医_003_和紫色药田交互后剧情D", nil, 0)
    s.popInfo("你获得了一株药人的药材")
    s.changeItemCount("201035",1)
    s.setTaskStep("医者自医","clinicStinkQuest")
end

-- 医者自医Step6 回来施肥触发剧情
function bt_yzzy_S6()
    if s.getCurrentTaskStep("医者自医") == "applyFertilizer" then
        s.setActive(objs.branchTask_yzzy_S6,false)
        local submit = s.submitItem("给药材施肥")
        if submit ~= nil and submit[0].Key == "201034" then
            s.soloAnimState(npcs.zhujue,"BSquat_Loop",true)
            s.showLoading("施肥中", 3)
            s.soloAnimState(npcs.zhujue,"BSquat_Loop",false)
            s.changeItemCount("201034",-1)
            s.blackScreen()
            s.setActive(npcs.branchTask_S6_npc,true)
            s.setPos(npcs.branchTask_S6_npc,pos.Pos_S6npc)
            s.turnToAsync(npcs.zhujue,npcs.branchTask_S6_npc)
            s.turnToAsync(npcs.branchTask_S6_npc,npcs.zhujue)
            s.lightScreen()
            s.runAvgTag("大世界区域三/支线/医者自医", "医者自医_005_玩家回到紫色药田施肥后触发剧情", nil, 0)
            s.blackScreen()
            s.setActive(npcs.branchTask_S6_npc,false)
            s.lightScreen()
            s.setTaskStep("医者自医","finished")
        else
            s.setActive(objs.branchTask_yzzy_S6,true)
        end
    end
end

-- 支线：燕归无期 --author：冬牧 2025-04-22
function bt_ygwq_fq()
    if s.getCurrentTaskStep("燕归无期") == "deliverLetter" then
        s.setActive(objs.branchTask_yqwg_fq,false)
        local submit = s.submitItem("交付信件")
        if submit ~= nil and submit[0].Key == "201031" then
            s.soloAnimState(npcs.zhujue,"BGreet",true)
            s.runAvgTag("大世界区域三/支线/燕归无期", "燕归无期_005_返回醉花荫与抚琴交互", nil, 0)
            s.changeItemCount("201031",-1)
            s.setActive(objs.branchTask_yqwg_fj,true)
            s.setTaskStep("燕归无期","talkWithFengJian")
        else
            s.setActive(objs.branchTask_yqwg_fq,true)
        end
    end
end

function bt_ygwq_fj()
    s.setActive(objs.branchTask_yqwg_fj,false)
    s.runAvgTag("大世界区域三/支线/燕归无期", "燕归无期_006_返回醉花荫与奉剑交互", nil, 0)
    s.changeItemCount("201032", 1)
    s.setTaskStep("燕归无期","seeLadyAgain")
end


-- 进入场景时判断：是否处于deliverLetter任务状态，若是则生成branchTask_ygwq_fq