
---@type 蜃境_长生劫_06决战之地
local context = require("MapStories/蜃境_长生劫_06决战之地/scene_蜃境_长生劫_06决战之地_h")


local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger
s.loadFlags(context.flags)

s.loadFlags(context.flags)
---@type flags_蜃境_长生劫_06决战之地

local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")


----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

--精英怪战斗
function eliteBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

--boss战斗
function bossBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

--最终boss战斗
function finalBossBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end


--小怪通用战斗指令
function battle(id)
    s.roamMapBattle(id)       
end

function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数
    reset_all()

    --第一次进入场景
    if step == 0 then
        s.setTaskStep("长生劫决战之地","forwardToExplore")
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.csj_21, 1)
    elseif step == 2 then
        level_guide_to(npcs.csj_22, 1)
    elseif step == 3 then
        level_guide_to(npcs.csj_23, 1)
    elseif step == 4 then
        level_guide_to(npcs.csj_24, 1)
    else
        level_guide_to(objs.exit)
    end
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.csj_21, false)
    s.setActive(npcs.csj_22, false)
    s.setActive(npcs.csj_23, false)
    s.setActive(npcs.csj_24, false)

    --收集物
    s.setActive(objs.csj_1010, not s.hasUnlockMemorySlot("csj_1010")  and get_level_step() >= 4)
    --删除所有的引导点
    s.removeAllGameMapGuides()
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("长生劫")
end

-------------------关卡记忆碎片和战斗实现---------------------

function csj_21()
    executeMemSlot("csj_21", quick_play_avg)
end

function csj_22()
    executeMemSlot("csj_22", quick_play_avg)
end

function csj_23()
    executeMemSlot("csj_23", function()
        s.playTimelineScene("Assets/GameScenes/动画演出场景/002椿岁之章/主线_长生劫7_狂战.unity")
    end)
end

function csj_24()
    executeMemSlot("csj_24", function()
        quick_play_avg()
        s.playTimelineScene("Assets/GameScenes/动画演出场景/002椿岁之章/主线_长生劫7_信.unity")
    end)
    s.setTaskStep("长生劫决战之地","leave")
end