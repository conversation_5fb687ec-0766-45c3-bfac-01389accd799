
---@type 挑战_夜战开封
local context = require("MapStories/挑战_夜战开封/scene_挑战_夜战开封_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_挑战_夜战开封
local flags = context.flags
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.setMapStatus("当前时辰",GetTime())
    s.setMapStatus("剩余战斗时间",flags.RemainTime .. "秒")

    --添加副本内主线任务
    if not s.hasTaskGroup("夜战开封副本内")then
        s.setTaskStep("夜战开封副本内","beatBoss")
    end

    --处理断线重连
    ResetData()

    --重复演出
    s.playMusic("tense2")

    --一次性演出
    if(s.getChallengeMapPersistentFlag("挑战_夜战开封","开场剧情") == 0)then
        s.setChallengeMapPersistentFlag("挑战_夜战开封","开场剧情",1)

        --演出准备
        s.camera(cameras.start_camera, true)
        s.setPos(npcs.zhujue, pos.start)
        s.setPos(npcs.yanluchong, pos.yanluchong_initPos)
  
        s.readyToStart()

        s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_首次门口对话_1", nil, 0)
        s.turnToAsync(npcs.zhujue, npcs.yanluchong)
        s.turnToAsync(npcs.yanluchong, npcs.zhujue)
        s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_首次门口对话_2", nil, 0)

        --如果有燕路重这张卡，则获得一张策略牌，记录flag
        if(s.formationHasHeroCard("燕路重")) then
            s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_首次门口对话_3_1", nil, 0)
            flags.isGetCard = 1
        else
            --如果没有燕路重这张卡，则获得燕路重召唤物，记录flag
            s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_首次门口对话_3_2", nil, 0)
            flags.isSummon = 1
        end

        s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_首次门口对话_4", nil, 0)
        s.cameraAsync(cameras.start_camera, false,true,blendHintEnum.EaseInOut,1,true,false)
    else
        if(s.formationHasHeroCard("燕路重")) then
            --如果有燕路重这张卡，则获得一张策略牌，记录flag
            flags.isGetCard = 1
        else
            --如果没有燕路重这张卡，则获得燕路重召唤物，记录flag
            flags.isSummon = 1
        end

        s.readyToStart()
    end
end

function ResetData()
    --周五：胡老海一开始先关了，演出再出现，否则就显示出了名字——2025.2.27修复BUG
    s.setActive(npcs.boss1, false)

    if(flags.Boss1Defeated == 1)then
        s.setPos(npcs.boss5, pos.boss2_bossPos)
        s.setActive(npcs.boss1, false)
    end

    if(flags.Boss2Defeated == 1)then
        s.setPos(npcs.boss5,pos.boss3_bossPos)
        s.setActive(npcs.boss2, false)
    end

    if(flags.Boss3Defeated == 1)then
        s.setPos(npcs.boss5,pos.boss5_bossPos)
        s.setActive(npcs.boss3, false)
    end

    if(flags.Boss4Defeated == 1)then
        s.setPos(npcs.boss5,pos.boss5_bossPos)
        s.setActive(npcs.boss4, false)
    end

    if(flags.Boss5Defeated == 1)then
        s.setPos(npcs.boss5,pos.boss5_bossPos)
        s.setActive(npcs.boss5, false)
        s.setActive(objs.quit,true)
    end
end

function GetTime()
    local pastTime = 900 - toNumber(flags.RemainTime)
    local timePeriods = {"丑时", "寅时", "卯时", "辰时"}
    local periodIndex = math.floor(pastTime / 300) + 1
    --print("INDEX"..periodIndex)
    if(periodIndex < 1) then
        periodIndex = 1
    end
    local period = timePeriods[periodIndex]
    --print("period" ..period)
    local periodDetail = pastTime % 300

    local detailText = ""
    if periodDetail < 75 then
        detailText = ""
    elseif periodDetail < 150 then
        detailText = "一刻"
    elseif periodDetail < 225 then
        detailText = "两刻"
    else
        detailText = "三刻"
    end

    -- if periodIndex < #timePeriods then
    --     if periodDetail >= 225 then
    --         period = "接近" .. timePeriods[periodIndex + 1]
    --         detailText = ""
    --     end
    -- else
    --     if periodDetail >= 225 then
    --         period = "接近" .. timePeriods[1]
    --         detailText = ""
    --     end
    -- end

    local currentTime = period .. detailText
    return currentTime
end

function boss1Trigger()
    --周五：胡老海一开始先关了，演出再出现，否则就显示出了名字——2025.2.27修复BUG
    s.setActive(npcs.boss1, true)
    s.cameraAsync(cameras.boss1_camera, true, true, blendHintEnum.EaseInOut, 1, true, true)
    s.moveAsync(npcs.zhujue, pos.boss1_zhujuePos, 4,true)
    s.moveAsync(npcs.yanluchong, pos.boss1_yanluchongPos, 4,true)
    s.wait(1)
    s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss1战前对话_1", nil, 0)
    s.animState(npcs.boss1, "Special_Loop_1", true, false, 0, false, false, 0.6)
    s.wait(0.2)
    s.setPos(npcs.boss1, pos.boss1_hulaohaiPos_1)
    s.wait(1)
    s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss1战前对话_2", nil, 0)
    battleBack()

    local battleTb = {}
    battleTb["战斗时间"] = flags.RemainTime
    battleTb["是否召唤"] = flags.isSummon
    battleTb["是否策略卡"] = flags.isGetCard

    local isWin, bResult = s.challengeMapBattle("101",battleTb)
    if isWin then
        s.camera(cameras.boss1_camera, false)
        s.setDeath(npcs.boss1)
        -- s.animState(npcs.boss1, "BDie",true)
        flags.Boss1Defeated = 1

        --重复演出
        s.setPos(npcs.boss5, pos.boss2_bossPos)
        s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss1战后对话_1", nil, 0)

        refreshTime(bResult)
    else
        handleBattleFailure(cameras.boss1_camera,npcs.zhujue,pos.boss1_revive_zhujue,npcs.yanluchong,pos.boss1_revive_yanluchong)
    end
end

function refreshTime(bResult)
    --如果获胜了，需要更新战斗时间
    --更新剩余时间，扣除战斗时长
    local battleDuration = math.floor(bResult.BattleDuration)
    flags.RemainTime = flags.RemainTime - battleDuration
    s.setMapStatus("当前时辰",GetTime())
    s.setMapStatus("剩余战斗时间",flags.RemainTime .. "秒")
end

function boss2Trigger()
    s.camera(cameras.boss2_camera1, true)
    s.setPos(npcs.zhujue, pos.boss2_zhujuePos)
    s.setPos(npcs.yanluchong, pos.boss2_yanluchongPos)
    s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss2战前对话_1", nil, 0)
    s.animState(npcs.boss2, "Special_1", true, false, 0, true, true, 0)
    s.wait(1)
    s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss2战前对话_2", nil, 0)
    s.camera(cameras.boss2_camera2, true, true, blendHintEnum.EaseInOut, 1, true, true)
    s.turnTo(npcs.boss2,pos.boss2_lookPos)
    s.turnTo(npcs.boss5,pos.boss2_lookPos)
    s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss2战前对话_3", nil, 0)
    battleBack()

    local battleTb = {}
    battleTb["战斗时间"] = flags.RemainTime
    battleTb["是否召唤"] = flags.isSummon
    battleTb["是否策略卡"] = flags.isGetCard

    local isWin, bResult = s.challengeMapBattle("102",battleTb)
    if isWin then
        s.camera(cameras.boss2_camera2, false)
        s.setActive(npcs.boss2, false)
        flags.Boss2Defeated = 1

        --重复演出
        s.setPos(npcs.boss5,pos.boss3_bossPos)
        s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss2战后对话", nil, 0)

        refreshTime(bResult)
    else
        handleBattleFailure(cameras.boss2_camera2,npcs.zhujue,pos.boss2_revive_zhujue,npcs.yanluchong,pos.boss2_revive_yanluchong)
    end
end

function boss3Trigger()
    s.camera(cameras.boss3_camera1, true)
    s.setPos(npcs.zhujue, pos.boss3_zhujuePos)
    s.setPos(npcs.yanluchong, pos.boss3_yanluchongPos)
    s.moveAsync(npcs.zhujue, pos.boss3_zhujuePos2, 1.5, true)
    s.moveAsync(npcs.yanluchong, pos.boss3_yanluchongPos2, 1.5, true)
    s.wait(1.5)
    s.animState(npcs.boss3, "Special_1", true, false, 0, true, true, 0)
    s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss3战前对话", nil, 0)

    local battleTb = {}
    battleTb["战斗时间"] = flags.RemainTime
    battleTb["是否召唤"] = flags.isSummon
    battleTb["是否策略卡"] = flags.isGetCard
    battleBack()

    local isWin, bResult = s.challengeMapBattle("103",battleTb)
    if isWin then
        s.camera(cameras.boss3_camera2, true)
        s.setActive(npcs.boss3, false)
        flags.Boss3Defeated = 1

        s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss3战后对话_1", nil, 0)
        s.animState(npcs.boss5, "Special_Loop_1",true,false,0,true,true,0)
        s.wait(0.5)
        s.setPos(npcs.boss5,pos.boss5_bossPos)
        s.camera(cameras.boss3_camera2, false,true, blendHintEnum.EaseInOut, 1.3, true, true)
        s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss3战后对话_2", nil, 0)

        refreshTime(bResult)
    else
        handleBattleFailure(cameras.boss3_camera2,npcs.zhujue,pos.boss3_revive_zhujue,npcs.yanluchong,pos.boss3_revive_yanluchong)
    end
end

function boss4Trigger()
    s.camera(cameras.boss4_camera1, true)
    s.setPos(npcs.zhujue, pos.boss4_zhujuePos)
    s.setPos(npcs.yanluchong, pos.boss4_yanluchongPos)
    s.moveAsync(npcs.zhujue, pos.boss4_zhujuePos2, 1.5, true)
    s.moveAsync(npcs.yanluchong, pos.boss4_yanluchongPos2, 1.5, true)
    s.wait(1.25)
    s.animState(npcs.boss4, "BGreet",true)
    s.wait(1)
    s.camera(cameras.boss4_camera2, true, true, blendHintEnum.EaseInOut, 1.5, true, true)
    s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss4战前对话", nil, 0)
    local battleTb = {}
    battleTb["战斗时间"] = flags.RemainTime
    battleTb["是否召唤"] = flags.isSummon
    battleTb["是否策略卡"] = flags.isGetCard
    battleBack()

    local isWin, bResult = s.challengeMapBattle("104",battleTb)
    if isWin then
        s.camera(cameras.boss4_camera2, false)
        s.setActive(npcs.boss4, false)       
        flags.Boss4Defeated = 1

        s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss4战后对话", nil, 0)

        refreshTime(bResult)
    else
        handleBattleFailure(cameras.boss4_camera2,npcs.zhujue,pos.boss4_revive_zhujue,npcs.yanluchong,pos.boss4_revive_yanluchong)
    end
end

function boss5Trigger()
    s.setActive(objs.boss5Effect, false)
    s.camera(cameras.boss5_camera1, true)
    s.setPos(npcs.zhujue, pos.boss5_zhujuePos)
    s.setPos(npcs.yanluchong, pos.boss5_yanluchongPos)
    s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss5战前对话_1", nil, 0)
    s.camera(cameras.boss5_camera2, true, true, blendHintEnum.EaseInOut, 1.5, true, true)
    s.soloAnimState(npcs.boss5, "BLaugh", true)
    s.wait(2)
    s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss5战前对话_2", nil, 0)
    s.soloAnimState(npcs.boss5, "Special_3", true)
    s.wait(2)
    s.setActive(objs.boss5Effect, true)
    s.wait(2)
    s.camera(cameras.boss5_camera2, false, true, blendHintEnum.EaseInOut, 1.5, true, true)
    s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss5战前对话_3", nil, 0)
    battleBack()

    local battleTb = {}
    battleTb["战斗时间"] = flags.RemainTime
    battleTb["是否召唤"] = flags.isSummon
    battleTb["是否策略卡"] = flags.isGetCard

    local isWin, bResult = s.challengeMapBattle("105",battleTb)
    if isWin then
        s.camera(cameras.boss5_camera1, false)
        s.setActive(npcs.boss5, false)
        flags.Boss5Defeated = 1

        if s.getCurrentTaskStep("湮天记") == "findMurdererInKaiFeng" then
            s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_越千山BOSS战斗正式剧情", nil, 0)
            require("RpgMap/任务线/湮天记").findMurdererInKaiFeng()
        else
            s.runAvgTag("副本/副本_夜战开封/副本_夜战开封", "副本_夜战开封_Boss5战后对话", nil, 0)
        end

        refreshTime(bResult)

        --结束副本内主线任务
        if s.hasTaskGroup("夜战开封副本内")then
            s.setTaskStep("夜战开封副本内","leaveScene")
        end

        --显示离开副本的传送点
        s.setActive(objs.quit,true)
        s.completeChallengeMap()
    else
        handleBattleFailure(cameras.boss5_camera1,npcs.zhujue,pos.boss5_revive_zhujue,npcs.yanluchong,pos.boss5_revive_yanluchong)
    end
end

--进入酒楼
function enterBossArea()
    s.blackScreen()
    s.setPos(npcs.zhujue, pos.bossAreaEnter_zhujue)
    s.setPos(npcs.yanluchong, pos.bossAreaEnter_yanluchong)
    s.lightScreen()
end

--离开酒楼
function exitBossArea()
    s.blackScreen()
    s.setPos(npcs.zhujue, pos.bossAreaQuit_zhujue)
    s.setPos(npcs.yanluchong, pos.bossAreaQuit_yanluchong)
    s.lightScreen()
end

--离开副本
function quitDungeon()
    s.showConfirmResetChallengeMap()
    if s.getCurrentTaskStep("夜战开封副本内") == "leaveScene" then
        s.setTaskStep("夜战开封副本内","finished")
    end
end

--和燕路重对话演出
function TalkToYanLuChong()
    s.talk(npcs.zhujue,"燕兄。")

    local selectTable = {}
    table.insert(selectTable, "你对现在的情况怎么看？")
    table.insert(selectTable, "我们还剩余多少时辰？")
    table.insert(selectTable, "没事了。")
    local ret = s.selectTalk(npcs.zhujue,"",selectTable)
    if ret == 0 then
        local textTable = {}
        table.insert(textTable, "……越千山为了今天谋划已久，如若不是我们以身入局，识破他的阴谋，恐怕他已经得逞。这样的敌人，我们必须要小心应对。")
        table.insert(textTable, "越千山身为武林盟主，不止他自己武功极高，其手下也定有不少高手为其卖命————说不定还有曾经我的好友。")
        table.insert(textTable, "温捕头能发动官府的势力封城一夜，已是做出了极大的贡献，而现在————就要看我们的了。")
        table.insert(textTable, "野心，权力，真的有那么重要吗？可以让人草营人命，丧失理智与人性。")
        table.insert(textTable, "今夜必有数场硬仗，但是请放心，我一定与你并肩作战到底。")
        local index = math.random(1, #textTable)
        s.talk(npcs.yanluchong, textTable[index])
    elseif ret == 1 then
        local time = 6
        local pastTime = 900 - toNumber(flags.RemainTime)

        local timePeriods = {"丑时", "寅时", "卯时", "辰时"}
        local periodIndex = math.floor(pastTime / 300) + 1
        local period = timePeriods[periodIndex]
        local periodDetail = pastTime % 300

        local detailText = ""
        if periodDetail < 75 then
            detailText = ""
        elseif periodDetail < 150 then
            detailText = "一刻"
        elseif periodDetail < 225 then
            detailText = "两刻"
        else
            detailText = "三刻"
        end

        -- if periodIndex < #timePeriods then
        --     if periodDetail >= 225 then
        --         period = "接近" .. timePeriods[periodIndex + 1]
        --         detailText = ""
        --     end
        -- else
        --     if periodDetail >= 225 then
        --         period = "接近" .. timePeriods[1]
        --         detailText = ""
        --     end
        -- end

        local currentTime = period .. detailText
        local remainTimeText = ""
        local remainTime = toNumber(flags.RemainTime)
        --print("remainTime" .. remainTime)
        local remainPeriods = tostring(math.floor(remainTime / 300))
        --print("remainPeriods1" .. remainPeriods)
        local remainPeriodDetail = remainTime % 300

        local remainPeriodText = ""
        local preText = ""
        if remainPeriodDetail < 75 then
            if(tonumber(remainPeriods) > 0) then
                preText = "接近"
            else
                remainPeriodText = "不足一刻"
            end
        elseif remainPeriodDetail < 150 then
            if(tonumber(remainPeriods) > 0) then
                remainPeriodText = "一刻多"
            else
                remainPeriodText = "不足两刻"
            end
        elseif remainPeriodDetail < 225 then
            if(tonumber(remainPeriods) > 0) then
                remainPeriodText = "两刻多"
            else
                remainPeriodText = "不足三刻"
            end
        else
            if(tonumber(remainPeriods) > 0) then
                remainPeriodText = "三刻多"
            else
                remainPeriodText = "不足1个时辰"
            end
        end

        --print("remainPeriods2" .. remainPeriods)
        if tonumber(remainPeriods) > 0 then
            remainTimeText = preText .. remainPeriods .. "个时辰" .. remainPeriodText
        else
            remainTimeText = remainPeriodText
        end
        s.talk(npcs.yanluchong, "此时是" .. currentTime .. "，距离辰时还有" .. remainTimeText .. "。(总战斗时间剩余" .. flags.RemainTime .. "秒)")
    end
end

--和士兵对话演出
function SbTalk()
    local sbDialogs = {
        PlayerName .. "少侠，各个主干要道已经封锁，请放心追捕凶手吧！",
        "目前情况一切正常，其余的就全仰仗少侠了。",
        "一直在开封府当差，没想到有一天我也能参与这样大的事件。",
        "开封府的商户和市民我们都已通知到位，避免影响少侠今晚的行动。",
        "越千山毕竟是武林盟主，少侠还请多加小心。",
    }
    local randomIndex = math.random(1, #sbDialogs)
    s.talk(npcs.sb, sbDialogs[randomIndex])
end

--战斗失败处理
function handleBattleFailure(cameras,npcs1,pos1,npcs2,pos2)
    s.blackScreen()
    s.camera(cameras, false)
    s.setPos(npcs1, pos1)
    s.setPos(npcs2, pos2)
    s.wait(1)
    s.lightScreen()
end

--从战斗场景切回来处理逻辑
function battleBack(...)
    s.setActive(npcs.boss1, false)
end