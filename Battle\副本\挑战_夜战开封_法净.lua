local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    local time = 903
    if(bcapi.get_team_dict_has_key(0, "战斗时间"))then
        time = bcapi.get_team_dict_int_value(0, "战斗时间", time) + 3
    end
    bcapi.reset_limit_time(time)
end

function start()
    bcapi.async_call(function()
        local isSummon = 0
        local isCard = 0

        if(bcapi.get_team_dict_has_key(0, "是否召唤"))then
            isSummon = bcapi.get_team_dict_int_value(0, "是否召唤", 0)
        end
        if(bcapi.get_team_dict_has_key(0, "是否策略卡"))then
            isCard = bcapi.get_team_dict_int_value(0, "是否策略卡", 0)
        end

        if(isSummon == 1)then
            bcapi.create_and_summon_role("副本_夜战开封_燕路重", 65, bcapi.get_team(0), bcapi.Vector2(-6, 0), 0.1,-1,1,1,1,1,1,"")
        end
        if(isCard == 1)then
            bcapi.create_and_summon_role("副本_夜战开封_开封士兵", 55, bcapi.get_team(0), bcapi.Vector2(-6, 2), 0.1,-1,1,1,1,1,1,"")
            bcapi.create_and_summon_role("副本_夜战开封_开封士兵", 55, bcapi.get_team(0), bcapi.Vector2(-6, -2), 0.1,-1,1,1,1,1,1,"")
        end
        bcapi.add_timer_trigger(2, StartTalk)

        bcapi.add_timer_trigger(20, Summon1)
        bcapi.add_timer_trigger(60, Summon2)
        bcapi.add_timer_trigger(100, Summon3)
    end)
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("法净#副本_夜战开封_法净", "阿弥陀佛，小僧擅长横练之法，两位请多加小心了。")
        bcapi.resume_and_show_ui()

        bcapi.add_condition_trigger("[%enemy:副本_夜战开封_法净->hp_pct%][<=]0.5", 1, EnemyStageTwo)
    end)
end

function EnemyStageTwo()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("法净#副本_夜战开封_法净", "阿弥陀佛……小僧曾在少林寺中修习，得高僧前辈传授一门口诀，不想竟在此时派上用场……")
        bcapi.talk("法净#副本_夜战开封_法净", "两位施主，小僧也是受人所托，忠人之事，还请不要过分为难小僧了！")
        bcapi.resume_and_show_ui()

        local Boss = bcapi.get_role(1, "副本_夜战开封_法净")
        Boss.Stat.BattleFlagDic:set_Item("可以变身", "1")

        bcapi.wait(1)
        bcapi.show_pop_info("法净莲台清明，获得超强御状态！！",4)
    end)
end

function Summon1()
    bcapi.async_call(function()
        local eTeam = bcapi.get_team(1)
        bcapi.show_pop_info("妙华寺的和尚们加入了战斗！",4)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(0, 2), 0.2)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(0, -2), 0.2)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(6, 2), 0.2)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(6, -2), 0.2)
    end)
end

function Summon2()
    bcapi.async_call(function()
        local eTeam = bcapi.get_team(1)
        bcapi.show_pop_info("妙华寺的和尚们加入了战斗！",4)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(6, 2), 0.2)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(6, -2), 0.2)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(0, 2), 0.2)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(0, -2), 0.2)
    end)
end

function Summon3()
    bcapi.async_call(function()
        local eTeam = bcapi.get_team(1)
        bcapi.show_pop_info("妙华寺的和尚们加入了战斗！",4)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(6, 2), 0.2)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(6, -2), 0.2)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(0, 2), 0.2)
        bcapi.create_join_role("副本_夜战开封_法净_和尚", 57, eTeam, bcapi.Vector2(0, -2), 0.2)
    end)
end