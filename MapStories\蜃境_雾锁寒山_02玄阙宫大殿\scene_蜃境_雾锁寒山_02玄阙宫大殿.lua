
---@type 蜃境_雾锁寒山_02玄阙宫大殿
local context = require("MapStories/蜃境_雾锁寒山_02玄阙宫大殿/scene_蜃境_雾锁寒山_02玄阙宫大殿_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_雾锁寒山_02玄阙宫大殿
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")
function getFlags(...)
    return flags
end

local capi = require("ChapterApi")

--临时的跑图falg的开关，由于此场景只有一个，先苟住
function setNewbieFlags11()
    s.setNewbieFlag(newbieFlags.chapter_wshs_03_finished,1)
end
----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function testcam()
    capi.finishMemorySlot("wshs_03")
    flags.isFinish_wshs_03 = 1     
    enemy_active()
    next_level_step(true)
end

function test1()
    s.setActive(objs.wshs2_1003,true)
end

function get_level_step()
    return flags.level_step
end

--设置雾锁寒山跑图设置
function try_execute_newbieGuide(...)
    -- 如果qyl_08未解锁，且qyl_08已通关，则finish qyl_08
    if (not s.hasUnlockMemorySlot("wshs_03") and s.getNewbieFlag(newbieFlags.chapter_wshs_03_finished) == 1) then
        capi.finishMemorySlot("wshs_03")
        flags.isFinish_wshs_03 = 1     
        enemy_active()
        next_level_step(true)
    end
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function start()
    print("start called")

    --若完成阿莫任务，隐藏奸商
    if s.getCurrentTaskStep("雾锁寒山阿莫2") == "backToAmo" then
        s.setActive(npcs.jianshang,false)
    end


    --若完成灵鸟任务，隐藏林契和沈聪安 
    if s.getCurrentTaskStep("灵鸟") == "finished" then
        s.setActive(npcs.linqi,false)
        s.setActive(npcs.shengcongan,false)
    end

    refreshMapStates()
    s.readyToStart(true)

    -- 防止跑图中强退导致流程跳过
    try_execute_newbieGuide()
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数
    s.playMusic("天阙")

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 5) then
        taskStep = "forwardToExplore"
    elseif (step == 5) then
        taskStep = "leave"
    end
    s.setTaskStep("雾锁寒山玄阙宫大殿", taskStep)

    reset_all()


    --第一次进入场景
    if step == 0 then
        s.setTaskStep("雾锁寒山玄阙宫大殿","forwardToExplore")
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.wshs_03, 1)
        if flags.find_wshs03 == 0 then
            s.readyToStart(true)
            s.camera(cameras.cam_start, true,true,blendHintEnum.Cut)
            s.lightScreen()
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "玄阙宫大殿_发现虚影")
            s.cameraAsync(cameras.cam_showEnmey, true,true,blendHintEnum.Custom,2)
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "玄阙宫大殿_发现虚影1")

            flags.find_wshs03 = 1
        end

        s.cameraAsync(cameras.cam_main, true,true,blendHintEnum.Custom,2)
    elseif step == 2 then
        --s.lightScreen()
        if flags.find_wshs04 == 0 then
            s.camera(cameras.cam_showEnmey, true,true,blendHintEnum.Custom,5)
            s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "玄阙宫大殿_第一个虚影消散")
            flags.find_wshs04 = 1
            s.camera(cameras.cam_main, true,true,blendHintEnum.Cut)
        end

        level_guide_to(npcs.wshs_04, 1)
    elseif step == 3 then
        level_guide_to(npcs.wshs_05, 1)
    elseif step == 4 then
        level_guide_to(npcs.wshs_06, 1)
    else
        level_guide_to(objs.exit)
    end
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.wshs_03, false)
    s.setActive(npcs.wshs_04, false)
    s.setActive(npcs.wshs_05, false)
    s.setActive(npcs.wshs_06, false)

    --阿莫任务
    if s.getCurrentTaskStep("雾锁寒山阿莫2") == "deactive" and s.getCurrentTaskStep("雾锁寒山阿莫") == "finished" then 
        s.setActive(npcs.amo,true)
        s.setActive(npcs.jianshang,true)
    elseif s.getCurrentTaskStep("雾锁寒山阿莫2") == "findJianshang" then
        s.setActive(npcs.amo,true)
        s.setActive(npcs.jianshang,true)
    elseif s.getCurrentTaskStep("雾锁寒山阿莫2") == "backToAmo" then
        s.setActive(npcs.amo,true)
        s.setActive(npcs.jianshang,true)
    elseif s.getCurrentTaskStep("雾锁寒山阿莫2") == "finished" then
        s.setActive(npcs.amo,false)
        s.setActive(npcs.jianshang,false)
    end

    --灵鸟任务
    if s.getCurrentTaskStep("灵鸟") == "deactive" then
        s.setActive(npcs.linqi,true)
        s.setActive(npcs.shengcongan,true)
    elseif s.getCurrentTaskStep("灵鸟") == "findShencongan" then
        s.setActive(npcs.linqi,true)
        s.setActive(npcs.shengcongan,true)
    elseif s.getCurrentTaskStep("灵鸟") == "backToLinqi" then
        s.setActive(npcs.linqi,true)
        s.setActive(npcs.shengcongan,true)
    end

    --尹晗的请求任务
    if s.getCurrentTaskStep("尹晗的请求") == "deactive" then
        s.setActive(npcs.yinhan,true)
        s.setActive(npcs.yinxueyin,true)
        s.setActive(npcs.noisyDizi,true)
    elseif s.getCurrentTaskStep("尹晗的请求") == "findYinxueyin" then
        s.setActive(npcs.yinxueyin,true)
        s.setActive(npcs.noisyDizi,true)
    elseif s.getCurrentTaskStep("尹晗的请求") == "shutUpDizi" then
        s.setActive(npcs.yinxueyin,true)
        s.setActive(npcs.noisyDizi,true)
    elseif s.getCurrentTaskStep("尹晗的请求") == "backToYinxueyin" then
        s.setActive(npcs.yinxueyin,true)
    end

    --激活敌人
    for i = 1, 3 do
        -- 构建当前循环对应的 flag 键名
        local flag_key = "enemy_" .. i
        -- 构建当前循环对应的 npc 键名
        local npc_key = "enemy_" .. i

        -- 检查对应的 flag 是否为 1

        if flags[flag_key] == 1 then
            -- 激活对应的 NPC
            s.setActive(npcs[npc_key], true)
        end
    end

    --动作状态机设置
    s.animState(npcs.yinxueyin,roleAnims.BSit_Loop,true)

    --收集物开启
    if flags.collect_1 == 1 then
        s.setActive(objs.wshs2_1002,true)
    else
        s.setActive(objs.wshs2_1002,false)
    end

    if flags.collect_2 == 0 then
        s.setActive(objs.wshs2_1003,true)
    else
        s.setActive(objs.wshs2_1003,false)
    end

    if flags.collect_3 == 0 then
        s.setActive(objs.wshs2_1001,true)
    else
        s.setActive(objs.wshs2_1001,false)
    end


    --删除所有的引导点
    s.removeAllGameMapGuides()
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("雾锁寒山")
end
-------------------地图跑图---------------------
--蜃境怪物出现
function evilAir_1(id)
    s.setActive(objs.evilAir_1,false)
    s.setActive(npcs.role_elite_7, true)
    s.animState(npcs.role_elite_7,"BAssault_Loop",true)
    s.wait(2)
    elite_7_fight(id)
end

function elite_7_fight(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,
    function()
        refreshMapStates()
    end,
    function()
        s.animState(npcs.role_elite_7,"BAssault_Loop",false)
    end,
    function()
    s.animState(npcs.role_elite_7,"BAssault_Loop",false)
    end)
end

function normal_battle(id,enmeyKey)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,function()
        --s.setActive(npcs[enmeyKey],false)
        flags[enmeyKey] = 2
    end)
end
-------------------支线和解密---------------------
function amo()
    if s.getCurrentTaskStep("雾锁寒山阿莫2") == "deactive" then
        s.turnTo(npcs.amo,npcs.zhujue)
        s.animState(npcs.amo,"BGreet",true)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "阿莫支线_再次碰到")
        s.setTaskStep("雾锁寒山阿莫2","findJianshang")
        require("MapStories/MapStoryCommonTools").tryIfFirstSideTask()
    elseif s.getCurrentTaskStep("雾锁寒山阿莫2") == "backToAmo" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "阿莫支线_返回阿莫处")
        s.moveAsync(npcs.amo,pos.pos_amo_leave,2,true)
        s.wait(5)
        s.setActive(npcs.amo,false)

        capi.finishMemorySlot("wshs2_sideTask_amo2")
        s.setTaskStep("雾锁寒山阿莫2","finished")
    else
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "阿莫支线_闲话")
    end
end

function jianshang()
    if s.getCurrentTaskStep("雾锁寒山阿莫2") == "findJianshang" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "阿莫支线_找到奸商")
        battle_jianshang()
    elseif s.getCurrentTaskStep("雾锁寒山阿莫2") == "finished" or s.getCurrentTaskStep("雾锁寒山阿莫2") == "backToAmo"then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "阿莫支线_奸商战后胜利闲话")
    else
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "阿莫支线_奸商闲话")
    end
end
function battle_jianshang()
    local iswin = s.battle("蜃境_雾锁寒山2_阿莫_与奸商战斗")
    if iswin then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "阿莫支线_战斗胜利")
        s.setTaskStep("雾锁寒山阿莫2","backToAmo")
    end
end

function yinhan()
    if s.getCurrentTaskStep("尹晗的请求") == "deactive" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_遇见尹晗")
        s.wait(0.1)
        s.moveAsync(npcs.yinhan,pos.pos_yinhan_leave,2,true)
        s.wait(1)
        s.setDeath(npcs.yinhan,1,true)
        s.setTaskStep("尹晗的请求","findYinxueyin")

        require("MapStories/MapStoryCommonTools").tryIfFirstSideTask()
    else
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_尹晗闲话")
    end
end

function yinxueyin()
    if s.getCurrentTaskStep("尹晗的请求") == "deactive" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_尹雪音闲话") 
    elseif s.getCurrentTaskStep("尹晗的请求") == "findYinxueyin" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_对话尹雪音") 

        s.camera(cameras.cam_noisyDizi, true,true,blendHintEnum.Custom,1.5)

        s.animState(npcs.noisyDizi_1,"BAngry",true)
        s.animState(npcs.noisyDizi_2,"BAgree",true)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_弟子怪叫") 

        s.cameraAsync(cameras.cam_noisyDizi, false,true,blendHintEnum.Cut)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_对话尹雪音_继续对话") 
        s.setTaskStep("尹晗的请求","shutUpDizi")
    elseif s.getCurrentTaskStep("尹晗的请求") == "backToYinxueyin" then

        s.camera(cameras.cam_yinxueyin, true,true,blendHintEnum.Custom,1.5)
        s.animState(npcs.yinxueyin,"BSit_Loop",false)
        
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_返回尹雪音处") 

        s.move(npcs.yinxueyin,pos.pos_yinxueyin_leave,2,true)
        s.setActive(npcs.yinxueyin,false)

        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_尹雪音离开") 

        s.camera(cameras.cam_main, true,true,blendHintEnum.Custom,1.5)

        --激活收集品1
        flags.collect_1 = 1

        capi.finishMemorySlot("wshs2_sideTask_yinhanQuest")
        s.setTaskStep("尹晗的请求","finished")
        s.nextStep("refreshMapStates")
    else
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_尹雪音闲话") 
    end
end

function noisyDizi()
    if s.getCurrentTaskStep("尹晗的请求") == "shutUpDizi" then
        s.turnTo(npcs.noisyDizi_1,npcs.zhujue)
        s.turnTo(npcs.noisyDizi_2,npcs.zhujue)
        s.wait(0.1)
        s.animState(npcs.noisyDizi_2,"BAgree",true)

        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_弟子战斗") 
        battleWithDizi()
    else
        s.animState(npcs.noisyDizi_1,"BAngry",true)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_弟子怪叫甲") 
        s.animState(npcs.noisyDizi_2,"BAngry",true)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_弟子怪叫乙") 
    end
end
function battleWithDizi()
    local ret = s.battle("蜃境_雾锁寒山2_尹晗的请求_与两位弟子战斗")
    if (ret) then
        s.camera(cameras.cam_noisyDizi_talk, true,true,blendHintEnum.Cut)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_弟子战斗_胜利")
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_弟子战斗_胜利_弟子鞠躬")
        s.wait(0.1)

        s.moveAsync(npcs.noisyDizi_1,pos.pos_dizi_leave,3,true)
        s.move(npcs.noisyDizi_2,pos.pos_dizi_leave,3,true)
        s.setActive(npcs.noisyDizi,false)
        s.camera(cameras.cam_main, true,true,blendHintEnum.Custom,1.5)

        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "尹晗的请求支线_弟子战斗_弟子散去")
        s.setTaskStep("尹晗的请求","backToYinxueyin")
    end
end

function ln_linqi()
    if s.getCurrentTaskStep("灵鸟") == "deactive" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "灵鸟支线_遇见林契")
        s.setTaskStep("灵鸟","findShencongan")
    elseif s.getCurrentTaskStep("灵鸟") == "findShencongan" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "灵鸟支线_林契_闲话")
    elseif s.getCurrentTaskStep("灵鸟") == "backToLinqi" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "灵鸟支线_回复林契")
        capi.finishMemorySlot("wshs2_sideTask_lingniao")
        s.setTaskStep("灵鸟","finished")
    elseif s.getCurrentTaskStep("灵鸟") == "finished" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "灵鸟支线_回复林契_闲话")
    end
end

function ln_shencongan()
    if s.getCurrentTaskStep("灵鸟") == "deactive" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "灵鸟支线_遇见沈聪安_沈聪安闲话")
    elseif s.getCurrentTaskStep("灵鸟") == "findShencongan" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "灵鸟支线_遇见沈聪安")
        s.setTaskStep("灵鸟","backToLinqi")
    elseif s.getCurrentTaskStep("灵鸟") == "backToLinqi" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "灵鸟支线_回复林契_沈聪安闲话")
    elseif s.getCurrentTaskStep("灵鸟") == "finished" then
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_玄阙宫大殿", "灵鸟支线_回复林契_沈聪安闲话")
    end
end
-------------------关卡记忆碎片和战斗实现---------------------
--激活敌人
function enemy_active()
    for i = 1, 6 do
        local flag_key = "enemy_" .. i
        flags[flag_key] = 1
    end
end

--精英怪战斗
function eliteBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

function wshs_03()
    executeMemSlot("wshs_03", function()
        require("Chapters/雾锁寒山").wshs2_03()
    end)
    --s.blackScreen(0)
    --临时跳过跑图
    --next_level_step(true)
end

function wshs_04()
    executeMemSlot("wshs_04", function()
        require("Chapters/雾锁寒山").wshs2_04()
    end)
end

function wshs_05()
    executeMemSlot("wshs_05", function()
        require("Chapters/雾锁寒山").wshs2_05()
    end)
end

function wshs_06()
    executeMemSlot("wshs_06", function()
        require("Chapters/雾锁寒山").wshs2_06()
    end,function()
        s.setTaskStep("雾锁寒山玄阙宫大殿","leave")
        next_level_step(true)
    end)
end

function shudu1(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
    flags.collect_1 = 2
end

function shudu2(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
    flags.collect_2 = 1
end

function huaji1(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
    flags.collect_3 = 1
end

