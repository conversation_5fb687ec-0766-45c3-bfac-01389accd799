---@type 蜃境_裂雾飞鹰_02玄阙宫弟子房
local context = require("MapStories/蜃境_裂雾飞鹰_02玄阙宫弟子房/scene_蜃境_裂雾飞鹰_02玄阙宫弟子房_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_裂雾飞鹰_02玄阙宫弟子房
local flags = context.flags
--不可省略，用于外部获取flags

local newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")

function getFlags(...)
    return flags
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function test()
    s.camera(cameras.sideQuest3,false)
end

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function start()
    print("start called")
    refreshMapStates()
    s.readyToStart(true)
    try_execute_newbieGuide()
end

function try_execute_newbieGuide(...)
    s.nextStep("newbieGuideInLevel")
end


function newbieGuideInLevel()end

--默认初始化的地图显隐状态
function reset_all()

    -- 如果谜题未完成，生成B房间陈设
    if flags.isCompleted == 0 then
        generateBRoomSetup()
    end
    
    --隐藏所有的剧情节点

    s.setActive(npcs.lwfy_03, false)
    s.setActive(npcs.lwfy_04, false)
    -- s.setActive(npcs.enemy_1, flags.enemy_1 == 0)
    -- s.setActive(npcs.enemy_2, flags.enemy_2 == 0)
    -- s.setActive(npcs.enemy_3, flags.enemy_3 == 0)
    -- s.setActive(npcs.elite_4, flags.elite_4 == 0)

    if(flags.alyd == 1)then
        s.setActive(objs.sideQuestTrigger,false)
        s.setActive(npcs.jiguanying,false)
    else
        s.animState(npcs.jiguanying,"Special_Loop_3",true,false,1,false,true,1,false)
    end

    s.setActive(objs.lwfy_1001, not s.hasUnlockMemorySlot("lwfy_1001"))
    s.setActive(objs.lwfy_1002, not s.hasUnlockMemorySlot("lwfy_1002"))
    s.removeAllGameMapGuides()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 3) then
        taskStep = "forwardToExplore"
    elseif (step == 3) then
        taskStep = "leaveDiZiFangShenJing"
    end
    s.setTaskStep("裂雾飞鹰玄阙宫弟子房", taskStep)

    reset_all()
    
    if step == 0 then
        ----第一次进入场景, 这里加入入场演出
        s.setTaskStep("裂雾飞鹰玄阙宫弟子房","forwardToExplore")
        s.blackScreen()
        s.wait(0.5)
        s.setPos(npcs.zhujue,pos.zhujue_start_pos)
        s.lightScreen()
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_02玄阙宫弟子房", "蜃境_裂雾飞鹰_02玄阙宫弟子房_进入")
        s.blackScreen()
        s.setActive(npcs.yuchi,true)
        s.camera(cameras.cam1,true)
        s.wait(0.5)
        s.setPos(npcs.zhujue,pos.zhujue_start_pos2)
        local offset = CS.UnityEngine.Vector3(0, 1.75, 0)
        s.lookAt(npcs.zhujue,offset, npcs.yuchi,true)
        s.lightScreen()
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_02玄阙宫弟子房", "蜃境_裂雾飞鹰_02玄阙宫弟子房_进入1")
        s.blackScreen()
        s.setActive(npcs.yuchi,false)
        s.camera(cameras.cam1,false)
        s.lightScreen()
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.lwfy_03, 0.8)
    elseif step == 2 then
        level_guide_to(npcs.lwfy_04, 0.8)
    else
        --do nothing
        level_guide_to(objs.exit)
    end
end

----------------------OVERRIDE----------------------------------

function lwfy_01_test()
    flags.lwfy_03_playing = 0
    flags.level_step = 1
    refreshMapStates()
end

function lwfy_03()
    executeMemSlot("lwfy_03", quick_play_avg)
end

function lwfy_04()
    executeMemSlot("lwfy_04", quick_play_avg,function()
    s.setTaskStep("裂雾飞鹰玄阙宫弟子房","leaveDiZiFangShenJing")
    next_level_step(true)
    end)
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("裂雾飞鹰")
end

function anLiuYongDong1()
    --进入触发区域后，关闭触发器
    s.setActive(objs.sideQuestTrigger,false)
    --黑屏切换摄像机
    s.blackScreen()
    s.camera(cameras.sideQuest1,true)
    s.setPos(npcs.zhujue,pos.sideQuest_Pos)
    s.turnTo(npcs.zhujue,npcs.jiguanying)
    s.lightScreen()
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_02玄阙宫弟子房", "蜃境_裂雾飞鹰_02玄阙宫弟子房_暗流涌动_1")
    s.soloAnimState(npcs.jiguanying,"",true)
    s.camera(cameras.sideQuest2,true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_02玄阙宫弟子房", "蜃境_裂雾飞鹰_02玄阙宫弟子房_暗流涌动_1_激活")
    local ret = s.battle("蜃境_裂雾飞鹰2_暗流涌动_打机关鹰")
    if(ret)then
        s.blackScreen()
        s.setActive(npcs.jiguanying,false)
        s.camera(cameras.sideQuest2,false)
        s.camera(cameras.sideQuest3,true)
        s.setActive(npcs.yuqiong,true)
        s.setPos(npcs.zhujue,pos.sideQuest_Pos_3)
        s.lightScreen()

        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_02玄阙宫弟子房", "蜃境_裂雾飞鹰_02玄阙宫弟子房_暗流涌动_2")
        s.blackScreen()
        s.setActive(npcs.yuqiong,false)
        s.setActive(cameras.sideQuest3,false)
        s.lightScreen()
        flags.alyd_step = 1
        s.setTaskStep("暗流涌动1","finished")
        finish_alydTask()
    else
        s.blackScreen()
        s.setActive(objs.sideQuestTrigger,true)
        s.setPos(npcs.zhujue,pos.sideQuest_Pos_2)

        s.camera(cameras.sideQuest3,false)
        s.lightScreen()
        defaultLose()
        add_alydTask()
    end
end

function add_alydTask()
    s.setTaskStep("暗流涌动1","defeatBird")
end

function finish_alydTask()
    local capi = require("ChapterApi")
    capi.finishMemorySlot("lwfy_sideTask_alyd")
end

---------------找不同谜题---------------

function init()
    -- 初始化flags
    if not flags.currentStep then
        flags.currentStep = 0
    end
    if not flags.isCompleted then
        flags.isCompleted = 0
    end
    
    -- 初始化B房间陈设
    generateBRoomSetup()
end

function generateBRoomSetup()
    -- 随机选择一个位置放置Bf物件
    local positions = {"1", "2", "3"}
    local bfPos = math.random(1, 3)
    flags.currentBfPosition = positions[bfPos]
    
    print("生成新的Bf位置: " .. tostring(flags.currentBfPosition))  -- 添加调试信息

    -- 隐藏所有B房间物件
    s.setActive(objs.puzzleKey_Bt_mid, false)
    s.setActive(objs.puzzleKey_Bt_right, false)
    s.setActive(objs.puzzleKey_Bt_left, false)
    s.setActive(objs.puzzleKey_Bf_mid, false)
    s.setActive(objs.puzzleKey_Bf_right, false)
    s.setActive(objs.puzzleKey_Bf_left, false)
    
    -- 根据随机位置显示对应物件
    if flags.currentBfPosition == "1" then
        s.setActive(objs.puzzleKey_Bt_mid, true)
        s.setActive(objs.puzzleKey_Bt_right, true)
        s.setActive(objs.puzzleKey_Bf_left, true)
    elseif flags.currentBfPosition == "2" then
        s.setActive(objs.puzzleKey_Bt_left, true)
        s.setActive(objs.puzzleKey_Bt_right, true)
        s.setActive(objs.puzzleKey_Bf_mid, true)
    elseif flags.currentBfPosition == "3" then
        s.setActive(objs.puzzleKey_Bt_left, true)
        s.setActive(objs.puzzleKey_Bt_mid, true)
        s.setActive(objs.puzzleKey_Bf_right, true)
    end
end

function puzzle_Npctalk()
    if flags.isCompleted == 1 then
        -- 已完成谜题，播放盛赞对话
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_02玄阙宫弟子房", "蜃蜃境_裂雾飞鹰_02玄阙宫弟子房_完成任务再和他对话")
        return
    end
    
    if flags.currentStep == 0 then
        -- 第一次对话，播放听取谜题对话
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_02玄阙宫弟子房", "蜃蜃境_裂雾飞鹰_02玄阙宫弟子房_听取谜题")
        flags.currentStep = 1
    elseif flags.currentStep == 1 then
        -- 第二次对话，播放选择不同处对话并处理选择结果
        local r = s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_02玄阙宫弟子房", "蜃蜃境_裂雾飞鹰_02玄阙宫弟子房_选择不同处")
        
        if r then
            print("玩家选择: " .. tostring(r))
            print("当前Bf位置: " .. tostring(flags.currentBfPosition))
            
            local isCorrect = false
            if r == 1 and tonumber(flags.currentBfPosition) == 1 then
                isCorrect = true
            elseif r == 2 and tonumber(flags.currentBfPosition) == 2 then
                isCorrect = true
            elseif r == 3 and tonumber(flags.currentBfPosition) == 3 then
                isCorrect = true
            end
            
            print("是否正确: " .. tostring(isCorrect))
            
            if isCorrect then
                -- 选择正确，播放盛赞对话并标记完成
                s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_02玄阙宫弟子房", "蜃蜃境_裂雾飞鹰_02玄阙宫弟子房_盛赞玩家")
                flags.isCompleted = 1
                local capi = require("ChapterApi")
                capi.finishMemorySlot("lwfy_question_findDiff")
            else
                -- 选择错误，播放错误对话并重新生成B房间陈设
                s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_02玄阙宫弟子房", "蜃蜃境_裂雾飞鹰_02玄阙宫弟子房_选择错误")
                generateBRoomSetup()
            end
        end
    end
end