
---@type 主线_雾锁寒山1_4
local context = require("MapStories/主线_雾锁寒山1_4/scene_主线_雾锁寒山1_4_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_主线_雾锁寒山1_4
local flags = context.flags 
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.playMusic("四海逍遥",5,nil,0)
    s.setActive(npcs.zhujue,false)
    s.setActive(objs.trigger_com,false)
    s.moveAsync(npcs.dzmove,pos.pos_dz,1,true)
    s.moveAsync(npcs.ycmove,pos.pos_yc,1,true)
    s.moveAsync(npcs.yhmove,pos.pos_yh,1,true)
    s.moveAsync(npcs.yxymove,pos.pos_yxy,1,true)
    s.moveAsync(npcs.tzmove,pos.pos_tz,1,true)
    s.moveAsync(npcs.tzmove,pos.pos_tz,1,true)
    s.readyToStart()

    s.camera(cameras.texie01,true,true,blendHintEnum.Cut,0,true,true)
    s.camera(cameras.texie02,true,true,blendHintEnum.Custom,3)
    s.aside("今日的玄阙宫大殿格外热闹。")
    s.camera(cameras.texie03,true,true,blendHintEnum.Custom,1.5,true,true)

    s.blackScreen()
    s.setActive(npcs.dzmove,false)
    s.setActive(npcs.yhmove,false)
    s.setActive(npcs.yxymove,false)
    s.setActive(npcs.ycmove,false)
    s.setActive(npcs.tzmove,false)
    s.setActive(npcs.dizi,true)
    s.setActive(npcs.yuchi,true)
    s.setActive(npcs.yinxueyin,true)
    s.setActive(npcs.yinhan,true)
    s.setActive(npcs.tuzhou,true)
    s.setActive(npcs.zhujue,true)
    s.setPos(npcs.zhujue,pos.pos_zj)
    s.camera(cameras.main,true)
    s.lightScreen(2)
    s.talk(npcs.zhujue,"唔，平时不在意，门下弟子竟有这般多人。")
    s.talk(npcs.zhujue,"开始前还有一段时间，先四下看看吧。")

    flags.FinishDizi = 0
    flags.FinishYuchi = 0
    flags.FinishTuzhou = 0
    flags.FinishYinhan = 0

    if s.hasTask(600001, 1) == false then
        s.addTask(600001, 1)
        --增加引导
        s.addGameMapGuide(objs.guide_dizi)
        s.addGameMapGuide(objs.guide_yuchi)
        s.addGameMapGuide(objs.guide_tuzhou)
        s.addGameMapGuide(objs.guide_yinhan)
    end
end

function dizi()
    s.setActive(objs.trigger_dizi,false)
    --清除引导
    s.removeGameMapGuide(objs.guide_dizi)

    s.animState(npcs.dizi,roleAnims.BGesture,true)
    s.talk(npcs.dizi,"哟，墨七，头一次看到他参与争彼大会……")
    s.animState(npcs.dizi,roleAnims.BGesture,false)
    s.talk(npcs.diziny,"仆役之子，怎能跟我们站到一块？")
    s.animState(npcs.dizinb,roleAnims.BGesture,true)
    s.talk(npcs.dizinb,"话不能这么说……他可是凭自己的实力站到这儿的。")
    s.animState(npcs.dizinb,roleAnims.BGesture,false)
    s.animState(npcs.zhujue,"DThink",true)
    s.talk(npcs.zhujue,"好像插不上话。")
    s.animState(npcs.zhujue,"DThink",false)

    flags.FinishDizi = 1
    over()
end

function yuchi()
    s.setActive(objs.trigger_yuchi,false)
    --清除引导
    s.removeGameMapGuide(objs.guide_yuchi)

    s.camera(cameras.ycTalk,true,true,blendHintEnum.Cut,0)
    s.setPos(npcs.zhujue,pos.pos_ycTalk)
    s.talk(npcs.zhujue,"（是玉尺……她方才说是有事，现下却心不在焉的，到底是怎么了呢？）")   
    s.talk(npcs.zhujue,"玉尺……")
    s.camera(cameras.cam_yuchi,true,true,blendHintEnum.Cut,0)
    s.talk(npcs.yuchi,"墨七……")
    s.animState(npcs.yuchi,roleAnims.BDeny,true)
    s.talk(npcs.yuchi,"（方才墨七应是无心，我却因他提起亡父而心有不甘，实在惭愧……）")
    s.animState(npcs.yuchi,roleAnims.BDeny,false)
    s.talk(npcs.yuchi,"(还是，先走吧……)")
    s.camera(cameras.ycTalk,true,true,blendHintEnum.Cut,0)
    s.wait(1.5)
    s.moveAsync(npcs.yuchi,pos.pos_ycGo,1,true)
    s.talk(npcs.zhujue,"她又生气了？")
    s.moveAsync(npcs.yuchi,pos.pos_ycGo,1,true)
    s.wait(1.5)
    s.setActive(npcs.yuchi,false)
    s.camera(cameras.ycTalk, false,true,blendHintEnum.EaseInOut,1.5,true,false)

    flags.FinishYuchi = 1
    over()
end

function tuzhou()
    s.setActive(objs.trigger_tuzhou,false)
    --清除引导
    s.removeGameMapGuide(objs.guide_tuzhou)

    s.animState(npcs.tuzhou,roleAnims.BHostile,true)
    s.talk(npcs.tuzhou,"哼……墨七。")
    s.talk(npcs.zhujue,"师兄，你是……？")
    s.talk(npcs.tuzhou,"像你这样的才子，自然是认不得我这种无名之辈了。")
    s.talk(npcs.zhujue,"（才子？是说我吗？）")
    s.talk(npcs.tuzhou,"可我好歹也是本门高阶弟子，位列护门玄卫，你得放尊重些！")
    s.talk(npcs.zhujue,"护门玄卫？那是看门的吗？")
    s.animState(npcs.tuzhou,roleAnims.BHostile,false)
    s.animState(npcs.tuzhou,roleAnims.BAngry,true)
    s.talk(npcs.tuzhou,"你……你好自为之。")
    s.talk(npcs.tuzhou,"呵呵呵，你蹦跶不了多久的……")
    s.animState(npcs.tuzhou,roleAnims.BAngry,false)
    s.animState(npcs.zhujue,"DThink",true)
    s.talk(npcs.zhujue,"难道……我又说错什么了？")
    s.animState(npcs.zhujue,"DThink",false)

    flags.FinishTuzhou = 1
    over()
end

function yinhan()
    s.setActive(objs.trigger_yinhan,false)
    --清除引导
    s.removeGameMapGuide(objs.guide_yinhan)

    s.camera(cameras.yhTalk,true,true,blendHintEnum.Cut,0)
    s.setPos(npcs.zhujue,pos.pos_yhTalk)
    s.animState(npcs.yinhan,roleAnims.BGesture,true)
    s.talk(npcs.yinhan,"哟，墨七，你到了？")
    s.animState(npcs.yinhan,roleAnims.BGesture,false)
    s.animState(npcs.yinxueyin,roleAnims.BDeny,true)
    s.talk(npcs.yinxueyin,"墨七，你能来真是太好了。我生怕你昨晚摔的太重。")
    s.animState(npcs.yinxueyin,roleAnims.BDeny,false)
    s.camera(cameras.cam_moqi,true,true,blendHintEnum.Cut,0.5,true)
    s.talk(npcs.zhujue,"并无大碍。")
    s.camera(cameras.yhTalk,true,true,blendHintEnum.Cut,0.5,true)
    s.animState(npcs.yinxueyin,roleAnims.BGesture,true)
    s.talk(npcs.yinxueyin,"今年你首次旁观争彼大会，待会还请多多关注尹晗的表现，给他一些指点。")
    s.animState(npcs.yinxueyin,roleAnims.BGesture,false)
    s.camera(cameras.cam_moqi,true,true,blendHintEnum.Cut,0.5,true)
    s.talk(npcs.zhujue,"只要不弄坏机关鼠……")
    s.camera(cameras.yhTalk,true,true,blendHintEnum.Cut,0.5,true)
    s.talk(npcs.yinhan,"咳咳咳……")
    s.animState(npcs.yinxueyin,roleAnims.BScratch,true)
    s.talk(npcs.yinxueyin,"机关鼠？")
    s.animState(npcs.yinxueyin,roleAnims.BScratch,false)
    s.talk(npcs.yinhan,"没事没事……墨七他……指导了不少机关术的事情，我谢他还来不及呢！")   
    s.talk(npcs.yinhan,"走了姐姐，我还得去准备准备……")
    s.moveAsync(npcs.yinhan,pos.pos_yhGo,2,true)
    s.wait(0.1)
    s.moveAsync(npcs.yinxueyin,pos.pos_yhGo,2,true)
    s.wait(2)
    s.camera(cameras.cam_moqi1,true,true,blendHintEnum.Cut,0.5,true)
    s.animState(npcs.zhujue,"DThink",true)
    s.talk(npcs.zhujue,"机关鼠……机关术……？")
    s.animState(npcs.zhujue,"DThink",false)
    s.move(npcs.yinhan,pos.pos_yhGo,2,true)
    s.setActive(npcs.yinhan,false)
    s.setActive(npcs.yinxueyin,false)
    s.camera(cameras.main,true,true,blendHintEnum.Cut,0.5,true)

    flags.FinishYinhan = 1
    over()
end

function over()
    if(flags.FinishDizi == 1 and flags.FinishYuchi == 1 and flags.FinishTuzhou == 1 and flags.FinishYinhan == 1)then
        s.setActive(npcs.fuzhi,true)
        s.camera(cameras.cam_fuzi,true,true,blendHintEnum.Cut,0,true)
        s.soloAnimState(npcs.fuzhi,"Walk",true)
        s.moveAsync(npcs.fuzhi,pos.pos_fuzi ,1,true)
        s.wait(2.5)
        s.animState(npcs.fuzhi,"Walk",false)
        s.camera(cameras.texie03,true,true,blendHintEnum.Cut)
        s.talk(npcs.zhujue,"夫子到了")
        s.talk(npcs.zhujue,"我也该准备参赛了。")
        s.talk(npcs.zhujue,"去面对雕像右边的比赛席位吧！")

        s.addGameMapGuide(objs.trigger_com,1)

        s.setActive(objs.trigger_com,true)
        s.camera(cameras.main,true,true,blendHintEnum.Cut)
    end
end

function jiuxu()
    s.removeAllGameMapGuides()

    s.setNewbieFlags("chapter_wshs_03_finished",1)
    s.finishInfiniteStory(true)

end

function saichang()
    s.camera(cameras.cam_saichang,true,true,blendHintEnum.Cut,0,false,true)
    s.talk(npcs.zhujue,"这中间应该就是大会场地了。")
    s.camera(cameras.main,true,true,blendHintEnum.Cut,0.5,true,true)
end

function fuzixiang()
    s.camera(cameras.texie03,true,true,blendHintEnum.Cut,0,true,true)
    s.talk(npcs.zhujue,"大殿中央矗立着一尊巨大的铜像。")
    s.talk(npcs.zhujue,"这应是玄阙宫初代掌门人的雕像。")
    s.camera(cameras.main,true,true,blendHintEnum.Custom,1,true,true)
end

function nv()
    s.aside("她们似乎并不认识你。")
end

function man()
    s.soloAnimState(npcs.r_man,roleAnims.BAngry,true)
    s.talk(npcs.r_man,"今年的大会我一定让大家记住我！")
end

function cunmin()
    s.soloAnimState(npcs.l_cunmin,roleAnims.BGesture,true)
    s.talk(npcs.l_cunmin,"每年争彼大会，我们做杂役的也可以参观。可是我什么都看不懂啊……")
    s.soloAnimState(npcs.l_cunmin,roleAnims.BGesture,false)
end

function linqi()
    s.soloAnimState(npcs.linqi,roleAnims.BGesture,true)
    s.talk(npcs.linqi,"这雕像是我们初代掌门人，他的母亲是当年声震江湖的神秘门派——黑衣教的一员。")
    s.talk(npcs.linqi,"祖师爷刻苦钻研，自成一派，在天山另一侧的伊宁雪谷，创立了现在的玄阙宫。")
    s.soloAnimState(npcs.linqi,roleAnims.BGesture,false)
end

function collider()
    s.talk(npcs.zhujue,"现在四处闲逛好像不太合适，赶紧回去准备比赛吧！")
    s.blackScreen()
    s.setPos(npcs.zhujue,pos.pos_back)
    s.camera(cameras.main,true,true,blendHintEnum.Cut,0,true,true)
    s.lightScreen()
end