
---@type 蜃境_裂雾飞鹰_04玄阙地宫
local context = require("MapStories/蜃境_裂雾飞鹰_04玄阙地宫/scene_蜃境_裂雾飞鹰_04玄阙地宫_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local assets = context.assets
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_裂雾飞鹰_04玄阙地宫
local flags = context.flags
--不可省略，用于外部获取flags

local newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")



function getFlags(...)
    return flags
end


function test()
    s.setActive(npcs.lwfy_09, true)
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function start()
    --print("start called")
    refreshMapStates()
    s.readyToStart(true)
    try_execute_newbieGuide()
end

function try_execute_newbieGuide(...)
    s.nextStep("newbieGuideInLevel")
end


function newbieGuideInLevel()end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    local step = get_level_step()
    s.setActive(npcs.lwfy_08, step < 3)
    s.setActive(npcs.lwfy_09, false)
    s.setActive(npcs.lwfy_10, false)
    s.setActive(npcs.lwfy_10_for_show, false)
    s.setActive(npcs.lwfy_11, false)
    s.setActive(npcs.lwfy_12, false)
    s.setActive(npcs.lwfy_13, false)
    s.setActive(npcs.lwfy_14, false)
    s.setActive(npcs.lwfy_14_rock, step <= 8)

    -- s.setActive(npcs.enemy_1, flags.enemy_1 == 0)
    -- s.setActive(npcs.enemy_2, flags.enemy_2 == 0)
    -- s.setActive(npcs.enemy_3, flags.enemy_3 == 0)
    -- s.setActive(npcs.enemy_4, flags.enemy_4 == 0)
    -- s.setActive(npcs.enemy_5, flags.enemy_5 == 0)
    -- s.setActive(npcs.enemy_6, flags.enemy_6 == 0)
    -- s.setActive(npcs.elite_7, flags.elite_7 == 0)
    -- s.setActive(npcs.elite_8, flags.elite_8 == 0)
    -- s.setActive(npcs.boss_9, flags.boss_9 == 0)

    s.setActive(objs.door1,step < 3)
    s.setActive(npcs.yuchi,step < 3)
    s.setActive(objs.door2,step < 9)

    s.setActive(objs.chest_3,step >= 4)
    s.setActive(objs.chest_3_Light,step == 4)
    s.setActive(objs.chest_3_trigger,step >= 5 and flags.chest_open == 0) 
    
    s.setActive(objs.startTrigger2,flags.chest_play == 0)

    s.setActive(objs.startTrigger,step == 1)

    s.setActive(objs.lwfy_1004, not s.hasUnlockMemorySlot("lwfy_1004"))
    s.setActive(objs.experiment, not s.hasUnlockMemorySlot("lwfy_question_experiment"))
    s.removeAllGameMapGuides()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 9) then
        taskStep = "forwardToExplore"
    elseif (step == 9) then
        taskStep = "leaveXuanQueDiGong"
    end
    s.setTaskStep("裂雾飞鹰玄阙地宫", taskStep)

    reset_all()

    --s.setActive(objs.door_collider,false)
    if step == 0 then
        ----第一次进入场景, 这里加入入场演出
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_04玄阙地宫", "蜃境_裂雾飞鹰_04玄阙地宫_进入")
        s.setTaskStep("裂雾飞鹰玄阙地宫","forwardToExplore")
        next_level_step(true)
    elseif step == 2 then
        level_guide_to(npcs.lwfy_08, 0.8)
    elseif step == 3 then
        level_guide_to(npcs.lwfy_09, 0.8)
    elseif step == 4 then
        if(flags.chest_play == 1)then
            level_guide_to(npcs.lwfy_10, 0.8)
                else
            level_guide_to(npcs.lwfy_10_for_show, 0.8)
        end
    elseif step == 5 then
        level_guide_to(npcs.lwfy_11, 0.8)
    elseif step == 6 then
        level_guide_to(npcs.lwfy_12, 0.8)
    elseif step == 7 then
        level_guide_to(npcs.lwfy_13, 0.8)
    elseif step == 8 then
        level_guide_to(npcs.lwfy_14, 0.8)
    else
        --do nothing
        level_guide_to(objs.exit)
    end
end

----------------------OVERRIDE----------------------------------

function lwfy_01_test()
    flags.lwfy_08_playing = 0
    flags.level_step = 1
    refreshMapStates()
end

function chest()
    local commonTools = require("MapStories/MapStoryCommonTools")
    commonTools.roamMapChest("lwfy4_chest_03")
    flags.chest_open = 1
    s.setActive(objs.chest_3_trigger,false) 
end

function startStory()
    s.setActive(objs.startTrigger,false)
    --s.blackScreen()
    s.camera(cameras.cam1,true)
    s.setPos(npcs.zhujue,pos.pos_1)
    s.wait(1)
    --s.lightScreen()

    s.moveAsync(npcs.zhujue,pos.pos_2,1.5,true)
    s.wait(1)
    s.turnTo(npcs.yuchi,npcs.zhujue,false)
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_04玄阙地宫", "蜃境_裂雾飞鹰_04玄阙地宫_进入2")
    --s.turnTo(npcs.yuchi,objs.door1)
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_04玄阙地宫", "蜃境_裂雾飞鹰_04玄阙地宫_进入21")
    s.camera(cameras.cam1,false,true,blendHintEnum.EaseInOut,1.5,true,true)
    next_level_step(true)
end

function startStory2()
    s.setActive(objs.startTrigger2,false)
    s.blackScreen()
    s.setActive(npcs.lwfy_10_for_show,true)
    s.setActive(npcs.lwfy_10,false)
    s.setPos(npcs.zhujue,pos.play_pos_1)
    s.camera(cameras.cameraPlay1,true)
    --s.setActive(npcs.lwfy_10,true)
    s.lightScreen()
    s.moveAsync(npcs.zhujue,pos.play_pos_2,1.5,true)
    s.wait(1.5)
    s.camera(cameras.cameraPlay2,true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.setActive(npcs.lwfy_10_for_show,false)
    if(s.isMale())then
        s.playTimeline(assets.timeline1,false)
    else
        s.playTimeline(assets.timeline2,false)
    end
    --s.lightScreen()
    s.blackScreen()
    s.setActive(npcs.lwfy_10,true)
    s.camera(cameras.cameraPlay1,false)
    s.camera(cameras.cameraPlay2,false)
    s.lightScreen()
    flags.chest_play = 1
end


function lwfy_08()
    executeMemSlot("lwfy_08", function()
       require("Chapters/裂雾飞鹰").lwfy_08() 
    end, function()
        s.blackScreen()
        s.camera(cameras.cam1,true)
        s.setPos(npcs.zhujue,pos.pos_2)
        s.turnTo(npcs.yuchi,npcs.zhujue,false)
        s.wait(1)
        s.lightScreen()
        s.move(objs.door1,objs.door1Pos,1,false,nil,nil,nil,false,false,false)
        s.wait(0.7)
        s.turnTo(npcs.yuchi,objs.door1)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_04玄阙地宫", "蜃境_裂雾飞鹰_04玄阙地宫_进入4")
        s.blackScreen()
        s.setActive(npcs.yuchi,false)
        s.camera(cameras.cam1,false)
        s.wait(0.5)
        s.lightScreen()
        next_level_step(true)
        try_execute_newbieGuide()
    end)
end

function lwfy_09()
    executeMemSlot("lwfy_09", quick_play_avg)
end

function lwfy_10()
    executeMemSlot("lwfy_10", quick_play_avg)
end

function lwfy_11()
    executeMemSlot("lwfy_11", quick_play_avg)
end

function lwfy_12()
    executeMemSlot("lwfy_12", quick_play_avg)
end

function lwfy_13()
    executeMemSlot("lwfy_13", quick_play_avg)
end

function lwfy_14()
    executeMemSlot("lwfy_14", quick_play_avg,function()
        s.setActive(objs.door2,false)
        s.startGhostDissolve(npcs.lwfy_14_rock)
        s.setActive(npcs.lwfy_14_rock, false)
        s.setTaskStep("裂雾飞鹰玄阙地宫","leaveXuanQueDiGong")
        next_level_step(true)
    end)
end

function test()
s.setPos(npcs.zhujue,pos.question_1)
end

function experiment()
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_04玄阙地宫", "蜃境_裂雾飞鹰_04玄阙地宫_实验台")
    local rapi = require("RpgMapApi")
    local ret = rapi.submitRoleCardWithInstanceReturn("请选择要进行实验的侠客",nil)

    if(ret == nil)then
        return
    end

    if(ret.Key == "玉尺")then
        s.blackScreen()
        s.setActive(objs.experiment,false)
        s.camera(cameras.question_1,true)
        s.setPos(npcs.zhujue,pos.question_1)
        s.setActive(npcs.yuchi_e,true)
        s.turnTo(npcs.zhujue,pos.question_2)
        s.setPos(npcs.yuchi_e,pos.question_2)
        s.lightScreen()
        s.move(npcs.yuchi_e,pos.question_3,1.5,true)
        s.blackScreen()
        s.camera(cameras.question_2,true)
        s.lightScreen()
        s.soloAnimState(npcs.yuchi_e,"Special_1")
        s.wait(1)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_04玄阙地宫", "蜃境_裂雾飞鹰_04玄阙地宫_实验台_玉尺")
        s.soloAnimState(npcs.yuchi_e,"BThink_Loop")
        s.wait(1)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_04玄阙地宫", "蜃境_裂雾飞鹰_04玄阙地宫_实验台_玉尺_2")
        s.blackScreen()
        s.setActive(npcs.yuchi_e,false)
        s.camera(cameras.question_1,false)
        s.camera(cameras.question_2,false)
        s.setActive(objs.experiment,true)
        s.lightScreen()

    elseif(ret.Key == "墨七")then
        s.blackScreen()
        s.setActive(objs.experiment,false)
        s.camera(cameras.question_1,true)
        s.setPos(npcs.zhujue,pos.question_1)
        s.setActive(npcs.moqi_e,true)
        s.turnTo(npcs.zhujue,pos.question_2)
        s.setPos(npcs.moqi_e,pos.question_2)
        s.lightScreen()
        s.move(npcs.moqi_e,pos.question_3,1.5,true)
        s.blackScreen()
        s.camera(cameras.question_2,true)
        s.lightScreen()
        s.soloAnimState(npcs.moqi_e,"Special_1")
        s.wait(1)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_04玄阙地宫", "蜃境_裂雾飞鹰_04玄阙地宫_实验台_墨七")
        s.soloAnimState(npcs.moqi_e,"Special_2")
        s.wait(1)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_04玄阙地宫", "蜃境_裂雾飞鹰_04玄阙地宫_实验台_墨七_2")
        s.blackScreen()
        s.setActive(npcs.moqi_e,false)
        s.camera(cameras.question_1,false)
        s.camera(cameras.question_2,false)
        local capi = require("ChapterApi")
        capi.finishMemorySlot("lwfy_question_experiment")
        s.lightScreen()
    else
        rapi.popInfo("好像没什么反应。")
    end

end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("裂雾飞鹰")
end

