--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_裂雾飞鹰_06最终房/flags_蜃境_裂雾飞鹰_06最终房")


---@class 角色_蜃境_裂雾飞鹰_06最终房
local npcs = {
    zhujue = "角色/zhujue",
    Boss_SS = "角色/Boss_SS",
}

---@class 物体_蜃境_裂雾飞鹰_06最终房
local objs = {
    exit = "物体/exit",
    shijian = "物体/shijian",
    shijian_interact = "物体/shijian_interact",
}

---@class 相机_蜃境_裂雾飞鹰_06最终房
local cameras = {
}

---@class 位置_蜃境_裂雾飞鹰_06最终房
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
}

---@class 资产_蜃境_裂雾飞鹰_06最终房
local assets = {
    BossShow_nanzhu = "资产/BossShow_nanzhu",
    BossShow_nvzhu = "资产/BossShow_nvzhu",
    shijian = "资产/shijian",
}

---@class 动作_蜃境_裂雾飞鹰_06最终房
local animationClips = {
}

---@class 蜃境_裂雾飞鹰_06最终房
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
