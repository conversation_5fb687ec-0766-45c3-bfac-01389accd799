
---@type 主线_青羽录10_楚家庄后山夜
local context = require("MapStories/主线_青羽录10_楚家庄后山夜/scene_主线_青羽录10_楚家庄后山夜_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_主线_青羽录10_楚家庄后山夜
local flags = context.flags --[[@as flags_主线_青羽录10_楚家庄后山夜]]
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
function tes111t()
    s.showGameMapUI()
end


--每次载入调用
function start()
    s.playMusic("qingming")
    s.setActive(npcs.ChuQing,false)
    s.setPos(npcs.zhujue,pos.pos_start)

    s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录10_楚家庄后山夜A.unity")
    s.setActive(npcs.ChuQing,true)
    s.readyToStart(false,0)

    s.addTask(310030,1)
end

function TriggerPlayTimeline()
    s.setActive(objs.Trigger1,false)
    -- s.finishTask(310030, 1)
    s.setActive(npcs.zhujue,false)
    s.setActive(npcs.ChuQing,false)

    s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录10_楚家庄后山夜B.unity", false)
    s.runAvgTag("青羽录（荆成之章）/青羽录_第十章_007","青羽录_第十章_007")

    s.lightScreen()
    s.setNewbieFlags("chapter_qyl_23_finished",1)
    s.finishInfiniteStory(true)

end