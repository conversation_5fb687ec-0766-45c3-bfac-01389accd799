--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_贼寇山洞/flags_挑战_贼寇山洞")


---@class 角色_挑战_贼寇山洞
local npcs = {
    zhujue = "角色/zhujue",
    enemy_gate_1 = "角色/enemy_gate_1",
    enemy_gate_2 = "角色/enemy_gate_2",
    enemy_2 = "角色/enemy_2",
    enemy_3 = "角色/enemy_3",
    caiyaonv = "角色/caiyaonv",
    boss = "角色/boss",
}

---@class 物体_挑战_贼寇山洞
local objs = {
    bossTrigger = "物体/bossTrigger",
    quit = "物体/quit",
    enemy_battle_gate_trigger = "物体/enemy_battle_gate_trigger",
    enemy_gate_show_trigger = "物体/enemy_gate_show_trigger",
    boss_weapon = "物体/boss_weapon",
    chest1 = "物体/chest1",
}

---@class 相机_挑战_贼寇山洞
local cameras = {
    camera_start = "相机/camera_start",
    camera_boss_1 = "相机/camera_boss_1",
    camera_boss_2 = "相机/camera_boss_2",
    camera_boss_3 = "相机/camera_boss_3",
}

---@class 位置_挑战_贼寇山洞
local pos = {
    start = "位置/start",
    pre_start = "位置/pre_start",
    pre_start_show = "位置/pre_start_show",
    boss_pos_1 = "位置/boss_pos_1",
    boss_pos_2 = "位置/boss_pos_2",
    boss_pos_3 = "位置/boss_pos_3",
    boss_pos_6 = "位置/boss_pos_6",
    boss_pos_7 = "位置/boss_pos_7",
    pos_enemy_defeat = "位置/pos_enemy_defeat",
    pos_boss_defeat = "位置/pos_boss_defeat",
    pos_start_show_enemy_1 = "位置/pos_start_show_enemy_1",
    pos_start_show_enemy_2 = "位置/pos_start_show_enemy_2",
}

---@class 资产_挑战_贼寇山洞
local assets = {
    --- "Boss演出"
    Timeline_1 = "资产/Timeline_1",
}

---@class 动作_挑战_贼寇山洞
local animationClips = {
}

---@class 挑战_贼寇山洞
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
