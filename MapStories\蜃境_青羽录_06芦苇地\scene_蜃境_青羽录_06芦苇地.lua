
local context = require("MapStories/蜃境_青羽录_06芦苇地/scene_蜃境_青羽录_06芦苇地_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_青羽录_06芦苇地
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")



function getFlags(...)
    return flags
end


----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")
require("MapStories/MapStoryCommonTools")

function start()
    print("start called")
    s.setPos(npcs.zhujue, pos.start)
    s.readyToStart(true)
    refreshMapStates()
    try_execute_newbieGuide()
end


function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function newbieGuideInLevel()

end

--默认初始化的地图显隐状态
function reset_all()
    s.setActive(npcs.qyl_24, flags.level_step == 1)
    s.setActive(objs.door, flags.level_step >= 2)

    
    s.setActive(objs.qyl6_1010,not s.hasUnlockMemorySlot("qyl6_1010"))
    s.setActive(objs.qyl6_1011,not s.hasUnlockMemorySlot("qyl6_1011"))
    s.setActive(objs.hitevent, flags.XMGS_0_step == 0)
    s.setActive(npcs.XMGS_NPC, s.getCurrentTaskStep("青羽录蜃境之主支线") ~= "finished")
    s.setActive(objs.XMGS_WMS, s.getCurrentTaskStep("青羽录蜃境之主支线") == "backToWumingshi"  or s.getCurrentTaskStep("青羽录蜃境之主支线") == "battle")
    s.setActive(objs.XMGS_LZ, s.getCurrentTaskStep("青羽录蜃境之主支线") == "findenemy")
    s.setActive(objs.XMGS_SS, s.getCurrentTaskStep("青羽录蜃境之主支线") == "findenemy")
    s.setActive(npcs.elite_03, s.hasUnlockMemorySlot("qyl6_battle_101"))
    s.setActive(npcs.boss_04, s.hasUnlockMemorySlot("qyl6_battle_102"))
    s.removeAllGameMapGuides()
end

function refreshMapStates()
    local step = get_level_step()
    s.playMusic("startjourney")

    reset_all()
    s.unTraceTask(10000001,50)
    if step == 0 then
        ----第一次进入场景, 这里加入入场演出
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_青羽录_告别之地_入口", nil, 0)
        s.setTaskStep("青羽录蜃楼之主","forwardToExplore")
        next_level_step(true)
    elseif step == 1 then
        s.setTaskStep("青羽录蜃楼之主","forwardToExplore")
        level_guide_to(npcs.qyl_24, 0.8)
    elseif step == 2 then
        s.setTaskStep("青羽录蜃楼之主","beatChuQingXieZi")
        level_guide_to(objs.door, 0)
    else
        s.setTaskStep("青羽录蜃楼之主","leaveLuWeiDiShenJing")
        level_guide_to(objs.exit)
    end
end

----------------------OVERRIDE----------------------------------

function qyl_24()
    executeMemSlot("qyl_24", function() 
        s.blackScreen(0)
        s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录11_告别之地.unity", false)
        s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录11_回忆之地.unity")
        s.setTaskStep("青羽录蜃楼之主","beatChuQingXieZi")    
    end,function()
        s.setActive(npcs.qyl_24,false)
        s.setActive(objs.door,true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_青羽录_告别之地_渲染", nil, 0)
        next_level_step(true)
        refreshMapStates()
    end)
end

--支线剧情 寻觅高手
function XMGS_0()
    if flags.XMGS_0_step ~= 1 then
        s.soloAnimState(npcs.wumingshi,"BScratch")
        s.turnToAsync(npcs.zhujue, npcs.wumingshi)
        s.turnToAsync(npcs.wumingshi, npcs.zhujue)
        --s.lookAt(npcs.wumingshi, nil, npcs.zhujue, true)
        s.lookAt(npcs.zhujue, nil, npcs.wumingshi, true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地","蜃境_告别之地_支线_寻觅高手")
        s.setActive(objs.hitevent,false)
        flags.XMGS_0_step = 1
        s.setTaskStep("青羽录蜃境之主支线","findenemy")
        s.setActive(objs.XMGS_SS,true)
        s.setActive(objs.XMGS_LZ,true)     
    end
end
function XMGS_1()
    if s.getCurrentTaskStep("青羽录蜃境之主支线") == "backToWumingshi"  or s.getCurrentTaskStep("青羽录蜃境之主支线") == "battle" then
        s.turnToAsync(npcs.zhujue, npcs.wumingshi)
        s.turnToAsync(npcs.wumingshi, npcs.zhujue)
        --s.lookAt(npcs.wumingshi, nil, npcs.zhujue, true)
        s.lookAt(npcs.zhujue, nil, npcs.wumingshi, true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地","蜃境_告别之地_支线_寻觅高手4")
                s.setTaskStep("青羽录蜃境之主支线","battle")
        local isWin = s.battle("蜃境_告别之地_支线_寻觅高手4")
        if isWin then
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_告别之地_支线_寻觅高手5", nil, 0)
            s.setActive(objs.XMGS_WMS,false)
            s.setTaskStep("青羽录蜃境之主支线","finished")
            capi.finishMemorySlot("qyl_sideTask_llzy2")
        end
    end
end

function XMGS_2()
    if s.getCurrentTaskStep("青羽录蜃境之主支线") == "findenemy" then
        s.turnToAsync(npcs.zhujue, npcs.laozhe)
        s.turnToAsync(npcs.laozhe, npcs.zhujue)
        --s.lookAt(npcs.laozhe, nil, npcs.zhujue, true)
        s.lookAt(npcs.zhujue, nil, npcs.laozhe, true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_告别之地_支线_寻觅高手2", nil, 0)
        flags.XMGS2_step = 1
        if flags.XMGS3_step == 1 then
            s.setTaskStep("青羽录蜃境之主支线","backToWumingshi")  
            s.setActive(objs.XMGS_WMS,true)
            s.setActive(objs.XMGS_SS,false)
            s.setActive(objs.XMGS_LZ,false)
        end
    end
end


function XMGS_3()
    if s.getCurrentTaskStep("青羽录蜃境之主支线") == "findenemy" then
        s.turnToAsync(npcs.zhujue, npcs.shusheng)
        s.turnToAsync(npcs.shusheng, npcs.zhujue)
        --s.lookAt(npcs.shusheng, nil, npcs.zhujue, true)
        s.lookAt(npcs.zhujue, nil, npcs.shusheng, true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_告别之地_支线_寻觅高手3", nil, 0)
        s.soloAnimState(npcs.shusheng,"Special_1")
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_告别之地_支线_寻觅高手3演出", nil, 0)
        flags.XMGS3_step = 1
        if flags.XMGS2_step == 1 then
            s.setTaskStep("青羽录蜃境之主支线","backToWumingshi")  
            s.setActive(objs.XMGS_WMS,true)
            s.setActive(objs.XMGS_SS,false)
            s.setActive(objs.XMGS_LZ,false)
        end
    end
end


function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene()
end

function door()
    --s.changeMapStory("蜃境_青羽录_06最终房")
    s.changeMap("Assets/GameScenes/游历场景/蜃境_青羽录_06最终房/蜃境_青羽录_06最终房.unity", "位置/start")
end

function test()
    local step = get_level_step()
    s.popInfo(step)
end