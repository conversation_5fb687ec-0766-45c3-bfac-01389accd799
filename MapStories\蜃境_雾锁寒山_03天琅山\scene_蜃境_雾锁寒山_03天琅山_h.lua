--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_雾锁寒山_03天琅山/flags_蜃境_雾锁寒山_03天琅山")


---@class 角色_蜃境_雾锁寒山_03天琅山
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "wshs_07"
    wshs_07 = "角色/wshs_07",
    --- "wshs_08"
    wshs_08 = "角色/wshs_08",
    --- "wshs_09"
    wshs_09 = "角色/wshs_09",
    --- "enemy_1"
    enemy_1 = "角色/enemy_1",
    --- "enemy_2"
    enemy_2 = "角色/enemy_2",
    --- "enemy_3"
    enemy_3 = "角色/enemy_3",
    --- "enemy_4"
    enemy_4 = "角色/enemy_4",
    --- "elite_5"
    elite_5 = "角色/elite_5",
    --- "boss_6"
    boss_6 = "角色/boss_6",
    --- "墨七幻影1"
    moqi_shadow_1 = "角色/moqi_shadow_1",
    --- "墨七幻影2"
    moqi_shadow_2 = "角色/moqi_shadow_2",
    --- "涂舟幻影1"
    tuzhou_shadow_1 = "角色/tuzhou_shadow_1",
    --- "精英怪1"
    role_elite_5 = "角色/role_elite_5",
    --- "老年阿莫"
    amo = "角色/amo",
    --- "思思"
    sisi = "角色/sisi",
    elite_7 = "角色/elite_7",
    boss_8 = "角色/boss_8",
}

---@class 物体_蜃境_雾锁寒山_03天琅山
local objs = {
    --- "出口"
    exit = "物体/exit",
    --- "chest_1"
    chest_1 = "物体/chest_1",
    --- "chest_2"
    chest_2 = "物体/chest_2",
    --- "evilAir_1"
    evilAir_1 = "物体/evilAir_1",
    --- "evilAirChest_1"
    evilAirChest_1 = "物体/evilAirChest_1",
    --- "小怪1守卫地"
    enemy_1_collider = "物体/enemy_1_collider",
    --- "小怪2守卫地"
    enemy_2_collider = "物体/enemy_2_collider",
    --- "小怪3守卫地"
    enemy_3_collider = "物体/enemy_3_collider",
    --- "小怪4守卫地"
    enemy_4_collider = "物体/enemy_4_collider",
    --- "enter_阿莫触发器"
    trigger_amo = "物体/trigger_amo",
    --- "wshs3_1004"
    wshs3_1004 = "物体/wshs3_1004",
    --- "wshs3_1000"
    wshs3_1000 = "物体/wshs3_1000",
}

---@class 相机_蜃境_雾锁寒山_03天琅山
local cameras = {
    cam_boss_near = "相机/cam_boss_near",
    cam_boss_far = "相机/cam_boss_far",
    cam_main = "相机/cam_main",
    cam_tl_far = "相机/cam_tl_far",
    cam_tl_near = "相机/cam_tl_near",
}

---@class 位置_蜃境_雾锁寒山_03天琅山
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    --- "敌人1位置"
    enemy_1 = "位置/enemy_1",
    --- "敌人2位置"
    enemy_2 = "位置/enemy_2",
    --- "敌人3位置"
    enemy_3 = "位置/enemy_3",
    --- "敌人4位置"
    enemy_4 = "位置/enemy_4",
    --- "涂舟返回位置"
    tuzhou_back = "位置/tuzhou_back",
}

---@class 资产_蜃境_雾锁寒山_03天琅山
local assets = {
}

---@class 动作_蜃境_雾锁寒山_03天琅山
local animationClips = {
}

---@class 蜃境_雾锁寒山_03天琅山
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
