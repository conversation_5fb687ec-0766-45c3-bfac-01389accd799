--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_长生劫_05仙外山径/flags_蜃境_长生劫_05仙外山径")


---@class 角色_蜃境_长生劫_05仙外山径
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "csj_18"
    csj_18 = "角色/csj_18",
    --- "csj_19"
    csj_19 = "角色/csj_19",
    --- "csj_20"
    csj_20 = "角色/csj_20",
    --- "enemy_1"
    enemy_1 = "角色/enemy_1",
    --- "enemy_2"
    enemy_2 = "角色/enemy_2",
    --- "enemy_3"
    enemy_3 = "角色/enemy_3",
    --- "enemy_4"
    enemy_4 = "角色/enemy_4",
}

---@class 物体_蜃境_长生劫_05仙外山径
local objs = {
    --- "出口"
    exit = "物体/exit",
    --- "chest_1"
    chest_1 = "物体/chest_1",
}

---@class 相机_蜃境_长生劫_05仙外山径
local cameras = {
}

---@class 位置_蜃境_长生劫_05仙外山径
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
}

---@class 资产_蜃境_长生劫_05仙外山径
local assets = {
}

---@class 动作_蜃境_长生劫_05仙外山径
local animationClips = {
}

---@class 蜃境_长生劫_05仙外山径
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
