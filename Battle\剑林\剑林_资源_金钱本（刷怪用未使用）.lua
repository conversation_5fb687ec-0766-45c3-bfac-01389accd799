local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)
local n = 0 --标记用，判断是否可以判断战斗结算，记录刷怪波次
local total = 40 --总刷怪波次
local time = 1.5 --刷新时间
local MonsterNum = 0 --判断已经刷了多少个怪了
local TotalMonsterNum = 150 --最大刷怪数量控制,超过该值立马停止刷怪
local spawnPoints = {
    --上面三个小怪
    { x = 3 , y = 4}, -- 点位1的坐标
    { x = 0, y = 4}, -- 点位2的坐标
    { x = -3, y = 4}, -- 点位3的坐标
    --下面三个小怪
    { x = 3, y = -4}, -- 点位4的坐标
    { x = 0, y = -4}, -- 点位5的坐标
    { x = -3, y = -4}, -- 点位6的坐标
    --右边三个小怪
    { x = 6, y = 4}, -- 点位7的坐标
    { x = 6, y = 0}, -- 点位8的坐标
    { x = 6, y = -4}, -- 点位9的坐标
    --左边三个小怪
    { x = -6, y = 4}, -- 点位10的坐标
    { x = -6, y = 0}, -- 点位11的坐标
    { x = -6, y = -4}  -- 点位12的坐标
}

function init()
    --战斗结算条件
    battle.ForceNotEndBattle = true
    --第一排小怪，定义小怪位置
    createRoles("剑林_叛军士兵", 15, eTeam, { {4, -1}, {4, -3}, {4, 1}, {4, 3}, {4, 0} })
    --第二排小怪，定义小怪位置
    createRoles("剑林_叛军长枪兵", 15, eTeam, { {6, 3}, {6, 1}, {6, 0}, {6, -1}, {6, -3} })
end

function start()
    for i = 1, total do
        if MonsterNum >= TotalMonsterNum then
            battle.ForceNotEndBattle = false
            break
        end
        local m = math.random(1, 4)
        local joinEnemyFunc = function() joinEnemy(m) end
        bcapi.add_timer_trigger(time * (i), joinEnemyFunc)
        MonsterNum = MonsterNum + m
        n = n + 1
    end
end

--遍历所填的数组保证所需要的怪物都生成出来
function createRoles(roleName, count, team, positions)
    bcapi.async_call(function()
        for _, pos in ipairs(positions) do
            bcapi.create_join_role(roleName, count, team, bcapi.Vector2(pos[1], pos[2]), 0.1)
        end
    end)
end
--传进来的是几就生成几个怪
function joinEnemy(count)
    local randomIndex = getRandom(count)
    bcapi.async_call(function()
        for i = 1, count do
            local roleName = i <= 2 and "剑林_叛军士兵" or "剑林_叛军长枪兵"
            local point = spawnPoints[randomIndex[i]]
            ---@diagnostic disable-next-line: need-check-nil
            bcapi.create_join_role(roleName, 15, eTeam, bcapi.Vector2(point.x, point.y), 0.1)
        end
    end)
    if n == total then
        battle.ForceNotEndBattle = false
    end
end

---@return table randoms
function getRandom(count)
    local numbers = {}
    local randoms = {}
    for i = 1, count do
        ---@type number
        local number
        repeat
            number = math.random(1, #spawnPoints)
        until not numbers[number]
        table.insert(randoms, number)
        ---@diagnostic disable-next-line: inject-field-fail
        numbers[number] = true
    end
    return randoms
end