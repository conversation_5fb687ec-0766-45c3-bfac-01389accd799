local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    local time = 903
    if(bcapi.get_team_dict_has_key(0, "战斗时间"))then
        time = bcapi.get_team_dict_int_value(0, "战斗时间", time) + 3
    end
    bcapi.reset_limit_time(time)
end

function start()
    bcapi.async_call(function()
        local isSummon = 0
        local isCard = 0

        if(bcapi.get_team_dict_has_key(0, "是否召唤"))then
            isSummon = bcapi.get_team_dict_int_value(0, "是否召唤", 0)
        end
        if(bcapi.get_team_dict_has_key(0, "是否策略卡"))then
            isCard = bcapi.get_team_dict_int_value(0, "是否策略卡", 0)
        end

        if(isSummon == 1)then
            bcapi.create_and_summon_role("副本_夜战开封_燕路重", 65, bcapi.get_team(0), bcapi.Vector2(-6, 0), 0.1,-1,1,1,1,1,1,"")
        end
        if(isCard == 1)then
            bcapi.create_and_summon_role("副本_夜战开封_开封士兵", 55, bcapi.get_team(0), bcapi.Vector2(-6, 2), 0.1,-1,1,1,1,1,1,"")
            bcapi.create_and_summon_role("副本_夜战开封_开封士兵", 55, bcapi.get_team(0), bcapi.Vector2(-6, -2), 0.1,-1,1,1,1,1,1,"")
        end
        bcapi.add_timer_trigger(2, StartTalk)
    end)
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("越千山#副本_夜战开封_越千山", "两位如此苦苦相逼，越某也不得不拿出十分精力应战了。")
        bcapi.talk("越千山#副本_夜战开封_越千山", "恒山越千山，请两位赐教！")
        bcapi.resume_and_show_ui()

        bcapi.add_condition_trigger("[%enemy:副本_夜战开封_越千山->hp_pct%][<=]0.5", 1, EnemyStageTwo)
        bcapi.add_condition_trigger("[%enemy:副本_夜战开封_越千山->flag:变身完毕%][>]0", 1, EnemyEnhanceDesc)
    end)
end

function EnemyStageTwo()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("越千山#副本_夜战开封_越千山", "呵呵呵呵，既然你们非想找死，那老夫也就不再留手了……")
        bcapi.talk("越千山#副本_夜战开封_越千山", "能见识到这门无上心法的威力，你们当死而无憾。")
        bcapi.resume_and_show_ui()

        local Boss = bcapi.get_role(1, "副本_夜战开封_越千山")
        Boss.Stat.BattleFlagDic:set_Item("可以变身", "1")
    end)
end

function EnemyEnhanceDesc()
    bcapi.async_call(function()
        bcapi.show_pop_info("越千山进入魔化状态，免疫晕眩并获得超强御效果，在御伤持续期间，将免疫所有伤害！", 4)    
    end)
end