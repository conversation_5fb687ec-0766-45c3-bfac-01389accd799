--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/漫游_雪谷/flags_漫游_雪谷")


---@class 角色_漫游_雪谷_远古地宫_入口
local npcs = {
    --- "墨七"
    moqi = "角色/moqi",
    --- "玄阙宫弟子"
    dizi = "角色/dizi",
}

---@class 物体_漫游_雪谷_远古地宫_入口
local objs = {
    --- "重伤猎物"
    animal = "物体/animal",
    --- "初始剧情触发器"
    triggerOpening = "物体/triggerOpening",
    --- "地宫传送入口"
    teleport = "物体/teleport",
    --- "地宫大门"
    door = "物体/door",
    --- "玄武血"
    xuanwu = "物体/xuanwu",
}

---@class 相机_漫游_雪谷_远古地宫_入口
local cameras = {
    --- "初始相机"
    start = "相机/start",
}

---@class 位置_漫游_雪谷_远古地宫_入口
local pos = {
    --- "通往营地"
    Yingdi = "位置/Yingdi",
    --- "通往地下"
    Neath = "位置/Neath",
    --- "墨七初始剧情"
    MQ_start = "位置/MQ_start",
    --- "墨七蹲下位置"
    MQ_squart = "位置/MQ_squart",
}

---@class 资产_漫游_雪谷_远古地宫_入口
local assets = {
}

---@class 动作_漫游_雪谷_远古地宫_入口
local animationClips = {
}

---@class 漫游_雪谷_远古地宫_入口
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
