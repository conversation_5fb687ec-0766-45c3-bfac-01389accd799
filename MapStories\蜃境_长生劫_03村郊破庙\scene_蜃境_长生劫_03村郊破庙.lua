
---@type 蜃境_长生劫_03村郊破庙
local context = require("MapStories/蜃境_长生劫_03村郊破庙/scene_蜃境_长生劫_03村郊破庙_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_长生劫_03村郊破庙
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")

s.setMapStatus("collection_csj_1005", 0)
----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

--精英怪战斗
function eliteBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end


function test()
    s.setPos(npcs.zhujue,pos.pos_start)
end

function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数
    reset_all()

    --第一次进入场景
    if step == 0 then
        next_level_step(true)
        s.setTaskStep("长生劫村郊破庙","forwardToExplore")
    elseif step == 1 then
        level_guide_to(npcs.csj_11, 1)
    elseif step == 2 then
        level_guide_to(npcs.csj_12, 1)
    elseif step == 3 then
        level_guide_to(npcs.csj_13, 1)
    elseif step == 4 then
        level_guide_to(npcs.csj_14, 1)
    else
        level_guide_to(objs.exit)
    end
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.csj_11, false)
    s.setActive(npcs.csj_12, false)
    s.setActive(npcs.csj_13, false)
    s.setActive(npcs.csj_14, false)
    --删除所有的引导点
    s.removeAllGameMapGuides()


    --支线末俗流弊显隐
    s.setActive(npcs.MSLB_NPC,s.getCurrentTaskStep("长生劫支线末俗流弊") ~= "finished")
    --删除已开启的宝箱邪气
    s.setActive(objs.evilAirChest_1,flags.open_shadowChest_1 == 0)
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("长生劫")
end


----------------------------------支线剧情 末俗流弊----------------------------------
--第一段
function MSLB_01()
    if s.getCurrentTaskStep("长生劫支线末俗流弊") == "deactive" and flags.MSLB_01_laoren == 0 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_村郊破庙","蜃境_村郊破庙_支线_末俗流弊")
        s.setTaskStep("长生劫支线末俗流弊","shanshenContent")
        flags.MSLB_01_laoren = 1
    else
        s.talk(npcs.MSLB_NPC_laoren,"这世道变了啊")
    end
end

--第二段
function MSLB_02()
    if s.getCurrentTaskStep("长生劫支线末俗流弊") == "shanshenContent" and flags.MSLB_01_laofuren == 0 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_村郊破庙","蜃境_村郊破庙_支线_末俗流弊2")
        flags.MSLB_01_laofuren = 1
        if s.getCurrentTaskStep("长生劫支线末俗流弊") == "shanshenContent" and flags.MSLB_01_laoren == 1 and flags.MSLB_01_laofuren == 1 and flags.MSLB_01_nianqingren == 1 and flags.MSLB_01_xiaohai == 1 then
            s.setTaskStep("长生劫支线末俗流弊","gotoshanshenmiao")
        end
    else
        s.talk(npcs.MSLB_NPC_laofuren,"现在的年轻人，还有人记得山神吗？")
    end
end

--第三段
function MSLB_03()
    if s.getCurrentTaskStep("长生劫支线末俗流弊") == "shanshenContent" and flags.MSLB_01_nianqingren == 0 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_村郊破庙","蜃境_村郊破庙_支线_末俗流弊3")
        flags.MSLB_01_nianqingren = 1
    if s.getCurrentTaskStep("长生劫支线末俗流弊") == "shanshenContent" and flags.MSLB_01_laoren == 1 and flags.MSLB_01_laofuren == 1 and flags.MSLB_01_nianqingren == 1 and flags.MSLB_01_xiaohai == 1 then
        s.setTaskStep("长生劫支线末俗流弊","gotoshanshenmiao")
    end
    else
        s.talk(npcs.MSLB_NPC_nianqingren,"山神？管他什么山神不山神")
    end
end

--第四段
function MSLB_04()
    if s.getCurrentTaskStep("长生劫支线末俗流弊") == "shanshenContent" and flags.MSLB_01_xiaohai == 0 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_村郊破庙","蜃境_村郊破庙_支线_末俗流弊4")
        flags.MSLB_01_xiaohai = 1
    if s.getCurrentTaskStep("长生劫支线末俗流弊") == "shanshenContent" and flags.MSLB_01_laoren == 1 and flags.MSLB_01_laofuren == 1 and flags.MSLB_01_nianqingren == 1 and flags.MSLB_01_xiaohai == 1 then
        s.setTaskStep("长生劫支线末俗流弊","gotoshanshenmiao")
    end
    else
        s.talk(npcs.MSLB_NPC_xiaohai,"地仙爷爷给我糖吃，是好山神")
    end
end

--第五段
function MSLB_05()
    -- 播放角色动画
    s.animState(npcs.zhujue, "BGreet", true, false, 1.5, true, true, 1)
    
    -- 如果任务步骤是去山神庙,则播放剧情并完成任务
    if s.getCurrentTaskStep("长生劫支线末俗流弊") == "gotoshanshenmiao" then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_村郊破庙", "蜃境_村郊破庙_支线_末俗流弊5")
        s.setTaskStep("长生劫支线末俗流弊", "finished")
    end

    -- 等待动画播放完成
    s.wait(3)

    -- 更新收集状态
    s.setMapStatus("collection_csj_1005",s.getMapStatusInt("collection_csj_1005") + 1)
    local collection_csj_1005_status = s.getMapStatusInt("collection_csj_1005")   
    -- 检查是否达到收集条件(虽然代码写的大于等于2，实际游戏中效果是拜3次山神后触发)
    if collection_csj_1005_status >= 2 and flags.collection_csj_1005 == 0 then
        csj_1005("csj_1005")
        flags.collection_csj_1005 = 1
    end
end




-------------------解密和宝箱---------------------
function shadowChest(id)
    if flags.open_shadowChest_1 == 0 then
        s.popInfo("需击败附近镇守的蜃境怪物！！！")
    else
        require("MapStories/MapStoryCommonTools").roamMapChest(id)
    end
end
-------------------关卡记忆碎片和战斗实现---------------------
--收集物开启
function csj_1005(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
end
function enemy_battle(id,enemyKey)
    print("敌人战斗")
    local colliderKey = enemyKey.."_collider"

    s.popInfo("你已被发现！！！")

    --s.setActive(objs[colliderKey],false)
    s.turnTo(npcs[enemyKey],npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs[enemyKey])

    s.animState(npcs[enemyKey],"BAssault_Loop",true)
    s.wait(2)

    require("MapStories/MapStoryCommonTools").roamMapBattle(    id,function()
        s.popInfo("战斗胜利！！！")
        flags[enemyKey.."_battle"] = 1
        s.setActive(objs[colliderKey],false)
        s.setActive(npcs[enemyKey],false)
    end,
    function()
        battle_cancle(id,enemyKey)
    end,
    function()
        battle_lose(id,enemyKey)
    end
)

    s.wait(0.5)
    refreshMapStates()
end

function battle_lose(id,enemyKey)
    local colliderKey = enemyKey.."_collider"
    print("战斗失败")
    s.popInfo("战斗失败！！！")

    s.blackScreen()
    s.animState(npcs[enemyKey],"BAssault_Loop",false)
    s.setActive(objs[colliderKey],true)
    s.setPos(npcs.zhujue,pos[enemyKey],false)
    s.lightScreen()
end
function battle_cancle(id,enemyKey)
    local colliderKey = enemyKey.."_collider"

    s.blackScreen()
    s.animState(npcs[enemyKey],"BAssault_Loop",false)
    s.setActive(objs[colliderKey],true)
    s.setPos(npcs.zhujue,pos[enemyKey],false)
    s.lightScreen()
end

function fightInBack(enemyKey)
    local colliderKey = enemyKey.."_collider"
    s.setActive(objs[colliderKey],false)

    s.animState(npcs.zhujue,"Special_2",true)
    s.wait(1)
    s.playSound("sfx","拳击3")
    s.wait(0.6)

    s.animState(npcs[enemyKey],"BDie",true)
    s.setDeath(npcs[enemyKey],0.5,false)

    s.popInfo("敌人暂时被击倒！！！")

    refreshMapStates()
end

function elite_battle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,function()
        flags.open_shadowChest_1 = 1
        s.setActive(objs.evilAirChest_1,false)
    end)
end

function csj_11()
    executeMemSlot("csj_11", function()
        s.playTimelineScene("Assets/GameScenes/动画演出场景/002椿岁之章/主线_长生劫3_拜访地仙.unity")
    end)
end

function csj_12()
    executeMemSlot("csj_12", function()
        s.playTimelineScene("Assets/GameScenes/动画演出场景/002椿岁之章/主线_长生劫4_赴约遇袭.unity")
    end)
end

function csj_13()
    executeMemSlot("csj_13", quick_play_avg)
end

function csj_14()
    executeMemSlot("csj_14", function()
        s.playTimelineScene("Assets/GameScenes/动画演出场景/002椿岁之章/主线_长生劫5_威逼阿俏.unity")
    end)
    s.setTaskStep("长生劫村郊破庙","leave")
end