--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_裂雾飞鹰_05玄阙宫求道院/flags_蜃境_裂雾飞鹰_05玄阙宫求道院")


---@class 角色_蜃境_裂雾飞鹰_05玄阙宫求道院
local npcs = {
    zhujue = "角色/zhujue",
    lwfy_15 = "角色/lwfy_15",
    lwfy_16 = "角色/lwfy_16",
    lwfy_17 = "角色/lwfy_17",
    lwfy_18 = "角色/lwfy_18",
    lwfy_19 = "角色/lwfy_19",
    enemy_1 = "角色/enemy_1",
    enemy_2 = "角色/enemy_2",
    enemy_3 = "角色/enemy_3",
    enemy_4 = "角色/enemy_4",
    elite_5 = "角色/elite_5",
    boss_6 = "角色/boss_6",
    npc_1 = "角色/npc_1",
    npc_2 = "角色/npc_2",
    yuqiong = "角色/yuqiong",
    jiguanshou = "角色/jiguanshou",
}

---@class 物体_蜃境_裂雾飞鹰_05玄阙宫求道院
local objs = {
    exit = "物体/exit",
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
    lwfy_1005 = "物体/lwfy_1005",
    lwfy_1006 = "物体/lwfy_1006",
    lwfy_1007 = "物体/lwfy_1007",
    sideTask_HuHua_hint = "物体/sideTask_HuHua_hint",
}

---@class 相机_蜃境_裂雾飞鹰_05玄阙宫求道院
local cameras = {
}

---@class 位置_蜃境_裂雾飞鹰_05玄阙宫求道院
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
    fly_pos_1 = "位置/fly_pos_1",
    fly_pos_2 = "位置/fly_pos_2",
}

---@class 资产_蜃境_裂雾飞鹰_05玄阙宫求道院
local assets = {
}

---@class 动作_蜃境_裂雾飞鹰_05玄阙宫求道院
local animationClips = {
}

---@class 蜃境_裂雾飞鹰_05玄阙宫求道院
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
