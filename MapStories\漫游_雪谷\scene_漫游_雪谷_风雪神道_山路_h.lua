--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/漫游_雪谷/flags_漫游_雪谷")


---@class 角色_漫游_雪谷_风雪神道_山路
local npcs = {
    --- "明慧"
    minghui = "角色/minghui",
    --- "墨七"
    moqi = "角色/moqi",
    --- "毛欢"
    maohuan = "角色/maohuan",
}

---@class 物体_漫游_雪谷_风雪神道_山路
local objs = {
    --- "积雪"
    snow = "物体/snow",
    --- "倒塌的石碑"
    steledown = "物体/steledown",
    --- "石碑"
    stele = "物体/stele",
}

---@class 相机_漫游_雪谷_风雪神道_山路
local cameras = {
}

---@class 位置_漫游_雪谷_风雪神道_山路
local pos = {
    --- "通向山脚"
    Shanjiao = "位置/Shanjiao",
    --- "通向悬崖"
    Cliff = "位置/Cliff",
}

---@class 资产_漫游_雪谷_风雪神道_山路
local assets = {
}

---@class 动作_漫游_雪谷_风雪神道_山路
local animationClips = {
}

---@class 漫游_雪谷_风雪神道_山路
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
