local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)
local waveCount = 1   --用来记录增援次数
local enemyTb = {"通用_沙匪_小怪_沙匪男_剑","通用_沙匪_小怪_沙匪男_剑","通用_沙匪_小怪_沙匪男_斧","通用_沙匪_小怪_沙匪男_斧"}    --记录前4次增援的团队
local timeOfSpawn = {25,20,20,15}    --用来记录3次时间触发器的倒计时时间，我们一共需要4次时间触发器，分别控制前4次增援的底线时间，第一次在开场已经启动了。
local waveSpawned = {0,0,0,0}     --用来记录4次增援是否已经完成，全部击杀或者时间均可导致增援，其中一个触发就会设置成1，另外一个就不会再做任何事了。
local positionY = {2.5,0.5,-1.5,-3.5}      --记录时间落怪的Y轴坐标，因为X轴都一样，所以只管Y
local isOnKilling = false

--初始化，刷4个怪
function init()
    InitSpawn()
end


function start()
    bcapi.async_call(function()
        --创建虚拟角色
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        --开局对话
        InitTalk()
        --增加第一个计时触发器，控制第一波增援的底线时间
        bcapi.add_timer_trigger(25,timerController)  
        --增加第一个击杀触发器，如果初始第一波怪被提前击杀，则第一波增援提前到
        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0.3, killingController)
    end)
end

--开场对话
function InitTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        -- 播放对白
        bcapi.talk("怀念", "少侠，我已经点燃了第一座火台。")
        bcapi.talk("沙城卫兵", "什么人？！")
        bcapi.talk("沙城卫兵", "来人啊，这里有个纵火妖僧，大家快来抓住他。")
        bcapi.talk("怀念", "不好，火光惊动了此间守卫，请少侠出手帮忙。")

        -- 恢复战斗进程
        bcapi.resume_and_show_ui()
    end)
end

--初始化，刷4个怪的具体代码
function InitSpawn()
    bcapi.async_call(function()
        bcapi.create_join_role("通用_沙匪_小怪_沙匪男_双斧", 40, eTeam, bcapi.Vector2(6, 2.5), 0.1)
        bcapi.create_join_role("通用_沙匪_小怪_沙匪男_双斧", 40, eTeam, bcapi.Vector2(6, 0.5), 0.1)
        bcapi.create_join_role("通用_沙匪_小怪_沙匪男_狼牙棒", 40, eTeam, bcapi.Vector2(6, -1.5), 0.1)
        bcapi.create_join_role("通用_沙匪_小怪_沙匪男_狼牙棒", 40, eTeam, bcapi.Vector2(6, -3.5), 0.1)
    end)
end

--和尚报数，初始点燃一个，后面每一次增援点燃一个，杀完6波增援后，7个全部点燃
function MonkCounts()
    bcapi.async_call(function()
        if waveCount == 1 then
            bcapi.pause_and_hide_ui()
            bcapi.append_async_aside("怀念",1,"第二座火台已经点燃了","","中年和尚")
            bcapi.resume_and_show_ui()   
        end

        if waveCount == 2 then
            bcapi.pause_and_hide_ui()
            bcapi.append_async_aside("怀念",1,"贫僧刚刚把第三座点燃了","","中年和尚")
            bcapi.resume_and_show_ui()   
        end

        if waveCount == 3 then
            bcapi.pause_and_hide_ui()
            bcapi.append_async_aside("怀念",1,"这是第四座了","","中年和尚")
            bcapi.resume_and_show_ui()   
        end

        if waveCount == 4 then
            bcapi.pause_and_hide_ui()
            bcapi.append_async_aside("怀念",1,"第五座也已经点燃了","","中年和尚")
            bcapi.resume_and_show_ui()  
        end

        if waveCount == 5 then
            bcapi.pause_and_hide_ui()
            bcapi.append_async_aside("怀念",1,"六座了，就快完成了，少侠再坚持一下","","中年和尚")
            bcapi.resume_and_show_ui()  
        end
    end)
end
--和尚报最后一座也点燃，之后胜利
function win()

    bcapi.async_call(function()

            bcapi.pause_and_hide_ui()
            bcapi.talk("怀念", "最后一座也被点燃了，大功告成，少侠可还好？")
            bcapi.win()
    end)
end

--击杀触发器的具体function
function killingController()
    bcapi.async_call(function()
    --[[
        if (isOnKilling) then 
            return 
        end

        isOnKilling = true
    ]]
        --第六个击杀触发器，说明已经杀了6波怪，结束。
        if waveCount == 6 then
            win()
        end
        --第五个击杀触发器，这一波没有时间控制，必须击杀，然后挑战最后一波。最后一波6个怪，难度增大。
        if waveCount == 5  then 
            

            bcapi.create_join_role("通用_沙匪_小怪_沙匪男_弓", 40, eTeam, bcapi.Vector2(6, 3), 0.1)
            bcapi.create_join_role("通用_沙匪_小怪_沙匪男_双斧", 40, eTeam, bcapi.Vector2(6, 1), 0.1)
            bcapi.create_join_role("通用_沙匪_小怪_沙匪男_双斧", 40, eTeam, bcapi.Vector2(6, -1), 0.1)
            bcapi.create_join_role("通用_沙匪_小怪_沙匪男_弓", 40, eTeam, bcapi.Vector2(6, -3), 0.1)
            bcapi.create_join_role("通用_沙匪_小怪_沙匪男_斧盾", 40, eTeam, bcapi.Vector2(4, -2), 0.1)
            bcapi.create_join_role("通用_沙匪_小怪_沙匪男_斧盾", 40, eTeam, bcapi.Vector2(4, 2), 0.1)

            --和尚报数
            MonkCounts()

            --召完人，报完数，增加一次波数
            waveCount = waveCount + 1

            --重设击杀触发器
            bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0.3, killingController)
            
        end

        --第一个杀戮触发器开局就加了，控制初始怪击杀的效果。之后会添加控制1234波增援的击杀触发器，每次触发则召4个怪。
        if waveCount<=4 and waveSpawned[waveCount]==0 then

            for i=1,4 do
                local role = enemyTb[i]
                bcapi.create_join_role(role, 40, eTeam, bcapi.Vector2(6,positionY[i]), 0.1) 
            end

            --召完怪，就会标记，时间触发器就不会再有效果了。因为击杀触发器的招人已经完成了。
            waveSpawned[waveCount] = 1

            --和尚报数
            MonkCounts()

            --增加波数
            waveCount = waveCount + 1
            
            --时间计时器少一个，因为最后一波召唤怪没有底线时间。
            if waveCount < 4 then 
                bcapi.add_timer_trigger(timeOfSpawn[waveCount], timerController) 
            end
            --设置击杀触发器
            bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0.3, killingController)

        end


        --isOnKilling = false
    end)
end

--时间触发器相应function
function timerController()
    bcapi.async_call(function()
        --因为时间触发器是保底，并且独立运行，如果击杀触发器先触发，则时间触发器就不再触发了，所以先检查相应的召唤波次是否已经被执行过，如果没有，执行。
        if waveCount<=4 and waveSpawned[waveCount]==0 then
            
            --检查场上的敌人数量，为了不让场面太乱，敌人最多6个，如果不足6个，就召唤至6个，已经有6个，就不召了。如果场上不足两个，最多招4个，时间触发器招的怪和击杀触发器都是一个数组里的怪
            
            local enemyCnt = eTeam.FightingRoleList.Count -1   --场上有一个虚拟施法者用来防止战斗自动结束，这里我们把它去掉。
            local summonCnt = 6-enemyCnt

            if(summonCnt > 4)then
                summonCnt = 4
            end
            
            for i=1,summonCnt do
                local role = enemyTb[i]
                bcapi.create_join_role(role, 40, eTeam, bcapi.Vector2(6,positionY[i]), 0.1) 
            end
           
            MonkCounts()   

            waveSpawned[waveCount] = 1

            waveCount = waveCount + 1   

            print(waveCount)
    
            bcapi.add_timer_trigger(timeOfSpawn[waveCount], timerController)      
            
            bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0.1, killingController)

        end
    end)
end 


