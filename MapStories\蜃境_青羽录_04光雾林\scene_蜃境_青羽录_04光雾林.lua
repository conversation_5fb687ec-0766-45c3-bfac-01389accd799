---@type 蜃境_青羽录_04光雾林
local context = require(
                    "MapStories/蜃境_青羽录_04光雾林/scene_蜃境_青羽录_04光雾林_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_青羽录_04光雾林
local flags = context.flags
-- 不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")

function getFlags(...) return flags end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")
require("MapStories/MapStoryCommonTools")

function get_level_step() return flags.level_step end

function set_level_step(value) flags.level_step = value end

function start()
    s.setPos(npcs.zhujue,pos.zhujue_start_pos)
    print("start called")
    reset_all()
    s.readyToStart(true)
    refreshMapStates()
    try_execute_newbieGuide()
end

function try_execute_newbieGuide(...)
    -- 如果qyl_15未解锁，且qyl_15已通关，则finish qyl_15
    if (not s.hasUnlockMemorySlot("qyl_15") and
        newbieApi.getNewbieFlag(newbieFlags.chapter_qyl_15_finished) == 1) then
        -- quick_battle("青羽录2-20", "", function()
        capi.finishMemorySlot("qyl_15")
        next_level_step(true)
        -- end)
    end

    -- qyl_16 不需要战斗
    if (not s.hasUnlockMemorySlot("qyl_16") and
        newbieApi.getNewbieFlag(newbieFlags.chapter_qyl_16_finished) == 1) then
        capi.finishMemorySlot("qyl_16")
        next_level_step(true)
    end
    s.nextStep("newbieGuideInLevel")
end

function newbieGuideInLevel()
    --这里sys_unlock_jingmai必须用s.getNewbieFlag 因为没有在newbieFlags中初始化
    if s.hasUnlockMemorySlot("qyl_18") and s.getNewbieFlag("sys_unlock_jingmai") == 0 then
        require("Newbie/newbie_guide")
        newbieApi.unlockSystem("sys_unlock_jingmai")
        -- 直接和服务器通讯,抢在打开页面之前把值先设置了
        newbieApi.setNewbieGuideFlagDirectly("sys_unlock_jingmai", 1)
        doTutorRoleJingmai()
    end
end

-- 默认初始化的地图显隐状态
function reset_all()
    -- 隐藏所有的剧情节点
    s.setActive(npcs.qyl_15, flags.level_step == 1)
    s.setActive(npcs.qyl_16, flags.level_step == 2)
    s.setActive(npcs.qyl_17, flags.level_step == 3)
    s.setActive(npcs.qyl_18, flags.level_step == 4)

    -- s.setActive(npcs.enemy_01, flags.enemy_01 == 0)
    -- s.setActive(npcs.enemy_02, flags.enemy_02 == 0)
    -- s.setActive(npcs.enemy_03, flags.enemy_03 == 0)
    s.setActive(objs.chanzi, flags.chanzi == 0)
    s.setActive(objs.stone_1Trigger, flags.chanzi == 1 and flags.stone_1 == 0)
    s.setActive(objs.stone_2Trigger, flags.chanzi == 1 and flags.stone_2 == 0)
    s.setActive(objs.stone_3Trigger, flags.chanzi == 1 and flags.stone_3 == 0)
    s.setActive(objs.stone_4Trigger, flags.chanzi == 1 and flags.stone_4 == 0)
    s.setActive(objs.stone_5Trigger, flags.chanzi == 1 and flags.stone_5 == 0)
    s.setActive(objs.stone_1, flags.stone_1 == 0)
    s.setActive(objs.stone_2, flags.stone_2 == 0)
    s.setActive(objs.stone_3, flags.stone_3 == 0)
    s.setActive(objs.stone_4, flags.stone_4 == 0)
    s.setActive(objs.stone_5, flags.stone_5 == 0)

    if(flags.defeat_water_enemy == 1 and flags.defeat_big_enemy == 0)then
        s.setActive(npcs.elite_06,true)
    end

    if(flags.stone_5 == 1 and flags.soul < 2)then
        s.setActive(npcs.soul,true)
    end

    if(flags.taijishike_step == 2)then
        s.setActive(npcs.npc_01,false)
    end

    s.removeAllGameMapGuides()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 5) then
        taskStep = "forwardToExplore"
    elseif (step == 5) then
        taskStep = "leaveGuangWuLinShenJing"
    end
    s.setTaskStep("青羽录光雾林", taskStep)


    s.playMusic("雾染辛夷")
    print("refreshMapStates level step = " .. tostring(step))
    reset_all()
    s.unTraceTask(10000001,50)
    
    if step == 0 then
        ----第一次进入场景, 这里加入入场演出
        s.setTaskStep("青羽录光雾林", "forwardToExplore")
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林",
                    "蜃境_青羽录_光雾林_进入", nil, 0)
        s.soloAnimState(npcs.zhujue, "BScratch", true)
        s.wait(2)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林",
                    "蜃境_青羽录_光雾林_进入2", nil, 0)
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.qyl_15, 0.8)
    elseif step == 2 then
        level_guide_to(npcs.qyl_16, 0.8)
    elseif step == 3 then
        level_guide_to(npcs.qyl_17, 0.8)
    elseif step == 4 then
        level_guide_to(npcs.qyl_18, 0.8)
    else
        -- do nothing
        level_guide_to(objs.exit)
    end

    s.setActive(objs.qyl4_1005, not s.hasUnlockMemorySlot("qyl4_1005"))
    s.setActive(objs.qyl4_1006, not s.hasUnlockMemorySlot("qyl4_1006"))
    s.setActive(objs.qyl4_1007, not s.hasUnlockMemorySlot("qyl4_1007"))
end

----------------------OVERRIDE----------------------------------
function qyl_15()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林",
                "蜃境_青羽录_光雾林_进入3", nil, 0)
    executeMemSlot("qyl_15",
                   function() require("Chapters/青羽录").qyl15_01() end)
end

function qyl_16()
    executeMemSlot("qyl_16",
                   function() require("Chapters/青羽录").qyl16_01() end)
end

function qyl_17()
    executeMemSlot("qyl_17",
                   function() require("Chapters/青羽录").qyl17_01() end,
                   function()
        next_level_step(true)
        try_execute_newbieGuide()
    end)
end

function qyl_18()
    executeMemSlot("qyl_18",
                   function() require("Chapters/青羽录").qyl18_01() end,
                   function()
        s.setTaskStep("青羽录光雾林", "leaveGuangWuLinShenJing")
        next_level_step(true)
        try_execute_newbieGuide()
    end)
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("青羽录")
end

function taijishike()
    if (flags.taijishike_step == 0) then
        flags.taijishike_step = 1
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林",
                    "蜃境_青羽录_光雾林_太极石刻_1", nil, 0)
        s.animState(npcs.npc_01, "Special_1", true)
        if (flags.taijishike == 0) then
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林",
                        "蜃境_青羽录_光雾林_太极石刻_2", nil, 0)

        else
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林",
                        "蜃境_青羽录_光雾林_太极石刻_2_已接任务",
                        nil, 0)

        end
    else
        if (flags.taijishike == 0) then
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林",
                        "好吧", nil, 0)
        else
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林",
                        "蜃境_青羽录_光雾林_太极石刻_4", nil, 0)
            local capi = require("ChapterApi")
            s.setActive(npcs.npc_01,false)
            flags.taijishike_step = 2
            capi.finishMemorySlot("qyl_sideTask_tjsk")
        end
    end
end

function chanzi()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林",
                "发现铲子", nil, 0)
    flags.chanzi = 1
    s.setActive(objs.chanzi, false)
    s.setActive(objs.stone_1Trigger, true)
    s.setActive(objs.stone_2Trigger, true)
    s.setActive(objs.stone_3Trigger, true)
    s.setActive(objs.stone_4Trigger, true)
    s.setActive(objs.stone_5Trigger, true)
    refreshMapStates()
end

function stone_1()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "石头1",
                nil, 0)
    flags.stone_1 = 1
    s.setActive(objs.stone_1, false)
    s.setActive(objs.stone_1Trigger, false)
    refreshMapStates()
end

function stone_2()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "石头2",
                nil, 0)
    flags.stone_2 = 1
    s.setActive(objs.stone_2, false)
    s.setActive(objs.stone_2Trigger,false)
    refreshMapStates()
end

function stone_3()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "石头3",
                nil, 0)
    flags.stone_3 = 1
    s.setActive(objs.stone_3, false)
    s.setActive(objs.stone_3Trigger,false)

    if(flags.taijishike_step == 0)then
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "蜃境_青羽录_光雾林_太极石刻_3_没接任务",nil, 0)
    else
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "蜃境_青羽录_光雾林_太极石刻_3_已接任务",nil, 0)
    end
    flags.taijishike = 1
    refreshMapStates()
end

function stone_4()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "石头4",
                nil, 0)
    flags.stone_4 = 1
    s.setActive(objs.stone_4, false)
    s.setActive(objs.stone_4Trigger,false)
    refreshMapStates()
end

function stone_5()
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "石头5",
                nil, 0)
    flags.stone_5 = 1
    s.setActive(objs.stone_5, false)
    s.setActive(objs.stone_5Trigger,false)
    s.setActive(npcs.soul,true)
    refreshMapStates()
end

function soul()
    if(flags.soul == 0)then
        flags.soul = 1
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "画中灵",nil, 0)
    else
        --如果stone_1到5的flag都为1，则弹出新的对话
        if flags.stone_1 == 1 and flags.stone_2 == 1 and flags.stone_3 == 1 and flags.stone_4 == 1 and flags.stone_5 == 1 then
            flags.soul = 2
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "画中灵_完成",nil, 0)
            s.setActive(npcs.soul,false)
            local capi = require("ChapterApi")
            capi.finishMemorySlot("qyl_question_soulOfPaint")
        else
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "画中灵_没完成",nil, 0)
        end
    end
end

function transportToRight()
    s.blackScreen()
    s.setPos(npcs.zhujue, pos.zhujue_start_pos2)
    s.wait(0.5)
    s.lightScreen()
end

function transportToLeft()
    s.blackScreen()
    s.setPos(npcs.zhujue, pos.zhujue_start_pos)
    s.wait(0.5)
    s.lightScreen()
end

function waveWaterEnemy()

    executeMemSlot("qyl4_battle_102",
    function()
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "打划水小怪")

    end,
    function()
        s.setActive(npcs.enemy_02,false)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "打败划水小怪")
        flags.defeat_water_enemy = 1
        s.blackScreen()
        s.setActive(npcs.elite_06,true)
        s.lightScreen()
        refreshMapStates()
    end,function()
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_光雾林", "取消打划水小怪")
    end,function()
        defaultLose()
    end,nil)

end

function bigEnemy()
    executeMemSlot("qyl4_battle_201",
    function()

    end,
    function()
        s.setActive(npcs.elite_06,false)
        flags.defeat_big_enemy = 1
        refreshMapStates()
    end,function()
        
    end,function()
        defaultLose()
    end,nil)
end

