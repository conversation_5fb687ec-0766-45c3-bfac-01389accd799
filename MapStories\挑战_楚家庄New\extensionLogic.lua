local s = require("StoryApi")

local extension = require 'MapStories/挑战_楚家庄New/extension'
---@type flags_挑战_楚家庄New
local flags = require("MapStories/挑战_楚家庄New/flags_挑战_楚家庄New")
s.loadFlags(flags)

---@type StringWidget
local stringWidget = extension.widgets.StringWidgetInfo.widget
---@type 挑战_楚家庄New_StringItemData
local stringItemData = extension.widgets.StringWidgetInfo.items

---不可改名
function get_string_item_desc(itemKey,sceneId)
    if itemKey == stringItemData.fire.key then
        local fireDesc = GetFireDesc(flags.initFireLv)
        return "火势等级:"..fireDesc
        --[[--local fireDamage = 30
        local fireLv = "<color=#00FF66>初燃</color>"
        if (flags.initFireLv < 1) then
            --
        elseif  ( 1 <= flags.initFireLv and flags.initFireLv <= 2 ) then
            fireLv = "<color=#CCFF66>轻微</color>"
            --fireDamage = 50
        elseif flags.initFireLv <= 4 then
            fireLv = "<color=#FFFF66>中等</color>"
            --fireDamage = 80
        elseif flags.initFireLv <= 6 then
            fireLv = "<color=#FF9933>危险</color>"
            --fireDamage = 120
        elseif flags.initFireLv <= 8 then
            fireLv = "<color=#FF3333>严重</color>"
            --fireDamage = 180
        else--if flags.initFireLv <= 10 then
            fireLv = "<color=#CC33FF>惨烈</color>"
            --fireDamage = 300
        end
        return "火势等级:" .. flags.initFireLv.. "(" .. fireLv .. ")"]] --.. "\n当前火势等级下，我方所有侠客在战斗中每5秒损失<color=yellow>" .. fireDamage .. "</color>点生命"
    --[[elseif itemKey == stringItemData.battletotalTime.key then
        local fireLv = "<color=#CCFF66>轻微</color>"
        local needTime = 30 - tonumber(flags.initBattleTimeTotal)
        if flags.initFireLv == 1 then
            fireLv = "<color=#CCFF66>中等</color>"
            needTime = 90 - tonumber(flags.initBattleTimeTotal)
        elseif flags.initFireLv == 2 then
            fireLv = "<color=#FFFF66>严重</color>"
            needTime = 180 - tonumber(flags.initBattleTimeTotal)
        elseif flags.initFireLv == 3 then
            fireLv = "<color=#FF9933>惨烈</color>"
            needTime = 300 - tonumber(flags.initBattleTimeTotal)
        elseif flags.initFireLv == 4 then
            fireLv = "<color=#FF3333>灭世</color>"
            needTime = 480 - tonumber(flags.initBattleTimeTotal)
        end
        return "战斗总时长:" .. tonumber(flags.initBattleTimeTotal) .. "秒\n再经过<color=yellow>" .. needTime .. "</color>秒，火势将提升至" ..  fireLv
    ]]
    end
    return "没有描述"
end

function GetFireDesc(initFireLv)
    local fireLv = "<color=#00FF66>初燃</color>"
    if (initFireLv < 1) then
        --
    elseif  ( 1 <= initFireLv and initFireLv <= 2 ) then
        fireLv = "<color=#CCFF66>轻微</color>"
        --fireDamage = 50
    elseif initFireLv <= 4 then
        fireLv = "<color=#FFFF66>中等</color>"
        --fireDamage = 80
    elseif initFireLv <= 6 then
        fireLv = "<color=#FF9933>危险</color>"
        --fireDamage = 120
    elseif initFireLv <= 8 then
        fireLv = "<color=#FF3333>严重</color>"
        --fireDamage = 180
    else--if flags.initFireLv <= 10 then
        fireLv = "<color=#CC33FF>惨烈</color>"
        --fireDamage = 300
    end
    return initFireLv.. "(" .. fireLv .. ")"
end