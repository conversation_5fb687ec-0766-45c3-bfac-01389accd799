local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
end

function start()
    bcapi.add_timer_trigger(0, TalkStart)
    bcapi.add_condition_trigger("[%c->enemy:副本_醉花荫_丐老%][<]1", 0, BattleWin)
end

function TalkStart()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        local playTb = {}
        local playerTeam = bcapi.get_player_team()
        for i = 0, playerTeam.FightingRoleList.Count-1 do
            local role = playerTeam.FightingRoleList[i]
            if(role.Stat.IsSummon == false)then
                table.insert(playTb,role.Key .. "#" .. role.Key)
            end
        end
        if #playTb > 0 then
            local randomIndex = math.random(1,#playTb)
            local randomRole = playTb[randomIndex]
            bcapi.talk(randomRole, "什么！这是......我们好像陷入了幻象之中。")
        end

        bcapi.talk("副本_醉花荫_丐老#副本_醉花荫_丐老", "不错，不错！老夫留下这一缕武道之意，一直在等候有缘者。")
        bcapi.talk("副本_醉花荫_丐老#副本_醉花荫_丐老", "若你们击败我，便算是通关了考验！")
        bcapi.resume_and_show_ui()
    end)
end

function BattleWin()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_醉花荫_丐老#副本_醉花荫_丐老", "好极，好极！老夫安心去也~~~")
        bcapi.resume_and_show_ui()
    end)
end