--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_风雪神道/flags_挑战_风雪神道")


---@class 角色_挑战_风雪神道山洞
local npcs = {
    zhujue = "角色/zhujue",
    boss3 = "角色/boss3",
    enemy_7 = "角色/enemy_7",
    enemy_8 = "角色/enemy_8",
    enemy_9 = "角色/enemy_9",
    boss1 = "角色/boss1",
    boss2 = "角色/boss2",
}

---@class 物体_挑战_风雪神道山洞
local objs = {
    boss3Trigger = "物体/boss3Trigger",
    trash18 = "物体/trash18",
    trash19 = "物体/trash19",
    trash20 = "物体/trash20",
    trash21 = "物体/trash21",
    trash22 = "物体/trash22",
    trash23 = "物体/trash23",
    chest2 = "物体/chest2",
    chest3 = "物体/chest3",
    quit = "物体/quit",
    teleport3 = "物体/teleport3",
}

---@class 相机_挑战_风雪神道山洞
local cameras = {
    camera_boss1 = "相机/camera_boss1",
    camera_boss2 = "相机/camera_boss2",
}

---@class 位置_挑战_风雪神道山洞
local pos = {
    start = "位置/start",
    boss_pos = "位置/boss_pos",
    boss_pos1 = "位置/boss_pos1",
    boss_pos2 = "位置/boss_pos2",
    boss_pos3 = "位置/boss_pos3",
}

---@class 资产_挑战_风雪神道山洞
local assets = {
    Timeline_1 = "资产/Timeline_1",
}

---@class 动作_挑战_风雪神道山洞
local animationClips = {
}

---@class 挑战_风雪神道山洞
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
