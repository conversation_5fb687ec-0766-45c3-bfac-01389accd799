local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.reset_limit_time(180)
    createRoles()
end

function start()

end

-- 取玩家队伍存的字典，后面两个函数中用到
local dict = bcapi.get_team(0).ValueDict

function createRoles()
    bcapi.async_call(function()
        -- 把玩家在大世界脚本选择的四张策略卡取过来
        local card1 = ""
        ---@type number?
        local card1Lv = 1
        local card2 = ""
        ---@type number?
        local card2Lv = 1
        local card3 = ""
        ---@type number?
        local card3Lv = 1
        local card4 = ""
        ---@type number?
        local card4Lv = 1
        local hero = ""
        -- dict是c#的dict,遍历dict的方法 用 ipairs
        for k,v in pairs(dict) do
            print("当前值"..k..v)
            if(k == "策略卡1")then
                card1 = v
            end
            if(k == "策略卡1等级")then
                card1Lv = tonumber(v)
            end
            if(k == "策略卡2")then
                card2 = v
            end
            if(k == "策略卡2等级")then
                card2Lv = tonumber(v)
            end
            if(k == "策略卡3")then
                card3 = v
            end
            if(k == "策略卡3等级")then
                card3Lv = tonumber(v)
            end
            if(k == "策略卡4")then
                card4 = v
            end
            if(k == "策略卡4等级")then
                card4Lv = tonumber(v)
            end
            if(k == "侠客")then
                hero = v
            end
        end
        if(card1 ~= "")then
            bcapi.add_strategy_card(card1, card1Lv, team)
        end
        if(card2 ~= "")then
            bcapi.add_strategy_card(card2, card2Lv, team)
        end
        if(card3 ~= "")then
            bcapi.add_strategy_card(card3, card3Lv, team)
        end
        if(card4 ~= "")then
            bcapi.add_strategy_card(card4, card4Lv, team)
        end

        --创建角色
        bcapi.create_join_runtime_role_with_card(hero,team,bcapi.Vector2(-4, 0),0)
    end)
end
