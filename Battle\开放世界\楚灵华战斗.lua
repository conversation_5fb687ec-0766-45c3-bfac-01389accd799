local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    
end
function start()
    bcapi.add_timer_trigger(0.1, Talk)
end

function Talk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("主角#主角", "……毕竟是了清的亲人，还是尽快解决战斗，不能引起太大的骚动。")
        bcapi.show_pop_info("战斗时间已被重设为45秒！",4)
        bcapi.reset_limit_time(46)
        bcapi.resume_and_show_ui()
    end)
end