--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/漫游_楚家庄/flags_漫游_楚家庄")


---@class 角色_漫游_楚家庄_前院
local npcs = {
    --- "莫知酉"
    mozhiyou = "角色/mozhiyou",
    --- "猫"
    cat = "角色/cat",
    --- "楚七"
    chuqi = "角色/chuqi",
    --- "风筝侍女"
    fengzhengshinv = "角色/fengzhengshinv",
    --- "风筝侍女2"
    fengzhengshinv2 = "角色/fengzhengshinv2",
    --- "狂妄剑客"
    kuangwangjianke = "角色/kuangwangjianke",
    --- "荆成"
    jingcheng = "角色/jingcheng",
    --- "楚八"
    chuba = "角色/chuba",
    --- "黑衣人"
    heiyiren = "角色/heiyiren",
    --- "菜农"
    cainong = "角色/cainong",
    --- "侍女甲"
    shinvjia = "角色/shinvjia",
    --- "侍女乙"
    shinvyi = "角色/shinvyi",
    --- "马夫"
    mafu = "角色/mafu",
    --- "护卫"
    huwei = "角色/huwei",
}

---@class 物体_漫游_楚家庄_前院
local objs = {
    --- "风筝"
    fengzheng = "物体/fengzheng",
    --- "楚八厢房"
    chu8xiangfang = "物体/chu8xiangfang",
    --- "石碑提示特效"
    suggest_shibei = "物体/suggest_shibei",
    --- "宝箱1"
    chest1 = "物体/chest1",
    --- "交互宝箱1"
    triggerChest1 = "物体/triggerChest1",
    --- "风筝剧情遮挡树"
    hiddentree_fz = "物体/hiddentree_fz",
    --- "枯木提示特效"
    suggest_kumu = "物体/suggest_kumu",
    --- "最终宝箱"
    finalchest = "物体/finalchest",
    --- "最终交互"
    finalinteract = "物体/finalinteract",
    --- "最终空地"
    finalscene = "物体/finalscene",
}

---@class 相机_漫游_楚家庄_前院
local cameras = {
    --- "开场剧情相机"
    start = "相机/start",
    --- "狂妄剑客相机"
    jianke = "相机/jianke",
    --- "密道入口相机"
    enter = "相机/enter",
    --- "最终决战相机"
    final = "相机/final",
    --- "最终决战后拉相机"
    final_back = "相机/final_back",
    --- "风筝相机"
    fengzheng = "相机/fengzheng",
    --- "石碑相机"
    shibei = "相机/shibei",
    --- "枯木相机"
    kumu = "相机/kumu",
    --- "竹节侠相机"
    zhujiexia = "相机/zhujiexia",
    --- "楚八相机"
    chuba = "相机/chuba",
    --- "楚八右移相机"
    chuba_move = "相机/chuba_move",
}

---@class 位置_漫游_楚家庄_前院
local pos = {
    --- "猫位置1"
    cat1 = "位置/cat1",
    --- "猫位置2"
    cat2 = "位置/cat2",
    --- "猫位置3"
    cat3 = "位置/cat3",
    --- "猫位置4"
    cat4 = "位置/cat4",
    --- "猫位置5"
    cat5 = "位置/cat5",
    --- "后山传送点位置"
    transportHoushan = "位置/transportHoushan",
    --- "莫知酉等待交互"
    MZY_wait = "位置/MZY_wait",
    --- "荆成密道入口"
    JC_enter = "位置/JC_enter",
    --- "莫知酉密道入口"
    MZY_enter = "位置/MZY_enter",
    --- "荆成最终位置"
    JC_final = "位置/JC_final",
    --- "莫知酉最终位置"
    MZY_final = "位置/MZY_final",
    --- "黑衣人最终位置"
    HYR_final = "位置/HYR_final",
    --- "莫知酉石碑站位"
    MZY_shibei = "位置/MZY_shibei",
    --- "莫知酉枯木站位"
    MZY_kumu = "位置/MZY_kumu",
    --- "莫知酉楚八站位"
    MZY_chuba = "位置/MZY_chuba",
    --- "荆成石碑站位"
    JC_shibei = "位置/JC_shibei",
    --- "荆成枯木站位"
    JC_kumu = "位置/JC_kumu",
    --- "荆成楚八站位"
    JC_chuba = "位置/JC_chuba",
    --- "荆成剑客站位"
    JC_jianke = "位置/JC_jianke",
    --- "莫知酉剑客站位"
    MZY_jianke = "位置/MZY_jianke",
    --- "荆成风筝站位"
    JC_fengzheng = "位置/JC_fengzheng",
    --- "荆成初始站位"
    JC_start = "位置/JC_start",
    --- "荆成楚八右移站位"
    JC_chuba_move = "位置/JC_chuba_move",
    --- "楚七初始站位"
    CQ_init = "位置/CQ_init",
    --- "楚七枯木站位"
    CQ_kumu = "位置/CQ_kumu",
    --- "楚七枯木画外站位"
    CQ_kumu_huawai = "位置/CQ_kumu_huawai",
    --- "莫知酉楚八出现"
    MZY_chuba_show = "位置/MZY_chuba_show",
    --- "黑衣人最终位置前移"
    HYR_final_move = "位置/HYR_final_move",
}

---@class 资产_漫游_楚家庄_前院
local assets = {
}

---@class 动作_漫游_楚家庄_前院
local animationClips = {
}

---@class 漫游_楚家庄_前院
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
