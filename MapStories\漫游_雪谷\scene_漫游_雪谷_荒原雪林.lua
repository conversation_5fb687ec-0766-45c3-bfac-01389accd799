
---@type 漫游_雪谷_荒原雪林
local context = require("MapStories/漫游_雪谷/scene_漫游_雪谷_荒原雪林_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

---组件相关
local extensions = require("MapStories/漫游_雪谷/extension")
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type StringWidget
local stringWidget = extensions.widgets.StringWidgetInfo.widget
---@type 雪谷_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 雪谷_ItemData]]
---@type 雪谷_StringItemData
local stringItemData = extensions.widgets.StringWidgetInfo.items


s.loadFlags(context.flags)
---@type flags_漫游_雪谷
local flags = context.flags

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    if flags.xl_opening_flag == 0 then
        triggerOpening()
        flags.xl_opening_flag = 1
    end
    s.readyToStart()
    reloadAllSceneStates()
end

function triggerCold()
    s.appendAsyncAside("墨七", 0, "寒风呼啸——")
    s.wait(1.5)
    s.appendAsyncAside("墨七", 1, "寒风呼啸——寒风呼啸——")
    s.wait(1.5)
end


function reloadAllSceneStates()
    --熊
    s.setActive(npcs.bear, flags.xl_ganjing_step < 2)
    
    --猎人
    s.setActive(npcs.hunter, flags.xl_angzang_step < 2)
    
    --盗猎事件
    s.setActive(objs.eventDaolie, flags.yd_lengchexinfei_step < 2)
end


function wrappedReloadAllSceneStates()
    s.blackScreen()
    reloadAllSceneStates()
    s.lightScreen()
end

function triggerOpening()
    s.blackScreen()
    s.setPos(npcs.moqi,pos.MQ_tied)
    s.camera(cameras.start,true)
    s.turnTo(npcs.duobu,npcs.moqi)
    s.lightScreen()
    --△墨七缓缓苏醒，发觉自己双手被人反绑，眼前是两名手持长矛的原住民勇士，正冷漠地盯着自己。
    --△镜头扫过部落一些有特点的素材：兽骨、笼子、捕兽夹、神秘雕像、原始武器等。
    s.talkThink(npcs.moqi,"这里是……原住民生活的地方？")
    s.talkThink(npcs.moqi,"早就听说雪谷中隐藏着一支尚未开化的原住民，如今不慎落到这群人手中，恐怕是凶多吉少了……")
    --△族长多布从后方走来，指了指墨七，又向原住民勇士耳边低语几句。
    --△原住民勇士得到命令，走到墨七身后，高高扬起长矛，墨七绝望地闭上眼睛。
    s.talkThink(npcs.moqi,"难道，真要交待在这里了？")
    --△片刻后墨七再次睁眼，发现绳索已被切断，他不可思议地看了看自己的双手，又一脸困惑地看向多布。
    s.talk(npcs.moqi,"为何将我放了？")
    s.talk(npcs.duobu,"贵客远道而来，刚才是族人眼拙，多有冒犯，还望贵客不要放在心里。")
    s.talk(npcs.moqi,"贵客？")
    s.talk(npcs.duobu,"我叫多布，是这里的族长，贵客既然是神女的朋友，我们自然应当以礼相待。")
    s.talk(npcs.moqi,"神女？朋友？")
    s.talk(npcs.duobu,"嗯，神女就是山神的女儿，我们生活的这片雪山，其中的一切生灵，一草一木，都归属于山神管辖。")
    s.talk(npcs.duobu,"我听族人详细描述了贵客刚才在林中与野兽搏斗的场景，我确信唯有神女的朋友，才能展示出那样令人惊叹的“法术”。")
    s.talk(npcs.moqi,"那可不是法术……")
    s.talk(npcs.duobu,"贵客天生神力，竟能驱使那些铁皮怪物为自己作战，不是法术还能是什么？就别再自谦了。")
    s.talk(npcs.moqi,"你的手臂是怎么回事？")
    --△多布闻言，骄傲地扬了扬与流亡者如出一辙的机关手臂。
    s.talk(npcs.duobu,"我就知道贵客会问起这个。")
    s.talk(npcs.duobu,"我这只手臂曾在睡梦中被闯入屋内的雪狼所断，后来多亏神女降下福泽，才使我不至于成为一个残废之人，从那以后，我就决心成为神女最忠实的追随者。")
    s.talk(npcs.moqi,"看来，你并不清楚它的用法……")
    s.talk(npcs.duobu,"贵客说笑了，我如今已完全适应了这只手臂，饮食起居都不成问题，甚至偶尔还能入山狩猎，这些已足够令族人惊叹！")
    s.talk(npcs.duobu,"况且这既然是神女的赐福，我相信它必然大有玄机，假以时日或许我会参透它的妙处。")
    s.talk(npcs.duobu,"眼下部落正忙着筹备一场重要的仪式，原谅我暂时不能奉陪，贵客若是感兴趣，可在这里随意逛逛。")
    --获得：多布的祭品清单
    --清单内容：仪式所需祭品——纯净之物、肮脏之物、珍贵之物
    s.talkThink(npcs.moqi,"（这么看来，所谓的法术、神女，莫非都是阿青在背后搞鬼……）")
    s.talkThink(npcs.moqi,"（不过她为何打这些原住民的主意？清单上莫名其妙的祭品又是怎么回事？）")
    s.talkThink(npcs.moqi,"（若想搞清楚其中的真相，看来得仔细调查一番了。）")

    s.camera(cameras.start,false)
    flags.xl_main_step = 1
    s.addTask(400210, 1)
end

function talkToDuobu()
    if flags.xl_main_step < 4 then
        talkToDuobu001()
    elseif flags.xl_main_step == 4 then
        talkToDuobu002()
    else
        talkToDuobu003()
    end
end

function  talkToDuobu001()
    s.talk(npcs.duobu,"贵客，部落给你留下的印象如何？以后如果见到神女，可别忘了替我们多美言几句。")
end

function  talkToDuobu002()
    s.talk(npcs.duobu,"什么？！你说你已经集齐了所有祭品？请拿给我看看……")
    
    s.selectTalk(npcs.moqi, "……", {"向多布介绍这些祭品"})
    s.talk(npcs.duobu,"唔……真是太令人意外了……我敢说世界上再找不出比这更合适的祭品了。")
    s.talk(npcs.moqi,"这就开始仪式吧。")
    s.talk(npcs.duobu,"很好，时候差不多了，贵客远道而来，也请一起参加吧。")
    --△原住民们聚集在部落中央的火堆旁。
    s.talk(npcs.duobu,"我有个不情之请，贵客是否愿意协助我们一起完成仪式？")
    s.talk(npcs.moqi,"有何不可。")
    s.talk(npcs.duobu,"首先，请将珍贵之物扔进火堆。")
    itemWidget:removeItem(itemData.yanlei,1)
    --△墨七将枯萎的枫叶扔进火堆，火焰变为蓝色。
    s.talk(npcs.duobu,"蓝色代表思念，忧伤，求而不得的落寞，经过浴火涅槃之后，这份恋情将在山神的赐福下得以永存。")
    s.talk(npcs.duobu,"接下来，请将污秽之物扔进火堆。")
    itemWidget:removeItem(itemData.wuqi,1)
    --△墨七将盗猎者的匕首扔进火堆，火焰变为黑色。
    s.talk(npcs.duobu,"冷漠，残忍，自私，冲动，在黑色的火焰中我似乎看见灾难的影子正在靠近，唯有山神的意志能够彻底焚烧这些罪恶。")
    s.talk(npcs.duobu,"最后，请将纯净之物扔进火堆。")
    itemWidget:removeItem(itemData.xuejing,1)
    --△墨七将无垢的雪晶扔进火堆，火焰变为金色。
    s.talk(npcs.duobu,"纯净，温暖，辽阔，仿佛回到自然母亲的怀抱，如果每一颗心灵都像这雪晶般剔透，人间就没有那么多灾难苦痛了。")
    s.talk(npcs.duobu,"有了这些祭品，相信山神必定会保佑我们来年风调雨顺，免受雪崩与灾祸的侵袭。")
    --（仪式结束，墨七与多布刷新到一旁）
    s.talk(npcs.duobu,"多亏贵客这次帮了大忙，才使仪式顺利进行，我真不知该如何答谢。")
    s.talk(npcs.moqi,"我并非为酬劳而来，不过心中仍有一事不解。")
    s.talk(npcs.duobu,"贵客请讲。")
    s.talk(npcs.moqi,"神女是否向你们提过什么要求？")
    s.talk(npcs.duobu,"要求？神女无所不能，咱们这些凡俗之人本领微薄，哪儿能入得了她眼？")
    s.talk(npcs.duobu,"等等，非要说的话……神女曾命我们留意山中古墓，若发现就记下位置将来向她禀告，也不知是何意。")
    s.talk(npcs.moqi,"（自语）古墓？为何她也在寻找，难道说，都是她在背后……")
    --△一名原住民斥候来到多布跟前，一阵耳语。
    s.talk(npcs.duobu,"唔……看来这伙流亡者又有新动作，你继续盯紧他们。")
    s.talk("原住民斥候","遵命。")
    --△斥候离去。
    s.talk(npcs.moqi,"你在监视那些流亡者？")
    s.talk(npcs.duobu,"贵客并非外人，我也不必隐瞒——我们部落自古有训诫，族人轻易不可离开这片幽深密闭的雪林，虽然这造成了我们对外界一无所知，但也因此获得相对安宁的生存环境。")
    s.talk(npcs.duobu,"然而，近来雪谷中却忽然冒出一群危险的流亡者，这群人似乎在搜寻某样东西，整日无休无止地挖掘打探，不仅把雪谷闹得乌烟瘴气，也严重威胁到部落的安全！")
    s.talk(npcs.duobu,"最可气的是，部分流亡者居然也获得了神女的赐福，我相信神女绝不会如此糊涂，这一定是某种邪术和障眼法！你以为呢？")
    s.talk(npcs.moqi,"或许吧……")
    s.talk(npcs.duobu,"一说到这个就忍不住激动起来，贵客见笑了。对了，这小玩意是神女送给我的礼物，我见识短浅，摸索了许久却完全参不透何意，不如转赠给贵客做个纪念吧。")
    s.talk(npcs.duobu,"相信凭借贵客的机敏聪慧，将来必能悟出其中的奥妙。")
    --（获得 机关零件，区域主线结束）
    flags.xl_ganjing_step = 3
    flags.xl_angzang_step = 3
    flags.xl_baogui_step = 4
    
    flags.xl_main_step = 5
    itemWidget:addItem(itemData.jiguanlingjian2,1)

    s.finishTask(400210, 1)
end

function  talkToDuobu003()
    s.talk(npcs.duobu,"你应该已经拿到机关零件了吧！")
end

    
function talkToLanxi()

    if flags.xl_ganjing_step == 0 then
        talkToLanxi001()
        flags.xl_ganjing_step = 1
    else
        talkToLanxi002()
    end
    --开启任务：纤尘不染
    --任务目标：在雪林中找到仪式祭品
    --（击败熊王后，获得无垢的雪晶）
    --（无垢的雪晶：未接触地面就凝结为坚硬晶体的雪花，极为罕有。）
    --（达成“纯净之物”）
end

function talkToLanxi001()
    s.talk(npcs.lanxi,"少年，你手里可是本次部族仪式的祭品清单？")
    s.talk(npcs.moqi,"嗯，只是这些祭品实在叫人难以理解。")
    s.talk(npcs.lanxi,"呵呵，若是唾手可得，便没有寻找的意义了。")
    s.talk(npcs.lanxi,"每次仪式开始之前，部族巫师都会孤身进入雪山深处聆听神的旨意，而我们要做的就是找到这些祭品，满足神的一切要求。")
    s.talk(npcs.moqi,"你们的神真是喜欢打哑谜呢。")
    s.talk(npcs.lanxi,"嘘——不可对神明不敬，会遭报应的。")
    s.talk(npcs.lanxi,"你既然是神女的朋友，又是多布族长的贵客，我便给你一些提示吧。")
    s.talk(npcs.lanxi,"这雪谷虽然物资匮乏，神奇的事情倒是不少，尤其外面这座林子位于山之阴，更是孕育了一些不寻常的生物，或许你可去其中碰碰运气。")

    s.addTask(400211, 1)
end

function talkToLanxi002()
    s.talk(npcs.lanxi,"少年，你可发现了？")
end


function talkToLulu()
    if flags.xl_angzang_step == 0 then
        talkToLulu001()
        flags.xl_angzang_step = 1
    else
        talkToLulu002()
    end
end

function talkToLulu001()
    --△部族男青年鲁鲁抚摸着一头被割去鹿角的死鹿，显得愤慨不已。
    s.talk(npcs.lulu,"这些坏人越来越过分了！")
    s.talk(npcs.lulu,"鲁鲁之前已经远远地警告过他们，没想到他们根本不听鲁鲁的，反而做出更多坏事！")
    s.talk(npcs.moqi,"是谁做的？")
    s.talk(npcs.lulu,"是那些盘踞在林子里的黑衣服坏人，他们杀死这只可怜的小鹿只是为了得到它的鹿角！")
    s.talk(npcs.lulu,"鲁鲁想问你，如果你只是为了求财，夺走对方的钱财就也罢了，是否还要顺手杀死对方呢？")

    local ret = s.selectTalk(npcs.moqi, "……", {"我不会这样残忍", "这可说不准，人性是复杂的"})
    if ret == 0 then
        s.talk(npcs.lulu,"鲁鲁就知道你不是那种人，可世上偏偏有一类坏人，他们明明衣食无忧，却以杀生为乐，比雪山上的石头还要冰冷无情！")
    else
        s.talk(npcs.lulu,"人性？那是什么？鲁鲁好像听不懂你的话。")
    end
    s.talk(npcs.lulu,"好吧，鲁鲁承认自己不是那些坏人的对手，他们不仅身手厉害，心里还藏着很多坏念头。")
    s.talk(npcs.lulu,"你看起来也很厉害，能否替鲁鲁给他们一点教训？")

    s.addTask(400212, 1)
end

function talkToLulu002()
    s.talk(npcs.lulu,"你看起来也很厉害，替鲁鲁给他们一点教训吧！")
end

function talkToHaiya()
    if flags.xl_baogui_step == 0 then
        talkToHaiya001()
        flags.xl_baogui_step = 1
    elseif flags.xl_baogui_step <= 2 then
        talkToHaiya002()
    else
        talkToHaiya003()
    end
end

function talkToHaiya001()
    --△原住民少女海雅站在山坡上眺望雪林。
    s.talk(npcs.haiya,"你去过雪林的另一边吗？那里有什么？")
    s.talk(npcs.moqi,"除了雪还是雪。")
    s.talk(npcs.haiya,"多布族长也说过类似的话，他说这个世界上只有两种地方——下雪的和不下雪的。")
    s.talk(npcs.haiya,"自从玄恩失踪，我出去寻找过无数次却始终没有发现蛛丝马迹，大雪把一切线索都掩埋了。")
    s.talk(npcs.moqi,"玄恩是谁？")
    s.talk(npcs.haiya,"他是部族成员之一，我们从小一起长大，一同学习狩猎技巧，就在不久前，我们已彼此宣誓结为夫妇。")
    s.talk(npcs.moqi,"你说的失踪是怎么一回事？")
    s.talk(npcs.haiya,"……玄恩已消失了整整十日，族人都说他是被野兽杀死的，这实在是笑话，他可是部族里最厉害的勇士，连最凶残的雪狼王都不是他的对手。")
    s.talk(npcs.moqi,"还有别的线索吗？")
    s.talk(npcs.haiya,"对了，自从多布族长的手臂得到赐福以后，玄恩好像失了魂一般，我总觉得，那奇异的手臂对他来说……仿佛具有某种巨大魔力。")
    s.talk(npcs.haiya,"而且，玄恩在失踪之前似乎有点不太对劲，总是念叨着什么“到地下去”……")

    s.addTask(400213, 1)
end

function talkToHaiya002()
    s.talk(npcs.haiya,"你，找到他了吗？")

    local submitItem = s.submitTempItem("请提交枯萎的枫叶！")--todo:进行
    if (submitItem ~= nil) then
        if (submitItem[0].Key == itemData.fengye.key) then
            itemWidget:removeItem(itemData.fengye,1)

            s.talk(npcs.haiya,"这是……我与玄恩的定情信物。")
            s.talk(npcs.haiya,"当我还很小的时候，偶然从一个云游僧人手里得到这片枫叶，我认为它能够交来好运就送给了玄恩，谁知……")
            s.talk(npcs.haiya,"既然玄恩已经不在人世，我会将与他相关的所有记忆永远刻在心里，这片叶子就送给你吧。")

            s.talkThink(npcs.moqi, "想必这就是最珍贵的东西吧！")
            itemWidget:addItem(itemData.yanlei,1)

            flags.xl_main_step =  flags.xl_main_step + 1
            flags.xl_baogui_step = 3

            s.finishTask(400213, 2)
        else
            s.talk(npcs.moqi, "选中的道具是" .. submitItem)
            s.aside("你提交错了！这不是枯萎的枫叶。")
        end
    else
        s.aside("你没有提交任何东西！")
    end
end

function talkToHaiya003()
    s.talk(npcs.haiya,"谢谢你。")
end


function talkToHunter()
    if flags.xl_angzang_step == 0 then
        talkToHunter001()
    else
        talkToHunter002()
    end
end

function talkToHunter001()
    s.talk(npcs.hunter,"我是猎人。")
end

function talkToHunter002()
    s.talk(npcs.hunter,"我是猎人。")
    
    --todo:战斗接入
    local battleWin = s.selectTalk(npcs.moqi, "……", {"战斗失败", "战斗胜利"})

    if battleWin == 0 then
        s.aside("你败了，再来一次吧。")
    else
        s.aside("你胜了！获得了凶手的武器。")

        itemWidget:addItem(itemData.wuqi,1)
        flags.xl_angzang_step = 2
        flags.xl_main_step =  flags.xl_main_step + 1
        
        wrappedReloadAllSceneStates()

        s.finishTask(400212, 1)
    end
end

function talkToBear()
    if (flags.xl_ganjing_step == 0) then
        talkToBear000()
    elseif (flags.xl_ganjing_step == 1) then
        talkToBear001()
        --刷新
        wrappedReloadAllSceneStates()
    end
end

function talkToBear000()--todo：待补充
    s.talk(npcs.bear,"我是一头熊，暂时还没有任务。")
end

function talkToBear001()
    
    s.talk(npcs.bear,"我是一头熊。")
    --todo:战斗接入
    local battleWin = s.selectTalk(npcs.moqi, "……", {"战斗失败", "战斗胜利"})

    if battleWin == 0 then
        s.aside("你败了，再来一次吧。")
    else
        s.aside("你胜了！获得了无垢的雪晶。")

        itemWidget:addItem(itemData.xuejing,1)
        flags.xl_ganjing_step = 2
        flags.xl_main_step =  flags.xl_main_step + 1

        s.finishTask(400211, 1)
    end
end

function eventDaolie()
    if (flags.yd_lengchexinfei_step == 0) then
        eventDaolie001()
    elseif (flags.yd_lengchexinfei_step == 1) then
        eventDaolie002()
        wrappedReloadAllSceneStates()
    end
end


function eventDaolie001()
    s.aside("这不关你的事。")
end

function eventDaolie002()
    s.aside("你发现盗猎事件！！")
    --todo:战斗接入
    local battleWin = s.selectTalk(npcs.moqi, "……", {"战斗失败", "战斗胜利"})

    if battleWin == 0 then
        s.aside("你败了，再来一次吧。")
    else
        s.aside("你胜了！从盗猎者身上获得了毛皮。")

        itemWidget:addItem(itemData.maopi,1)
        flags.yd_lengchexinfei_step = 2

        s.finishTask(400012, 1)
    end
end


function eventMaijiu()
    if flags.gd_bingxueniang_step == 1 then
        local submitItem = s.submitTempItem("请提交阿飞的酒！")
        if (submitItem ~= nil) then
            if (submitItem[0].Key == itemData.afeijiu.key) then
                itemWidget:removeItem(itemData.afeijiu,1)
                
                --todo:添加计时器！
                s.aside("你把阿飞的酒埋进了地里！需要等待四个时辰！")
                flags.gd_bingxueniang_step = 2
                
            else
                s.aside("你提交错了！这不是阿飞的酒。")
            end
        else
            s.aside("你没有提交任何东西！")
        end
    elseif flags.gd_bingxueniang_step == 2 then
        --todo:计时器判断！
        s.aside("假装你已经等待了四个时辰！")

        s.aside("获得了冰雪酿！")
        itemWidget:addItem(itemData.bingxueniang,1)
        flags.gd_bingxueniang_step = 3

        s.finishTask(400111, 1,2)
    else
        
        s.aside("这里是埋酒地。")
    end
end

function transportYingdi()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_流亡者营地.unity", "位置/Forest")
end