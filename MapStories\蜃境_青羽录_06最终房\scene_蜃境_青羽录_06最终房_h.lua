--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_青羽录_06最终房/flags_蜃境_青羽录_06最终房")


---@class 角色_蜃境_青羽录_06最终房
local npcs = {
    zhujue = "角色/zhujue",
    boss = "角色/boss",
    chuqing = "角色/chuqing",
    songshu = "角色/songshu",
    boss2 = "角色/boss2",
}

---@class 物体_蜃境_青羽录_06最终房
local objs = {
    exit = "物体/exit",
    door_start = "物体/door_start",
    door = "物体/door",
}

---@class 相机_蜃境_青羽录_06最终房
local cameras = {
    camera1 = "相机/camera1",
    camera2 = "相机/camera2",
    camera3 = "相机/camera3",
    camera4 = "相机/camera4",
    camera5 = "相机/camera5",
    camera6 = "相机/camera6",
    camera7 = "相机/camera7",
    cam_main = "相机/cam_main",
}

---@class 位置_蜃境_青羽录_06最终房
local pos = {
    start = "位置/start",
    jingcheng_start = "位置/jingcheng_start",
    yunwusheng_start = "位置/yunwusheng_start",
    jingcheng_play_pos = "位置/jingcheng_play_pos",
    zhujue_play_pos = "位置/zhujue_play_pos",
    yunwusheng_play_pos = "位置/yunwusheng_play_pos",
    start_1 = "位置/start_1",
    chuqing = "位置/chuqing",
    chuqing2 = "位置/chuqing2",
}

---@class 资产_蜃境_青羽录_06最终房
local assets = {
    songshu = "资产/songshu",
    shijian = "资产/shijian",
}

---@class 动作_蜃境_青羽录_06最终房
local animationClips = {
}

---@class 蜃境_青羽录_06最终房
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
