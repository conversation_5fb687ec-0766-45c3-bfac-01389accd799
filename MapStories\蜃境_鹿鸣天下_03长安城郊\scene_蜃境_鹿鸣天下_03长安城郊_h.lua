--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_鹿鸣天下_03长安城郊/flags_蜃境_鹿鸣天下_03长安城郊")


---@class 角色_蜃境_鹿鸣天下_03长安城郊
local npcs = {
    lmtx_12 = "角色/lmtx_12",
    lmtx_13 = "角色/lmtx_13",
    lmtx_14 = "角色/lmtx_14",
    lmtx_15 = "角色/lmtx_15",
    enemy_01 = "角色/enemy_01",
    enemy_02 = "角色/enemy_02",
    enemy_03 = "角色/enemy_03",
    enemy_elite_01 = "角色/enemy_elite_01",
    branchTask_xyll_Shusheng = "角色/branchTask_xyll_Shusheng",
    branchTask_xyll_Linglong = "角色/branchTask_xyll_Linglong",
    branchTask_twz_NvZi = "角色/branchTask_twz_NvZi",
    branchTask_twz_aLiang01 = "角色/branchTask_twz_aLiang01",
    branchTask_twz_aLiang02 = "角色/branchTask_twz_aLiang02",
    branchTask_twz_CiKe = "角色/branchTask_twz_CiKe",
    lmtx_12_npc01 = "角色/lmtx_12_npc01",
    lmtx_12_npc02 = "角色/lmtx_12_npc02",
    lmtx_12_npc03 = "角色/lmtx_12_npc03",
    lmtx_12_npc04 = "角色/lmtx_12_npc04",
    lmtx_13_npc01 = "角色/lmtx_13_npc01",
    lmtx_13_npc02 = "角色/lmtx_13_npc02",
    lmtx_14_npc01 = "角色/lmtx_14_npc01",
    lmtx_15_npc01 = "角色/lmtx_15_npc01",
    bt_twz_npc1_S1 = "角色/bt_twz_npc1_S1",
    bt_twz_npc2_S1 = "角色/bt_twz_npc2_S1",
    bt_twz_npc3_S1 = "角色/bt_twz_npc3_S1",
    bt_twz_npc4_S1 = "角色/bt_twz_npc4_S1",
    bt_twz_npc5_S1 = "角色/bt_twz_npc5_S1",
    bt_twz_npc6_S1 = "角色/bt_twz_npc6_S1",
    bt_twz_npc7_S1 = "角色/bt_twz_npc7_S1",
    bt_twz_npc8_S1 = "角色/bt_twz_npc8_S1",
    bt_twz_npc8_S2 = "角色/bt_twz_npc8_S2",
    bt_twz_npc7_S2 = "角色/bt_twz_npc7_S2",
    bt_twz_npc6_S2 = "角色/bt_twz_npc6_S2",
    bt_twz_npc5_S2 = "角色/bt_twz_npc5_S2",
    bt_twz_npc4_S2 = "角色/bt_twz_npc4_S2",
    bt_twz_npc3_S2 = "角色/bt_twz_npc3_S2",
    bt_twz_npc2_S2 = "角色/bt_twz_npc2_S2",
    bt_twz_npc1_S2 = "角色/bt_twz_npc1_S2",
    Player = "角色/Player",
}

---@class 物体_蜃境_鹿鸣天下_03长安城郊
local objs = {
    exit = "物体/exit",
    chest_1 = "物体/chest_1",
    lmtx_1004 = "物体/lmtx_1004",
    check_XueJie = "物体/check_XueJie",
    check_TianQi = "物体/check_TianQi",
    branchTask_twz_Trigger = "物体/branchTask_twz_Trigger",
    BranchTask_twz_Trigger_1 = "物体/BranchTask_twz_Trigger_1",
}

---@class 相机_蜃境_鹿鸣天下_03长安城郊
local cameras = {
    branchTaskS1cam = "相机/branchTaskS1cam",
    maincam = "相机/maincam",
}

---@class 位置_蜃境_鹿鸣天下_03长安城郊
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
    branchTask_twz_AliangS1_pos = "位置/branchTask_twz_AliangS1_pos",
    branchTask_twz_AliangS2_pos = "位置/branchTask_twz_AliangS2_pos",
}

---@class 资产_蜃境_鹿鸣天下_03长安城郊
local assets = {
}

---@class 动作_蜃境_鹿鸣天下_03长安城郊
local animationClips = {
}

---@class 蜃境_鹿鸣天下_03长安城郊
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
