local bcapi = require("BattleClientA<PERSON>")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

-- 脚本内容：
-- 三个敌人依次序上场跟玩家单挑，时不时有观众的讨论声

function init()

end

function start()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        bcapi.add_condition_trigger("[%t->teammate:世界_芙蕖望月_摔跤赛第一人%][<]1", 0, secondChanllenger)
    end)
end

function secondChanllenger(...)
    bcapi.async_call(function()
        bcapi.create_join_role("世界_芙蕖望月_摔跤赛第二人", 60, eTeam, bcapi.Vector2(6, 3), 0)
        bcapi.add_condition_trigger("[%t->teammate:世界_芙蕖望月_摔跤赛第二人%][<]1", 0, third<PERSON>hanllenger)
    end)
end

function thirdChanllenger()
    bcapi.async_call(function()
        bcapi.create_join_role("世界_芙蕖望月_摔跤赛第三人", 60, eTeam, bcapi.Vector2(6, 0), 0)
        bcapi.add_condition_trigger("[%t->teammate:世界_芙蕖望月_摔跤赛第三人%][<]1", 0, finalChanllenger)
    end)
end

function finalChanllenger(...)
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("主角", "你们就这点本事么？")
        bcapi.talk("主角", "若是无人挑战，我便是今日第一了吧？")
        bcapi.talk("金齿族长老", "我乃金齿族长老西古然，我来做你的对手。")
        bcapi.resume_and_show_ui()
        bcapi.create_join_role("世界_芙蕖望月_摔跤赛长老", 60, eTeam, bcapi.Vector2(6, 0), 0)
        bcapi.add_condition_trigger("[%t->teammate:世界_芙蕖望月_摔跤赛长老%][<]1", 0, winTalk)
    end)
end

function winTalk()
    bcapi.async_call(function()
        bcapi.win()
    end)
end
