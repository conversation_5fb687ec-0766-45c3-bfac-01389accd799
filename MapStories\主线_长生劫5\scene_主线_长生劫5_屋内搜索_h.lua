--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/主线_长生劫5/flags_主线_长生劫5")


---@class 角色_主线_长生劫5_屋内搜索
local npcs = {
    --- "椿岁"
    zhujue = "角色/zhujue",
    aqiao = "角色/aqiao",
}

---@class 物体_主线_长生劫5_屋内搜索
local objs = {
    --- "信"
    Letter = "物体/Letter",
    --- "狗洞"
    DogHole = "物体/DogHole",
    AQiao = "物体/AQiao",
}

---@class 相机_主线_长生劫5_屋内搜索
local cameras = {
    --- "狗洞镜头"
    LensDogHole = "相机/LensDogHole",
    cam_main = "相机/cam_main",
}

---@class 位置_主线_长生劫5_屋内搜索
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
}

---@class 资产_主线_长生劫5_屋内搜索
local assets = {
}

---@class 动作_主线_长生劫5_屋内搜索
local animationClips = {
}

---@class 主线_长生劫5_屋内搜索
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
