
---@type 挑战_玄阙地宫外层
local context = require("MapStories/挑战_玄阙地宫外层/scene_挑战_玄阙地宫外层_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger
local selectCharactor,selectOption

--副本特点
--完成BOSS1和BOSS3后，才显示特殊战斗和BOSS2

s.loadFlags(context.flags)
---@type flags_挑战_玄阙地宫外层
local flags = context.flags
--不可省略，用于外部获取flags

local taskReadTag = 0

function getFlags(...)
    return flags
end
--每次载入调用
function start()

    --有bug，在start里播放会被打断。
    s.playMusic("雾染辛夷")

    --每次进入重新给主线
    --添加副本内主线任务

    if (not s.hasTask(996001,1) and not s.hasTask(996001,2) and not s.hasTask(996001,3)) then
        s.addTask("996001",1)
    end

    print("检查flag值"..flags.boss_1_complete)

    independentCheckTask()
    -- s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","添加主任务",0)

    --只负责检查玩家总进度，确定相关节点{BOSS2,特殊战斗，离开传送门}是否需要打开
    checkBossAlive()

    --负责统一的检查所有小怪和BOSS是否需要打开
    LoadRecord()

    --表演开门
    doorShow()

    --表演对话
    startShow()

    s.readyToStart()
end

function independentCheckTask(...)
    
end

--每次副本都会开门
function doorShow()
    --s.animState(objs.Gate_Left, "Special_1",true,false,5)
    --s.animState(objs.Gate_Right, "Special_1",true,false,5)
    s.soloAnimState(objs.Gate_Left, "Special_Loop_1",true)
    s.soloAnimState(objs.Gate_Right, "Special_Loop_1",true)
    if (flags.boss_1_complete == 1
        and flags.boss_3_complete == 1) then
        s.soloAnimState(objs.boss_2_gate, "Special_Loop_1",true)
    end
end

function doorOpne(...)
    s.soloAnimState(objs.Gate_Left, "Special_Loop_1",true)
    s.soloAnimState(objs.Gate_Right, "Special_Loop_1",true)
    if (flags.boss_1_complete == 1
        and flags.boss_3_complete == 1 
        and s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","BOSS2引导镜头") == 1) then
        s.soloAnimState(objs.boss_2_gate, "Special_Loop_1",true)
    end
end

--仅第一次进入地宫播放对话
function startShow()
    s.blackScreen()
    s.setPos(npcs.zhujue, pos.start)
    -- local tagtag = s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","登场演出")
    -- print(tagtag)
    if (s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","登场演出") == 0)then
        -- s.camera(cameras.camera_show01_01, true)
        s.camera(cameras.camera_show01_01, true)
        s.wait(1)
        s.lightScreen()
        --镜头平移eastinout
        s.camera(cameras.camera_show01_02, true, true, blendHintEnum.Linear, 3, true, true)
        s.cameraAsync(cameras.camera_show01_02, false,true, blendHintEnum.Linear, 2, true, false)
        s.moveAsync(npcs.npc_2,pos.show_01_npc_2_1,2,true)
        s.wait(0.5)
        s.moveAsync(npcs.npc_1,pos.show_01_npc_1_1,2,true)
        s.runAvgTag("副本/副本_玄阙地宫外层/副本_玄阙地宫外层", "副本_玄阙地宫外层_开场_1_师弟", nil, 0)
        s.wait(0.5)
        -- s.talk(npcs.npc_1, "哼哼……让我看看这下面都藏着些什么玄妙之物……")
        s.runAvgTag("副本/副本_玄阙地宫外层/副本_玄阙地宫外层", "副本_玄阙地宫外层_开场_2_师姐", nil, 0)
        s.move(npcs.npc_2,pos.show_01_npc_2_1,2,true)
        s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","登场演出",1)
    else
        s.wait(1)
        s.lightScreen()
    end
end

--手动退出副本或杀进程，重新载入副本时，判断哪些需要刷新，相当于写了一个存档功能
function LoadRecord()
    for k,v in pairs(npcs) do
        --检查BOSS打过没，胜利过就隐藏
        if (string.find(k,"boss") ~= nil)then
            bossCheckAndReset(k)
        elseif (string.find(k,"minor_enemies_") ~= nil)then
            enemyCheckAndReset(k)
        end
    end
end

--批量处理BOSS开关的函数
function bossCheckAndReset(roleKey)
    if (flags[roleKey .. "_complete"] == 1) then
        s.setActive(objs[roleKey .. "_Trigger"],false)
        s.setActive(npcs[roleKey],false)
    end
end

--批量处理小怪开关的函数
function enemyCheckAndReset(roleKey)
    if (flags[roleKey .. "_complete"] == 1) then
        s.setActive(npcs[roleKey],false)
    end
end

--刚进正门后的触发对话，只播一次
function enterGateTalk_1()
    -- 关闭触发器
    s.setActive(objs.show_entergate,false)
    if (s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次进门表演") == 0) then
        s.appendAsyncAside(npcs.npc_1,0,"玉尺先前提醒的不错，地宫的构造果然与前几次不同……","","默认")
        s.wait(4)
        s.appendAsyncAside(npcs.npc_1,0,"少侠、师弟，前进时务必当心四周！","","默认")
        s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次进门表演",1)
    end
end

--刚进中央区域的触发对话，只播一次
function enterCenterTalk_1()
    -- 关闭触发器
    s.setActive(objs.show_entercenter,false)
    if (s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次进门中枢位置表演") == 0) then
    s.appendAsyncAside(npcs.npc_1,0,"此处陈设，比起地宫或是墓穴，倒更像是庭园了。","","默认")
    s.wait(4)
    s.appendAsyncAside(npcs.npc_2,0,"或许是……机关兽的……游园？","","默认")
    s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次进门中枢位置表演",1)
    end
end

--BOSS1的战斗及表演
function boss_1()
    -- 关闭触发器
    s.setActive(objs.boss_1_Trigger,false)

    --演出的部分，只播1次
    if (s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次BOSS1表演") == 0) then
        s.cameraAsync(cameras.boss_1_camera_1, true,true, blendHintEnum.Linear, 2)
        s.wait(1.5)
        s.setActive(npcs.boss_1,true)
        -- s.animState(npcs.boss_1,"Special_2",true,false,0.5)
        s.setPos(npcs.boss_1,pos.boss_1_Show_StartPos)
        s.setActive(objs.effect_boss_1_02,true)
        s.wait(0.2)
        -- s.animState(npcs.boss_1,"Special_1",true,false,2)
        s.runAvgTag("副本/副本_玄阙地宫外层/副本_玄阙地宫外层", "副本_玄阙地宫外层_BOSS1_1_师弟", nil, 0)
        -- s.talk(npcs.npc_2, "是前代机关——辟邪！它所用的兵器前所未见，少侠务必当心！")
        s.wait(0.5)
        s.cameraAsync(cameras.boss_1_camera_2, true,true, blendHintEnum.Linear, 1)
        -- s.move(npcs.boss_1,pos.boss_1_Show_FirePos,1,true)
        s.moveAsync(npcs.boss_1,pos.boss_1_Show_FirePos,5,true)
        s.runAvgTag("副本/副本_玄阙地宫外层/副本_玄阙地宫外层", "副本_玄阙地宫外层_BOSS1_2_师姐", nil, 0)
        -- s.talk(npcs.npc_1, "注意它的攻击范围与时机，才能找到破绽！")
        -- s.animState(npcs.boss_1,"Special_3",true,false,1.5)
        -- s.wait(1.5)
        s.soloAnimState(npcs.boss_1,"Special_Loop_3",true)
        s.setActive(objs.effect_boss_1,true)
        s.wait(0.5)
        s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次BOSS1表演",1)
    end

    -- 调取战斗胜利
    local isWin,bResult = s.challengeMapBattle("1")
    if(isWin)then
        flags.boss_1_complete = 1
        s.setActive(npcs.boss_1,false)
    else
        s.setActive(objs.boss_1_Trigger,true)
    end

    --关闭演出中用到的东西
    s.setActive(objs.effect_boss_1,false)
    s.setActive(objs.effect_boss_1_02,false)
    doorOpne()
    s.camera(cameras.boss_1_camera_2, false)

    --检查任务进度
    checkBossAlive()
end

--第一次进入BOSS2入口时的对话，只播一次
function showEnterBoss2()
    s.setActive(objs.show_enterboss2,false)
    if (s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次机关人提醒") == 0) then
        s.appendAsyncAside(npcs.npc_1,0,"是先前未曾发现的通路……当心脚下……","","默认")
        s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次机关人提醒",1)
    end
end

--BOSS2的战斗和表演
function boss_2()
    -- 关闭触发器
    s.setActive(objs.boss_2_Trigger,false)

    -- 演出
    if (s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次BOSS2表演") == 0) then
        -- s.camera(cameras.boss_2_camera_1, true,true, blendHintEnum.Linear, 2)
        s.setActive(objs.effect_boss_2_sakura,true)
        s.moveAsync(npcs.zhujue,pos.boss_2_Show_zhujue_FinalPos,2,true)
        s.cameraAsync(cameras.boss_2_camera_3, true,true, blendHintEnum.EaseOut, 5)
        s.wait(3.5)
        s.setActive(npcs.boss_2,true)
        s.wait(0.5)
        s.animState(npcs.boss_2,"Special_1",true,false)
        s.wait(0.5)
        s.setPos(npcs.boss_2,pos.boss_2_Show_FinalPos)
        s.wait(1)
        s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次BOSS2表演",1)
    end

    --调取战斗胜利
    local isWin,bResult = s.challengeMapBattle("2")
    if(isWin)then
        flags.boss_2_complete = 1

        s.completeChallengeMap()
    end

    --关闭演出镜头
    doorOpne()
    s.setActive(npcs.boss_2,false)
    s.camera(cameras.boss_2_camera_3, false)
    --检查任务进度
    checkBossAlive()
end

function boss_3()
    -- 关闭触发器
    s.setActive(objs.boss_3_Trigger,false)

    --演出
    if (s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次BOSS3表演") == 0) then
        s.camera(cameras.boss_3_camera_1,true,true,blendHintEnum.EaseInOut,1.5,true,true)
        s.runAvgTag("副本/副本_玄阙地宫外层/副本_玄阙地宫外层", "副本_玄阙地宫外层_BOSS3_1_门口", nil, 0)
        -- s.talk(npcs.npc_1, "我们已不是第一次见过此物——榴榴，即是今日所有机关犬兽之祖。")
        -- s.talk(npcs.npc_2, "它的一举一动都是仿自世间野兽。像对付野兽一样将它制服吧！")
        s.wait(0.5)
        s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","第一次BOSS3表演",1)
    end

    --调取战斗胜利
    local isWin,bResult = s.challengeMapBattle("3")
    if(isWin)then
        flags.boss_3_complete = 1
        s.setActive(npcs.boss_3,false)
    else
        s.setActive(objs.boss_3_Trigger,true)
    end

    --关闭演出镜头
    doorOpne()
    s.camera(cameras.boss_3_camera_1, false)
    --检查任务进度
    checkBossAlive()
end

--特殊战斗
function specialBattle()
    --表演
    s.blackScreen()
    s.wait(0.5)
    s.setPos(npcs.zhujue,pos.special_talk_Pos)
    s.camera(cameras.special_1_camera_1,true)
    s.lightScreen()
    s.turnTo(npcs.zhujue,npcs.npc_4)
    s.animState(npcs.npc_4,"BScratch",true,false,3.5)
    -- s.talk(npcs.npc_2, "两只机关兽的残骸都集中在这了，就等之后支援的师兄们回收了。")
    -- s.wait(0.5)
    -- s.talk(npcs.npc_2, "不过我倒是好奇……少侠、师姐，你们说，方才制服的两只机关兽里，哪一只实力较为强劲？")
    -- s.wait(0.5)
    -- selectOption = s.selectTalk(npcs.zhujue, "...",{"机关兵器【辟邪】","机关犬兽【榴榴】"})
    -- s.camera(cameras.special_1_camera_2,true,true,blendHintEnum.Linear,0,true,true)
    local selectOption = s.runAvgTag("副本/副本_玄阙地宫外层/副本_玄阙地宫外层", "副本_玄阙地宫外层_特殊战斗_1_站桩对聊", nil, 0)
    s.animState(npcs.npc_3,"BAngry",true,false,1)
    -- s.wait(0.2)
    -- s.runAvgTag("副本/副本_玄阙地宫外层/副本_玄阙地宫外层", "副本_玄阙地宫外层_特殊战斗_2_开战", nil, 0)
    -- s.talk(npcs.npc_1, "我的看法正好相反……哼哼，口说无凭，师弟，快快将他们修复，让它们一决高下吧！")
    s.wait(0.5)

    local isWin,bResult
    local battleTb = {}
    -- 调取特殊战斗
    if selectOption == 0 then
        -- isWin,bResult = s.challengeMapBattle("8") 
        isWin,bResult = s.battle( "玄阙地宫外层_特殊战斗_机关炮", battleTb, true)  
    else
        -- isWin,bResult = s.challengeMapBattle("9") 
        isWin,bResult = s.battle( "玄阙地宫外层_特殊战斗_机关犬", battleTb, true)  
    end

    if(isWin)then
        flags.special_1_complete = 1
        --设置完成副本成就
        if(s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","特殊战斗完成") == 0)then
            s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","特殊战斗完成",1)
        end
        s.setActive(npcs.npc_3,false)
        s.setActive(npcs.npc_4,false)
    end
    doorOpne()
    --关闭演出镜头
    s.camera(cameras.special_1_camera_1, false)
end

--在这里将小怪分类，调用不同的战斗（涉及不同的flags）
function minorEnemiesType(roleKey)
    if (roleKey == "minor_enemies_1" or roleKey == "minor_enemies_4") then
        minorEnemiesBattle_1(roleKey)
    else
        minorEnemiesBattle_2(roleKey)
    end
end

--需要实现的是，在1类表中随机过的战斗，下次遇到同类型敌人后不再随机该战斗（风险是需要保证战斗场数一定要大于该类型敌人的数量）

--机关鹰小怪战斗表，按小怪外貌分类调用
local minorEnemiesBattke_1_Table = {"玄阙地宫外层_小怪_机关鹰_1","玄阙地宫外层_小怪_机关鹰_2"}
--防卫机小怪战斗表，按小怪外貌分类调用
local minorEnemiesBattke_2_Table = {"玄阙地宫外层_小怪_防卫机_1","玄阙地宫外层_小怪_防卫机_2"}
--这是用flag来存已经完成的战斗的id
local minorEnemiesBattke_1_completed = {flags.minor_enemies_1_complete,flags.minor_enemies_4_complete}
local minorEnemiesBattke_2_completed = {flags.minor_enemies_2_complete,flags.minor_enemies_3_complete}

-- local minorEnemiesBattke_1_completed = {flags.minorEnemiesBattke_1_count_1,flags.minorEnemiesBattke_1_count_2}
-- local minorEnemiesBattke_2_completed = {flags.minorEnemiesBattke_2_count_1,flags.minorEnemiesBattke_2_count_2}

--机关鹰小怪战斗触发函数
function minorEnemiesBattle_1(roleKey)
    local battleTb = {}
    local isWin,bResult

    if ( roleKey == "minor_enemies_1" ) then
        isWin,bResult = s.battle( minorEnemiesBattke_1_Table[1], battleTb)
    elseif roleKey == "minor_enemies_4" then 
        isWin,bResult = s.battle( minorEnemiesBattke_1_Table[2], battleTb)
    end

    if (isWin) then
        --赢了，把场景怪杀了
        s.setDeath(npcs[roleKey])
        ---@diagnostic disable-next-line: inject-field-fail
        flags[roleKey .. "_complete"] = 1
        ---@diagnostic disable-next-line: inject-field-fail
        -- minorEnemiesBattke_1_completed[minorEnemiesBattle_1_complete] = uncompletedBattles[minorBattleKey]
        killFirstEnemyShow()
        doorOpne()
    else
        --失败了给你传走
        s.blackScreen()
        s.setPos(npcs.zhujue,pos.enemy_1_RetryPos)
        doorOpne()
        s.wait(1)
        s.lightScreen()
        s.aside("地宫艰难重重，请克服它们吧！")
    end
end

--防卫机小怪战斗触发函数
function minorEnemiesBattle_2(roleKey)
    local battleTb = {}
    local isWin,bResult

    if ( roleKey == "minor_enemies_2" ) then
        isWin,bResult = s.battle( minorEnemiesBattke_2_Table[1], battleTb)
    elseif roleKey == "minor_enemies_3" then 
        isWin,bResult = s.battle( minorEnemiesBattke_2_Table[2], battleTb)
    end

    if (isWin) then
        --赢了，把场景怪杀了
        s.setDeath(npcs[roleKey])
        ---@diagnostic disable-next-line: inject-field-fail
        flags[roleKey .. "_complete"] = 1
        ---@diagnostic disable-next-line: inject-field-fail
        killFirstEnemyShow()
        doorOpne()
    else
        --失败了给你传走
        s.blackScreen()
        s.setPos(npcs.zhujue,pos.enemy_1_RetryPos)
        doorOpne()
        s.wait(1)
        s.lightScreen()
        -- s.aside("地宫艰难重重，请克服它们吧！")
    end
end

--第一次战胜小怪给后，播一次对话
function killFirstEnemyShow()
    if (s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","击杀第一个小怪后演出") == 0) then
        s.appendAsyncAside(npcs.npc_1,0,"这些机关兽的技术与今时的玄阙宫全然不同……","","默认")
        s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","击杀第一个小怪后演出",1)
    end
end

--检查任务进度用
function checkBossAlive()
    -- CS.UnityEngine.Debug.LogError("flags.boss_1_complete:" .. flags.boss_1_complete)
    -- CS.UnityEngine.Debug.LogError("flags.boss_2_complete:" .. flags.boss_2_complete)
    -- CS.UnityEngine.Debug.LogError("flags.boss_3_complete:" .. flags.boss_3_complete)
    if (flags.boss_1_complete == 1
        and flags.boss_3_complete == 1) then
        checkLeave()
    else
        s.setActive(objs.leave_Trigger,false)
        s.setActive(objs.boss_2_Trigger,false)
        s.setActive(npcs.boss_2,false)
        s.setActive(objs.show_enterboss2,false)
        s.setActive(npcs.npc_3,false)
        s.setActive(npcs.npc_4,false)  
    end
end

--检查任务进度的第二层判断
function checkLeave()
    -- CS.UnityEngine.Debug.LogError("flags.boss_1_complete:" .. flags.boss_1_complete)
    -- CS.UnityEngine.Debug.LogError("flags.boss_2_complete:" .. flags.boss_2_complete)
    -- CS.UnityEngine.Debug.LogError("flags.boss_3_complete:" .. flags.boss_3_complete)
    if (flags.boss_1_complete == 1
        and flags.boss_2_complete == 1
        and flags.boss_3_complete == 1) then
        s.setActive(objs.leave_Trigger,true)
        specialBattleCheck()
        if s.getCurrentTaskStep("玄阙地宫外层") == "xqdgwc_FinalBoss" then
            s.setTaskStep("玄阙地宫外层","xqdgwc_Leave")
        end

        s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","已完整完成一次",1)
    else
        s.setActive(objs.leave_Trigger,false)
        resetBoss2()
        s.setActive(objs.show_enterboss2,true)
        if s.getCurrentTaskStep("玄阙地宫外层") == "xqdgwc_Active" then
            s.setTaskStep("玄阙地宫外层","xqdgwc_FinalBoss")
        end
        specialBattleCheck()
    end
    --只要完成了BOSS1和BOSS3，就要关闭跟随NPC
    s.setActive(npcs.npc_1,false)
    s.setActive(npcs.npc_2,false)
end

--用来重置BOSS2
function resetBoss2()
    s.setActive(objs.boss_2_Trigger,true)
    -- s.setActive(npcs.boss_2,true)
end

--特殊战斗的开关状态控制
function specialBattleCheck()
    if (s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","特殊战斗完成") == 1)  then
        s.setActive(npcs.npc_3,false)
        s.setActive(npcs.npc_4,false)
    else
        boss2GuideShow()
        s.setActive(npcs.npc_3,true)
        s.setActive(npcs.npc_4,true)
    end
end

--完成boss1和boss3后的镜头演出
function boss2GuideShow()
    if (s.getChallengeMapPersistentFlag("挑战_玄阙地宫外层","BOSS2引导镜头") == 0) then
        s.blackScreen()
        -- s.setPos(npcs.zhujue,pos.enemy_1_RetryPos)
        s.camera(cameras.boss_2_guide_1,true)
        s.wait(0.5)
        s.lightScreen()
        -- s.camera(cameras.boss_2_guide_2,true,true,blendHintEnum.EaseInOut,2,true,true)
        -- -- s.camera(cameras.boss_2_guide_3,true,true,blendHintEnum.EaseInOut,1.5,true,true)    
        -- s.camera(cameras.boss_2_guide_4,true,true,blendHintEnum.EaseInOut,2,true,true)
        s.soloAnimState(objs.boss_2_gate,"Special_Loop_1",true)
        s.wait(0.5)
        -- s.talk(npcs.npc_1, "仔细听，似乎有什么打开的声音……")
        s.runAvgTag("副本/副本_玄阙地宫外层/副本_玄阙地宫外层", "副本_玄阙地宫外层_解锁BOSS2_1_开门镜头", nil, 0)
        -- s.wait(0.5)
        -- -- s.cameraAsync(cameras.boss_2_guide_5,true,true,blendHintEnum.Linear,6.5,true,true)
        -- s.wait(0.5)
        -- s.talk(npcs.npc_1, "往来时的地方看看吧。")
        -- s.wait(3)

        boss2GuideShowEnd()

        s.setChallengeMapPersistentFlag("挑战_玄阙地宫外层","BOSS2引导镜头",1)
    end
end

function boss2GuideShowEnd()
    s.blackScreen()
    s.cameraAsync(cameras.boss_2_guide_5,false,true,blendHintEnum.Cut,0,true,true)
    s.wait(0.5)
    s.lightScreen()
end
--离开结算函数
function Leave()
    s.showConfirmResetChallengeMap()
    if s.getCurrentTaskStep("玄阙地宫外层") == "xqdgwc_Leave" then
        s.setTaskStep("玄阙地宫外层","finished")
    end
end
