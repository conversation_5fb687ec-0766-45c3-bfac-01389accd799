
---@type 主线_青羽录9_战斗后返回光雾林
local context = require("MapStories/主线_青羽录9_战斗后返回光雾林/scene_主线_青羽录9_战斗后返回光雾林_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local assets = context.assets
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger


s.loadFlags(context.flags)
---@type flags_主线_青羽录9_战斗后返回光雾林
local flags = context.flags --[[@as flags_主线_青羽录9_战斗后返回光雾林]]
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
--每次载入调用
function start()
    s.setPos(npcs.zhujue,pos.Pos_start)
    s.setActive(npcs.zhujue,true)
    s.camera(cameras.camera_hurt,true,true,blendHintEnum.Custom,1,true,true)
end


local nani = "蜃境/主线/青羽录_醒"

local function playNani(tag,transitionTime,alpha)
    return s.runAvgTag(nani, tag,transitionTime,alpha)
end


function test()

end

--第一次进入地图
function Trigger1()
    s.talk(npcs.ShiFu, "徒儿不辱使命……七百年基业……未绝我手……")
    s.talk(npcs.JingCheng, "师父……")
    s.setActive(npcs.ShiFu,false)
    s.setActive(npcs.ChuQing,true)
    s.move(npcs.ChuQing,pos.Pos_ChuQing,1,true)
    --s.wait(1)
    s.turnTo(npcs.JingCheng, npcs.ChuQing)
    s.animState(npcs.JingCheng,roleAnims.BGesture)
    local ret = s.selectTalk(npcs.JingCheng, "...", {"你怎么在这里？","我现在在你的幻境中？"})
        if(ret == 0) then
            s.animState(npcs.ChuQing, roleAnims.BDeny)
            s.talk(npcs.ChuQing, "我不在这里，这只是一缕虚影。")
            s.animState(npcs.JingCheng,roleAnims.BThink_Loop)
        else
            s.animState(npcs.ChuQing, roleAnims.BDeny)
            s.talk(npcs.ChuQing, "这不是我的幻境，是你的。")
            s.animState(npcs.JingCheng,roleAnims.BThink_Loop)
        end
    local ret = s.selectTalk(npcs.JingCheng, "...", {"我该怎么离开这里？"})
    s.triggerReset(npcs.JingCheng)
    s.animState(npcs.ChuQing,roleAnims.BGesture)
    s.talk(npcs.ChuQing, "幻境是一面镜子，镜面蒙上了雾，你需要拨开云雾，看清你镜中的你自己。")
    s.talk(npcs.ChuQing, "等你看清了自己，便可自由离去。")
    
    local ret = s.selectTalk(npcs.JingCheng, "...", {"我想救你","我想阻止你"})
        if(ret == 0) then
            s.talk(npcs.ChuQing, "是么？我明明是个罪孽滔天的人，为什么你会对我生出怜悯之心？")
            local ret = s.selectTalk(npcs.JingCheng, "...", {"我不知道，但我想救你出来","并非怜悯，出于道义"})
                if(ret == 0) then
                    s.animState(npcs.ChuQing, roleAnims.BDeny)
                    s.talk(npcs.ChuQing, "若你我二人早些相识，尚有回头之路，但现在……")
                else
                    s.animState(npcs.ChuQing, roleAnims.BDeny)
                    s.talk(npcs.ChuQing, "我早已离经叛道，道义救不了我。")
                end
        else
            s.animState(npcs.ChuQing, roleAnims.BDeny)
            s.talk(npcs.ChuQing, "事已至此，我已无路可回头。")
        end

    s.talk(npcs.ChuQing, "这也许并非你所求。")
    s.talk(npcs.ChuQing, "荆少侠，你该醒了。")
    s.setNewbieFlags("chapter_qyl_16_finished",1)
    s.finishInfiniteStory(true)

end
