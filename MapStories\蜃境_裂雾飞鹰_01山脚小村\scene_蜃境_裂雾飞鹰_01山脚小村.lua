
---@type 蜃境_裂雾飞鹰_01山脚小村
local context = require("MapStories/蜃境_裂雾飞鹰_01山脚小村/scene_蜃境_裂雾飞鹰_01山脚小村_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local assets = context.assets
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_裂雾飞鹰_01山脚小村
local flags = context.flags
--不可省略，用于外部获取flags

local newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")



function getFlags(...)
    return flags
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function test()
    s.setTaskStep("笛声悠悠","findGirl")
end

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function set_pos()
    s.setPos(npcs.zhujue,pos.zhujue_start_pos)
end

function start()
    print("start called")
    if(flags.exit_play == 1)then
        flags.exit_play = 2
        s.setPos(npcs.zhujue,pos.zhujue_start_pos)
    end
    refreshMapStates()
    s.readyToStart(true)
    try_execute_newbieGuide()
end

function try_execute_newbieGuide(...)
    s.nextStep("newbieGuideInLevel")
end


function newbieGuideInLevel()end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点

    s.setActive(npcs.lwfy_01, false)
    s.setActive(npcs.lwfy_02, false)

    if(flags.dsyy_step == 0)then
        s.setActive(npcs.dsyy_gril,true)
        s.setActive(objs.dsyy_trigger,true)
        s.setActive(objs.dsyy_item,false)
    elseif(flags.dsyy_step == 1)then
        s.setActive(npcs.dsyy_gril,true)
        s.setActive(objs.dsyy_trigger,false)
        s.setActive(objs.dsyy_item,false)
    elseif(flags.dsyy_step == 2)then
        s.setActive(npcs.dsyy_gril,true)
        s.setActive(objs.dsyy_trigger,false)
        s.setActive(objs.dsyy_item,true)
    elseif(flags.dsyy_step == 3)then
        s.setActive(npcs.dsyy_gril,true)
        s.setActive(objs.dsyy_trigger,false)
        s.setActive(objs.dsyy_item,false)
    else
        s.setActive(npcs.dsyy_gril,false)
        s.setActive(objs.dsyy_trigger,false)
        s.setActive(objs.dsyy_item,false)
    end

    if(flags.eyu == 1)then
        s.setActive(npcs.eyu,false)
    end

    s.removeAllGameMapGuides()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 3) then
        taskStep = "forwardToExplore"
    elseif (step == 3) then
        taskStep = "leaveShanJiaoXiaoCun"
    end
    s.setTaskStep("裂雾飞鹰山脚小村", taskStep)
        
    reset_all()

    if step == 0 then
        ----第一次进入场景, 这里加入入场演出
        s.blackScreen()
        s.setPos(npcs.zhujue,pos.zhujue_start_pos)
        s.camera(cameras.start_cam,true)
        s.lightScreen()
        s.move(npcs.zhujue,pos.zhujue_start_pos_1,1,true)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_01山脚小村", "蜃境_裂雾飞鹰_01山脚小村_进入")
        s.blackScreen()
        s.camera(cameras.start_cam,false)
        s.camera(cameras.show_cam_1,true)
        s.lightScreen()
        s.camera(cameras.show_cam_2,true,true,blendHintEnum.EaseInOut,2.5,true,true)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_01山脚小村", "蜃境_裂雾飞鹰_01山脚小村_进入2")
        s.blackScreen()
        s.camera(cameras.show_cam_2,false)
        s.lightScreen()
        s.setTaskStep("裂雾飞鹰山脚小村","forwardToExplore")
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.lwfy_01, 0.8)
    elseif step == 2 then
        level_guide_to(npcs.lwfy_02, 0.8)
    else
        --do nothing
        level_guide_to(objs.exit)
    end
end

----------------------OVERRIDE----------------------------------

function lwfy_01()
    executeMemSlot("lwfy_01", quick_play_avg)
end

function lwfy_02()
    executeMemSlot("lwfy_02", quick_play_avg,function()
    s.setTaskStep("裂雾飞鹰山脚小村","leaveShanJiaoXiaoCun")
    next_level_step(true)
    end)
end

function exitScene()
    print("离开场景")
    if(flags.exit_play == 0)then
        flags.exit_play = 1
        s.blackScreen()
        s.camera(cameras.timeline_cam,true)
        s.setActive(objs.exit,false)
        s.setPos(npcs.zhujue,pos.timeline_pos_1)
        s.wait(0.5)
        s.lightScreen()
        s.move(npcs.zhujue,pos.timeline_pos_2,2,true)
        s.camera(cameras.timeline_cam2,true,true,blendHintEnum.EaseInOut,1,true,true)
        s.playTimeline(assets.exit,false)
    end

    local capi = require("ChapterApi")
    capi.exitMemoryScene("裂雾飞鹰")
end

function dsyy_girl()
    if(flags.dsyy_step < 2)then
        s.setActive(objs.dsyy_trigger,false)
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_01山脚小村", "蜃境_裂雾飞鹰_01山脚小村_笛声悠悠_2")
        s.setTaskStep("笛声悠悠","findQuPu")
        s.setActive(objs.dsyy_item,true)
        flags.dsyy_step = 2
    elseif(flags.dsyy_step == 2)then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_01山脚小村", "蜃境_裂雾飞鹰_01山脚小村_笛声悠悠_2_重复对话")
    elseif(flags.dsyy_step == 3)then
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_01山脚小村", "蜃境_裂雾飞鹰_01山脚小村_笛声悠悠_4")
        s.blackScreen()
        s.setActive(npcs.dsyy_gril,false)
        s.wait(0.5)
        s.lightScreen()
        flags.dsyy_step = 4
        s.finishTask("40052004",30)
        local capi = require("ChapterApi")
        capi.finishMemorySlot("lwfy_sideTask_dsyy")
    end
end

function dsyy_enter()
    s.setActive(objs.dsyy_trigger,false)
    flags.dsyy_step = 1
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_01山脚小村", "蜃境_裂雾飞鹰_01山脚小村_笛声悠悠_1")
    s.setTaskStep("笛声悠悠","findGirl")
    refreshMapStates()
end

function dsyy_qupu()
    s.setActive(objs.dsyy_item,false)
    flags.dsyy_step = 3
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_01山脚小村", "蜃境_裂雾飞鹰_01山脚小村_笛声悠悠_3")
    s.setTaskStep("笛声悠悠","backToGirl")
    refreshMapStates()
end

function eyu()
    s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_01山脚小村", "蜃境_裂雾飞鹰_01山脚小村_鳄鱼")
    local ret = s.battle("蜃境_裂雾飞鹰1_打鳄鱼")
    if(ret)then
        flags.eyu = 1
        s.runAvgTag("蜃境/蜃境_裂雾飞鹰/蜃境_裂雾飞鹰_01山脚小村", "蜃境_裂雾飞鹰_01山脚小村_鳄鱼2")
        s.setActive(npcs.eyu,false)
        local capi = require("ChapterApi")
        capi.finishMemorySlot("lwfy_question_crocodile")
    else
        defaultLose()
    end
end