--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/漫游_楚家庄/flags_漫游_楚家庄")


---@class 角色_漫游_楚家庄_别院
local npcs = {
    --- "老仆"
    laopu = "角色/laopu",
    --- "荆成"
    jingcheng = "角色/jingcheng",
    --- "莫知酉"
    mozhiyou = "角色/mozhiyou",
}

---@class 物体_漫游_楚家庄_别院
local objs = {
    --- "竹节侠提示"
    suggest_zhujiexia = "物体/suggest_zhujiexia",
    --- "小石头"
    xiaoshitou = "物体/xiaoshitou",
    --- "小石头擦痕"
    xiaoshitoucaheng = "物体/xiaoshitoucaheng",
    --- "宝箱2"
    chest2 = "物体/chest2",
    --- "宝箱2特效"
    triggerChest2 = "物体/triggerChest2",
    --- "宝箱3"
    chest3 = "物体/chest3",
    --- "宝箱3特效"
    triggerChest3 = "物体/triggerChest3",
    --- "宝箱4"
    chest4 = "物体/chest4",
    --- "宝箱4特效"
    triggerChest4 = "物体/triggerChest4",
    --- "宝箱5"
    chest5 = "物体/chest5",
    --- "宝箱5特效"
    triggerChest5 = "物体/triggerChest5",
}

---@class 相机_漫游_楚家庄_别院
local cameras = {
    --- "竹节侠相机"
    zhujiexia = "相机/zhujiexia",
    --- "竹节侠老仆相机"
    zhujiexia2 = "相机/zhujiexia2",
}

---@class 位置_漫游_楚家庄_别院
local pos = {
    --- "开始位置"
    start = "位置/start",
    --- "莫知酉竹节侠站位"
    MZY_zhujiexia = "位置/MZY_zhujiexia",
    --- "荆成竹节侠站位"
    JC_zhujiexia = "位置/JC_zhujiexia",
    --- "莫知酉竹节侠老仆站位"
    MZY_zhujiexia2 = "位置/MZY_zhujiexia2",
    --- "荆成竹节侠老仆站位"
    JC_zhujiexia2 = "位置/JC_zhujiexia2",
}

---@class 资产_漫游_楚家庄_别院
local assets = {
}

---@class 动作_漫游_楚家庄_别院
local animationClips = {
}

---@class 漫游_楚家庄_别院
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
