local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        team = bcapi.get_team(0)
        bcapi.create_join_role_with_card("诸葛鹿", 34, team, bcapi.Vector2(-5, 0), 0)
        bcapi.create_join_role_with_card("诸葛玲珑", 34, team, bcapi.Vector2(-2, 0), 0)
        bcapi.add_strategy_card("吐纳法", 5, team)
        bcapi.add_strategy_card("天罡真气", 5, team)
        bcapi.add_strategy_card("梧叶舞秋风", 5, team)
        bcapi.add_strategy_card("大藏心经", 5, team)
    end)
end

function start()
    StartTalk()
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        
        bcapi.talk("诸葛鹿", "南山老怪，看拳！")
        
        bcapi.resume_and_show_ui()
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
