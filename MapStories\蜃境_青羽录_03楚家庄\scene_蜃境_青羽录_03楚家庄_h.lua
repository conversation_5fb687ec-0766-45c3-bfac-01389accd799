--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_青羽录_03楚家庄/flags_蜃境_青羽录_03楚家庄")


---@class 角色_蜃境_青羽录_03楚家庄
local npcs = {
    zhujue = "角色/zhujue",
    qyl_08 = "角色/qyl_08",
    qyl_09 = "角色/qyl_09",
    qyl_10 = "角色/qyl_10",
    qyl_11 = "角色/qyl_11",
    qyl_12 = "角色/qyl_12",
    qyl_13 = "角色/qyl_13",
    qyl_14 = "角色/qyl_14",
    enemy_01 = "角色/enemy_01",
    enemy_02 = "角色/enemy_02",
    enemy_03 = "角色/enemy_03",
    enemy_04 = "角色/enemy_04",
    enemy_05 = "角色/enemy_05",
    elite_06 = "角色/elite_06",
    elite_07 = "角色/elite_07",
    jingcheng = "角色/jingcheng",
    yunwusheng = "角色/yunwusheng",
    lingling = "角色/lingling",
}

---@class 物体_蜃境_青羽录_03楚家庄
local objs = {
    exit = "物体/exit",
    qyl_11_guide = "物体/qyl_11_guide",
    qyl3_1002 = "物体/qyl3_1002",
    qyl3_1003 = "物体/qyl3_1003",
    qyl3_1004 = "物体/qyl3_1004",
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
    fireTrigger1 = "物体/fireTrigger1",
    fireTrigger2 = "物体/fireTrigger2",
    fireTrigger3 = "物体/fireTrigger3",
    fire1 = "物体/fire1",
    fire2 = "物体/fire2",
    fire3 = "物体/fire3",
    chest_3 = "物体/chest_3",
    hint = "物体/hint",
    tree = "物体/tree",
}

---@class 相机_蜃境_青羽录_03楚家庄
local cameras = {
    camera1 = "相机/camera1",
    camera2 = "相机/camera2",
    camera3 = "相机/camera3",
    overview = "相机/overview",
}

---@class 位置_蜃境_青羽录_03楚家庄
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
}

---@class 资产_蜃境_青羽录_03楚家庄
local assets = {
}

---@class 动作_蜃境_青羽录_03楚家庄
local animationClips = {
}

---@class 蜃境_青羽录_03楚家庄
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
