local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    local time = 903
    if(bcapi.get_team_dict_has_key(0, "战斗时间"))then
        time = bcapi.get_team_dict_int_value(0, "战斗时间", time) + 3
    end
    bcapi.reset_limit_time(time)
end

function start()
    bcapi.async_call(function()
        local isSummon = 1
        local isCard = 0

        if(bcapi.get_team_dict_has_key(0, "是否召唤"))then
            isSummon = bcapi.get_team_dict_int_value(0, "是否召唤", 0)
        end
        if(bcapi.get_team_dict_has_key(0, "是否策略卡"))then
            isCard = bcapi.get_team_dict_int_value(0, "是否策略卡", 0)
        end

        if(isSummon == 1)then
            bcapi.create_and_summon_role("副本_夜战开封_燕路重", 65, bcapi.get_team(0), bcapi.Vector2(-6, 0), 0.1,-1,1,1,1,1,1,"")
        end
        if(isCard == 1)then
            bcapi.create_and_summon_role("副本_夜战开封_开封士兵", 65, bcapi.get_team(0), bcapi.Vector2(-6, 2), 0.1,-1,1,1,1,1,1,"")
            bcapi.create_and_summon_role("副本_夜战开封_开封士兵", 65, bcapi.get_team(0), bcapi.Vector2(-6, -2), 0.1,-1,1,1,1,1,1,"")
        end
        bcapi.add_timer_trigger(3, Talk)
    end)
end

function Talk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("张熙和#副本_夜战开封_张熙和", "越盟主，请放心，没有谁能突破我们天武军的长枪铁壁！")
        bcapi.talk("越千山#副本_夜战开封_越千山3", "天武军的威名我自是知晓，但张兄弟仍不可大意，这伙贼人的实力的确不容小觑。")
        bcapi.talk("张熙和#副本_夜战开封_张熙和", "小可省的，越盟主请顾全好自己的安危，并且不要忘记对我的承诺！")
        bcapi.talk("主角#主角", "……")
        bcapi.resume_and_show_ui()

        bcapi.add_condition_trigger("[%enemy:副本_夜战开封_张熙和->buff_lv:张熙和_巨灵附体计数%][>=]50", 1, EnemyTransform)
    end)
end

function EnemyTransform()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("张熙和#副本_夜战开封_张熙和", "呵呵呵……哈哈哈哈，天武军的究极奥义，就让你们来见识一下吧！")
        bcapi.talk("张熙和#副本_夜战开封_张熙和", "这就是——巨灵附体！")

        local Boss = bcapi.get_role(1, "副本_夜战开封_张熙和")
        Boss.Stat.BattleFlagDic:set_Item("可以变身", "1")
        bcapi.resume_and_show_ui()

        bcapi.add_condition_trigger("[%enemy:副本_夜战开封_张熙和->flag:变身完毕%][>]0", 1, EnemyTransformDesc)
    end)
end

function EnemyTransformDesc()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("主角#主角", "……他的体型竟突然暴涨数尺，变得如此巨大！")
        bcapi.talk("燕路重#副本_夜战开封_燕路重", "我早听闻天武军有这一不传之秘，可以极大地激发肉体的潜能，在一段时间内爆发出惊人的力量，今日终于得见。")
        bcapi.talk("燕路重#副本_夜战开封_燕路重", "这样强大的招式也伴随着代价，维持这种状态需要耗费他大量精力，我们只要保持进攻，他这个状态定然持续不长！")
        bcapi.resume_and_show_ui()
        bcapi.show_pop_info("张熙和进入巨灵附体状态，攻击力大幅度提高！", 2)
        bcapi.wait(2)
        bcapi.show_pop_info("通过持续造成伤害可以令其退出巨灵附体状态！", 4)
    end)
end