local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
--玩家队伍
local team = bcapi.get_team(0)
--敌人队伍
local eTeam = bcapi.get_team(1)

--初始化战斗函数，在战斗的最初执行
function init()
    bcapi.async_call(function()
        --埋点
        bcapi.ta_event_track("player_start_story", "id", "120")
        ---- 禁用卡牌等级显示
        --battle.HideCardLevel = true
        ---- 禁用自动战斗按钮
        bcapi.disable_auto_battle()
        ---- 禁用加速
        bcapi.disable_speed_up()
        -- 禁用身法按钮
        bcapi.disable_dart()
        ---- 禁用左侧登场牌
        --bcapi.disable_join_card_expand_mode()

        -- 禁用战场UI，留待需要时显示
        bcapi.hide_ui("画布")
        bcapi.hide_ui("疗伤")
        bcapi.hide_ui("怒气条")
        bcapi.hide_ui("角色技区")
        bcapi.hide_ui("策略技区")

        -- 创建玩家角色
        bcapi.create_join_new_player_with_card_newbie(1, team, bcapi.Vector2(1, 0), 0)
    end)
end

--战斗开始时自动执行的函数，差不多在切完镜头之后
function start()
    --添加触发器，开场演出
    -- bcapi.add_timer_trigger(2, cardLesson1)
    --初始对白
    bcapi.add_condition_trigger("[%enemy:世界_归天人->flag:世界_归天人_对话%][>]0", 0, talkStart)
    bcapi.add_condition_trigger("[%teammate:主角_新手战斗->hp_pct%][<=]0.2", 0, Battle1)
    -- 策略卡教学触发器，在归天人对主角造成特殊攻击后触发
    bcapi.add_condition_trigger("[%enemy:世界_归天人->flag:归天人_教学阶段%][>]0", 0, Battle2)
    bcapi.add_condition_trigger("[%enemy:世界_归天人->flag:世界_归天人_突进教学%][>]0", 0, cardLesson1)
    bcapi.add_condition_trigger("[%enemy:世界_归天人->flag:世界_归天人_技能1教学%][>]0", 0, cardLesson2)
    -- bcapi.add_condition_trigger("[%enemy:世界_归天人->flag:世界_归天人_技能2教学%][>]0", 0, cardLesson3)

    -- 敌人增援触发器，同时也会教学绝招牌
    -- bcapi.add_condition_trigger("[%enemy:世界_归天人->hp_pct%][<=]0.7", 0, summonEnemy)

    -- 主角获得化劫触发器，防止玩家没按正常流程走

    -- 主角触发化劫触发器
    -- bcapi.add_condition_trigger("[%teammate:主角->flag:主角触发化劫%][=]1", 0, endBattle2)
end

function talkStart()
    bcapi.async_call(function()
        bcapi.pause()
        -- 播放对白
        bcapi.talk("主角#主角_新手战斗", bcapi.text("你的话到底什么意思？"))
        bcapi.talk("世界_归天人#世界_归天人", "有什么意义？这里的一切对你来说都不过一幅画，一场梦。")
        bcapi.talk("世界_归天人#世界_归天人", "这里的人，生在这个世界，活在这个世界，爱这个世界，恨这个世界是什么滋味，你不会懂得。因为你根本就不属于这里。")
        bcapi.talk("世界_归天人#世界_归天人", "天外来客！杀无赦！")

        -- 恢复战斗进程
        bcapi.resume()
        bcapi.wait(2)
        bcapi.show_ui("画布")
        bcapi.show_ui("怒气条")
        bcapi.show_ui("角色技区")
        bcapi.show_ui("策略技区")


    end)
end
function Battle1()
    bcapi.async_call(function()
        -- 暂停游戏进程
        bcapi.pause_and_hide_ui()
        bcapi.add_role_card("药曲老", 30, 0, 0, team)
        bcapi.talk("药曲老#药曲老", "阁主撑住，我来助你！")
        -- 恢复战斗进程
        bcapi.resume_and_show_ui()
        bcapi.pause()
        -- bcapi.show_ui("技能区")
        -- bcapi.show_ui("角色技区")
        -- bcapi.show_ui("怒气条")
        -- bcapi.wait(0.1)
        bcapi.show_join_role_guide("药曲老", "将药曲老拖至场上，令其加入战斗吧！")
        bcapi.wait(0.5)
        bcapi.pause()
        bcapi.show_unique_skill_guide("药曲老", "主角_新手战斗", "拖拽绝招按钮至自己身上，释放绝招「来一剂猛药」")
        --给药曲老减攻速，不当内鬼了
        YaoQulao = bcapi.get_player_role("药曲老")
        YaoQulao.Buff:AddBuff("通用_教学关_药曲老减攻速", 300, 1, 1, YaoQulao)
        YaoQulao.Buff:AddBuff("通用_无法选中_无显示", 300, 1, 1, YaoQulao)
    end)
end

function Battle2()
    bcapi.async_call(function()
        -- 暂停游戏进程
        bcapi.pause_and_hide_ui()
        bcapi.talk("世界_归天人#世界_归天人", "啧，果真邪祟手段！")
        bcapi.talk("世界_归天人#世界_归天人#真容", "咳咳。天地广袤，天道恒常，天命为尊，天意难违！")
        bcapi.talk("世界_归天人#世界_归天人#真容", "万事皆有因缘，天灾亦是天怒！破天律者，人人当诛！")
        bcapi.talk("世界_归天人#世界_归天人#真容", "身外身！")       

        -- bcapi.free_play_unique_card(team,"主角",nil,bcapi.Vector2(Boss.Transform.Position.x-1, Boss.Transform.Position.y))

        -- 恢复战斗进程
        bcapi.resume_and_show_ui()
        local Boss = bcapi.get_role(1, "世界_归天人")
        bcapi.use_skill_to_role(Boss, "世界_归天人_子技能_指挥变剑灵", Boss, 1)
    end)
end

function cardLesson1()
    bcapi.async_call(function()
        -- 播报对白
        bcapi.pause_and_hide_ui()
        bcapi.talk("药曲老#药曲老", "阁主！此人剑招凌厉不能硬抗，使用「大藏心经」对其克制！")
        bcapi.resume_and_show_ui()

        bcapi.show_hint_desc_panel("御伤教学", "御伤", "以守为攻，拥有「御伤」状态的侠客，会出现盾牌标志，数字代表当前层数。当侠客在「御伤」的保护之下时，受到的伤害会大幅度降低。只有内劲伤害才可击破「御伤」状态。", "知道了")
        bcapi.show_strategy_guide("大藏心经", "主角_新手战斗", "拖拽卡牌到主角身上，令其获得「御伤」", 10)
        bcapi.add_strategy_card("大藏心经", 10, team, true)
        --埋点
        bcapi.ta_event_track("player_start_story", "id", "121")

    end)
end

function cardLesson2()
    bcapi.async_call(function()
        --豪云剑魂教学
        bcapi.pause_and_hide_ui()
        bcapi.talk("药曲老#药曲老", "阁主，使用「血沸剑魂」，抵抗控制，分而歼之！")
        bcapi.resume_and_show_ui()
        bcapi.show_strategy_guide("血沸剑魂", "主角_新手战斗", "使用血沸剑魂获得强大增益", 1)
        bcapi.add_strategy_card("血沸剑魂", 10, team, true)
        --教学结束后添加策略
        bcapi.add_strategy_card("吐纳法", 10, team)
        ----埋点
        bcapi.ta_event_track("player_start_story", "id", "122")
    end)
end

-- function cardLesson3()
--     bcapi.async_call(function()
--         --霸王怒吼教学
--         bcapi.pause_and_hide_ui()
--         bcapi.talk("药曲老#药曲老", "使用霸王怒吼，更快发挥剑魂的威力，打断对方招式！")
--         bcapi.resume_and_show_ui()
--         bcapi.show_strategy_guide("霸王怒吼", "主角", "使用霸王怒吼增加攻速，更快触发豪云剑魂的效果", 1)
--         bcapi.add_strategy_card("霸王怒吼", 10, team, true)

--         --埋点
--         bcapi.ta_event_track("player_start_story", "id", "123")
        
--         --教学结束后添加策略
--         bcapi.add_strategy_card("吐纳法", 10, team)
--     end)
-- end