--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/漫游_雪谷/flags_漫游_雪谷")


---@class 角色_漫游_雪谷_风雪神道_山脚
local npcs = {
    --- "明慧"
    minghui = "角色/minghui",
    --- "守山人"
    shoushanren = "角色/shoushanren",
    --- "玄阙宫弟子"
    xqgdizi = "角色/xqgdizi",
    --- "墨七"
    moqi = "角色/moqi",
}

---@class 物体_漫游_雪谷_风雪神道_山脚
local objs = {
    --- "洞口"
    caveGate = "物体/caveGate",
}

---@class 相机_漫游_雪谷_风雪神道_山脚
local cameras = {
}

---@class 位置_漫游_雪谷_风雪神道_山脚
local pos = {
    --- "通往山路"
    Shanlu = "位置/Shanlu",
    --- "通往流亡者营地"
    Yingdi = "位置/Yingdi",
    --- "明慧上山"
    MH_shangshan = "位置/MH_shangshan",
    --- "通往悬崖井边"
    Shanya = "位置/Shanya",
}

---@class 资产_漫游_雪谷_风雪神道_山脚
local assets = {
}

---@class 动作_漫游_雪谷_风雪神道_山脚
local animationClips = {
}

---@class 漫游_雪谷_风雪神道_山脚
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
