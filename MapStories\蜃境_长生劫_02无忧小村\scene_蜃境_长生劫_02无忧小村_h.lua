--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_长生劫_02无忧小村/flags_蜃境_长生劫_02无忧小村")


---@class 角色_蜃境_长生劫_02无忧小村
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "csj_05"
    csj_05 = "角色/csj_05",
    --- "csj_06"
    csj_06 = "角色/csj_06",
    --- "csj_07"
    csj_07 = "角色/csj_07",
    --- "csj_08"
    csj_08 = "角色/csj_08",
    --- "csj_09"
    csj_09 = "角色/csj_09",
    --- "csj_10"
    csj_10 = "角色/csj_10",
    --- "enemy_1"
    enemy_1 = "角色/enemy_1",
    --- "enemy_2"
    enemy_2 = "角色/enemy_2",
    --- "enemy_3"
    enemy_3 = "角色/enemy_3",
    --- "enemy_4"
    enemy_4 = "角色/enemy_4",
    --- "elite_6"
    elite_6 = "角色/elite_6",
    --- "man_shadow_8"
    man_shadow_8 = "角色/man_shadow_8",
    --- "pangfuren_shadow_9"
    pangfuren_shadow_9 = "角色/pangfuren_shadow_9",
    --- "man_shadpw_9"
    man_shadow_9 = "角色/man_shadow_9",
    --- "womanJ_shadow_10"
    woman_shadow_10 = "角色/woman_shadow_10",
    --- "chunsui"
    chunsui = "角色/chunsui",
    HYWJ_NPC_02 = "角色/HYWJ_NPC_02",
    --- "小孩子"
    HYWJ_NPC_02_child_1 = "角色/HYWJ_NPC_02_child_1",
    --- "顽童"
    HYWJ_NPC_02_child_2 = "角色/HYWJ_NPC_02_child_2",
    --- "老人"
    HYWJ_NPC_02_oldman = "角色/HYWJ_NPC_02_oldman",
    --- "小花"
    HYWJ_NPC_02_child_3 = "角色/HYWJ_NPC_02_child_3",
    QZLL_NPC = "角色/QZLL_NPC",
    --- "观众"
    QZLL_NPC_guanzhong = "角色/QZLL_NPC_guanzhong",
    --- "地仙"
    QZLL_NPC_dixian = "角色/QZLL_NPC_dixian",
    --- "青年"
    QZLL_NPC_qingnian = "角色/QZLL_NPC_qingnian",
    --- "辩手"
    QZLL_NPC_bianshou = "角色/QZLL_NPC_bianshou",
}

---@class 物体_蜃境_长生劫_02无忧小村
local objs = {
    --- "出口"
    exit = "物体/exit",
    --- "chest_1"
    chest_1 = "物体/chest_1",
    --- "enemy_1_collider"
    enemy_1_collider = "物体/enemy_1_collider",
    --- "enemy_2_collider"
    enemy_2_collider = "物体/enemy_2_collider",
    trigger_chunsui_1 = "物体/trigger_chunsui_1",
    HYWJ_object_02_Medicine = "物体/HYWJ_object_02_Medicine",
    --- "书牍·饿死事小"
    csj_1003 = "物体/csj_1003",
    --- "书牍·万事无忧"
    csj_1004 = "物体/csj_1004",
}

---@class 相机_蜃境_长生劫_02无忧小村
local cameras = {
    cam_main = "相机/cam_main",
    cam_start_near = "相机/cam_start_near",
    cam_start_far = "相机/cam_start_far",
    --- "清浊理论摄像机"
    cam_QZLL = "相机/cam_QZLL",
    --- "何以为家摄像机"
    cam_HYWJ = "相机/cam_HYWJ",
}

---@class 位置_蜃境_长生劫_02无忧小村
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    --- "enemy_1"
    enemy_1 = "位置/enemy_1",
    --- "enemy_2"
    enemy_2 = "位置/enemy_2",
    --- "何以为家小孩演出跑动目标点"
    HYWJ_NPC_02_child_1_aim = "位置/HYWJ_NPC_02_child_1_aim",
    --- "何以为家顽童演出跑动目标点"
    HYWJ_NPC_02_child_2_aim = "位置/HYWJ_NPC_02_child_2_aim",
    --- "何以为家主角演出跑动目标点"
    HYWJ_Player_story_location = "位置/HYWJ_Player_story_location",
}

---@class 资产_蜃境_长生劫_02无忧小村
local assets = {
    --- "chunsui_enter"
    chunsui_enter = "资产/chunsui_enter",
}

---@class 动作_蜃境_长生劫_02无忧小村
local animationClips = {
}

---@class 蜃境_长生劫_02无忧小村
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
