---@type flags_挑战_楚家庄New
local flags = require("MapStories/挑战_楚家庄New/flags_挑战_楚家庄New")
local s = require("StoryApi")

s.loadFlags(flags)


---@class 挑战_楚家庄New_StringItemData
local stringItemData = {
    fire = {key = "initFireLv",showName="火势等级",desc = "火势等级达到10级时，本次挑战失败"},
    battletotalTime = {key = "initBattleTimeTotal",showName="战斗总时序",desc = "测试参数名2"},
    survivor = {key = "initSurvivor",showName="救出人数",desc = "测试参数名3"},
}

local stringWidget = StringWidget:new(stringItemData,flags)

local stringWidgetInfo = {
    title = "副本信息",
    desc = "楚家庄已经笼罩在火焰中了，战斗时序每增加30秒，火势等级提升1级，当火势等级达到10级时，本次挑战失败。\n",
    items = stringItemData,
    widget = stringWidget
}

extensions = {
    useWidgets = {"StringWidget","ItemWidget"},
    widgets = {
        StringWidgetInfo = stringWidgetInfo,
        ItemWidgetInfo = nil
    }
}

s.syncExtendsInfo(extensions)
return extensions