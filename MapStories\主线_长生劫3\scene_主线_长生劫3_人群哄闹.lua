
---@type 主线_长生劫3_人群哄闹
local context = require("MapStories/主线_长生劫3/scene_主线_长生劫3_人群哄闹_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger
local assets = context.assets

s.loadFlags(context.flags)
---@type flags_主线_长生劫3
local flags = context.flags --[[@as flags_主线_长生劫3]]
--不可省略，用于外部获取flags

---@type NewbieApi
local newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.setPos(npcs.zhujue,pos.Pos_start)
    s.playMusic("bird")
    s.addTask(505000,1)
    s.camera(cameras.camera_start,true)
    s.readyToStart()
    
    s.talk(npcs.zhujue,"好生喧闹，去看看怎么回事。")
    s.camera(cameras.camera_start,false,false,false,3)
end

function TriggerPlayTimeline()
    -- s.finishTask(505000, 1)
    s.setActive(objs.effects_task,false)
    s.setActive(objs.Trigger1,false)
    s.setActive(objs.role,false)
    s.setActive(npcs.zhujue,false)
    s.playTimeline(assets.Timeline1)
    s.setNewbieFlags(newbieFlags.chapter_csj_10_finished,1)
    s.finishInfiniteStory(true)

end