local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
end

function start()
    bcapi.async_call(function()
        TalkBegin()
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 1, TalkEnd)
        bcapi.add_condition_trigger("[%enemy:剧情_野店老板->flag:剧情_野店老板_老婆挂了%][=]1", 0, TalkKuangBao)
    end)
end

function TalkBegin()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("云舞笙#云舞笙#生气", "你——你何时下的蛊！")
        bcapi.talk("楚青#剧情_坏楚青#开心", "这荒郊野店鲜有人来，一对贫贱夫妻，清贫惯了，怎么舍得就这么把肉倒掉。")
        bcapi.talk("云舞笙#云舞笙#惊讶", "是那块肥肉！")
        bcapi.talk("楚青#剧情_坏楚青#开心", "聪明~不过我丑话说在前头，这狂蛊若不及时解开，他们就会接连发狂而死。")
        bcapi.talk("楚青#剧情_坏楚青#开心", "如何，要连累无辜之人吗？")
        bcapi.talk("莫知酉#莫知酉#严肃", "先把他们打晕再说。")
        bcapi.resume_and_show_ui()

        bcapi.show_auto_battle_guide("点击自动按钮，可以开启自动战斗。", 
            "点击自动策略按钮，打开自动策略的编辑面板。", 
            "拖拽面板摇杆可以改变侠客自动战斗的策略。")

        bcapi.wait(5)
        bcapi.hide_auto_battle_guide("再次点击自动策略按钮，可以关闭面板哦！")
    end)
end

function TalkKuangBao()
    bcapi.async_call(function()
    bcapi.wait(2)
        bcapi.pause_and_hide_ui()
        bcapi.talk("莫知酉#莫知酉#震惊", "坏了，老板以为我们杀了他娘子，看来一时压不住他了。")
        bcapi.talk("莫知酉#莫知酉#叹气", "唉，没想到这老板还是有点武功的，我们应该先打晕他的。")
        bcapi.talk("云舞笙#云舞笙#严肃", "阁主，我们没有十足的把握，要不还是先撤为妙！")
        bcapi.talk("主角", "先试试再说！")
        bcapi.resume_and_show_ui()
    end)
end

function TalkEnd()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("楚青#剧情_坏楚青#开心", "你打晕又如何？只要我不解蛊，他们一旦醒了就会继续发狂，直至精血耗尽。")
        bcapi.talk("莫知酉#莫知酉", "楚姑娘，如果楚老爷肯认你娘的身份，你是否会原谅他？")
        bcapi.talk("楚青#剧情_坏楚青#严肃", "你想做什么？")
        bcapi.talk("莫知酉#莫知酉#玩味笑", "如果你不解蛊，你的事我们就不管了，但是在下会劝说楚老爷还你娘一个外室的身份，连着你的身份一起，公布天下。")
        bcapi.talk("楚青#剧情_坏楚青#愤怒", "你敢！！我娘不需要这种令人作呕的身份！！")
        bcapi.talk("莫知酉#莫知酉", "那就还请楚姑娘解毒。")
        bcapi.talk("楚青#剧情_坏楚青#严肃", "呵……原来正派也用这等腌臜手段。")
        bcapi.talk("莫知酉#莫知酉#大笑", "哎呀，世上哪有什么正派邪派，有的只是好人恶人，好事恶事。")
        bcapi.talk("莫知酉#莫知酉#玩味笑", "既然不收你作恶之心，我也只好以恶制恶。")
        bcapi.talk("楚青#剧情_坏楚青#严肃", "算你有本事。解药，拿去。")
        bcapi.talk("莫知酉#莫知酉", "荆兄，验药。")
        bcapi.talk("楚青#剧情_坏楚青", "解药不会有假，我也不会再做什么了。")
        bcapi.talk("莫知酉#莫知酉#大笑", "那就好，不然我们还真不好意思在这儿住一晚了。")
        bcapi.resume_and_show_ui()
        bcapi.win()
    end)
end