--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/主线_青羽录9.2_返回光雾林幻境/flags_主线_青羽录9.2_返回光雾林幻境")


---@class 角色_主线_青羽录9.2_返回光雾林幻境
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "楚青"
    chuqing = "角色/chuqing",
    jingcheng = "角色/jingcheng",
}

---@class 物体_主线_青羽录9.2_返回光雾林幻境
local objs = {
    --- "最终亭子"
    trigger_tingzi  = "物体/trigger_tingzi ",
    --- "交界地"
    trigger_through  = "物体/trigger_through ",
    --- "最后"
    trigger_final = "物体/trigger_final",
    --- "1左传送"
    left_1 = "物体/left_1",
    --- "2右传送"
    right_2 = "物体/right_2",
    --- "3右传送"
    right_3 = "物体/right_3",
}

---@class 相机_主线_青羽录9.2_返回光雾林幻境
local cameras = {
    --- "跟随镜头"
    cam_follow = "相机/cam_follow",
    --- "跟随镜头1"
    cam_follow_1 = "相机/cam_follow_1",
    --- "跟随镜头2"
    cam_follow_2 = "相机/cam_follow_2",
    --- "亭子"
    cam_tingzi_1 = "相机/cam_tingzi_1",
    --- "跟随镜头3"
    cam_follow_3 = "相机/cam_follow_3",
    --- "初始位置"
    cam_start = "相机/cam_start",
    --- "亭子1"
    cam_tingzi_2 = "相机/cam_tingzi_2",
    --- "亭子2"
    cam_tingzi_3 = "相机/cam_tingzi_3",
    --- "最终的亭子"
    cam_final = "相机/cam_final",
    --- "交界地"
    cam_through = "相机/cam_through",
    --- "石头"
    cam_stone = "相机/cam_stone",
    --- "枇杷"
    cam_pipa = "相机/cam_pipa",
    --- "近景石头"
    cam_stone_n = "相机/cam_stone_n",
    --- "最后"
    cam_end = "相机/cam_end",
    --- "荆成"
    cam_jingcheng = "相机/cam_jingcheng",
    --- "楚青"
    cam_chuqing = "相机/cam_chuqing",
}

---@class 位置_主线_青羽录9.2_返回光雾林幻境
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    --- "传送2右"
    pos_tele_2_r = "位置/pos_tele_2_r",
    --- "传送2左"
    pos_tele_2_l = "位置/pos_tele_2_l",
    --- "传送1右"
    pos_tele_1_r = "位置/pos_tele_1_r",
    --- "传送1左"
    pos_tele_1_l = "位置/pos_tele_1_l",
    --- "传送3右"
    pos_tele_3_r = "位置/pos_tele_3_r",
    --- "传送3左"
    pos_tele_3_l = "位置/pos_tele_3_l",
    --- "传送"
    pos_tele = "位置/pos_tele",
    --- "测试"
    pos_test = "位置/pos_test",
    --- "楚青"
    pos_chuqing = "位置/pos_chuqing",
    --- "玩家"
    pos_player = "位置/pos_player",
    --- "世界左"
    pos_world_l = "位置/pos_world_l",
}

---@class 资产_主线_青羽录9.2_返回光雾林幻境
local assets = {
    gwl_1 = "资产/gwl_1",
    gwl_2 = "资产/gwl_2",
}

---@class 动作_主线_青羽录9.2_返回光雾林幻境
local animationClips = {
}

---@class 主线_青羽录9.2_返回光雾林幻境
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
