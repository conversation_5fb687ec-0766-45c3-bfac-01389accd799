--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_裂雾飞鹰_02玄阙宫弟子房/flags_蜃境_裂雾飞鹰_02玄阙宫弟子房")


---@class 角色_蜃境_裂雾飞鹰_02玄阙宫弟子房
local npcs = {
    zhujue = "角色/zhujue",
    lwfy_03 = "角色/lwfy_03",
    lwfy_04 = "角色/lwfy_04",
    enemy_1 = "角色/enemy_1",
    enemy_2 = "角色/enemy_2",
    enemy_3 = "角色/enemy_3",
    elite_4 = "角色/elite_4",
    jiguanying = "角色/jiguanying",
    yuchi = "角色/yuchi",
    yuqiong = "角色/yuqiong",
    puzzle_Npc = "角色/puzzle_Npc",
}

---@class 物体_蜃境_裂雾飞鹰_02玄阙宫弟子房
local objs = {
    exit = "物体/exit",
    chest_1 = "物体/chest_1",
    sideQuestTrigger = "物体/sideQuestTrigger",
    lwfy_1001 = "物体/lwfy_1001",
    lwfy_1002 = "物体/lwfy_1002",
    puzzleKey_A_mid = "物体/puzzleKey_A_mid",
    puzzleKey_A_left = "物体/puzzleKey_A_left",
    puzzleKey_A_right = "物体/puzzleKey_A_right",
    puzzleKey_Bt_mid = "物体/puzzleKey_Bt_mid",
    puzzleKey_Bt_left = "物体/puzzleKey_Bt_left",
    puzzleKey_Bt_right = "物体/puzzleKey_Bt_right",
    puzzleKey_Bf_mid = "物体/puzzleKey_Bf_mid",
    puzzleKey_Bf_left = "物体/puzzleKey_Bf_left",
    puzzleKey_Bf_right = "物体/puzzleKey_Bf_right",
}

---@class 相机_蜃境_裂雾飞鹰_02玄阙宫弟子房
local cameras = {
    sideQuest1 = "相机/sideQuest1",
    sideQuest2 = "相机/sideQuest2",
    cam1 = "相机/cam1",
    sideQuest3 = "相机/sideQuest3",
}

---@class 位置_蜃境_裂雾飞鹰_02玄阙宫弟子房
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
    sideQuest_Pos = "位置/sideQuest_Pos",
    sideQuest_Pos_2 = "位置/sideQuest_Pos_2",
    zhujue_start_pos2 = "位置/zhujue_start_pos2",
    sideQuest_Pos_3 = "位置/sideQuest_Pos_3",
}

---@class 资产_蜃境_裂雾飞鹰_02玄阙宫弟子房
local assets = {
}

---@class 动作_蜃境_裂雾飞鹰_02玄阙宫弟子房
local animationClips = {
}

---@class 蜃境_裂雾飞鹰_02玄阙宫弟子房
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
