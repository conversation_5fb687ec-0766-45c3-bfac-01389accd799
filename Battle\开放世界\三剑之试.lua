local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)


function init()
    bcapi.hide_ui("疗伤")
    bcapi.reset_limit_time(60)
    -- 开局创建角色
    CreateRoles()
    
end

function start()
    -- 1秒后开始第1次AVG
    bcapi.add_timer_trigger(0.1, Talk1)
    bcapi.add_timer_trigger(7.5, Sword1)
    bcapi.add_timer_trigger(20, Sword2)
    bcapi.add_timer_trigger(39, Sword3)

    bcapi.add_timer_trigger(58, Win)
    bcapi.add_condition_trigger("[%c->fighting_friend_count%][>]1", 0, Lose)
    bcapi.add_condition_trigger("[%enemy:世界_剑术大师->buff_lv:弱点击破状态%][>=]1", 3, SuperWin)
end

function Sword1()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("剑术大师", "这是第一剑！")
        local Boss = bcapi.get_role(1, "世界_剑术大师")
        Boss.Stat.BattleFlagDic:set_Item("第一剑", "1")

        bcapi.resume_and_show_ui()
    end)
end

function Sword2()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("剑术大师", "第二剑来了，你可接好！")
        local Boss = bcapi.get_role(1, "世界_剑术大师")
        Boss.Stat.BattleFlagDic:set_Item("第二剑", "1")

        bcapi.resume_and_show_ui()
    end)
end

function Sword3()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("剑术大师", "……这是最后一剑，你不要勉强！")
        local Boss = bcapi.get_role(1, "世界_剑术大师")
        Boss.Stat.BattleFlagDic:set_Item("第三剑", "1")

        bcapi.resume_and_show_ui()
    end)
end

function CreateRoles()
    bcapi.async_call(function()
        -- 创建玩家角色（不带绝招牌）
        bcapi.create_join_role("世界_剑术学徒", 40, team, bcapi.Vector2(-5, 0), 0.1)
        local card1 = ""
        ---@type number?
        local card1Lv = 1
        local card2 = ""
        ---@type number?
        local card2Lv = 1

        local dict = bcapi.get_team(0).ValueDict
        -- dict是c#的dict,遍历dict的方法 用 ipairs
        for k,v in pairs(dict) do
            print("当前值"..k..v)
            if(k == "策略卡1")then
                card1 = v
                print(card1)
            end
            if(k == "策略卡1等级")then
                card1Lv = tonumber(v)
                print(card1Lv)
            end
            if(k == "策略卡2")then
                card2 = v
                print(card2)
            end
            if(k == "策略卡2等级")then
                card2Lv = tonumber(v)
                print(card2Lv)
            end
        end
        if(card1 ~= "")then
            bcapi.add_strategy_card(card1, card1Lv, team)
        end

        if(card2 ~= "")then
            bcapi.add_strategy_card(card2, card2Lv, team)
        end
    end)
end

function Talk1()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("剑术大师", "接我三剑，这关就算你通过！")
        bcapi.talk("学徒", "是！我一定要通过师父的考验！")
        bcapi.resume_and_show_ui()
    end)
end

function Win()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("剑术大师", "也罢，时间已到，你过关了。")
        bcapi.win()
    end)
end

function SuperWin()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("剑术大师", "！竟然主动抢攻，以我教的招式妄图破解这最后一剑？！")
        bcapi.talk("剑术大师", "……如果强行施展这一剑，就需要动用三成功力，有悖我之前所言。")
        bcapi.talk("剑术大师", "你的勇气和智慧，让我刮目相看，这一关，你过了！")
        bcapi.get_team(0).BattleFlags:Add("HiddenWin", "1")
        bcapi.win()
    end)
end

function Lose()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("剑术大师", "笑话！为师考校你的功夫，你竟然还叫他人来协助？")
        bcapi.talk("剑术大师", "既然这样，那为师也就不留手了！")
        local Boss = bcapi.get_role(1, "世界_剑术大师")
        Boss.Stat.BattleFlagDic:set_Item("必杀", "1")
        bcapi.resume_and_show_ui()

        bcapi.wait(8)
        bcapi.lose()
    end)
end
