---@type 主线_青羽录6_楚正雄交谈
local context = require("MapStories/主线_青羽录6/scene_主线_青羽录6_楚正雄交谈_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger


s.loadFlags(context.flags)
---@type flags_主线_青羽录6
local flags = context.flags --[[@as flags_主线_青羽录6]]
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
--每次载入调用
function start()
    s.playMusic("huanxing")
    s.camera(cameras.init, true)
    s.setPos(npcs.zhujue, pos.JC)
    s.readyToStart()

    s.turnTo(npcs.shinv, npcs.mozhiyou)
    s.animState(npcs.shinv, "Special_Loop_1", true)
    s.turnTo(npcs.zhujue, npcs.shinv,true)
    local offset = CS.UnityEngine.Vector3(0, 1.75, 0)
    s.talk(npcs.shinv, "二位贵客，老爷就在前面水榭等候。")
    s.camera(cameras.init_zoomIn, true, true, blendHintEnum.EaseInOut, 2, true, true)
    s.animState(npcs.chuzhengxiong, roleAnims.BGesture)
    s.wait(1)
    s.animState(npcs.shenmiren, roleAnims.BAgree)
    s.wait(1)
    s.moveAsync(npcs.shenmiren, pos.SMR_leave, 1, true)
    s.camera(cameras.init, true, true)
    s.lookAt(npcs.mozhiyou,offset,npcs.shinv,true)
    s.lookAt(npcs.zhujue,offset,npcs.shinv,true)
    s.talk(npcs.mozhiyou, "我们这就过去。")
    s.camera(cameras.init, false, true, blendHintEnum.EaseInOut, 1, true, true)
    s.animState(npcs.shinv, "Special_Loop_1", false)
    s.setActive(npcs.shenmiren, false)

    if s.hasTask(310010, 1) == false then
        s.addTask(310010, 1)
    end
    --增加引导
    s.addGameMapGuide(objs.guide_chuzhengxiong)
end

function talkMozhiyou()
    s.talk(npcs.mozhiyou, "楚老爷就在水榭中，我们过去吧。")
end

function talkLaopu()
    local random = math.random(1, 3)
    if (random == 1) then
        s.talk(npcs.laopu, "夫人前些日子去探望六小姐了，她往日最疼六小姐，这一去不知道要多久才回来。")
    elseif (random == 2) then
        s.talk(npcs.laopu, "老爷夫人年轻时候也有矛盾，也会吵闹，但如今头发白了，脸上皱了，反倒是心平气和了，彼此多了几分理解，也多了几分恭敬。")
    elseif (random == 3) then
        s.talk(npcs.laopu, "楚家有六位小姐，两位公子，六位小姐无不是知书达礼，贤德淑良，大公子为人和善，小公子也调皮可爱，楚家真是好福气。")
    end
end

function eventChuzhengxiong()
    --清除引导
    s.removeGameMapGuide(objs.guide_chuzhengxiong)
    s.setActive(objs.trigger_behind,false)
    s.setActive(objs.trigger_left,false)
    s.setActive(objs.trigger_right,false)
    s.setActive(npcs.zhujue, false)
    s.setActive(npcs.mozhiyou, false)
    s.setActive(npcs.chuzhengxiong, false)
    s.setActive(npcs.shinvA, false)
    s.setActive(npcs.shinvB, false)

    s.playTimeline(assets.qyl1_6_1)

    -- if s.hasTask(310010, 1) then
    --     s.finishTask(310010, 1)
    -- end

    s.wait(0.1)
    s.setNewbieFlags("chapter_qyl_09_finished",1)
    s.finishInfiniteStory(true)
end