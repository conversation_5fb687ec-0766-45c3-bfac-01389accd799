local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle

function init()
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        team = bcapi.get_team(0)
        bcapi.create_join_role("诸葛鹿", 80, team, bcapi.Vector2(-5, 0), 0)
        bcapi.create_join_role("诸葛玲珑", 80, team, bcapi.Vector2(-2, 0), 0)
    end)
end

function start()
    bcapi.add_condition_trigger("[%enemy:剧情_白衣剑客->hp_pct%][<]0.5", 1, Talk50)
    StartTalk()
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        
        bcapi.talk("诸葛鹿", "白衣剑客，看拳！")
        bcapi.talk("白衣剑客","猖狂！")
        
        bcapi.resume_and_show_ui()
    end)
end

function Talk50()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("白衣剑客","冥顽不灵,此剑一出，邪魔速速伏诛！")
        bcapi.resume_and_show_ui()
        battle.ForceNotEndBattle = false
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
