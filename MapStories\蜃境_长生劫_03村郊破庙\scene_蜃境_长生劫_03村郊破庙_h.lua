--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_长生劫_03村郊破庙/flags_蜃境_长生劫_03村郊破庙")


---@class 角色_蜃境_长生劫_03村郊破庙
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "csj_11"
    csj_11 = "角色/csj_11",
    --- "csj_12"
    csj_12 = "角色/csj_12",
    --- "csj_13"
    csj_13 = "角色/csj_13",
    --- "csj_14"
    csj_14 = "角色/csj_14",
    --- "enemy_1"
    enemy_1 = "角色/enemy_1",
    --- "enemy_2"
    enemy_2 = "角色/enemy_2",
    --- "enemy_3"
    enemy_3 = "角色/enemy_3",
    --- "enemy_4"
    enemy_4 = "角色/enemy_4",
    --- "elite_6"
    elite_6 = "角色/elite_6",
    --- "老人"
    MSLB_NPC_laoren = "角色/MSLB_NPC_laoren",
    --- "小孩"
    MSLB_NPC_xiaohai = "角色/MSLB_NPC_xiaohai",
    --- "年轻人"
    MSLB_NPC_nianqingren = "角色/MSLB_NPC_nianqingren",
    --- "老妇人"
    MSLB_NPC_laofuren = "角色/MSLB_NPC_laofuren",
    MSLB_NPC = "角色/MSLB_NPC",
}

---@class 物体_蜃境_长生劫_03村郊破庙
local objs = {
    --- "出口"
    exit = "物体/exit",
    --- "chest_1"
    chest_1 = "物体/chest_1",
    enemy_1_collider = "物体/enemy_1_collider",
    enemy_2_collider = "物体/enemy_2_collider",
    enemy_3_collider = "物体/enemy_3_collider",
    enemy_4_collider = "物体/enemy_4_collider",
    evilAirChest_1 = "物体/evilAirChest_1",
    --- "山神"
    MSLB_object_shanshen = "物体/MSLB_object_shanshen",
    --- "山神"
    MSLB_object_shanshen2 = "物体/MSLB_object_shanshen2",
}

---@class 相机_蜃境_长生劫_03村郊破庙
local cameras = {
}

---@class 位置_蜃境_长生劫_03村郊破庙
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    enemy_1 = "位置/enemy_1",
    enemy_2 = "位置/enemy_2",
    enemy_3 = "位置/enemy_3",
    enemy_4 = "位置/enemy_4",
}

---@class 资产_蜃境_长生劫_03村郊破庙
local assets = {
}

---@class 动作_蜃境_长生劫_03村郊破庙
local animationClips = {
}

---@class 蜃境_长生劫_03村郊破庙
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
