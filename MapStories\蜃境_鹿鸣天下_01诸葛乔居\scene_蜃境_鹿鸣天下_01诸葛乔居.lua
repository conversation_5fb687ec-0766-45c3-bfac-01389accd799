---@type 蜃境_鹿鸣天下_01诸葛乔居
local context = require("MapStories/蜃境_鹿鸣天下_01诸葛乔居/scene_蜃境_鹿鸣天下_01诸葛乔居_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_鹿鸣天下_01诸葛乔居
local flags = context.flags
--不可省略，用于外部获取flags

local newbieApi = require("NewbieApi")
---@type NewbieFlags
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")

function getFlags(...)
    return flags
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

local memoryKey = "lmtx_01"

function test()
    s.setTaskStep("幽幽水流","fightWithDizi")
end

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

function start()
    print("start called")
    s.readyToStart(true)
    refreshMapStates()
    try_execute_newbieGuide()
end

function try_execute_newbieGuide(...)
    s.nextStep("newbieGuideInLevel")
end

function newbieGuideInLevel()end

--默认初始化的地图显隐状态
function reset_all()

s.setActive(objs.lmtx_1001,not s.hasUnlockMemorySlot("lmtx_1001"))
s.setActive(objs.lmtx_1002,not s.hasUnlockMemorySlot("lmtx_1002"))

    if s.getCurrentTaskStep("幽幽水流") == "deactive" then
        s.setActive(npcs.branchTask_Lu01,true)
        s.setActive(npcs.branchTask_Lu01_1,false)
        s.setActive(objs.branchTask_box,true)
        s.setActive(objs.branchTask_water,false)
        s.setActive(npcs.branchTask_Lu02,false)
        s.setActive(npcs.branchTask_Lu02_1,false)
        s.setActive(npcs.branchTask_Lu03,false)
        s.setActive(npcs.branchTask_Qiao,false)
        s.setActive(npcs.branchTask_Lu04,false)
        s.setActive(npcs.branchTask_Dizi,false)
        s.setActive(objs.branchTaskTrigger_2,false)
    elseif s.getCurrentTaskStep("幽幽水流") == "talkWithLu" then
        s.setActive(npcs.branchTask_Lu01,true)
        s.setActive(npcs.branchTask_Lu01_1,false)
        s.setActive(objs.branchTask_box,false)
        s.setActive(objs.branchTask_water,false)
        s.setActive(npcs.branchTask_Lu02,false)
        s.setActive(npcs.branchTask_Lu02_1,false)
        s.setActive(npcs.branchTask_Lu03,false)
        s.setActive(npcs.branchTask_Qiao,false)
        s.setActive(npcs.branchTask_Lu04,false)
        s.setActive(npcs.branchTask_Dizi,false)
        s.setActive(objs.branchTaskTrigger_2,false)
    elseif s.getCurrentTaskStep("幽幽水流") == "findWater" then
        s.setActive(npcs.branchTask_Lu01,false)
        s.setActive(npcs.branchTask_Lu01_1,false)
        s.setActive(objs.branchTask_box,false)
        s.setActive(objs.branchTask_water,true)
        s.setActive(npcs.branchTask_Lu02,true)
        s.setActive(npcs.branchTask_Lu02_1,false)
        s.setActive(npcs.branchTask_Lu03,false)
        s.setActive(npcs.branchTask_Qiao,false)
        s.setActive(npcs.branchTask_Lu04,false)
        s.setActive(npcs.branchTask_Dizi,false)
        s.setActive(objs.branchTaskTrigger_2,false)
    elseif s.getCurrentTaskStep("幽幽水流") == "giveWater" then
        s.setActive(npcs.branchTask_Lu01,false)
        s.setActive(npcs.branchTask_Lu01_1,false)
        s.setActive(objs.branchTask_box,false)
        s.setActive(objs.branchTask_water,false)
        s.setActive(npcs.branchTask_Lu02,false)
        s.setActive(npcs.branchTask_Lu02_1,true)
        s.setActive(npcs.branchTask_Lu03,false)
        s.setActive(npcs.branchTask_Qiao,false)
        s.setActive(npcs.branchTask_Lu04,false)
        s.setActive(npcs.branchTask_Dizi,false)
        s.setActive(objs.branchTaskTrigger_2,true)
    elseif s.getCurrentTaskStep("幽幽水流") == "fightWithDizi" then
        s.setActive(npcs.branchTask_Lu01,false)
        s.setActive(npcs.branchTask_Lu01_1,false)
        s.setActive(objs.branchTask_box,false)
        s.setActive(objs.branchTask_water,false)
        s.setActive(npcs.branchTask_Lu02,false)
        s.setActive(npcs.branchTask_Lu02_1,false)
        s.setActive(npcs.branchTask_Lu03,false)
        s.setActive(npcs.branchTask_Qiao,false)
        s.setActive(npcs.branchTask_Lu04,false)
        s.setActive(npcs.branchTask_Dizi,false)
        s.setActive(objs.branchTaskTrigger_2,false)
        s.setActive(npcs.branchTask_Lu05,true)
        s.setActive(npcs.enemy_Branchtask,true)
    elseif s.getCurrentTaskStep("幽幽水流") == "finished" then
        s.setActive(npcs.branchTask_Lu01,false)
        s.setActive(npcs.branchTask_Lu01_1,false)
        s.setActive(objs.branchTask_box,false)
        s.setActive(objs.branchTask_water,false)
        s.setActive(npcs.branchTask_Lu02,false)
        s.setActive(npcs.branchTask_Lu02_1,false)
        s.setActive(npcs.branchTask_Lu03,false)
        s.setActive(npcs.branchTask_Qiao,false)
        s.setActive(npcs.branchTask_Lu04,false)
        s.setActive(npcs.branchTask_Dizi,true)
        s.setActive(objs.branchTaskTrigger_2,false)
    elseif flags.branchTask_Lu02_1_finished == 1 and s.getCurrentTaskStep("幽幽水流") == "giveWater" then
        s.setActive(npcs.branchTask_Lu02_1,false)
        s.setActive(npcs.branchTask_Lu01_1,false)
    end

    --隐藏所有的剧情节点
    s.setActive(npcs.lmtx_01, false)
    s.setActive(npcs.lmtx_02, false)
    s.setActive(npcs.lmtx_03, false)
    s.setActive(npcs.lmtx_04, false)
    s.setActive(npcs.lmtx_05, false)

    s.removeAllGameMapGuides()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 6) then
        taskStep = "forwardToExplore"
    elseif (step == 6) then
        taskStep = "leaveZhuGeQiaoJu"
    end
    s.setTaskStep("鹿鸣天下诸葛乔居", taskStep)

    reset_all()

    
    if step == 0 then
        --第一次进入诸葛鹿居, 这里加入入场演出
        if s.isMale() then
            s.playTimeline(assets.timeline_01,false)
        else
            s.playTimeline(assets.timeline_01_NvZhu,false)
        end
        s.setPos(npcs.zhujue,pos.zhujue_start_pos_1)
        s.setActive(npcs.lmtx_01,true)
        s.lightScreen()
        s.setTaskStep("鹿鸣天下诸葛乔居","forwardToExplore")
        next_level_step(true)

    elseif step == 1 then

        level_guide_to(npcs.lmtx_01, 0.8)
    elseif step == 2 then
        level_guide_to(npcs.lmtx_02, 0.8)
    elseif step == 3 then
        level_guide_to(npcs.lmtx_03, 0.8)
    elseif step == 4 then
        level_guide_to(npcs.lmtx_04, 0.8)
    elseif step == 5 then
        level_guide_to(npcs.lmtx_05, 0.8)
    else
        --do nothing
        level_guide_to(objs.exit)
    end
end


----------------------OVERRIDE----------------------------------

function lmtx_01()
    executeMemSlot("lmtx_01", quick_play_avg)
end


function lmtx_02()
    executeMemSlot("lmtx_02", quick_play_avg)
end

function lmtx_03()
    executeMemSlot("lmtx_03", quick_play_avg)
end

function lmtx_04()
    executeMemSlot("lmtx_04", quick_play_avg)
    s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_诸葛乔居", "蜃境_鹿鸣天下_01诸葛乔居_碎片4交互后")
end

function lmtx_05()
    executeMemSlot("lmtx_05", quick_play_avg,function()
    -- s.setActive(objs.branchTask_box,true)
    s.setTaskStep("鹿鸣天下诸葛乔居","leaveZhuGeQiaoJu")
    next_level_step(true)
    end)
end


function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("鹿鸣天下")
end

function enterTimeline_02()
    if s.isMale() then
        s.setActive(objs.Timeline02_box,false)
        s.setActive(npcs.lmtx_02,false)
        s.playTimeline(assets.timeline_02,false)
        s.setActive(npcs.lmtx_02,true)
        s.lightScreen()
    else
        s.setActive(objs.Timeline02_box,false)
        s.setActive(npcs.lmtx_02,false)
        s.playTimeline(assets.timeline_02_NvZhu,false)
        s.setActive(npcs.lmtx_02,true)
        s.lightScreen()
    end
end

--[[function enterTimeline_03()
    s.setActive(objs.Timeline03_box,false)
    s.playTimeline(assets.timeline_03,false)
    s.lightScreen()
end--]]

function activeBranchTask()
    s.setActive(objs.branchTask_box,false)
    --s.playTimeline(assets.timeline_04,false)
    --s.setActive(npcs.branchTask_Lu01,true)
    --s.setActive(objs.branchTask_water,true)
    --s.lightScreen()
    s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_诸葛乔居", "蜃境_鹿鸣天下_01诸葛乔居_幽幽水流任务激活")
    s.setTaskStep("幽幽水流","talkWithLu")
end

function branchTask_zhugelu01()
    if s.getCurrentTaskStep("幽幽水流") == "deactive" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_诸葛乔居", "蜃境_鹿鸣天下_01诸葛乔居_未激活时和鹿对话")
    elseif s.getCurrentTaskStep("幽幽水流") == "talkWithLu" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_诸葛乔居", "蜃境_鹿鸣天下_01诸葛乔居_幽幽水流Step2交谈")
        s.setActive(npcs.branchTask_Lu01,false)
        s.setActive(npcs.branchTask_Lu01_1,true)
        --s.moveAsync(npcs.branchTask_Lu01_1,pos.branchTask_Lu02_pos_1,1,true)
        s.agentMoveToAsync(npcs.branchTask_Lu01_1,pos.branchTask_Lu02_pos_2,3,3)
        --s.setActive(npcs.branchTask_Lu01_1,false)
        --s.setActive(npcs.branchTask_Lu02,true)
        --s.blackScreen()
        --s.setActive(npcs.branchTask_Lu01,false)
        s.setActive(objs.branchTask_water,true)
        --s.setActive(npcs.branchTask_Lu02,true)
        --s.lightScreen()
        s.setTaskStep("幽幽水流","findWater")
    end
    --[[elseif s.getCurrentTaskStep("幽幽水流") == "giveWater" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_诸葛乔居", "蜃境_鹿鸣天下_01诸葛乔居_给水")
        local lmtx01_battleWin = s.battle("蜃境_鹿鸣天下1_支线与精英伥怪战斗")
        if lmtx01_battleWin then
            s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_诸葛乔居", "蜃境_鹿鸣天下_01诸葛乔居_战斗后")
            s.setTaskStep("幽幽水流","finished")
        end
    end--]]
end

--[[function branchTask_zhugelu02()
    if s.getCurrentTaskStep("幽幽水流") == "findWater" then
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_诸葛乔居", "蜃境_鹿鸣天下_01诸葛乔居_取水前")
    end
end--]]

function findWater()
    s.setActive(objs.branchTask_water,false)
    s.soloAnimState(npcs.zhujue,roleAnims.BSquat_Loop,true)
    s.playSound("sfx","水滴声",1,1,1,false)
    s.showLoading("取水中", 3)
    s.soloAnimState(npcs.zhujue,roleAnims.BSquat_Loop,false)
    s.blackScreen()
    s.setActive(npcs.branchTask_Lu01_1,false)
    s.setActive(npcs.branchTask_Lu02,false)
    s.setActive(npcs.branchTask_Lu02_1,true)
    s.setActive(objs.branchTaskTrigger_2,true)
    s.lightScreen()
    s.popInfo("获得了一壶泉水")
    s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_诸葛乔居", "蜃境_鹿鸣天下_01诸葛乔居_取水后")
    s.setTaskStep("幽幽水流","giveWater") 
end

function branchTask_zhugelu03()
    if s.getCurrentTaskStep("幽幽水流") == "giveWater" then
        flags.branchTask_Lu02_1_finished = 1
        s.setActive(npcs.branchTask_Lu02_1,false)
        s.setActive(objs.branchTaskTrigger_2,false)
        branchTask_timelineShow()

        local lmtx01_battleWin = s.battle("蜃境_鹿鸣天下1_支线与精英伥怪战斗")
        if lmtx01_battleWin then
            s.setPos(npcs.zhujue,pos.zhujue_bt_pos_1)
            s.setActive(npcs.branchTask_Lu03,false)
            s.setActive(npcs.branchTask_Lu05,false)
            s.setActive(npcs.enemy_Branchtask,false)
            s.setActive(npcs.branchTask_Qiao,true)
            s.setActive(npcs.branchTask_Lu04,true)
            s.setActive(npcs.branchTask_Dizi,true)
            s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_诸葛乔居", "蜃境_鹿鸣天下_01诸葛乔居_战斗后")
            s.blackScreen()
            s.setActive(npcs.branchTask_Qiao,false)
            s.setActive(npcs.branchTask_Lu04,false)
            s.lightScreen()
            s.setTaskStep("幽幽水流","finished")

            --支线奖励
            local capi = require("ChapterApi")
            capi.finishMemorySlot("lmtx_branchTask_yyls")
        else
            s.setActive(npcs.branchTask_Lu05,true)
            s.setActive(npcs.enemy_Branchtask,true)
            s.setTaskStep("幽幽水流","fightWithDizi")
        end
    end
end
function branchTask_timelineShow()
    local step = get_level_step()
    if s.isMale() then
        --这里为了处理当step=4时候的诸葛鹿虚影
        if step == 4 then
            s.setActive(npcs.lmtx_04,false)
            s.playTimeline(assets.timeline_05,false)
            s.setActive(npcs.lmtx_04,true)
            s.lightScreen()
        else
            s.playTimeline(assets.timeline_05,false)
            s.lightScreen()
        end
    else
        if step == 4 then
            -- s.blackScreen()
            s.setActive(npcs.lmtx_04,false)
            s.playTimeline(assets.timeline_05_Nvzhu,false)
            s.setActive(npcs.lmtx_04,true)
            s.lightScreen()
        else
            -- s.blackScreen()
            s.playTimeline(assets.timeline_05_Nvzhu,false)
            s.lightScreen()
        end
    end
end

function enemy_Branchtask()
    local lmtx01_battleWin = s.battle("蜃境_鹿鸣天下1_支线与精英伥怪战斗")
    if lmtx01_battleWin then
        s.setPos(npcs.zhujue,pos.zhujue_bt_pos_1)
        s.setActive(npcs.branchTask_Lu03,false)
        s.setActive(npcs.branchTask_Lu05,false)
        s.setActive(npcs.enemy_Branchtask,false)
        s.setActive(npcs.branchTask_Qiao,true)
        s.setActive(npcs.branchTask_Lu04,true)
        s.setActive(npcs.branchTask_Dizi,true)
        s.runAvgTag("蜃境/蜃境_鹿鸣天下/蜃境_鹿鸣天下_诸葛乔居", "蜃境_鹿鸣天下_01诸葛乔居_战斗后")
        s.blackScreen()
        s.setActive(npcs.branchTask_Qiao,false)
        s.setActive(npcs.branchTask_Lu04,false)
        s.lightScreen()
        s.setTaskStep("幽幽水流","finished")
        local capi = require("ChapterApi")
        capi.finishMemorySlot("lmtx_branchTask_yyls")
    else
        s.setPos(npcs.zhujue,pos.zhujue_bt_pos_1)
        s.setActive(npcs.branchTask_Lu05,true)
        s.setActive(npcs.enemy_Branchtask,true)
    end
end

function lmtx_1001()
    s.setActive(objs.lmtx_1001,false)
end

function lmtx_1002()
    s.setActive(objs.lmtx_1002,false)
end

