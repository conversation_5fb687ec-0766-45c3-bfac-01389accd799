
---@type 蜃境_长生劫_04裘地仙居
local context = require("MapStories/蜃境_长生劫_04裘地仙居/scene_蜃境_长生劫_04裘地仙居_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_长生劫_04裘地仙居
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")



----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function testteleport()
    s.changeMap("Assets/GameScenes/游历场景/蜃境_长生劫_04地仙居秘宝/蜃境_长生劫_04地仙居秘宝.unity","位置/pos_start")
end

--精英怪战斗
function eliteBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

function test()
    s.setPos(npcs.zhujue,pos.pos_start)
end

function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function start()
    print("start called")
    refreshMapStates()
    s.readyToStart(true)

    -- 防止跑图中强退导致流程跳过
    map_story_guide()
end

function map_story_guide()
    -- 如果未解锁，且已通关，则finish
    if (not s.hasUnlockMemorySlot("csj_17") and newbieFlags.chapter_csj_17_finished == 1) then
        capi.finishMemorySlot("csj_17")
        s.setTaskStep("长生劫裘地仙居","leave")
        next_level_step(true)
    end
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数
    reset_all()

    --第一次进入场景
    if step == 0 then
        next_level_step(true)
        s.setTaskStep("长生劫裘地仙居","forwardToExplore")
    elseif step == 1 then
        level_guide_to(npcs.csj_15, 1)
    elseif step == 2 then
        level_guide_to(npcs.csj_16, 1)
    elseif step == 3 then
        level_guide_to(npcs.csj_17, 1)
    else
        level_guide_to(objs.exit)
    end
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.csj_15, false)
    s.setActive(npcs.csj_16, false)
    s.setActive(npcs.csj_17, false)

    --忍者谜题男子显隐
    if flags.defeat_ninja == 1 and flags.talk_keyMan_outside == 1 then
        s.setActive(npcs.keyMan,false)
        s.setActive(npcs.keyMan_insideHouse,true)

        --偷听触发器显隐
        if flags.treasure_door_open == 1 then
            s.setActive(objs.eavesdrop_man,false)
        else
            s.setActive(objs.eavesdrop_man,true)
        end
    else
        s.setActive(npcs.keyMan,true)
    end

    --钥匙藏匿点显隐
    if flags.key_trigger == 1 then
        s.setActive(objs.findKey,false)
    else
        if flags.talk_keyMan_outside == 1 then
            s.setActive(objs.findKey,true)
        else
            s.setActive(objs.findKey,false)
        end
    end

    --宝库门状态
    if flags.treasure_door_open == 1 then
        s.setActive(objs.treasureDoor,false)
        s.setActive(objs.enter_treasureDoor,true)
    else
        s.setActive(objs.treasureDoor,true)
        s.setActive(objs.enter_treasureDoor,false)
    end

    --药渣支线
    if s.getCurrentTaskStep("长生劫支线药渣") ~= "deactive" then
        s.setActive(npcs.YZ_NPC_shangren,false)
        s.setActive(objs.YZ_object_huoxiang,false)
    end
    s.setActive(npcs.YZ_NPC_keyirenwu,s.getCurrentTaskStep("长生劫支线药渣") == "waitNight")
    s.setActive(npcs.YZ_NPC,s.getCurrentTaskStep("长生劫支线药渣") ~= "finished")


    --数箱子解谜
    if flags.SXZ_Puzzle_step == 2 then
        s.setActive(npcs.SXZ_Puzzle_xiaonvhai,false)
    end

    --收集物
    s.setActive(objs.csj_1006,not s.hasUnlockMemorySlot("csj_1006"))
    s.setActive(objs.csj_1007,not s.hasUnlockMemorySlot("csj_1007"))
    --删除所有的引导点
    s.removeAllGameMapGuides()
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("长生劫")
end
-------------------支线和谜题相关---------------------
function up_roof()
    local upRoof = s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "上屋檐")
    if upRoof == 1  then
        s.blackScreen()
        s.setPos(npcs.zhujue,pos.upper_roof,true)
        s.lightScreen()
    end
end

function down_roof()
    s.blackScreen()
    s.setPos(npcs.zhujue,pos.down_roof,true)
    s.lightScreen()
end

function keyMan()
    if flags.defeat_ninja == 1 then
        if flags.talk_keyMan_outside == 0 then
            s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "蹲下的男人_击败忍者")
            flags.talk_keyMan_outside = 1
        elseif flags.talk_keyMan_outside == 1 then
            s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "蹲下的男人_击败忍者_后")
        end
    else
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "蹲下的男人")
        s.setActive(npcs.elite_6,false)
        s.playTimeline(assets.timeline_ninjia,false)
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "被忍者砍")
    
        s.setPos(npcs.zhujue,pos.pos_start)
        s.setActive(npcs.elite_6,true)
        s.lightScreen()
    end
end

function keyMan_inside()
    if flags.talk_keyMan_inside == 0 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "对话屋子里的男人")
        flags.talk_keyMan_inside = 1
    elseif flags.talk_keyMan_inside == 1 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "对话屋子里的男人_闲话")
    elseif flags.talk_keyMan_inside == 2 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "战斗胜利后青年男子对话")
    end
end

function findKey()
    s.setActive(objs.findKey,false)
    flags.key_trigger = 1
    s.showLoading("翻翻找找……",2)
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "发现钥匙")
    s.changeItemCount("蜃境_长生劫_裘地仙宝藏钥匙",1)
end

function treasureDoor()
    if flags.talk_keyMan_inside == 0 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "宝藏房_未了解情况")
    elseif flags.talk_keyMan_inside == 1 then
        local ret = s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "打开宝藏房")
        if ret == 1 then
            battle_man()
        elseif ret == 2 then
            open_treasureDoor()
        end
    end
end
function open_treasureDoor()
    local submit = s.submitItem("打开房门")
    if submit ~= nil and submit[0].Key == "蜃境_长生劫_裘地仙宝藏钥匙"  then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "使用钥匙")
        flags.treasure_door_open = 1
        enter_treasureDoor()
    else
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "使用钥匙失败")
    end
end
function battle_man()
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "强行踹开")
    local isWin = s.battle("蜃境_长生劫_谜题_与青年男子战斗")
    if isWin then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "与青年男子战斗_胜利")
        s.setActive(objs.treasureDoor,false)
        s.setActive(objs.enter_treasureDoor,true)
        flags.treasure_door_open = 1

        --战斗胜利后青年男子对话变量
        flags.talk_keyMan_inside = 2
        refreshMapStates()
    else
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "与青年男子战斗_失败")
    end
end

function enter_treasureDoor()
    s.changeMap("Assets/GameScenes/游历场景/蜃境_长生劫_04地仙居秘宝/蜃境_长生劫_04地仙居秘宝.unity","位置/pos_start")
end

function eavesdrop()
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "青年男子_偷听")
end

----------------------------------支线剧情 药渣-----------------------------------
--第一段
function YZ_01()
    if s.getCurrentTaskStep("长生劫支线药渣") == "deactive" then
        s.turnToAsync(npcs.YZ_NPC_shangren,npcs.zhujue,true)
        s.turnToAsync(npcs.zhujue,npcs.YZ_NPC_shangren,true)
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_支线_药渣")
        s.setTaskStep("长生劫支线药渣","findshangren")
        s.blackScreen(0.8)
        s.setActive(npcs.YZ_NPC_shangren,false)
        s.setActive(objs.YZ_object_huoxiang,false)
        s.lightScreen(0.8)
    end
end

--第二段
function test2() 
end
function YZ_02()
    if s.getCurrentTaskStep("长生劫支线药渣") == "findshangren" then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_支线_药渣2")
        s.blackScreen(0.8)
        s.lightScreen(0.8)
        s.aside("夜幕降临……")
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_支线_药渣2补充剧情")
        s.setTaskStep("长生劫支线药渣","waitNight")
        s.setActive(npcs.YZ_NPC_keyirenwu,true)
    elseif s.getCurrentTaskStep("长生劫支线药渣") == "findshangren2" then
        YZ_04()
        s.setTaskStep("长生劫支线药渣","finished")
    else
        s.talk(npcs.YZ_NPC_cunminjia,"下次吃饱喝足，要到什么时候呢？")
        s.talk(npcs.YZ_NPC_cunminyi,"还想吃？没啦！")
    end
end

--第三段
function YZ_03()
    if s.getCurrentTaskStep("长生劫支线药渣") == "waitNight" then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_支线_药渣3")
        local isWin = s.battle("蜃境_长生劫_支线_药渣3")
        if isWin then
            s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_支线_药渣3战斗对话")
            s.setTaskStep("长生劫支线药渣","findshangren2")
            s.blackScreen(0.8)
            s.setActive(npcs.YZ_NPC_keyirenwu,false)
            s.lightScreen(0.8)
        end
    end
end

--第四段
function YZ_04()
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_支线_药渣4")
end

-----------------------解谜相关---------------------
---解谜数箱子
function SXZ_01_Puzzle()
    if flags.SXZ_Puzzle_step == 0 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_解谜_数箱子")
        flags.SXZ_Puzzle_step = 1
        --刷新箱子
        refresh_SXZ_Puzzle_box()
    elseif flags.SXZ_Puzzle_step == 1 then
        SXZ_01_Puzzle_02()
    end
end

function SXZ_01_Puzzle_02()
    local ret = s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_解谜_数箱子选择")
    if ret == 0 then
    elseif ret == flags.SXZ_Puzzle_answer and flags.SXZ_Puzzle_answer == 4 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_解谜_数箱子答案正确四个")
        flags.SXZ_Puzzle_step = 2
        s.blackScreen(0.8)
        s.setActive(npcs.SXZ_Puzzle_xiaonvhai,false)
        s.lightScreen(0.8)
    elseif ret == flags.SXZ_Puzzle_answer and flags.SXZ_Puzzle_answer == 5 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_解谜_数箱子答案正确五个")
        flags.SXZ_Puzzle_step = 2
        s.blackScreen(0.8)
        s.setActive(npcs.SXZ_Puzzle_xiaonvhai,false)
        s.lightScreen(0.8)
    elseif ret == flags.SXZ_Puzzle_answer and flags.SXZ_Puzzle_answer == 6 then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_解谜_数箱子答案正确六个")
        flags.SXZ_Puzzle_step = 2
        s.blackScreen(0.8)
        s.setActive(npcs.SXZ_Puzzle_xiaonvhai,false)
        s.lightScreen(0.8)
    elseif ret ~= flags.SXZ_Puzzle_answer then
        s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居","蜃境_裘地仙居_解谜_数箱子答案错误")
    end
end

--数箱子解谜刷新箱子逻辑函数
function refresh_SXZ_Puzzle_box()
    -- 随机决定显示几个箱子(3-5个)
    local boxCount = math.random(3, 5)
    flags.SXZ_Puzzle_answer = boxCount + 1  -- 答案为箱子数量+1，因为场景中有一个宝箱常驻显示
    
    -- 6号箱子必定显示
    s.setActive(objs.SXZ_Puzzle_box_6, true)
    
    -- 从1-5号箱子中随机选择boxCount个显示
    s.blackScreen(0.8)
    local boxes = {1,2,3,4,5}
    for i = 1, boxCount do
        local index = math.random(1, #boxes)
        local boxNum = boxes[index]
        s.setActive(objs["SXZ_Puzzle_box_"..boxNum], true)
        table.remove(boxes, index)
    end
    
    -- 隐藏未选中的箱子
    for _, boxNum in ipairs(boxes) do
        s.setActive(objs["SXZ_Puzzle_box_"..boxNum], false)
    end
    --刷新地图
    refreshMapStates()
    s.lightScreen(0.8)
    s.popInfo("房屋内的箱子数量变化了，请注意哦")
end

-----------------------跑图传送相关---------------------

-------------------关卡记忆碎片和战斗实现---------------------
function elite_battle(id)
    s.runAvgTag("蜃境/蜃境_长生劫/蜃境_长生劫_裘地仙居", "忍者发现")
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,
    function()
        flags.defeat_ninja = 1
        s.setActive(npcs.elite_6,false)
    end)
end

function csj_15()
    executeMemSlot("csj_15", quick_play_avg)
end

function csj_16()
    executeMemSlot("csj_16", function()
        s.playTimelineScene("Assets/GameScenes/动画演出场景/002椿岁之章/主线_长生劫5_所谓仙人.unity")
    end)
end

function csj_17()
    executeMemSlot("csj_17", quick_map_story)
end