--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_青羽录_06芦苇地/flags_蜃境_青羽录_06芦苇地")


---@class 角色_蜃境_青羽录_06芦苇地
local npcs = {
    zhujue = "角色/zhujue",
    jingcheng = "角色/jingcheng",
    yunwusheng = "角色/yunwusheng",
    qyl_24 = "角色/qyl_24",
    boss = "角色/boss",
    shusheng = "角色/shusheng",
    laozhe = "角色/laozhe",
    wumingshi = "角色/wumingshi",
    --- "寻觅高手NPC组"
    XMGS_NPC = "角色/XMGS_NPC",
    enemy_01 = "角色/enemy_01",
    enemy_02 = "角色/enemy_02",
    elite_03 = "角色/elite_03",
    boss_04 = "角色/boss_04",
}

---@class 物体_蜃境_青羽录_06芦苇地
local objs = {
    exit = "物体/exit",
    door = "物体/door",
    qyl6_1010 = "物体/qyl6_1010",
    qyl6_1011 = "物体/qyl6_1011",
    hitevent = "物体/hitevent",
    XMGS_WMS = "物体/XMGS_WMS",
    XMGS_SS = "物体/XMGS_SS",
    XMGS_LZ = "物体/XMGS_LZ",
}

---@class 相机_蜃境_青羽录_06芦苇地
local cameras = {
    camera1 = "相机/camera1",
    camera2 = "相机/camera2",
    camera3 = "相机/camera3",
}

---@class 位置_蜃境_青羽录_06芦苇地
local pos = {
    start = "位置/start",
    jingcheng_start = "位置/jingcheng_start",
    yunwusheng_start = "位置/yunwusheng_start",
    jingcheng_play_pos = "位置/jingcheng_play_pos",
    zhujue_play_pos = "位置/zhujue_play_pos",
    yunwusheng_play_pos = "位置/yunwusheng_play_pos",
}

---@class 资产_蜃境_青羽录_06芦苇地
local assets = {
}

---@class 动作_蜃境_青羽录_06芦苇地
local animationClips = {
}

---@class 蜃境_青羽录_06芦苇地
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
