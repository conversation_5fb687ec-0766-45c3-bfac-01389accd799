
local context = require("MapStories/漫游_楚家庄/scene_漫游_楚家庄_前院_h")
local s = context.sapi
local flags = context.flags
s.loadFlags(flags)

---@class 楚家庄_StringItemData
local stringItemData = {
    test1 = {key = "test1",showName="参数1",desc = "测试参数名1"},
    test2 = {key = "test2",showName="参数2",desc = "测试参数名2"},
}

---@class 楚家庄_ItemData
local itemData = {
    cangbaotu1 = {key = "cangbaotu1",showName = "藏宝图碎片（其一）",desc = "看起来像是一张藏宝图的碎片",icon = "map", canUse = 1},
    cangbaotu2 = {key = "cangbaotu2",showName = "藏宝图碎片（其二）",desc = "看起来像是一张藏宝图的碎片",icon = "map", canUse = 1},
    cangbaotu3 = {key = "cangbaotu3",showName = "藏宝图碎片（其三）",desc = "看起来像是一张藏宝图的碎片",icon = "map", canUse = 1},
    cangbaotu4 = {key = "cangbaotu4",showName = "藏宝图碎片（其四）",desc = "看起来像是一张藏宝图的碎片",icon = "map", canUse = 1},
}


local stringWidget = StringWidget:new(stringItemData,flags)
local itemWidget = ItemWidget:new(itemData,flags)

local itemWidgetInfo = {
    title = "道具",
    items = itemData,
    widget = itemWidget
}

local stringWidgetInfo = {
    items = stringItemData,
    widget = stringWidget
}

extensions = {
    useWidgets = {"ItemWidget"},
    widgets = {
        --StringWidgetInfo = stringWidgetInfo,
        ItemWidgetInfo = itemWidgetInfo
    }
}

context.sapi.syncExtendsInfo(extensions)
return extensions