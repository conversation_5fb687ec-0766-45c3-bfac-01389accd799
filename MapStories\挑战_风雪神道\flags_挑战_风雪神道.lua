
--变量设置
---@class flags_挑战_风雪神道
local flags = {
    --- "变量名"
    foo = "foo",
    testFlag = 0,

    --补给和资源
    trash = 0,
    resource = 0,
    find_shoe = 0,

    --trash从1到30
    trash1 = 0,
    trash2 = 0,
    trash3 = 0,
    trash4 = 0,
    trash5 = 0,
    trash6 = 0,
    trash7 = 0,
    trash8 = 0,
    trash9 = 0,
    trash10 = 0,
    trash11 = 0,
    trash12 = 0,
    trash13 = 0,
    trash14 = 0,
    trash15 = 0,
    trash16 = 0,
    trash17 = 0,
    trash18 = 0,
    trash19 = 0,
    trash20 = 0,
    trash21 = 0,
    trash22 = 0,
    trash23 = 0,
    trash24 = 0,
    trash25 = 0,
    trash26 = 0,
    trash27 = 0,
    trash28 = 0,
    trash29 = 0,
    trash30 = 0,

    --boss记录，从1到3
    boss1Defeated = 0,
    boss1BeDefeated = 0,
    boss2Defeated = 0,
    boss2BeDefeated = 0,
    boss3Defeated = 0,
    boss3BeDefeated = 0,

    --首次进入对应场景记录
    firstEnterMap1 = 0,
    firstEnterMap2 = 0,
    firstEnterMap3 = 0,

    --enemy从1到9
    enemy_1 = 0,
    enemy_2 = 0,
    enemy_3 = 0,
    enemy_4 = 0,
    enemy_5 = 0,
    enemy_6 = 0,
    enemy_7 = 0,
    enemy_8 = 0,
    enemy_9 = 0,

    --itemdata中的物品flag
    StructureFire = 0,
    StructureFood = 0,
    StructureAlcohol = 0,
    StructureClothes = 0,
    StructureKnifeStone = 0,
    StructureMap = 0,
    StructurePet = 0,
    StructureEnhance = 0,
}

return flags
