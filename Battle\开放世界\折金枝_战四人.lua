local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

local enemyLevel = 60
-- 脚本内容：
-- 

function init()
    createRoles()
end

function start()
    bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0.1, secondCallForEnemy)
end

function createRoles(...)
    bcapi.async_call(function()
        bcapi.create_join_role("世界_折金枝_萧明漪", enemyLevel, team, bcapi.Vector2(-6, 0), 0)
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
    end)
end

function secondCallForEnemy(...)
    bcapi.async_call(function()
        bcapi.create_join_role("世界_折金枝_奉书", enemyLevel, eTeam, bcapi.Vector2(6, 3), 0)
        bcapi.create_join_role("世界_折金枝_武痴", enemyLevel, eTeam, bcapi.Vector2(6, -3), 0)
        bcapi.wait(0.5)
        bcapi.pause_and_hide_ui()
        bcapi.talk("奉书", "等你们好久了，来吧，来吧，我已经许久没与人交手了。")
        bcapi.talk("武痴", "……点到为止，还请二位当心……")
        bcapi.resume_and_show_ui()
        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0, win)
    end)
end


function win()
    bcapi.async_call(function()
        bcapi.win()
    end)
end
