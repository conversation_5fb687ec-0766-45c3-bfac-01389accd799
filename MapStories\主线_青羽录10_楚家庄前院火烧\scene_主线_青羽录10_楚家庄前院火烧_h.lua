--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/主线_青羽录10_楚家庄前院火烧/flags_主线_青羽录10_楚家庄前院火烧")


---@class 角色_主线_青羽录10_楚家庄前院火烧
local npcs = {
    --- "荆成"
    zhujue = "角色/zhujue",
    --- "莫知酉"
    MoZhiYou = "角色/MoZhiYou",
    --- "云舞笙"
    YunWuSheng = "角色/YunWuSheng",
}

---@class 物体_主线_青羽录10_楚家庄前院火烧
local objs = {
    --- "播放timeline"
    Trigger1 = "物体/Trigger1",
}

---@class 相机_主线_青羽录10_楚家庄前院火烧
local cameras = {
    --- "开始镜头"
    LensStart = "相机/LensStart",
}

---@class 位置_主线_青羽录10_楚家庄前院火烧
local pos = {
    --- "莫知酉移动终点位置"
    Pos_MoZhiYou = "位置/Pos_MoZhiYou",
    --- "云舞笙移动终点位置"
    Pos_YunWuSheng = "位置/Pos_YunWuSheng",
    --- "玩家初始位置"
    Pos_start = "位置/Pos_start",
}

---@class 资产_主线_青羽录10_楚家庄前院火烧
local assets = {
}

---@class 动作_主线_青羽录10_楚家庄前院火烧
local animationClips = {
}

---@class 主线_青羽录10_楚家庄前院火烧
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
