
---@type 挑战_风雪神道山脚
local context = require("MapStories/挑战_风雪神道/scene_挑战_风雪神道山脚_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_挑战_风雪神道
local flags = context.flags

-- local extensions = require 'MapStories/挑战_风雪神道/extension'
-- -- ---@type StringWidget
-- -- local stringWidget = extensions.widgets.StringWidgetInfo.widget
-- -- ---@type 挑战_风雪神道_StringItemData
-- -- local stringItemData = extensions.widgets.StringWidgetInfo.items
-- ---@type ItemWidget
-- local itemWidget = extensions.widgets.ItemWidgetInfo.widget
-- ---@type 挑战_风雪神道_ItemData
-- local itemData = extensions.widgets.ItemWidgetInfo.items

--不可省略，用于外部获取flag

function testestsetsets()
    s.setTaskStep("新风拂雾开","goToChengDu")
end

function getFlags(...)
    return flags
end

--每次载入调用
function start()
    local isTeleport = false
    --如果是传送，就不能setPos
    if(args ~= nil and args:ContainsKey("IsTransport"))then
        isTeleport = true
    end

    --如果不是传送，再设置主角位置
    if(not isTeleport)then
        s.setPos(npcs.zhujue,pos.pre_start)
        --s.move(npcs.zhujue,pos.start,0.85,true)
        --s.wait(1.5)
    end

    s.playMusic("凌云惊雪")

    --本次副本内是否击败了Boss
    if(flags.boss1Defeated == 1) then
        s.setActive(npcs.boss1,false)
    end

    --是否播放过Boss1战前演出
    if(s.getChallengeMapPersistentFlag("挑战_风雪神道","播放过Boss1战前演出") == 0)then
        --s.setPos(npcs.boss1,pos.boss_pos_1)
        s.setActive(npcs.boss_2,true)
    else
        s.setPos(npcs.boss1,pos.boss_pos_3)
    end

    --处理传送点
    s.setActive(objs.teleport,flags.boss1Defeated == 1)

    --把itemdata里的所有物品都获取1件
    -- for k,v in pairs(itemData) do
    --     if(string.find(k,"Done") == nil)then
    --         itemWidget:addItem(itemData[k],1)
    --     end
    -- end

    --刷新副本信息
    -- s.setMapStatus("补给数量", flags.trash)
    -- s.setMapStatus("资源数量", flags.resource)
    -- itemWidget:setItem(itemData.TrashDone, flags.trash)
    -- itemWidget:setItem(itemData.ResourceDone, flags.resource)

    --重新载入场景，根据flag去处理每个gameobject是否是active
        -- for k,v in pairs(objs) do
    --     --print(k)
    --     --print(v)
    --     --处理垃圾点
    --     if(string.find(k,"trash") ~= nil)then
    --         s.setActive(v,flags[k] == 0)
    --     end
    -- end

    s.readyToStart()

    --是否播放过开场演出2
    if(s.getChallengeMapPersistentFlag("挑战_风雪神道","开场") == 0)then
        --设置flag
        s.setChallengeMapPersistentFlag("挑战_风雪神道","开场",1)

        --开场演出2
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次门口对话_1", nil, 0)
        s.camera(cameras.overview1,true)
        s.wait(0.5)
        --切换到overview2相机，缩放过去
        s.camera(cameras.overview2,true,true,blendHintEnum.EaseInOut,3,true,true)
        --切换回跟随相机
        s.camera(cameras.overview2,false,true,blendHintEnum.EaseInOut,1.5,true,true)

        --副本机制指引，以及弹hintPanel
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次门口对话_2", nil, 0)
        -- s.talk(npcs.zhujue, "在山路上散落着不少资源和补给，如果能妥善地加以收集，应该可以做出不少有利于探索雪山的道具……")
        -- s.showHintDescPanel("风雪神道捡垃圾", "「在风雪山道中，收集散落的物资吧！」", "在副本的各个场景中，会有散落在各地的发亮物品。\n靠近时即可拾取这些掉落的物资，并随机获取资源或者补给。", "继续")
        -- s.showHintDescPanel("风雪神道建造", "「利用建造面板，使用物资建造工具」", "点开「菜单」——「建造面板」，可以使用收集的资源和补给建造在副本中临时生效的工具。\n工具可以提供各种不同的增益，帮助您更好地通关副本。", "知道了")
    end

    --添加副本内主线任务
    if not s.hasTaskGroup("风雪神道副本内")then
        s.setTaskStep("风雪神道副本内","beatBoss")
    end
end

--触发Boss1战斗
function boss1Trigger()
    --是否首次击败过Boss1
    if(s.getChallengeMapPersistentFlag("挑战_风雪神道","播放过Boss1战前演出") == 0)then
        --没有就播放一次性演出
        s.moveAsync(npcs.zhujue,pos.pos_boss_1,1.5,true)
        s.camera(cameras.camera_boss1,true,true,blendHintEnum.EaseInOut,1,true,true)
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss1战前对话_1", nil, 0)
        s.turnTo(npcs.boss_2,npcs.zhujue)
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss1战前对话_2", nil, 0)
        s.animState(npcs.boss_2, "BFight_Loop",true,false,0,false,false,0,false)
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss1战前对话_3", nil, 0)
        s.animState(npcs.boss1, "Special_2",true,false,0.5,true,true,1.5)
        s.wait(0.2)
        s.setPos(npcs.boss1,pos.boss_pos_1)
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss1战前对话_4", nil, 0)
        s.animState(npcs.boss_2, "BFight_Loop",false,false,0,false,false,0,false)
        s.turnTo(npcs.boss_2,npcs.boss1)
        s.turnTo(npcs.zhujue,npcs.boss1)
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss1战前对话_5", nil, 0)
        s.turnTo(npcs.boss1,npcs.boss_2)
        s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss1战前对话_6", nil, 0)
        s.moveAsync(npcs.boss_2,pos.boss_pos_2,5,true)
        s.wait(0.5)
        s.setChallengeMapPersistentFlag("挑战_风雪神道","播放过Boss1战前演出",1)
    end

    --重复演出
    s.turnTo(npcs.boss1,npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs.boss1)
    s.animState(npcs.boss1, "Special_1",true,false,0.5,true,true,1.5)
    s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_Boss1战前对话_1", nil, 0)

     --itemdata中的物品key，传进去对应flag的值
    --  local battleTb = {}
    --  battleTb["StructureFire"] = flags.StructureFire
    --  battleTb["StructureFood"] = flags.StructureFood
    --  battleTb["StructureAlcohol"] = flags.StructureAlcohol
    --  battleTb["StructureClothes"] = flags.StructureClothes
    --  battleTb["StructureKnifeStone"] = flags.StructureKnifeStone
    --  battleTb["StructureMap"] = flags.StructureMap
    --  battleTb["StructurePet"] = flags.StructurePet
    --  battleTb["StructureEnhance"] = flags.StructureEnhance
 
     --触发战斗
     local isWin,bResult = s.challengeMapBattle("101")
     s.setPos(npcs.boss1,pos.boss_pos_3)
     s.setActive(npcs.boss_2,false)
     if(isWin)then
        --设置断线重连flag
        flags.boss1Defeated = 1

        --播放演出，判断是否首次击败过Boss1
        if(s.getChallengeMapPersistentFlag("挑战_风雪神道","击败过Boss1") == 0)then
            s.setChallengeMapPersistentFlag("挑战_风雪神道","击败过Boss1",1)
            --一次性演出
            s.animState(npcs.boss1, "BSquat_Loop",true)
            s.runAvgTag("副本/副本_风雪神道/副本_风雪神道", "副本_风雪神道_首次Boss1战后对话_1", nil, 0)
            s.animState(npcs.boss1, "BSquat_Loop",false)
            s.animState(npcs.boss1, "Special_3",true)
            --必须要给延迟，状态机过渡过去需要时间
            s.wait(3.5)
            s.setActive(npcs.boss1,false)
            s.animState(npcs.boss1, "Special_3",false)
        else
            --非一次性演出
            s.setActive(npcs.boss1,false)
        end
        
        --重复演出
        s.wait(0.5)
        s.camera(cameras.camera_boss1,false,true,blendHintEnum.EaseInOut,1,true,true)

        --刷出传送点
        s.setActive(objs.teleport,true)
     else
        s.camera(cameras.camera_boss1,false)
     end
end

--前往山路
function enterMiddle()
    s.changeMap("Assets/GameScenes/游历场景/挑战_风雪神道/挑战_风雪神道山路.unity", "位置/start")
end

--捡垃圾
-- function catchTrash(trashKey)
--     s.setActive("物体/" .. trashKey,false)

--     --50%的概率获得补给，50%的概率获得资源
--     local rand = math.random(1,100)
--     if(rand <= 50)then
--         --30%的概率获得2份，70%的概率获得1份
--         local rand2 = math.random(1,100)
--         if(rand2 <= 30)then
--             s.popInfo("获得补给 × 2!")
--             flags.trash = flags.trash + 2
--             s.setMapStatus("补给数量", flags.trash)
--             itemWidget:addItem(itemData.TrashDone, 2)
--         else
--             s.popInfo("获得补给 × 1!")
--             flags.trash = flags.trash + 1
--             s.setMapStatus("补给数量", flags.trash)
--             itemWidget:addItem(itemData.TrashDone, 1)
--         end
--     else
--         --30%的概率获得2份，70%的概率获得1份
--         local rand2 = math.random(1,100)
--         if(rand2 <= 30)then
--             s.popInfo("获得资源 × 2!")
--             flags.resource = flags.resource + 2
--             s.setMapStatus("资源数量", flags.resource)
--             itemWidget:addItem(itemData.ResourceDone, 2)
--         else
--             s.popInfo("获得资源 × 1!")
--             flags.resource = flags.resource + 1
--             s.setMapStatus("资源数量", flags.resource)
--             itemWidget:addItem(itemData.ResourceDone, 1)
--         end
--     end
    
--     flags[trashKey] = -1
-- end

-- --小怪通用战斗
-- function enemyBattle(roleKey)
--     local battleTb = {}

--     battleTb["StructureFire"] = flags.StructureFire
--     battleTb["StructureFood"] = flags.StructureFood
--     battleTb["StructureAlcohol"] = flags.StructureAlcohol
--     battleTb["StructureClothes"] = flags.StructureClothes
--     battleTb["StructureKnifeStone"] = flags.StructureKnifeStone
--     battleTb["StructureMap"] = flags.StructureMap
--     battleTb["StructurePet"] = flags.StructurePet
--     battleTb["StructureEnhance"] = flags.StructureEnhance

--     如果有风雪神道地图(flags.StructureMap>0)，可以使用1补给跳过战斗
--     if(flags.StructureMap > 0 and flags.trash >= 1)then
--         local ret = s.selectTalk(npcs.zhujue, "是否使用1补给跳过战斗？", {"跳过", "不跳过"})
--         if ret == 0 then
--             flags.trash = flags.trash - 1
--             s.setMapStatus("补给数量", flags.trash)
--             itemWidget:removeItem(itemData.TrashDone, 1)
--             enemyBattleEnd(roleKey)
--             return
--         end
--     end

--     --否则继续进入战斗
--     local isWin,bResult = s.battle("副本_风雪神道_战斗_" .. roleKey, battleTb)
--     if(isWin)then
--         s.popInfo("获得补给 × 1,资源 × 1!")
--         flags.trash = flags.trash + 1
--         s.setMapStatus("补给数量", flags.trash)
--         flags.resource = flags.resource + 1
--         s.setMapStatus("资源数量", flags.resource)
--         itemWidget:addItem(itemData.TrashDone, 1)
--         itemWidget:addItem(itemData.ResourceDone, 1)
--     end
-- end