--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/漫游_雪谷/flags_漫游_雪谷")


---@class 角色_漫游_雪谷_远古地宫_地下
local npcs = {
    --- "玄恩"
    xuanen = "角色/xuanen",
    --- "老黎"
    laoli = "角色/laoli",
    --- "墨七"
    moqi = "角色/moqi",
}

---@class 物体_漫游_雪谷_远古地宫_地下
local objs = {
    --- "药方"
    yaofang = "物体/yaofang",
}

---@class 相机_漫游_雪谷_远古地宫_地下
local cameras = {
}

---@class 位置_漫游_雪谷_远古地宫_地下
local pos = {
    --- "通往入口"
    Entre = "位置/Entre",
}

---@class 资产_漫游_雪谷_远古地宫_地下
local assets = {
}

---@class 动作_漫游_雪谷_远古地宫_地下
local animationClips = {
}

---@class 漫游_雪谷_远古地宫_地下
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
