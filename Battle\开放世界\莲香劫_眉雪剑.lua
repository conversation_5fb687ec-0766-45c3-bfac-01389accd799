local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)
local show = 1
-- local summon = 0

function init()

end

function start()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        bcapi.add_timer_trigger(0.1, startTalk)
        bcapi.add_condition_trigger("[%enemy:世界_莲香劫_眉雪剑->flag:无敌触发%][=]1", 0.1, winTalk)
    end)
end

function startTalk(...)
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("俪茵", "【小声】大雾天气，虽然看不清楚，但我会小心，不会伤了少侠。")
        bcapi.resume_and_show_ui()
    end)
end

function winTalk(...)
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("俪茵", "少侠，你接下无相三剑之后，我们就要动真本事了！")
        bcapi.talk("主角", "……")
        bcapi.talk("俪茵", "记住我昨天告诉你的出剑位置……准备……")
        bcapi.resume_and_show_ui()
        bcapi.create_join_role("世界_莲香劫_黑衣男杀手", 34, eTeam, bcapi.Vector2(-6, -3), 0)
        bcapi.create_join_role("世界_莲香劫_黑衣男杀手_头子", 34, eTeam, bcapi.Vector2(-6, 0), 0)
        bcapi.create_join_role("世界_莲香劫_黑衣男杀手_暗器", 34, eTeam, bcapi.Vector2(-6, 3), 0)
        local Boss1 = bcapi.get_role(1, "世界_莲香劫_黑衣男杀手")
        local Boss2 = bcapi.get_role(1, "世界_莲香劫_黑衣男杀手_头子")
        local Boss3 = bcapi.get_role(1, "世界_莲香劫_黑衣男杀手_暗器")
        bcapi.add_condition_trigger("[%enemy:世界_莲香劫_黑衣男杀手_头子->flag:表演结束%][=]1", 0.1, endShow)
    end)
end

function endShow(...)
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("俪茵", "这……这并非演武的内容……")
        bcapi.talk("主角", "什么人？！")
        bcapi.resume_and_show_ui()
        bcapi.win()
    end)
end