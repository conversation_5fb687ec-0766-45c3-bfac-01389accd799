--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_长生劫_04裘地仙居/flags_蜃境_长生劫_04裘地仙居")


---@class 角色_蜃境_长生劫_04裘地仙居
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "csj_15"
    csj_15 = "角色/csj_15",
    --- "csj_16"
    csj_16 = "角色/csj_16",
    --- "csj_17"
    csj_17 = "角色/csj_17",
    --- "enemy_1"
    enemy_1 = "角色/enemy_1",
    --- "enemy_2"
    enemy_2 = "角色/enemy_2",
    --- "enemy_3"
    enemy_3 = "角色/enemy_3",
    --- "enemy_4"
    enemy_4 = "角色/enemy_4",
    --- "enemy_5"
    enemy_5 = "角色/enemy_5",
    --- "elite_6"
    elite_6 = "角色/elite_6",
    --- "拿钥匙的男人"
    keyMan = "角色/keyMan",
    --- "拿钥匙的男人_室内"
    keyMan_insideHouse = "角色/keyMan_insideHouse",
    YZ_NPC = "角色/YZ_NPC",
    --- "商人"
    YZ_NPC_shangren = "角色/YZ_NPC_shangren",
    --- "村民甲"
    YZ_NPC_cunminjia = "角色/YZ_NPC_cunminjia",
    --- "村民乙"
    YZ_NPC_cunminyi = "角色/YZ_NPC_cunminyi",
    --- "可疑人物"
    YZ_NPC_keyirenwu = "角色/YZ_NPC_keyirenwu",
    --- "小女孩"
    SXZ_Puzzle_xiaonvhai = "角色/SXZ_Puzzle_xiaonvhai",
}

---@class 物体_蜃境_长生劫_04裘地仙居
local objs = {
    --- "出口"
    exit = "物体/exit",
    --- "chest_1"
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
    findKey = "物体/findKey",
    treasureDoor = "物体/treasureDoor",
    enter_treasureDoor = "物体/enter_treasureDoor",
    eavesdrop_man = "物体/eavesdrop_man",
    --- "货箱"
    YZ_object_huoxiang = "物体/YZ_object_huoxiang",
    --- "画记·童言无忌……？"
    csj_1006 = "物体/csj_1006",
    --- "书牍·玉盘珍羞"
    csj_1007 = "物体/csj_1007",
    --- "解谜箱子1"
    SXZ_Puzzle_box_1 = "物体/SXZ_Puzzle_box_1",
    --- "解谜箱子2"
    SXZ_Puzzle_box_2 = "物体/SXZ_Puzzle_box_2",
    --- "解谜箱子3"
    SXZ_Puzzle_box_3 = "物体/SXZ_Puzzle_box_3",
    --- "解谜箱子4"
    SXZ_Puzzle_box_4 = "物体/SXZ_Puzzle_box_4",
    --- "解谜箱子5"
    SXZ_Puzzle_box_5 = "物体/SXZ_Puzzle_box_5",
    --- "解谜箱子6"
    SXZ_Puzzle_box_6 = "物体/SXZ_Puzzle_box_6",
}

---@class 相机_蜃境_长生劫_04裘地仙居
local cameras = {
}

---@class 位置_蜃境_长生劫_04裘地仙居
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    enemy_1 = "位置/enemy_1",
    enemy_2 = "位置/enemy_2",
    enemy_3 = "位置/enemy_3",
    enemy_4 = "位置/enemy_4",
    enemy_5 = "位置/enemy_5",
    upper_roof = "位置/upper_roof",
    down_roof = "位置/down_roof",
}

---@class 资产_蜃境_长生劫_04裘地仙居
local assets = {
    timeline_ninjia = "资产/timeline_ninjia",
}

---@class 动作_蜃境_长生劫_04裘地仙居
local animationClips = {
}

---@class 蜃境_长生劫_04裘地仙居
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
