local bcapi = require("BattleClientA<PERSON>")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    -- 开局增添随机BUFF类策略卡
    local sLevel = 5
    local sTable = {"银针返命","风露杏雪","大藏心经","吐纳法","不动如山","龙虎心法","铁甲阵法","藏锋积锐",
    "霸王怒吼","梧叶舞秋风","豪云剑魂","血沸剑魂","刺隐剑魂","如归剑魂","青莲剑魂","分身解厄"}

    local index = math.random(1, #sTable)
    bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
    index = math.random(1, #sTable)
    bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
    index = math.random(1, #sTable)
    bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
    index = math.random(1, #sTable)
    bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
end

function start()

end
