local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    local sLevel = 7

    local sTable = {"万钧重锤","银针返命","大藏心经","吐纳法","龙虎心法",
    "藏锋积锐","霸王怒吼","梧叶舞秋风","江湖刀客","木人筑","豪云剑魂","血沸剑魂",
    "刺隐剑魂","如归剑魂","青莲剑魂","朔风吹雪","灵兽助阵","神射手","光明圣火"}

    index = math.random(1, #sTable)
    bcapi.add_strategy_card(sTable[index], sLevel, eTeam)
    index = math.random(1, #sTable)
    bcapi.add_strategy_card(sTable[index], sLevel, eTeam)
    index = math.random(1, #sTable)
    bcapi.add_strategy_card(sTable[index], sLevel , eTeam)
    index = math.random(1, #sTable)
    bcapi.add_strategy_card(sTable[index], sLevel , eTeam)
end

function start()

end
