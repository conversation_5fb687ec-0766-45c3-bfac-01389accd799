local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    
end

function start()
    bcapi.add_timer_trigger(0, EnemyTalk)
    bcapi.add_condition_trigger("[%enemy:副本_风雪神道_驱豹人->hp_pct%][<=]0.7", 1, EnemyStage2)
    bcapi.add_condition_trigger("[%enemy:副本_风雪神道_驱豹人->flag:变身2%][>]0", 1, EnemyStage2Hint)
    bcapi.add_condition_trigger("[%enemy:副本_风雪神道_驱豹人->flag:变身3%][>]0", 1, EnemyStage3Hint)
end

function EnemyStage2()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_风雪神道_驱豹人#副本_风雪神道_驱豹人", "哼——这下我是真的生气了——")
        local teammate = bcapi.get_role(1, "副本_风雪神道_驱豹人")
        teammate.Stat.BattleFlagDic:set_Item("变身2阶段", "1")
        bcapi.resume_and_show_ui()
        bcapi.add_condition_trigger("[%enemy:副本_风雪神道_驱豹人->hp_pct%][<=]0.4", 1, EnemyStage3)
    end)
end

function EnemyStage2Hint()
    bcapi.async_call(function()
        bcapi.show_pop_info("驱豹人进入狂化状态！", 4)
    end)
end

function EnemyStage3()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_风雪神道_驱豹人#副本_风雪神道_驱豹人", "为什么——你就不能乖乖地躺下呢？！")
        local teammate = bcapi.get_role(1, "副本_风雪神道_驱豹人")
        teammate.Stat.BattleFlagDic:set_Item("变身3阶段", "1")
        bcapi.resume_and_show_ui()
    end)
end

function EnemyStage3Hint()
end

function EnemyStage3()
    bcapi.async_call(function()
        bcapi.show_pop_info("驱豹人进入终极魔化状态！", 4)
    end)
end


function EnemyTalk()
    bcapi.async_call(function()
        bcapi.append_async_aside("驱豹人",0,"喂，小鬼，这是你自己活腻歪了，可别怪我的狼牙棒手下无情！","","驱豹人")
    end)
end
