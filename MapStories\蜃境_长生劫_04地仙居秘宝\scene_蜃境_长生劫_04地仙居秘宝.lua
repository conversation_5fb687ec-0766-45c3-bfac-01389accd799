
---@type 蜃境_长生劫_04地仙居秘宝
local context = require("MapStories/蜃境_长生劫_04地仙居秘宝/scene_蜃境_长生劫_04地仙居秘宝_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_长生劫_04地仙居秘宝
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")
--每次载入调用

function start()
end

function enter_dixianRoom()
    s.changeMap("Assets/GameScenes/游历场景/蜃境_长生劫_04裘地仙居/蜃境_长生劫_04裘地仙居.unity","位置/pos_start")
end