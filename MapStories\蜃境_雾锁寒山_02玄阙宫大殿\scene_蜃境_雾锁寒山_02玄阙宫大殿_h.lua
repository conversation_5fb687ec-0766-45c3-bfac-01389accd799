--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_雾锁寒山_02玄阙宫大殿/flags_蜃境_雾锁寒山_02玄阙宫大殿")


---@class 角色_蜃境_雾锁寒山_02玄阙宫大殿
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "wshs_03"
    wshs_03 = "角色/wshs_03",
    --- "wshs_04"
    wshs_04 = "角色/wshs_04",
    --- "wshs_05"
    wshs_05 = "角色/wshs_05",
    --- "wshs_06"
    wshs_06 = "角色/wshs_06",
    --- "enemy_1"
    enemy_1 = "角色/enemy_1",
    --- "enemy_2"
    enemy_2 = "角色/enemy_2",
    --- "enemy_3"
    enemy_3 = "角色/enemy_3",
    --- "enemy_4"
    enemy_4 = "角色/enemy_4",
    --- "enemy_5"
    enemy_5 = "角色/enemy_5",
    --- "enemy_6"
    enemy_6 = "角色/enemy_6",
    --- "elite_7"
    elite_7 = "角色/elite_7",
    --- "role_elite_7"
    role_elite_7 = "角色/role_elite_7",
    --- "尹晗幻影1"
    yinhan_shadow_1 = "角色/yinhan_shadow_1",
    --- "戒律长老幻影1"
    jlzhanglao_shadow_1 = "角色/jlzhanglao_shadow_1",
    --- "尹雪音幻影1"
    jinxueyin_shadow_1 = "角色/jinxueyin_shadow_1",
    --- "程夫子幻影1"
    chengfuzi_shadow_1 = "角色/chengfuzi_shadow_1",
    --- "青年阿莫"
    amo = "角色/amo",
    --- "尹晗"
    yinhan = "角色/yinhan",
    --- "吵闹的弟子"
    noisyDizi = "角色/noisyDizi",
    --- "尹雪音"
    yinxueyin = "角色/yinxueyin",
    --- "弟子甲"
    noisyDizi_1 = "角色/noisyDizi_1",
    --- "弟子乙"
    noisyDizi_2 = "角色/noisyDizi_2",
    --- "奸商"
    jianshang = "角色/jianshang",
    --- "林契"
    linqi = "角色/linqi",
    --- "沈聪安"
    shengcongan = "角色/shengcongan",
    --- "elite_8"
    elite_8 = "角色/elite_8",
}

---@class 物体_蜃境_雾锁寒山_02玄阙宫大殿
local objs = {
    --- "出口"
    exit = "物体/exit",
    --- "chest_1"
    chest_1 = "物体/chest_1",
    --- "chest_2"
    chest_2 = "物体/chest_2",
    --- "chest_3"
    chest_3 = "物体/chest_3",
    --- "chest_4"
    chest_4 = "物体/chest_4",
    --- "evilAir_1"
    evilAir_1 = "物体/evilAir_1",
    --- "阿莫的触发器"
    trigger_amo = "物体/trigger_amo",
    --- "wshs1_1001"
    wshs2_1001 = "物体/wshs2_1001",
    --- "wshs2_1002"
    wshs2_1002 = "物体/wshs2_1002",
    --- "wshs2_1003"
    wshs2_1003 = "物体/wshs2_1003",
}

---@class 相机_蜃境_雾锁寒山_02玄阙宫大殿
local cameras = {
    --- "cam_start"
    cam_start = "相机/cam_start",
    --- "cam_main"
    cam_main = "相机/cam_main",
    --- "cam_showEnmey"
    cam_showEnmey = "相机/cam_showEnmey",
    --- "cam_noisyDizi"
    cam_noisyDizi = "相机/cam_noisyDizi",
    --- "cam_yinxueyin"
    cam_yinxueyin = "相机/cam_yinxueyin",
    --- "cam_noisyDizi_talk"
    cam_noisyDizi_talk = "相机/cam_noisyDizi_talk",
}

---@class 位置_蜃境_雾锁寒山_02玄阙宫大殿
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    --- "阿莫离开位置"
    pos_amo_leave = "位置/pos_amo_leave",
    --- "pos_yinxueyin_leave"
    pos_yinxueyin_leave = "位置/pos_yinxueyin_leave",
    --- "pos_yinhan_leave"
    pos_yinhan_leave = "位置/pos_yinhan_leave",
    --- "pos_dizi_leave"
    pos_dizi_leave = "位置/pos_dizi_leave",
}

---@class 资产_蜃境_雾锁寒山_02玄阙宫大殿
local assets = {
}

---@class 动作_蜃境_雾锁寒山_02玄阙宫大殿
local animationClips = {
}

---@class 蜃境_雾锁寒山_02玄阙宫大殿
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
