---@type 挑战_远古地宫
local context = require("MapStories/挑战_远古地宫/scene_挑战_远古地宫_h")
local s = context.sapi
local flags = context.flags
s.loadFlags(flags)


---@class 挑战_远古地宫_StringItemData
local stringItemData = {
    CreatureHp = {key = "CreatureHp",showName="最大生命",desc = "测试参数名1"},
    CreatureOuterPower = {key = "CreaturePower",showName="外功攻击",desc = "测试参数名2"},
    CreatureOuterDefence = {key = "CreatureOuterDefence",showName="外功防御",desc = "测试参数名3"},
    CreatureInnerPower = {key = "CreatureInnerPower",showName="内功攻击",desc = "测试参数名4"},
    CreatureInnerDefence = {key = "CreatureInnerDefence",showName="内功防御",desc = "测试参数名5"},
    CreatureInfo = {key = "CreatureInfo",showName="机关熊 - 当前属性",desc = "测试参数名5"},
}

---@class 挑战_远古地宫_ItemData
local itemData = {
    CreatureHpLvUpItem0 = {key = "CreatureHpLvUpItem0",showName = "机关熊生命升级(0级)", rare = 1,desc = "消耗<color=#157DEC>2</color>个机关零件，提高机关熊20%最大生命值",icon = "shipinupgrade", canUse = true},
    CreatureHpLvUpItem1 = {key = "CreatureHpLvUpItem1",showName = "机关熊生命升级(1级)", rare = 2,desc = "消耗<color=#157DEC>4</color>个机关零件，提高机关熊25%最大生命值",icon = "shipinupgrade", canUse = true},
    CreatureHpLvUpItem2 = {key = "CreatureHpLvUpItem2",showName = "机关熊生命升级(2级)", rare = 3,desc = "消耗<color=#157DEC>6</color>个机关零件，提高机关熊35%最大生命值",icon = "shipinupgrade", canUse = true},
    CreatureHpLvUpItem3 = {key = "CreatureHpLvUpItem3",showName = "机关熊生命升级(3级)", rare = 4,desc = "机关熊的最大生命已经无法再提升了。",icon = "shipinupgrade", canUse = false},
    CreaturePowerLvUpItem0 = {key = "CreaturePowerLvUpItem0",showName = "机关熊攻击升级(0级)", rare = 1,desc = "消耗<color=#157DEC>3</color>个机关零件，提高机关熊15%外功攻击和内功攻击",icon = "weaponupgrade", canUse = true},
    CreaturePowerLvUpItem1 = {key = "CreaturePowerLvUpItem1",showName = "机关熊攻击升级(1级)", rare = 2,desc = "消耗<color=#157DEC>6</color>个机关零件，提高机关熊20%外功攻击和内功攻击",icon = "weaponupgrade", canUse = true},
    CreaturePowerLvUpItem2 = {key = "CreaturePowerLvUpItem2",showName = "机关熊攻击升级(2级)", rare = 3,desc = "消耗<color=#157DEC>9</color>个机关零件，提高机关熊25%外功攻击和内功攻击",icon = "weaponupgrade", canUse = true},
    CreaturePowerLvUpItem3 = {key = "CreaturePowerLvUpItem3",showName = "机关熊攻击升级(3级)", rare = 4,desc = "机关熊的攻击已经无法再提升了。",icon = "weaponupgrade", canUse = false},
    CreatureDefenceLvUpItem0 = {key = "CreatureDefenceLvUpItem0",showName = "机关熊防御升级(0级)", rare = 1,desc = "消耗<color=#157DEC>1</color>个机关零件，提高机关熊10%外功防御和内功防御",icon = "clothesupgrade", canUse = true},
    CreatureDefenceLvUpItem1 = {key = "CreatureDefenceLvUpItem1",showName = "机关熊防御升级(1级)", rare = 2,desc = "消耗<color=#157DEC>5</color>个机关零件，提高机关熊40%外功防御和内功防御",icon = "clothesupgrade", canUse = true},
    CreatureDefenceLvUpItem2 = {key = "CreatureDefenceLvUpItem2",showName = "机关熊防御升级(2级)", rare = 3,desc = "消耗<color=#157DEC>10</color>个机关零件，提高机关熊100%外功防御和内功防御",icon = "clothesupgrade", canUse = true},
    CreatureDefenceLvUpItem3 = {key = "CreatureDefenceLvUpItem3",showName = "机关熊防御升级(3级)", rare = 4,desc = "机关熊的防御已经无法再提升了。",icon = "clothesupgrade", canUse = false},
    CreatureMoney = {key = "CreatureMoney",showName = "机关零件", rare = 3,desc = "击败机关造物所掉落的零件，可以用来强化机关熊",icon = "xiakelingsuipian", canUse = false}
}

local stringWidget = StringWidget:new(stringItemData,flags)
local itemWidget = ItemWidget:new(itemData,flags)

local itemWidgetInfo = {
    title = "道具",
    items = itemData,
    widget = itemWidget
}

local stringWidgetInfo = {
    title = "额外队友 - 机关熊",
    desc = "机关熊会在所有的战斗中作为召唤物助战，可通过机关零件进行强化。\n",
    items = stringItemData,
    widget = stringWidget
}

extensions = {
    useWidgets = {"StringWidget","ItemWidget"},
    widgets = {
        StringWidgetInfo = stringWidgetInfo,
        ItemWidgetInfo = itemWidgetInfo
    }
}

context.sapi.syncExtendsInfo(extensions)
return extensions