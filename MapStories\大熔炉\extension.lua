---@type 大熔炉
local context = require("MapStories/大熔炉/scene_大熔炉_h")
local s = context.sapi
local flags = context.flags
s.loadFlags(flags)


---@class 大熔炉_StringItemData
local stringItemData = {
    test1 = {key = "test1",showName="参数1",desc = "测试参数名1"},
    test2 = {key = "test2",showName="参数2",desc = "测试参数名2"},
}

---@class 大熔炉_ItemData
local itemData = {
    test1 = {index = 0,key = "test1",showName = "测试物品1", rare = 2,desc = "测试物品1",icon = "achi_title_3_diaoxiang_big", canUse = 1},
    test2 = {index = 1,key = "test2",showName = "测试物品2", rare = 2,desc = "测试物品2",icon = "achi_title_2_jinnang_big", canUse = 0},
    test3 = {index = 2,key = "test3",showName = "测试物品3", rare = 2,desc = "测试物品3",icon = "achi_title_1_jinnang_big", canUse = 1},
    test4 = {index = 3,key = "test4",showName = "测试物品4", rare = 2,desc = "测试物品4",icon = "achi_title_3_diaoxiang_big", canUse = 0},
    test5 = {index = 4,key = "test5",showName = "测试物品5", rare = 2,desc = "测试物品5",icon = "achi_title_2_jinnang_big", canUse = 1 },
}

local stringWidget = StringWidget:new(stringItemData,flags)
local itemWidget = ItemWidget:new(itemData,flags)

local itemWidgetInfo = {
    title = "道具",
    items = itemData,
    widget = itemWidget
}

local stringWidgetInfo = {
    title = "测试标题",
    desc = "测试描述",
    items = stringItemData,
    widget = stringWidget
}

extensions = {
    useWidgets = {"StringWidget","ItemWidget"},
    widgets = {
        StringWidgetInfo = stringWidgetInfo,
        ItemWidgetInfo = itemWidgetInfo
    }
}

context.sapi.syncExtendsInfo(extensions)
return extensions