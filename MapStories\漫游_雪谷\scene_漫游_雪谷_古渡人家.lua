
---@type 漫游_雪谷_古渡人家
local context = require("MapStories/漫游_雪谷/scene_漫游_雪谷_古渡人家_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

---组件相关
local extensions = require("MapStories/漫游_雪谷/extension")
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type 雪谷_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 雪谷_ItemData]]


s.loadFlags(context.flags)
---@type flags_漫游_雪谷
local flags = context.flags

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.readyToStart()
    reloadAllSceneStates()
end

function testAddItem11()
    itemWidget:addItem(itemData.bingxueniang,1)
end

function reloadAllSceneStates()
    --大牛
    s.setActive(npcs.daniu, flags.yd_buyierfei_step == 4)
    --米袋
    s.setActive(objs.midai, flags.yd_buyierfei_step == 4)
    
    --初始剧情触发
    s.setActive(objs.triggerOpening,flags.gd_opening_step ~= 3)
    
    --琴谱
    s.setActive(objs.qinpu,flags.gd_qinpu_collected == 0)
    
    --朱雀心
    s.setActive(objs.zhuque,flags.dg_main_zhuque == 0)
end

function wrappedReloadAllSceneStates()
    s.blackScreen()
    reloadAllSceneStates()
    s.lightScreen()
end

--只有在flag被修改时，会提交服务器，所以改变任务状态需要同时修改flag
--一个事件只会在结束后进行一次“同时”（无时间顺序）的提交，因此对同一个任务的操作只能发生一次（不能先add再finish，因为此时finish的提交会找不到任务对象）
function triggerOpening()
    if flags.gd_opening_step == 0 then
        s.talk(npcs.kanshou,"这里不欢迎外来者，速速离去！")
        s.talk(npcs.moqi,"前面是什么地方？")
        s.talk(npcs.kanshou,"这不是你该问的，不想惹麻烦就赶紧离开！")
        s.addTask(400110, 1)
        flags.gd_opening_step = 1
        return
        
    elseif flags.gd_opening_step == 1 then
        s.talk(npcs.moqi,"如果我偏要闯呢？")
        s.talk(npcs.kanshou,"那就只好给你些教训了！")
        
    elseif flags.gd_opening_step == 2 then
        s.talk(npcs.kanshou,"又是你？这里不许外人出入，难道非要我再提醒一次？")
        s.talk(npcs.moqi,"雪谷又不是你家的，闪开。")
        s.talk(npcs.kanshou,"狂妄之徒，看招！")
    end
    
    local battleWin = s.selectTalk(npcs.moqi, "……", {"战斗失败", "战斗胜利"})--todo:战斗接入

    if battleWin == 0 then
        s.talk(npcs.kanshou,"我已警告过你，这里严禁外人出入，速速离去吧！")
        s.talk(npcs.moqi,"……")
        
        --todo:处理bug。需要把墨七传送出trigger区域
    else
        s.talk(npcs.moqi,"现在我可以过去了吗？")
        s.talk(npcs.kanshou,"想进入此地，除非先杀了我！")
        s.talk(npcs.moqi,"我倒没那种兴趣，只是恰好路过，想看看究竟是何人在这里隐居。")
        s.talk(npcs.kanshou,"少装模作样！前些日子打伤我师兄的，就是你的同门吧！")
        s.talk(npcs.moqi,"我的同门？打伤你师兄？")
        s.talk(npcs.kanshou,"难道不是吗？我师兄前几日在附近被一神秘女子打伤，那女子与你一样都擅长机关术，依我看，你们多半是玄阙宫之人！")
        s.talk(npcs.moqi,"呵，玄阙宫这么厉害的门派，怎么看得上我这样的穷小子，更何况我若真是玄阙宫弟子，又何必来你这荒野之地受苦？")
        s.talk(npcs.kanshou,"这……似乎有几分道理，我听说玄阙宫弟子都穿着精致的丝绸，你这模样倒像个落魄难民。")
        s.talk(npcs.kanshou,"如此说来，你并不是来闹事的？")
        s.talk(npcs.moqi,"当然。")
        s.talk(npcs.kanshou,"好吧，刚才和你交手时，见你出招都是点到为止，与打伤我师兄那人截然不同，想来也不是什么阴险毒辣之辈，你先别走，待我入内通报一声。")

        s.aside("一会的时间……")
        s.blackScreen()
        s.wait(1)
        s.lightScreen()
        s.talk(npcs.kanshou,"你运气不错，家师同意你入内，他老人家此刻就在竹林之中，见面后切记不可失了礼数，去吧！")
        s.talk(npcs.moqi,"多谢。")

        flags.gd_opening_step = 3
        s.finishTask(400110, 1,2)
        
        wrappedReloadAllSceneStates()
    end
end

--region 大牛

function talkToDaniu()
    talkToDaniu001()
    --刷新所有场景状态
    wrappedReloadAllSceneStates()
end

function talkToDaniu001()
    s.talk(npcs.moqi,"找到你了，小偷。")
    s.talk(npcs.daniu,"……我当是谁呢，原来是你啊，新来的小屁孩。")
    s.talk(npcs.moqi,"你也是营地一员？都说家贼难防，果然如此。")
    s.talk(npcs.moqi,"交出米袋，我便既往不咎。")
    s.talk(npcs.daniu,"休想，除非你赢得过我的拳头。")

    
    local battleWin = s.selectTalk(npcs.moqi, "……", {"战斗失败", "战斗胜利"})--todo:战斗接入

    if battleWin == 0 then
        s.talk(npcs.daniu,"小兄弟，不是我大牛故意欺负你，生于乱世大家都只想苟活，咱们各走各的道，这样对谁都好。")
        
    else
        s.talk(npcs.daniu,"好了好了，我认输。")
        s.talk(npcs.daniu,"实话告诉你吧，我就是黑娃他爹，这米袋是我偷的不假，你……想要就拿走吧。")
        s.talk(npcs.moqi,"你是黑娃他爹？为何要偷自己营地的粮食？")
        s.talk(npcs.daniu,"唉！阿凉他们被仇恨蒙蔽了双眼，至于我呢，虽然也痛恨玄阙宫那伙人，但好歹还有一个黑娃在身边。")
        s.talk(npcs.daniu,"我已在山的那边寻到一个太平镇子，想着这几日就带黑娃离开，顺手将几袋粮食藏在这里。")
        s.talk(npcs.daniu,"人这一辈子太短，眨眼就没了，我……我就想能过个安宁日子。")

        s.aside("获得了米袋！")
        itemWidget:addItem(itemData.midai,1)
        flags.yd_buyierfei_step = 5

        s.finishTask(400011, 2)
    end
end

--endregion

function talkToWatchman()
    s.talk(npcs.kanshou,"古渡与外界并无来往，家师素来喜欢清净，还请你谨言慎行。")
end

function talkToHelao()
    if(flags.gd_main_step == 0) then
        talkToHelao000()
        flags.gd_main_step = 1
    elseif (flags.gd_main_step < 4) then
        talkToHelao001()
    elseif (flags.gd_main_step == 4) then
        talkToHelao002()
    elseif (flags.gd_main_step == 5) then
        talkToHelao003()
    else
        talkToHelao004()
    end
end

function talkToHelao000()
    s.talk(npcs.helao,"少年，你来做什么？")
    s.talk(npcs.moqi,"只是偶然路过，没想到这里竟有人隐居，而且住所还如此大气考究，倒像个门派一般。")
    s.talk(npcs.helao,"你眼力不错，这些房屋都是凭记忆建造，为的是复原那已损毁门派的一砖一瓦。")
    s.talk(npcs.moqi,"已损毁？难道说……")
    s.talk(npcs.helao,"你并非是第一个提出这疑问的，在你之前还有其他人出现过。")
    s.talk(npcs.moqi,"可我从未听过关于这里的消息。")
    s.talk(npcs.helao,"为了让那些人保守秘密，我们偶尔也会采取一些非常手段。")
    s.talk(npcs.moqi,"……")
    s.talk(npcs.moqi,"你们究竟在躲避什么？")
    s.talk(npcs.helao,"我们是一个古老的门派，已经彻底退出了江湖纷争，为了长久地存在下去，隐姓埋名是最好的选择。")
    s.talk(npcs.moqi,"所以，你为何允许我入内？")
    s.talk(npcs.helao,"今年冬天，这条从不上冻的永流河竟意外结冰，随后你出现了，或许这是上天的暗示，我觉得有必要给你一个走进古渡的机会。")
    s.talk(npcs.helao,"我解释的已经够多了，你可以在此地随意逛逛，切记，不要打扰了众人的安宁。")

    s.finishTask(400110, 2,3)
end

function talkToHelao001()
    s.talk(npcs.helao,"深林人不知，明月来相照。你能来此地，也是缘分。")
end

function talkToHelao002()
    --△竹林，贺老在等你。
    s.talk(npcs.helao,"我找你，是有一件大事与你商议。")
    s.talk(npcs.moqi,"大事？")
    s.talk(npcs.helao,"不错，我打算在几位弟子里选拔一位佼佼者，并让出掌门职位。")
    s.talk(npcs.moqi,"让位？可是您精神尚且矍铄……")
    s.talk(npcs.helao,"老啦，不中用了！就如同这些挺拔的竹子一般，虽从外表看来尚且坚韧，其实内里早已受不住雨打风吹，或许随时都会倒塌。")
    s.talk(npcs.moqi,"您可有中意人选？")
    s.talk(npcs.helao,"唉……我们“鸿雁剑派”创立数百年，原本英才荟萃，后来却死的死散的散，如今真正有资格继任这掌门之位的，不过三人而已。")
    s.talk(npcs.moqi,"哪三人？")
    s.talk(npcs.helao,"相信你们已打过照面，他们是我的三位关门弟子，分别为阿飞、马三和小云。")
    s.talk(npcs.helao,"我预备在<color=red>四个时辰</color>后开始下一任掌门的选拔仪式，想请你一起做个见证，你可有兴趣参加？")
    s.talk(npcs.moqi,"甚好")
    
    --todo:添加计时器
    
    flags.gd_main_step = flags.gd_main_step + 1
    wrappedReloadAllSceneStates()--todo:处理所有人的位置？
end

function talkToHelao003()
    if flags.gd_main_step == 5 then
        gd_main_story001()
    else
        gd_main_story002()
    end
    
end

function talkToHelao004()
    s.talk(npcs.helao,"归去来兮，田园将芜胡不归？")
end

function gd_main_story000()
    s.blackScreen()
    s.talk("古渡弟子","这位客人，家师有事与你相商，请你前往竹林一叙。")
    s.talk(npcs.moqi,"贺老找我？")
    s.talk("古渡弟子","正是！还请客人即刻前往竹林。")
    s.lightScreen()
end

function gd_main_story001()
    --todo：演出级处理
    --△古渡擂台上，贺老手握镇派宝剑（或者将镇派宝剑放在武器架上展示），缓缓环顾众人。
    s.talk(npcs.helao,"今日召集大家过来，是有一件重要之事要宣布——")
    s.talk(npcs.helao,"我们“鸿雁剑派”创立数百年，曾在这江湖纵横捭阖，声名煊赫，诞生过许多英雄豪杰，谁人不曾心向往之？")
    s.talk(npcs.helao,"然而世风变幻，局势莫测，我派遭奸人暗算，险些覆灭，无奈之下来到这雪谷隐居避世，时也，命也！")
    s.talk(npcs.helao,"我年事已高，时日无多，终有凋敝衰败的时候，而门派不可一日无主，我近日思虑良久，如今召集大家来此，正是为了选出鸿雁剑派下一任掌门——")
    --△众弟子一阵嘈杂低语。
    s.talk(npcs.helao,"阿飞、小云、马三，你三人听令。")
    --△三人闻言，一齐向何老作了一揖。
    s.talkAmazed(npcs.afei,"弟子在！")
    s.talkAmazed(npcs.xiaoyun,"弟子在！")
    s.talkAmazed(npcs.masan,"弟子在！")
    s.talk(npcs.helao,"你们都是我多年的关门弟子，也是门派里的佼佼者，我决定将掌门之位传给你们中的一人，以比武论定——")
    --△贺老扬起手中镇派宝剑（或镜头给到武器架上的镇派宝剑）
    s.talk(npcs.helao,"你三人需光明正大地互相角逐，不可偷奸耍诈，最后取胜者，即可获得掌门之位，以及这柄代代相传的镇派宝剑。")
    --△众人一阵喧哗。
    s.talk(npcs.afei,"是！")
    s.talk(npcs.xiaoyun,"是！")
    s.talk(npcs.masan,"是！")
    s.talk(npcs.helao,"按照规矩，你们三人需抓阄选定对手，轮番角逐，比武立即开始——")
    s.blackScreen()
    s.aside("按照贺老的要求，三位弟子抓阄后依次踏上擂台，扣人心弦的比武随之拉开帷幕……")
    s.aside("大弟子阿飞以酒御剑，剑法恣肆汹涌，宛如滔滔江水涌来，令人目不暇接——")
    s.aside("三弟子马三剑法飘忽不定，如顽童嬉戏，令人捉摸不透——")
    s.aside("五弟子小云以琴音入剑，时而宛转悠扬，时而激烈抒怀，令人心旷神怡——")
    s.aside("数番激斗下来，三位关门弟子使劲浑身解数，却始终未能分出胜负，而天色渐晚，眉头紧皱的贺老忽而看向你，似是有了主意——")
    s.lightScreen()
    s.talk(npcs.helao,"可以了，你们暂且停下——")
    --△场上，马三正与阿飞气喘吁吁地停住，连同一旁观战的小云，都不解地望向贺老。
    s.talk(npcs.helao,"你们彼此顾忌同门之谊，实力又不分伯仲，这么打下去，怕是打到天明也没个结果，我倒是有个法子。")
    --△贺老望向你。
    s.talk(npcs.helao,"这位小友远道而来，虽与咱们非亲非故，却让我想起了曾经的一位弟子。")
    s.talk(npcs.moqi,"哦？")
    s.talk(npcs.helao,"这位弟子名叫顾七，他天资极高，不过二十出头就已跻身一流高手之境，实在是咱们鸿雁剑派之福，可惜顾七在那场劫难中遭人暗算，丧生火海，我至今难以释怀……")
    --△贺老长叹一声，摇了摇头，再次看向你。
    s.talk(npcs.helao,"顾七为一弃婴，捡到他那日，地冻天寒河流凝冰，恰好小友的名字里也有个七字，而古渡门前这座湍流不息的永流河，近日也意外结冰，所以，这一切正是天意！")
    s.talk(npcs.moqi,"阁下的意思是？")
    s.talk(npcs.helao,"小友身手不俗，冥冥之中又与咱们鸿雁剑派有宿缘，我想请小友分别与他们三位交手，以测定其实力，不知意下如何？")
    s.talk(npcs.moqi,"兹事体大，怕是不妥吧？")
    --△贺老郑重其事地摇了摇头。
    s.talk(npcs.helao,"不妨，“鸿雁剑派”正因我多年来幽闭试听，不肯和光同尘，终于渐渐衰败至此，我年岁已高，决定抛开已见，将一切交由上天来决定！")
    s.talk(npcs.helao,"还请小友不吝赐教。")
    s.talk(npcs.moqi,"那就恭敬不如从命")
    
    local isWin = s.battle("楚家庄_漫游_黑衣人") --todo:待调整

    if isWin then
        --△贺老赞许地看着你。
        s.talk(npcs.helao,"小友实力不凡，出手点到为止，他们三人输得心服口服，依小友看，谁更适合接任掌门之位？")
        local ret = s.selectTalk(npcs.moqi, "……", {"阿飞", "马三", "小云"})

        if ret == 0 then
            s.talk(npcs.helao,"阿飞在关门弟子里排行老大，性子虽有些憨直，但为人忠贞，剑法精湛，一心光复门派，我一直在考虑之中。")
            flags.gd_main_step = 6
        elseif ret == 1 then
            s.talk(npcs.helao,"马三虽有些随性烂漫，实则引而不发，是个内秀之人，若由他来统领门派，想必是个不错的选择。")
            flags.gd_main_step = 7
        else
            s.talk(npcs.helao,"我这徒儿虽为女流，若论功夫才智却丝毫不输男儿，若由她掌舵，鸿雁剑派或许真有再次发迹的一天。")
            flags.gd_main_step = 8
        end
        s.talk(npcs.helao,"甚好，如此甚好……")
        --（黑屏：比试结束，贺老决定稍后公布结果，众弟子也渐渐散去……）
        s.talk(npcs.helao,"今日事毕，小友的意见对我而言极为宝贵，我将慎重琢磨，随后做出最终决定。")
        s.talk(npcs.helao,"小友既帮了我的忙，于我们又有宿缘，我这里贫寒荒芜，拿不出什么像样的谢礼，这小玩意就送给你做个纪念吧。")
        s.talk(npcs.moqi,"这是……")
        s.talk(npcs.helao,"我在这永流河畔垂钓，曾偶然在鱼腹中得此华贵之物，如今赠予小友，也算是缘分前定。")
        
        itemWidget:addItem(itemData.jiguanlingjian2,1)

        s.finishTask(400110, 3)
    else
        s.talk(npcs.helao,"唔……实在可惜，是小友败了。")
        s.talk(npcs.helao,"小友实力不俗，若调息恢复后再来尝试一回，结果想必会大不相同。")
    end
    
end

function gd_main_story002()
    s.talk(npcs.helao,"小友可打算再试一次？")

    local isWin = s.battle("楚家庄_漫游_黑衣人") --todo:待调整

    if isWin then
        --△贺老赞许地看着你。
        s.talk(npcs.helao,"小友实力不凡，出手点到为止，他们三人输得心服口服，依小友看，谁更适合接任掌门之位？")
        local ret = s.selectTalk(npcs.moqi, "……", {"阿飞", "马三", "小云"})

        if ret == 0 then
            s.talk(npcs.helao,"阿飞在关门弟子里排行老大，性子虽有些憨直，但为人忠贞，剑法精湛，一心光复门派，我一直在考虑之中。")
            flags.gd_main_step = 6
        elseif ret == 1 then
            s.talk(npcs.helao,"马三虽有些随性烂漫，实则引而不发，是个内秀之人，若由他来统领门派，想必是个不错的选择。")
            flags.gd_main_step = 7
        else
            s.talk(npcs.helao,"我这徒儿虽为女流，若论功夫才智却丝毫不输男儿，若由她掌舵，鸿雁剑派或许真有再次发迹的一天。")
            flags.gd_main_step = 8
        end
        s.talk(npcs.helao,"甚好，如此甚好……")
        --（黑屏：比试结束，贺老决定稍后公布结果，众弟子也渐渐散去……）
        s.talk(npcs.helao,"今日事毕，小友的意见对我而言极为宝贵，我将慎重琢磨，随后做出最终决定。")
        s.talk(npcs.helao,"小友既帮了我的忙，于我们又有宿缘，我这里贫寒荒芜，拿不出什么像样的谢礼，这小玩意就送给你做个纪念吧。")
        s.talk(npcs.moqi,"这是……")
        s.talk(npcs.helao,"我在这永流河畔垂钓，曾偶然在鱼腹中得此华贵之物，如今赠予小友，也算是缘分前定。")

        itemWidget:addItem(itemData.jiguanlingjian2,1)

        s.finishTask(400110, 3)
    else
        s.talk(npcs.helao,"唔……实在可惜，是小友败了。")
        s.talk(npcs.helao,"小友实力不俗，若调息恢复后再来尝试一回，结果想必会大不相同。")
    end
end

function talkToMasan()
    if(flags.gd_main_step == 0) then
        talkToMasan000()
    elseif (flags.gd_zhuzi_step == 0) then
        talkToMasan001()
        flags.gd_zhuzi_step = 1
    elseif (flags.gd_zhuzi_step < 3) then
        talkToMasan002()
    elseif (flags.gd_zhuzi_step == 3) then
        talkToMasan003()
    else
        talkToMasan004()
    end
end

function talkToMasan000()
    s.talk(npcs.masan,"一边去，别打扰我马三下棋！")
    s.talk(npcs.masan,"嗯？居然是个外来人？你先去别处逛逛吧，我这会正忙着呢，晚些时候再与你一叙。")
end

function talkToMasan001()
    s.talk(npcs.masan,"这招“滚打包收”使的不错，不过还是有些心浮气躁，且看我如何破解——")
    s.talk(npcs.moqi,"你为何与自己下棋？")
    s.talk(npcs.masan,"渡口就这么几个人，我寻不到对手，只好自娱自乐。")
    s.talk(npcs.moqi,"这么说来，你的棋力一定很厉害了。")
    s.talk(npcs.masan,"那有什么用？下棋是攻心之举，总是与自己勾心斗角，时间久了也会觉得乏味。")
    s.talk(npcs.masan,"听说江湖中流行一种戏法，将竹子制成小人儿形状，操纵者各持一方，相互争斗，似乎颇为有趣。")
    s.talk(npcs.masan,"我这里恰好就有一张图纸，是先前从一位云游货郎手里高价买下的，你快去竹林里寻些上好的绿竹来，咱们这就试上一试。")
    
    s.addTask(400112, 1)
end

function talkToMasan002()
    s.talk(npcs.masan,"找到竹子了吗？")

    local submitItem = s.submitTempItem("请提交竹子！")
    if (submitItem ~= nil) then
        if (submitItem[0].Key == itemData.zhuzi.key) then
            itemWidget:removeItem(itemData.zhuzi,1)
            
            s.talk(npcs.masan,"这些竹子质地不错，你稍等片刻，待我按照图纸将其组装。")
            s.blackScreen()
            s.aside("马三兴冲冲地组装着。")
            s.lightScreen()

            s.talk(npcs.masan,"成了！")
            s.talk(npcs.masan,"你从外面来，见识肯定比我广，这玩意叫什么名字？")
            s.talk(npcs.moqi,"竹节侠。")
            s.talk(npcs.masan,"好名字，以竹为风骨，以侠为义气！")
            s.talk(npcs.masan,"你可愿与我以这竹节侠切磋一番？")

            s.finishTask(400112, 1,2)
            --todo:竹节侠玩法接入
            local zjxWin = s.selectTalk(npcs.moqi, "……", {"失败", "胜利"})

            if zjxWin == 0 then
                s.talk(npcs.masan,"哈哈哈哈哈！手下败将，是我马三赢啦！")
                s.talk(npcs.moqi,"……")
                flags.gd_zhuzi_step = 3
            else
                zjxWinMasan()
            end
        else
            s.aside("你提交错了！这不是竹子。")
        end
    else
        s.aside("你没有提交任何东西！")
    end
end

function talkToMasan003()
    s.talk(npcs.masan,"你可愿与我以这竹节侠切磋一番？")

    --todo:竹节侠玩法接入
    local zjxWin = s.selectTalk(npcs.moqi, "……", {"失败", "胜利"})

    if zjxWin == 0 then
        s.talk(npcs.masan,"哈哈哈哈哈！手下败将，是我马三赢啦！")
        s.talk(npcs.moqi,"……")
        flags.gd_zhuzi_step = 3
    else
        zjxWinMasan()
    end
end

function zjxWinMasan()
    s.talk(npcs.masan,"发明这戏法的真是个妙人，不过方寸之间的争斗，仿佛演绎出高手过招的气势来。")
    s.talk(npcs.masan,"我马三将来若是当上掌门，一定要将普天之下有趣的戏法一股脑都找来，大家整日在这山谷中嬉戏玩耍，岂不快哉？")
    s.talk(npcs.moqi,"你要做掌门？")
    s.talk(npcs.masan,"家师年事已高，正打算从几位关门弟子中选拔下一任掌门，我马三就是候选人之一。")
    s.talk(npcs.moqi,"你若真做了掌门，还是整日想着玩耍？")
    s.talk(npcs.masan,"江湖之争永无休止，不是你死就是我亡，我马三领着大家快快活活过一辈子，难道不是一件美事？")
    s.talk(npcs.moqi,"若门中弟子有宏图远志呢？")
    s.talk(npcs.masan,"放心，想走的留不住，想留的自然会留下，我马三绝不干预。")
    
    flags.gd_main_step = flags.gd_main_step + 1
    flags.gd_zhuzi_step = 4
    s.finishTask(400112, 2)

    if flags.gd_main_step == 4 then
        gd_main_story000()
    end
end

function talkToMasan004()
    s.talk(npcs.masan,"外面的江湖真是精彩，若是将那些有趣之物统统搬到这里，咱们古渡何尝不是一方乐土？")
end


function talkToAfei()
    if(flags.gd_main_step == 0) then
        talkToAfei000()
    elseif (flags.gd_bingxueniang_step == 0) then
        talkToAfei001()
        flags.gd_bingxueniang_step = 1
    elseif (flags.gd_bingxueniang_step < 4) then
        talkToAfei002()
    elseif (flags.gd_bingxueniang_step == 4) then
        talkToAfei003()
    else
        talkToAfei004()
    end
end

function talkToAfei000()
    s.talk(npcs.afei,"金樽清酒斗十千，玉盘珍羞直万钱。世上若有一壶永远喝不完的美酒就好了。")
end

function talkToAfei001()
    s.talk(npcs.afei,"且慢，可否与我讲讲外面的事？")
    s.talk(npcs.moqi,"你是谁？")
    s.talk(npcs.afei,"我叫阿飞，在关门弟子中排行老大。")
    s.talk(npcs.moqi,"你想知道什么？")
    s.talk(npcs.afei,"告诉我，外面的江湖如今是个什么模样，是刀剑纵横，还是机关术当道？")
    local ret = s.selectTalk(npcs.moqi, "……", {"刀剑纵横", "机关术当道"})

    if ret == 0 then
        s.talk(npcs.afei,"哼！休要诓我，守卫弟子都已告诉我了，连你这小小少年都会使用机关术，更别提外面的这偌大的江湖！")
    else
        s.talk(npcs.afei,"这答案我并不意外，守卫弟子都已告诉我了，连你这小小少年都会使用机关术，更别提外面的这偌大的江湖！")
    end
    s.talk(npcs.moqi,"你为何愤愤不平？")
    s.talk(npcs.afei,"酒没啦。")
    s.talk(npcs.moqi,"我看那边还有很多酿好的酒。")
    s.talk(npcs.afei,"那些酒我已喝腻了，听说雪谷中有一种冰雪酿，你若是能弄来，咱们再来好好聊一聊这个江湖。")
    s.talk(npcs.moqi,"冰雪酿？")
    s.talk(npcs.afei,"嗯，这种酒可不多见吶，做法也很奇特，将普通酒浆埋在雪里四个时辰，就成啦。")
    s.talk(npcs.moqi,"雪谷这么大，要埋到何处？")
    s.talk(npcs.afei,"唔……我也是偶然听师父说起，好像是一个有很多树木的偏僻地方。")
    s.talk(npcs.afei,"这坛酒你且拿去，若是能将其制成冰雪酿，咱们再来畅聊一番。")
    
    itemWidget:addItem(itemData.afeijiu,1)
    flags.gd_bingxueniang_step = 1
    s.addTask(400111, 1)
end

function talkToAfei002()
    s.talk(npcs.afei,"嗝——找到我的冰雪酿了吗？")

    local submitItem = s.submitTempItem("请提交冰雪酿！")
    if (submitItem ~= nil) then
        if (submitItem[0].Key == itemData.bingxueniang.key) then
            itemWidget:removeItem(itemData.bingxueniang,1)
            
            s.talk(npcs.afei,"还真让你找到了。")
            s.talk(npcs.afei,"不错，这才是江湖人该喝的酒，我的血忽然热起来了，与我切磋一番吧！")
            s.finishTask(400111, 2,3)
            --todo:战斗接入
            local battleWin = s.selectTalk(npcs.moqi, "……", {"战斗失败", "战斗胜利"})

            if battleWin == 0 then
                s.talk(npcs.afei,"我刚才怎么说的？有了这美酒的加持，剑法自然也凌厉几分，你输给我，不算冤枉！")
                flags.gd_bingxueniang_step = 4
            else
                battleWinAfei()
            end
            
        else
            s.aside("你提交错了！这不是冰雪酿。")
        end
    else
        s.aside("你没有提交任何东西！")
    end
end

function talkToAfei003()
    s.talk(npcs.afei,"你来了？与我切磋一番吧！")

    --todo:战斗接入
    local battleWin = s.selectTalk(npcs.moqi, "……", {"战斗失败", "战斗胜利"})

    if battleWin == 0 then
        s.talk(npcs.afei,"我刚才怎么说的？有了这美酒的加持，剑法自然也凌厉几分，你输给我，不算冤枉！")
        flags.gd_bingxueniang_step = 4
    else
        battleWinAfei()
    end
end

function battleWinAfei()
    s.talk(npcs.afei,"你的机关术确实厉害，不过我阿飞不会轻易认输的！")
    s.talk(npcs.afei,"等我继任掌门之位，一定会带领弟子们重振旗鼓，用刀剑棍棒揍得那些机关术门派满地找牙！")
    s.talk(npcs.moqi,"你？继任掌门？")
    s.talk(npcs.afei,"家师最近正为选拔继任者一事忧虑，虽然目前未有定论，但无论是资历还是武学造诣，最终必然会是我阿飞胜出！")
    s.talk(npcs.moqi,"你为何厌恶机关术？")
    s.talk(npcs.afei,"哼！若不是机关术当道，我们不会流落至此，门派也不会变成如今这副萧条模样。")

    flags.gd_main_step = flags.gd_main_step + 1
    flags.gd_bingxueniang_step = 5

    s.finishTask(400111, 3)
    if flags.gd_main_step == 4 then
        gd_main_story000()
    end
end


function talkToAfei004()
    s.talk(npcs.afei,"机关术不过是些雕虫小技罢了，这江湖的武学正宗，还得是老祖宗传下来的刀枪棍棒！")
end

function talkToXiaoyun()
    if(flags.gd_main_step == 0) then
        talkToXiaoyun000()
    elseif (flags.gd_qinpu_step == 0) then
        talkToXiaoyun001()
        flags.gd_qinpu_step = 1
    elseif (flags.gd_qinpu_step < 2) then
        talkToXiaoyun002()
    else
        talkToXiaoyun003()
    end
end

function talkToXiaoyun000()
    s.talk(npcs.moqi,"（这名少女正专心抚琴，还是先不要打扰她了。）")
end

function talkToXiaoyun001()
    s.talk(npcs.xiaoyun,"少年，可否听我一曲琴音？")
    s.selectTalk(npcs.moqi, "……", {"驻足聆听"})
    --播一小段紊乱的古琴声
    s.talk(npcs.xiaoyun,"这琴音你以为如何？")

    local ret = s.selectTalk(npcs.moqi, "……", {"心旷神怡", "暗藏波澜"})

    if ret == 0 then
        s.talk(npcs.xiaoyun,"是么？其实我的心思并不在这古琴上。")
    else
        s.talk(npcs.xiaoyun,"你很聪慧，一下就听出了我的心思。")
    end
    s.talk(npcs.xiaoyun,"我听守卫弟子说，你的机关术很厉害，他们根本不是你的对手。")
    s.talk(npcs.moqi,"我并非有意与他们为敌。")
    s.talk(npcs.xiaoyun,"你误会了，我并没有丝毫责备，反而很欣赏你在机关术方面的造诣。")
    s.talk(npcs.xiaoyun,"我乃贺老关门弟子，排行第五，平日除了习武与抚琴，没什么别的爱好。")
    s.talk(npcs.moqi,"你的琴声为何有些奇异？")
    s.talk(npcs.xiaoyun,"唉，近来发生一件怪事，我那本由爹娘留下的琴谱忽然不翼而飞，怎么也寻不到踪影，所以……")
    s.talk(npcs.xiaoyun,"家师有令，弟子们若非特殊情况不得离开古渡，或许那琴谱还在附近。")
    
    s.addTask(400113, 1)
end

function talkToXiaoyun002()
    s.talk(npcs.xiaoyun,"或许那琴谱还在附近。")

    local submitItem = s.submitTempItem("请提交琴谱！")
    if (submitItem ~= nil) then
        if (submitItem[0].Key == itemData.qinpu.key) then
            
            itemWidget:removeItem(itemData.qinpu,1)
            
            s.talk(npcs.xiaoyun,"多谢，我已找了许久，不再抱什么希望，竟真让你找来了。")
            s.talk(npcs.xiaoyun,"虽然解决了这个烦恼，但……眼下还有一件大事令我介怀。")
            s.talk(npcs.moqi,"何事？")
            s.talk(npcs.xiaoyun,"家师正在考虑下一任掌门之事，继任者将从我与阿飞、马三中选出，或许受到此事影响，我们三人平日关系甚好，这些日子却很少讲话。")
            s.talk(npcs.moqi,"选谁有什么区别么？")
            s.talk(npcs.xiaoyun,"当然，三人各有其志，无论谁继任掌门，门派的命运都会大不相同。")
            s.talk(npcs.moqi,"你的志向是什么？")
            s.talk(npcs.xiaoyun,"不怕你笑话，我虽自幼修习剑法，但却羡慕你这样的擅长使用机关术之人……若家师择我为下一任掌门，我必会带领众人研习机关术。")
            s.talk(npcs.moqi,"你为何要这么做？")
            s.talk(npcs.xiaoyun,"自“十日雨”后，传统武学日渐凋敝，发展机关术早已是大势所趋，若不想被这江湖淘汰，唯有顺应天道。")
            s.talk(npcs.moqi,"这是我心中所想，还请你不要告诉别人。")

            flags.gd_main_step = flags.gd_main_step + 1
            flags.gd_qinpu_step = 2

            s.finishTask(400113, 1)
            if flags.gd_main_step == 4 then
                gd_main_story000()
            end
        else
            s.aside("你提交错了！这不是琴谱。")
        end
    else
        s.aside("你没有提交任何东西！")
    end
end
function talkToXiaoyun003()
    s.talk(npcs.xiaoyun,"听说从前有一高人善于抚琴，将琴音与武学融为一体，制敌于无形，真想亲眼见识一番。")
end


function eventBamboo()--todo：待补充
    s.talk(npcs.moqi,"这根竹子质地坚韧，色泽青翠，正好作为竹节侠的制作材料。")
    
    local ret = s.selectTalk(npcs.moqi, "……", {"砍竹子", "离开"})

    if ret == 0 then
        s.talk(npcs.moqi,"这些绿竹应该足够了，回去找马三吧。")
        itemWidget:addItem(itemData.zhuzi,1)
    end
end

function eventZhuque()
    if flags.dg_main_zhuque == 0 then
        s.talk(npcs.moqi,"咦？这块石头似乎与众不同，底部竟隐隐传来扑鼻的药香。")
        s.selectTalk(npcs.moqi, "……", {"掀起石块"})--todo:模型需要改成石头

        flags.dg_main_zhuque = 1
        wrappedReloadAllSceneStates()
        itemWidget:addItem(itemData.zhuquexin,1)
    end

end

function eventQinpu()
    s.talk(npcs.moqi,"这似乎是……")
    s.talk(npcs.moqi,"一卷琴谱？")
    
    flags.gd_qinpu_collected = 1
    itemWidget:addItem(itemData.qinpu,1)
    wrappedReloadAllSceneStates()
end

function transportYingdi()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_流亡者营地.unity", "位置/Gudu")
end
