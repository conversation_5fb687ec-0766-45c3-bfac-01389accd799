
--变量设置
---@class flags_漫游_雪谷
local flags = {
    --- "开场剧情"
    open_scene_flag = 0,

    --- "营地主线任务进程"
    yd_main_step = 0,
    
    --- "营地不翼而飞任务进程"
    yd_buyierfei_step = 0,
    
    --- "营地冷彻心扉任务进程"
    yd_lengchexinfei_step = 0,
    
    --- "营地美酒任务进程"
    yd_meijiu_step = 0,

    --- "营地鹿肉任务进程"
    yd_lurou_step = 0,

    --- "营地是否取走机关零件"
    yd_gear_collected = 0,

    --- "营地是否获取过灯油"
    yd_dengyou_get = 0,
    
    --- "营地符号任务进程"
    yd_fuhao_step = 0,
    
    --- "营地豆沙任务进程"
    yd_dousha_step = 0,
    
    --- "营地决斗任务进程"
    yd_juedou_step = 0,

    --- "古渡主线任务进程"
    gd_main_step = 0,

    --- "古渡看守弟子初始剧情进展"
    gd_opening_step = 0,

    --- "古渡取酒任务进程"
    gd_bingxueniang_step = 0,
    
    --- "古渡竹子任务进程"
    gd_zhuzi_step = 0,

    --- "古渡琴谱任务进程"
    gd_qinpu_step = 0,

    --- "是否获取琴谱"
    gd_qinpu_collected = 0,

    --- "雪林主线任务进程"
    xl_main_step = 0,

    --- "雪林冰雪酿是否取走"
    xl_jiu_collected = 0,

    --- "雪林初始剧情是否触发"
    xl_opening_flag = 0,

    --- "雪林干净任务进程"
    xl_ganjing_step = 0,

    --- "雪林肮脏任务进程"
    xl_angzang_step = 0,

    --- "雪林宝贵任务进程"
    xl_baogui_step = 0,

    --- "神道主线任务进程"
    sd_main_step = 0,

    --- "神道的洞口是否开启"
    sd_cave_open = 0,

    --- "神道信手拈来任务进程
    sd_drug_step = 0,
    
    --- "是否采集珠子参
    sd_zhuzishen_collected = 0,

    --- "是否采集回香草
    sd_huixiangcao_collected = 0,
    
    --- "神道说服玄阙宫弟子任务进程"
    sd_xqg_step = 0,

    --- "神道清理山道任务进程"
    sd_clean_step = 0,

    --- "神道清理积雪"
    sd_clean_snow = 0,

    --- "神道清理石碑"
    sd_clean_stele = 0,

    --- "地宫主线任务进程"
    dg_main_step = 0,

    --- "玄恩是否被击杀"
    dg_xuanen_killed = 0,

    --- "地宫初始剧情是否发生"
    dg_opening_flag = 0,

    --- "地宫大门开启进程"
    dg_unlock_step = 0,

    --- "地宫恨之入骨任务进程"
    dg_pantu_step = 0,

    --- "获取苍龙精"
    dg_main_canglong = 0,

    --- "获取白虎骨"
    dg_main_baihu = 0,

    --- "获取朱雀心"
    dg_main_zhuque = 0,

    --- "获取玄武血"
    dg_main_xuanwu = 0,

    --- "合成解药"
    dg_main_jieyao = 0,

    --- "击败熊豪"
    dg_main_xiong = 0,

    --- "检查熊豪上衣"
    dg_xiong_shangyi = 0,

    --- "检查熊豪裤子"
    dg_xiong_kuzi = 0,

    --- "检查熊豪靴子"
    dg_xiong_xuezi = 0,

    --- "击败汪虎"
    dg_main_hu = 0,

    --- "击败苟欢"
    dg_main_mao = 0,
    
    --测试用
    --- "地宫踩中方块"
    dg_cube_down = 0,
}

return flags
