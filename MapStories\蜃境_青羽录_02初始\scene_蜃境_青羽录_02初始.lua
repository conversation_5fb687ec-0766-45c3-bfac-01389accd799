
---@type 蜃境_青羽录_02初始
local context = require("MapStories/蜃境_青羽录_02初始/scene_蜃境_青羽录_02初始_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local assets = context.assets
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_青羽录_初始
local flags = context.flags
--不可省略，用于外部获取flags
newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")
local rapi = require("MapStories/MapStoryCommonTools")

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")
require("MapStories/MapStoryCommonTools")
function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

--复写进入调用
function start()
    refreshMapStates()
    s.readyToStart(true)

    --楚青出现的逻辑
    if flags.chuqing_show == 0 then
        s.setActive(npcs.chuqing_show,false)
    elseif flags.chuqing_show == 1 then
        if s.getCurrentTaskStep("释往断缘") == "finished" then
            chuqingHide()
        else
            s.setActive(npcs.chuqing_show,true)
        end
    elseif flags.chuqing_show == 2 then
        s.setActive(npcs.chuqing_show,false)
    end


    try_execute_newbieGuide()
end

function newbieGuideInLevel()
    require("Newbie/newbie_guide")

    --引导给荆成升级
    if s.hasUnlockMemorySlot("qyl_02") and newbieApi.getNewbieFlag(newbieFlags.tutorRoleLevelup) == 0 then
        wait_close_bonusPanel()
        tutorRoleLevelup()
        tutorItems() --直接一并教了道具，回头改成教吃药
        newbieApi.setNewbieFlag(newbieFlags.tutorRoleLevelup, 1)
        return
    end

    if s.hasUnlockMemorySlot("qyl_04") and newbieApi.getNewbieFlag(newbieFlags.tutorPrayPanel) == 0 then
        wait_close_bonusPanel()
        switchToJianzhichuan() --后续的逻辑在newbie_guide.lua中实现，引导祈福
        return
    end

    if s.hasUnlockMemorySlot("qyl_06") and newbieApi.getNewbieFlag(newbieFlags.tutorBossPanelExp) == 0 then
        wait_close_bonusPanel()
        switchToJianzhichuan() --后续的逻辑在newbie_guide.lua中实现，引导剑林
        return
    end
    
    CheckUnlockSystem()
end

---初始化地图状态
function refreshMapStates()
    local step = get_level_step()

    s.playMusic("ruin")
    reset_all()
    --任务状态变化
    s.unTraceTask(10000001,20)

    --第一次进入场景s
    if step == 0 then
        s.camera(cameras.camera1,true)
        s.setPos(npcs.zhujue,pos.start)
        s.setPos(npcs.jingcheng,pos.jingcheng_start)
        s.setPos(npcs.yunwusheng,pos.yunwusheng_start)

        s.wait(0.5)
        s.readyToStart(true)
        s.moveAsync(npcs.zhujue,pos.start_2,2,true)
        s.moveAsync(npcs.jingcheng,pos.jingcheng_2,2,true)
        s.moveAsync(npcs.yunwusheng,pos.yunwusheng_2,2,true)
        s.wait(2)
        
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落","蜃境_青羽录_破败村落_进入",0,0)
        --s.wait(1)
        s.blackScreen()
        s.camera(cameras.camera1,false)
        --s.setTaskStep("青羽录破败村落","forwardToExplore")
        newbieApi.eventTrack("newbieStep", "step", "enter_scene_qingyulu02")
        
        next_level_step(true)
    elseif step == 1 then
        s.setTaskStep("青羽录破败村落","forwardToExplore")
        level_guide_to(npcs.qyl_02, 0.5)
        s.animState(npcs.qyl_02,"Special_Loop_2",true)
        s.lightScreen()
        --s.setActive(npcs.qyl_01,false)
    elseif step == 2 then
        s.setTaskStep("青羽录破败村落","continueToExplore")
        level_guide_to(npcs.qyl_03, 0.3)
        s.animState(npcs.qyl_03,"Special_Loop_2",true)
        --s.setActive(npcs.qyl_02,false)
    elseif step == 3 then
        s.setTaskStep("青羽录破败村落","talkToJingCheng")
        level_guide_to(npcs.qyl_04, 1.2)
    elseif step == 4 then
        --抽卡引导完回来后先改任务状态
        --if newbieApi.getNewbieFlag(newbieFlags.tutorPrayPanel) == 1 then
            --如果未完成任务，任务状态：finishSWDY
            if flags.chuqing_show == 1 then
                s.setTaskStep("青羽录破败村落","finishSWDY")
                level_guide_to(npcs.chuqing_show, 1)
            else
                s.setTaskStep("青羽录破败村落","explorMoreMemory")
            end
        --end
    elseif step == 5 then
        s.setTaskStep("青羽录破败村落","explorMoreMemory")
        level_guide_to(npcs.qyl_05, 1.2)
    elseif step == 6 then
        s.setTaskStep("青羽录破败村落","explorMoreMemory")
        level_guide_to(npcs.qyl_06, 0.3)
        s.animState(npcs.qyl_06,"Special_Loop_3",true)
    elseif step == 7 then
        s.setTaskStep("青羽录破败村落","explorMoreMemory")
        level_guide_to(npcs.qyl_07, 0.4)
        s.removeGameMapGuide(npcs.qyl_07)
        s.addGameMapGuide(objs.qyl_07_guide)
        s.animState(npcs.qyl_07,"Special_Loop_1",true)
    elseif step == 8 then
        s.setTaskStep("青羽录破败村落","leaveBrokenVillageShenJing")  
        level_guide_to(objs.exit)
    else
        --do nothing
    end

    if(flags.teamTalk1 == 0) then
        s.setActive(objs.talk,true)
    end

    if(flags.girl_interact_step == 0) then
        s.setActive(npcs.zhuomicang_01,true)
        s.setActive(npcs.zhuomicang_02,false)
        s.setActive(npcs.zhuomicang_03,false)
        --s.animState(npcs.zhuomicang_01,"Special_Loop_2",true)
    elseif(flags.girl_interact_step == 1)then
        s.setActive(npcs.zhuomicang_01,false)
        s.setActive(npcs.zhuomicang_03,false)
        s.setActive(npcs.zhuomicang_02,true)
        s.animState(npcs.zhuomicang_02,"BSquat_Loop",true)
    elseif(flags.girl_interact_step == 2)then
        s.setActive(npcs.zhuomicang_03,true)
        s.setActive(npcs.zhuomicang_02,false)
        s.setActive(npcs.zhuomicang_01,false)
        s.animState(npcs.zhuomicang_03,"BAkimbo_Loop",true)
    else
        s.setActive(npcs.zhuomicang_01,false)
        s.setActive(npcs.zhuomicang_02,false)
        s.setActive(npcs.zhuomicang_03,false)
    end
end

function testAnimiate()
end

--默认初始化的地图显隐状态
function reset_all()
    s.setActive(npcs.qyl_02, flags.level_step == 1)
    s.setActive(npcs.qyl_03, flags.level_step == 2)
    s.setActive(npcs.qyl_04, flags.level_step == 3)
    s.setActive(npcs.chuqing_show, flags.level_step == 4)
    s.setActive(npcs.qyl_05, flags.level_step == 5)
    s.setActive(npcs.qyl_06, flags.level_step == 6)
    s.setActive(npcs.qyl_07, flags.level_step == 7)

    s.setActive(objs.talk , false)

    if(flags.in_zone ~= 1)then
        s.setActive(objs.switcher1,true)
        s.setActive(objs.switcher2,false)
    else
        s.setActive(objs.switcher1,false)
        s.setActive(objs.switcher2,true)
    end

    s.setActive(objs.elite_03_area, flags.level_step >= 7)
    s.removeAllGameMapGuides()
end
----------------------OVERRIDE----------------------------------

function exitScene()
    print("离开场景")
    capi.exitMemoryScene()
end

-------------------关卡记忆碎片和战斗实现---------------------
function qyl_02()
    local levelKey = "qyl_02"
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落","蜃境_青羽录_破败村落_渲染",0,0)
    local ret = executeMemSlot(levelKey, 
    function()
        --s.setActive(npcs.qyl_02, false)
        require("Chapters/青羽录").qyl2_01()
    end,
    function(ret)
        next_level_step(true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落","蜃境_青羽录_破败村落_记忆碎片1",0,0)
        
        s.setTaskStep("青羽录破败村落","continueToExplore")
        try_execute_newbieGuide()
        newbieApi.eventTrack("newbieStep", "step", "finish_memoryslot_qyl_02")
        capi.finishMemorySlot(levelKey) --不管输赢都胜利
    end)
end

function qyl_03()
    local levelKey = "qyl_03"
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落","蜃境_青羽录_破败村落_记忆碎片2",0,0)
    executeMemSlot(levelKey, 
    function()
        --s.setActive(npcs.qyl_03, false)
        require("Chapters/青羽录").qyl3_01()
    end, 
    function(ret)
        s.setTaskStep("青羽录破败村落","talkToJingCheng")
        next_level_step(true)
        newbieApi.eventTrack("newbieStep", "step", "finish_memoryslot_qyl_03")
        capi.finishMemorySlot(levelKey) --不管输赢都胜利
    end)
end

function qyl_04()
    local levelKey = "qyl_04"
    executeMemSlot(levelKey, 
    function()
        --s.setActive(npcs.qyl_04, false)
        require("Chapters/青羽录").qyl4_01()
    end, 
    
    function()
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落", "蜃境_青羽录_破败村落_主角习得技能1")

        --临时处理弹出popinfo
        s.popInfo("已习得：<color=yellow>瀚海神爪</color>")

        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落", "蜃境_青羽录_破败村落_主角习得技能2")

        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落", "蜃境_青羽录_破败村落_莫知酉出场前")
        s.blackScreen()
        -- s.camera(cameras.camera3,true)
        -- s.setPos(npcs.mozhiyou_show,pos.mozhiyou_pos1)
        -- s.wait(0.5)
        -- s.lightScreen()
        -- s.move(npcs.mozhiyou_show,pos.mozhiyou_pos2,1,true)
        -- s.blackScreen()
        s.camera(cameras.camera3,false)
        s.camera(cameras.camera4,true)
        s.setPos(npcs.zhujue,pos.zhujue_pos3)
        s.setPos(npcs.jingcheng,pos.jingcheng_pos3)
        s.setPos(npcs.yunwusheng,pos.yunwusheng_pos3)
        s.setPos(npcs.mozhiyou_show,pos.mozhiyou_pos3)
        s.wait(0.5)
    
        --在nani里亮屏
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落", "蜃境_青羽录_破败村落_莫知酉出场")
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落", "蜃境_青羽录_破败村落_莫知酉出场后")
    
        s.blackScreen()
        s.camera(cameras.camera4,false)
        --s.setActive(npcs.mozhiyou,true)
        --s.setPos(npcs.mozhiyou,pos.mozhiyou_pos3)
        --s.setActive(npcs.mozhiyou_show,false)
        s.wait(0.5)
        --s.lightScreen()
        -- s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落","蜃境_青羽录_破败村落_记忆碎片3",0,0)
        -- s.blackScreen()
        -- s.camera(cameras.camera2,true)
        -- s.setActive(npcs.qyl_04, false)
        -- s.setActive(npcs.qyl2_battle_101,true)
        -- s.setPos(npcs.zhujue,pos.zhujue_pos_2)
        -- s.setPos(npcs.jingcheng,pos.jingcheng_pos_2)
        -- s.setPos(npcs.yunwusheng,pos.yunwusheng_pos_2)
        -- s.turnTo(npcs.zhujue,npcs.qyl2_battle_101)
        -- s.turnTo(npcs.qyl2_battle_101,npcs.zhujue)
        -- s.animState(npcs.qyl2_battle_101, "Special_1",true,false,0,true,true,0)
        -- s.setPos(npcs.mozhiyou,pos.mozhiyou_pos4)
        -- s.lightScreen()
        -- s.soloAnimState(npcs.qyl2_battle_101, "Special_3",true)
        -- s.wait(2.5)
        -- s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落","蜃境_青羽录_破败村落_即将交战前夕",0,0)
        -- s.camera(cameras.camera2, false,true,blendHintEnum.EaseInOut,1.5,true,false)
        -- --s.soloAnimState(npcs.qyl2_battle_101, "Special_3",true)
        -- --s.wait(1)
        -- s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落","蜃境_青羽录_破败村落_即将交战",0,0)
        -- s.setTaskStep("青羽录破败村落","exitToQiFu")
        newbieApi.deliveryClientReward("首个侠客", true)
        newbieApi.setFormationHero({"主角","荆成","云舞笙","莫知酉"})
        newbieApi.eventTrack("newbieStep", "step", "finish_memoryslot_qyl_04")
        chuqingJudge()
        next_level_step(true)
        try_execute_newbieGuide()
    end)
end

function chuqingJudge()
    if s.getCurrentTaskStep("释往断缘") == "finished" then
        if s.getCurrentTaskStep("青羽录破败村落") == "talkToJingCheng" then
            s.setTaskStep("青羽录破败村落","explorMoreMemory")
        end
        s.lightScreen()
        next_level_step(true)
        return
    else
        s.setActive(npcs.chuqing_show,true)
        chuqingShow()
    end
end

function testCam()
end

function chuqingShow()
    --楚青相机
    s.camera(cameras.cam_chuqing_near,true,true,blendHintEnum.Cut,0,true,true)
    s.lightScreen()
    s.playSound("sfx","诡异声音")
    s.wait(0.5)
    s.cameraAsync(cameras.cam_chuqing_far,true,true,blendHintEnum.Custom,10,true,true)
    --同步播旁白
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落","蜃境_青羽录_破败村落_楚青出现_释往断缘未完成_1")

    --切回主相机
    s.camera(cameras.cam_main,true,true,blendHintEnum.Cut,0,true,true)
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落","蜃境_青羽录_破败村落_楚青出现_释往断缘未完成_2")

    --设置任务状态
    s.setTaskStep("青羽录破败村落","finishSWDY")

    --设置楚青出现状态为1
    flags.chuqing_show = 1
end

function chuqingHide()
    s.camera(cameras.cam_chuqing_far,true,true,blendHintEnum.Cut,0,true,true)
    --楚青渐隐消失
    s.setDeath(npcs.chuqing_show)
    s.wait(2)

    --切回主相机
    s.camera(cameras.cam_main,true,true,blendHintEnum.Cut,0,true,true)

    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落","蜃境_青羽录_破败村落_楚青出现_释往断缘已完成")
    --设置楚青出现状态为2
    flags.chuqing_show = 2

    next_level_step(true)
end


function qyl_05()
    executeMemSlot("qyl_05", 
    function()
        require("Chapters/青羽录").qyl5_01()
        --s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录3_傀儡.unity")
    end, function()
        newbieApi.eventTrack("newbieStep", "step", "finish_memoryslot_qyl_05")
        next_level_step(true)
        try_execute_newbieGuide()
    end)
end

function qyl_06()
    executeMemSlot("qyl_06", function()
        require("Chapters/青羽录").qyl6_01()
        --s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录3_傀儡.unity")
    end, function()
        newbieApi.eventTrack("newbieStep", "step", "finish_memoryslot_qyl_06")
        next_level_step(true)
        try_execute_newbieGuide()
    end)
end

function qyl_07()
    executeMemSlot("qyl_07", 
    function()
        require("Chapters/青羽录").qyl7_01()
        --s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录3_傀儡.unity")
    end,
    function()
        s.setTaskStep("青羽录破败村落","leaveBrokenVillageShenJing")

        --设置章节完成RPGFlag用于蜃境地图文件on_enter判断
        s.setRpgMapFlag("qyl_brokenVillage_chapter_finished", 1)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落", "蜃境_青羽录_02初始_完成")
        newbieApi.eventTrack("newbieStep", "step", "finish_memoryslot_qyl_07")
        next_level_step(true)
        try_execute_newbieGuide()
    end)
end

function enemey_01()
    executeMemSlot("qyl2_battle_101",
    nil,
    function()
        next_level_step(true)
        try_execute_newbieGuide()
    end)
end

function test()
    roamMapBattle("qyl2_battle_101",function(rst)
        if(rst)then
            s.talk("","战斗胜利")
        else
            s.talk("","战斗失败")
        end
    end,function()
        s.talk("","战斗取消")
    end)
end

function blendSwitch(id)
    if(id == "1") then
        s.setActive(objs.switcher1,false)
        s.setActive(objs.switcher2,true)
        flags.in_zone = 1
    else
        s.setActive(objs.switcher1,true)
        s.setActive(objs.switcher2,false)
        flags.in_zone = 2
    end
    s.wait(0.5)

    if(id == "1" and flags.girl_interact_step == 0)then
        s.wait(0.5)
        s.soloAnimState(npcs.zhuomicang_01,"BLaugh",true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落", "蜃境_青羽录_02初始_捉迷藏交互1提前渲染")
    end
end

function girl_interact()
    --s.camera(cameras.hard_camera1, true, false)
    s.camera(cameras.hard_camera1, true, true, blendHintEnum.EaseInOut, 1, true, false)
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落", "蜃境_青羽录_02初始_捉迷藏交互1前")
    s.camera(cameras.hard_camera1, false, true, blendHintEnum.EaseInOut, 1, true, false)
    s.setActive(npcs.zhuomicang_01,false)
    s.setActive(npcs.zhuomicang_02,true)
    flags.girl_interact_step = 1
end

function girl_interact2()
    --s.camera(cameras.hard_camera1, true, false)
    --s.camera(cameras.hard_camera1, true, true, blendHintEnum.EaseInOut, 1, true, false)
    s.soloAnimState(npcs.zhuomicang_02,"BShock_Loop",true)
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落", "蜃境_青羽录_02初始_捉迷藏交互2")
    --s.camera(cameras.hard_camera1, false, true, blendHintEnum.EaseInOut, 1, true, false)
    s.setActive(npcs.zhuomicang_02,false)
    s.setActive(npcs.zhuomicang_03,true)
    flags.girl_interact_step = 2
end

function girl_interact3()
    --s.camera(cameras.hard_camera1, true, false)
    --s.camera(cameras.hard_camera1, true, true, blendHintEnum.EaseInOut, 1, true, false)
    s.soloAnimState(npcs.zhuomicang_03,"Special_2",true)
    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落", "蜃境_青羽录_02初始_捉迷藏交互3")
    --s.camera(cameras.hard_camera1, false, true, blendHintEnum.EaseInOut, 1, true, false)
    s.setActive(npcs.zhuomicang_03,false)
    local capi = require("ChapterApi")
    capi.finishMemorySlot("qyl_question_zhuomicang",false)
    flags.girl_interact_step = 3
end

function teamTalk1()
    flags.teamTalk1 = 1
    s.setActive(objs.talk,false)
    s.appendAsyncAside(npcs.jingcheng, 1,"这个地方……前方那个虚影……","","惊讶")
    s.wait(2.5)
    s.appendAsyncAside(npcs.jingcheng, 1,"这个姑娘是？","","回忆")
    --s.wait(2.5)
end

---------------测试用------------------------

function cgtest()
    s.clearAllMemorySlotsRecord()
    set_level_step(0)
    start()
    s.setNewbieFlag(newbieFlags.tutorRoleLevelup, 0)
    s.setNewbieFlag(newbieFlags.tutorPrayPanel, 0)
end

function cgtest2()
    --s.setNewbieFlags("tutorRoleLevelup", 0)
    --s.setNewbieFlags("tutorPrayPanel", 0)
    --print(newbieFlags.tutorRoleLevelup)
    --print(newbieFlags.tutorPrayPanel)

    s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_破败村落", "蜃境_青羽录_02初始_完成")
end

function testOpenChest()
    s.openChest("","farmfield_ZeiKouShanDong_Boss_repeat", true)
end

function testChangeMapStory()
    -- 编辑窗口中有一个通关当前漫游可以跳过方便测试
    s.changeMapStory("主线_青羽录5")
    -- 只能放在一个逻辑最后，返回之后再通过额外的逻辑判断是否通关这个mapstory
end


--战斗表现
function enemy_02(id)
    s.move(npcs.enemy_02, pos.enemy_02_pos,5,true)
    s.soloAnimState(npcs.enemy_02, "BAssault_Start",true)
    s.wait(1)
    s.setActive(objs.enemy_02_area, false)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end


function enemy_elite(id)
    s.camera(cameras.cam_shensheshou, true, true, blendHintEnum.EaseInOut, 1, true, false)
    s.aside("这里似乎视野开阔，可以施展弓术解决阁楼上的伥怪")
    local rapi = require("RpgMapApi")
    local ret = rapi.submitStrategyCard("选择弓术策略卡")
    
    if (ret ~= nil) then
        -- 检查是否使用了正确的策略卡
        if (ret.getSubmitStrategyCardItem(0).Key == "神射手") then
            s.playTimeline(assets.shensheshou)
            require("MapStories/MapStoryCommonTools").roamMapBattle(id)
        else
            s.aside("好像没什么用。")
        end
    else
        s.aside("没做好准备，稍后再来也不迟。")
    end
end
