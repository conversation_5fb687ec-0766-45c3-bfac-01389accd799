local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.ta_event_track("PlayerStartStory", "Step", "BattleTutor_Step1")
    battle.HideCardLevel = true
    bcapi.disable_auto_battle()
    bcapi.disable_speed_up()
    bcapi.disable_dart()
    -- bcapi.hide_ui("技能区")
    -- bcapi.hide_ui("怒气条")
    StartBattle()
end

function start()
    Talk1()
    bcapi.add_timer_trigger(10, End)
end

function StartBattle()
    bcapi.async_call(function()
        CreateRoles()
        bcapi.show_env("开场蜃境_战斗_Env")
        bcapi.play_film("开场蜃境_战斗", 16, true)
        bcapi.manual_start()
    end)
end

function CreateRoles()
    bcapi.async_call(function()
        -- 创建玩家角色
        bcapi.create_join_role_with_card("荆成", 30, team, bcapi.Vector2(0.88, -1), 0)
        bcapi.create_join_role_with_card("云舞笙", 30, team, bcapi.Vector2(-0.91, -0.53), 0)

        -- 创建敌人
        local enemy = bcapi.create_join_role("剧情_珍娘_开场1", 20, eTeam, bcapi.Vector2(7.83, 0), 0)
        local jingCheng = bcapi.get_player_role("荆成")
        enemy.Movement:LookAt(jingCheng.Transform.Position)

        -- 创建蝎子
        CreateScorpion(3.56, 3)
        CreateScorpion(4.33, -0.85)
        CreateScorpion(4.33, -0.85)
        CreateScorpion(1.14, -3.83)
        CreateScorpion(-3.25, -2.12)
        CreateScorpion(-4.41, 0.02)
        CreateScorpion(-5, 4.82)
        CreateScorpion(5.71, 2.15)
        CreateScorpion(5.6, -1.78)
        CreateScorpion(3.4, -2.6)
        CreateScorpion(-1.52, -3.13)
        CreateScorpion(-5.91, -1.12)
        CreateScorpion(-5.29, 2.48)
        CreateScorpion(5.73, 4)
    end)
end

function CreateScorpion(posX, posY)
    bcapi.async_call(function()
        local newScorpion = bcapi.create_and_summon_role("剧情_珍娘_开场蝎子", 10, eTeam, bcapi.Vector2(posX, posY), 0)
        local jingCheng = bcapi.get_player_role("荆成")
        newScorpion.Movement:LookAt(jingCheng.Transform.Position)
    end)
end

function Talk1()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        local team = bcapi.get_team(0)
        -- team.NuqiSystem.CurNuqi = 10
        bcapi.talk("云舞笙", "荆大哥，<color=red>阁主</color>怎么还没醒过来？")
        bcapi.talk("云舞笙", "这<color=red>蜃境幻影</color>越来越多了！")
        bcapi.talk("荆成", "唔~药前辈说北斗七见二隐之时，阁主就会苏醒，再坚持一会吧！")

        -- local playerCard1 = bcapi.get_player_unique_card("荆成");
        -- local playerCard2 = bcapi.get_player_unique_card("云舞笙");
        -- team.CardSystem:CostCard(playerCard1)
        -- team.CardSystem:CostCard(playerCard2)
        -- team.NuqiSystem.CurNuqi = 10
        -- bcapi.wait(0.1)
        -- bcapi.show_ui("技能区")
        -- bcapi.show_ui("怒气条")
        bcapi.resume_and_show_ui()
    end)
end

function End()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.aside("剑川之主……",1)
        bcapi.aside("醒来吧！",1)
        bcapi.play_film("开场蜃境_唤醒", 2, false)
        bcapi.win()
    end)
end