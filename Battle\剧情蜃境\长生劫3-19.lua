local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    CreateRoles()
end

function start()
    Talk()
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("椿岁", 79, team, bcapi.Vector2(-2, 0), 0)
    end)
end

function Talk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("椿岁", "你的亲姐姐如此敬爱那个人，他却信了江湖骗子胡诌的故事。")
        bcapi.talk("椿岁", "一夕之间，贤德淑慧的宠妃就沦为了人人喊打的妖物。")
        bcapi.talk("狂刀", "你不配提起她。")
        bcapi.talk("椿岁", "难道你这个为仇人卖命的人就配了吗？！")
        bcapi.talk("狂刀", "什么都不舍的放下，就什么都拿不住……那位大人舍弃了作为丈夫，作为父亲的情感……")
        bcapi.talk("狂刀", "他才能成为至高无上的！真正的王！！！！")
        bcapi.resume_and_show_ui()
    end)
end
