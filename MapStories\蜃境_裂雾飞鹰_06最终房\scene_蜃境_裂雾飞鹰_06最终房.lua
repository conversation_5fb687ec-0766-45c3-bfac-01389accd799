
---@type 蜃境_裂雾飞鹰_06最终房
local context = require("MapStories/蜃境_裂雾飞鹰_06最终房/scene_蜃境_裂雾飞鹰_06最终房_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger
local rapi = require("RpgMapApi")
s.loadFlags(context.flags)
---@type flags_蜃境_裂雾飞鹰_06最终房
local flags = context.flags
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")


function test()
    s.setActive(objs.shijian,true)
end

function get_level_step()
    return flags.level_step
end

function set_level_step(value)
    flags.level_step = value
end

--每次载入调用
function start()
    if flags.shijian_open == 0 then
        s.setActive(objs.shijian,false)
    else
        s.setActive(objs.shijian,true)
        s.setActive(objs.shijian_interact,true)
    end
    s.readyToStart(true)
    refreshMapStates()
    try_execute_newbieGuide()

    s.setPos(npcs.zhujue,pos.zhujue_start_pos)
end

function try_execute_newbieGuide(...)
    s.nextStep("newbieGuideInLevel")
end

function newbieGuideInLevel()end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有NPC
    s.setActive(npcs.Boss_SS,false)
    -- s.setActive(npcs.yuchi,false)
    
    -- --控制npc显隐
    -- if s.getCurrentTaskStep("裂雾飞鹰最终房") == "fightWithSS" then
    --     s.setActive(npcs.yuchi,true)
    --     --s.setActive(npcs.Boss_SS,true)
    -- end

    s.removeAllGameMapGuides()
end

function refreshMapStates()
    local step = get_level_step()

    --任务状态激活
    local taskStep = "fightWithSS"
    if (step >= 0) and (step < 2) then
        taskStep = "fightWithSS"
    elseif (step == 2) then
        taskStep = "leaveBossRoom"
    end
    s.setTaskStep("裂雾飞鹰最终房", taskStep)

    reset_all()

    if step == 0 then
        --第一次进入地图
        if s.isMale() then
            s.playTimeline(assets.BossShow_nanzhu,false)
        else
            s.playTimeline(assets.BossShow_nvzhu,false)
        end

        s.setPos(npcs.zhujue,pos.zhujue_start_pos)
        s.setActive(npcs.Boss_SS,true)

        s.lightScreen()
        s.setTaskStep("裂雾飞鹰最终房","fightWithSS")
        next_level_step(true)
    
    elseif step == 1 then
        s.setTaskStep("裂雾飞鹰最终房","fightWithSS")
        level_guide_to(npcs.Boss_SS)
    else
        s.setTaskStep("裂雾飞鹰最终房","leaveBossRoom")
        --do nothing
        level_guide_to(objs.exit)
    end
end

--第一次进入地图
--[[function first_time_into_map()
    -- 填写逻辑
    if isMale() then
        s.playTimeline(assets.BossShow_nanzhu,false)   
    else
        s.playTimeline(assets.BossShow_nvzhu,false)   
    end

    s.lightScreen()
end--]]

function boss()
    executeMemSlot("lwfy6_battle_306",
    nil,
    function()
        s.setActive(npcs.Boss_SS,false)
        s.playTimeline(assets.shijian,false)
        s.setActive(objs.shijian,true)
        s.setActive(objs.shijian_interact,true)
        flags.shijian_open = 1
        s.lightScreen()

        s.setRpgMapFlag("luMingTianXia_rpgMapEnterance_unlock",1)
        s.setRpgMapFlag("yuChi_chapter_finish",1)

        next_level_step(true)
    end,function()
            
    end,function()
            defaultLose()
    end,nil,true)

end

function boss2()
    rapi.showBossSoloPanel("1004")
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("裂雾飞鹰")
end