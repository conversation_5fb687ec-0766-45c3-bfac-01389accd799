local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    
end

function start()
    bcapi.async_call(function()
        bcapi.create_and_summon_role("世界_诺迭_友方版", 40, team, bcapi.Vector2(-7, 0), 0)
        bcapi.add_timer_trigger(0.1, Talk)
    end)
end

function Talk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("白猿", "吼！！！！")
        bcapi.talk("灵湫", "什么？他伤害了你的朋友？")
        bcapi.talk("诺迭", "孽畜，挡我者死！")
        -- bcapi.show_pop_info("战斗时间已被重设为45秒！",4)
        -- bcapi.reset_limit_time(46)
        bcapi.resume_and_show_ui()
    end)
end