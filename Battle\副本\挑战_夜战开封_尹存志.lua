local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle

function init()
    local time = 903
    if(bcapi.get_team_dict_has_key(0, "战斗时间"))then
        time = bcapi.get_team_dict_int_value(0, "战斗时间", time) + 3
    end
    bcapi.reset_limit_time(time)
end

function start()
    bcapi.async_call(function()
        local isSummon = bcapi.get_team_dict_int_value(0, "是否召唤", 0)
        local isCard = bcapi.get_team_dict_int_value(0, "是否策略卡", 0)

        battle.ForceNotEndBattle = true
        if(isSummon == 1)then
            bcapi.create_and_summon_role("副本_夜战开封_燕路重", 65, bcapi.get_team(0), bcapi.Vector2(-6, 0), 0.1,-1,1,1,1,1,1,"")
        end
        if(isCard == 1)then
            bcapi.create_and_summon_role("副本_夜战开封_开封士兵", 55, bcapi.get_team(0), bcapi.Vector2(-6, 2), 0.1,-1,1,1,1,1,1,"")
            bcapi.create_and_summon_role("副本_夜战开封_开封士兵", 55, bcapi.get_team(0), bcapi.Vector2(-6, -2), 0.1,-1,1,1,1,1,1,"")
        end
        bcapi.add_timer_trigger(2, Talk)
    end)
end

function Talk()
    bcapi.async_call(function()
        local eTeam = bcapi.get_team(1)
        bcapi.pause_and_hide_ui()
        bcapi.talk("尹存志#副本_夜战开封_尹存志", "一气化三清，去！")
        bcapi.resume_and_show_ui()

        bcapi.wait(0.2)
        local role1 = bcapi.create_join_role("副本_夜战开封_尹存志_剑", 63, eTeam, bcapi.Vector2(6, 2), 0.1)
        local role2 = bcapi.create_join_role("副本_夜战开封_尹存志_气", 63, eTeam, bcapi.Vector2(-6, -1), 0.1)
        local role3 = bcapi.create_join_role("副本_夜战开封_尹存志_速", 63, eTeam, bcapi.Vector2(6, -4), 0.1)

        role1.Stat:SetBlock(2,true)
        role2.Stat:SetBlock(2,true)
        role3.Stat:SetBlock(2,true)

        bcapi.wait(0.5)
        bcapi.pause_and_hide_ui()
        bcapi.talk("主角#主角", "？！他竟然一道剑气化为三道人影……而且这三道人影上均传来了杀气！")
        bcapi.resume_and_show_ui()

        bcapi.add_condition_trigger("[%c->enemy:副本_夜战开封_尹存志_剑%][<=]0&[%c->flag:阵亡数量%][<]2", 2, JianHint)
        bcapi.add_condition_trigger("[%c->enemy:副本_夜战开封_尹存志_气%][<=]0&[%c->flag:阵亡数量%][<]2", 2, QiHint)
        bcapi.add_condition_trigger("[%c->enemy:副本_夜战开封_尹存志_速%][<=]0&[%c->flag:阵亡数量%][<]2", 2, SuHint)
        battle.ForceNotEndBattle = false
    end)
end

function JianHint()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("尹存志#副本_夜战开封_尹存志", "好功夫……不过这样一来，其他的分身怕是都要<color=red>获得凌厉的剑意了</color>！")

        local conditionRole = bcapi.get_index_role(0,0)
        conditionRole.Stat.BattleFlagDic:set_Item("阵亡数量", tostring(bcapi.get_dict_int_value(conditionRole.Stat.BattleFlagDic,"阵亡数量")+1))
        
        bcapi.resume_and_show_ui()
        bcapi.show_pop_info("尹存志的分身·剑阵亡，其他分身恢复满状态并获得了强化！",4)
    end)
end

function QiHint()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("尹存志#副本_夜战开封_尹存志", "好功夫……不过这样一来，其他的分身怕是都要<color=red>获得混元的气劲了</color>！")
    
        local conditionRole = bcapi.get_index_role(0,0)
        conditionRole.Stat.BattleFlagDic:set_Item("阵亡数量", tostring(bcapi.get_dict_int_value(conditionRole.Stat.BattleFlagDic,"阵亡数量")+1))

        bcapi.resume_and_show_ui()
        bcapi.show_pop_info("尹存志的分身·气阵亡，其他分身恢复满状态并获得了强化！",4)
    end)
end

function SuHint()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("尹存志#副本_夜战开封_尹存志", "好功夫……不过这样一来，其他的分身怕是都要<color=red>获得迅捷的速度了</color>！")
    
        local conditionRole = bcapi.get_index_role(0,0)
        conditionRole.Stat.BattleFlagDic:set_Item("阵亡数量", tostring(bcapi.get_dict_int_value(conditionRole.Stat.BattleFlagDic,"阵亡数量")+1))

        bcapi.resume_and_show_ui()
        bcapi.show_pop_info("尹存志的分身·速阵亡，其他分身恢复满状态并获得了强化！",4)
    end)
end
