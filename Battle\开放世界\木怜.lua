local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)
local x = 0
local y = 0

function init()
end

function start()
    bcapi.add_timer_trigger(0, talkStart)
    bcapi.add_condition_trigger("[%enemy:世界_木怜->flag:二阶段%][>]0", 0, enemyEnhance)
end

function talkStart()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("主角", "你到底是谁？不对，应该问，你们究竟是谁。")
        bcapi.talk("世界_木怜#世界_木怜", "早在那个“楚家庄”时，就有人告诉过你了。")
        bcapi.talk("主角", "你们知道我要来？")
        bcapi.talk("世界_木怜#世界_木怜", "不知道，但我们这么多年一直在等你。")
        bcapi.talk("主角", "为何？")
        bcapi.talk("世界_木怜#世界_木怜", "为了天道不改。")
        bcapi.talk("世界_木怜#世界_木怜", "但我不是很明白，我只明白，我在等你，而我现在等到了。")
        bcapi.talk("世界_木怜#世界_木怜", "出剑吧，恩人。")
        bcapi.resume_and_show_ui()
    end)
end

function enemyEnhance()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("世界_木怜#世界_木怜", "恩人可曾听过目连救母的故事。")
        bcapi.talk("世界_木怜#世界_木怜", "目连费尽心力让母亲转世为狗，再历尽艰辛，才让一条狗又变作了人。")
        bcapi.talk("世界_木怜#世界_木怜", "可谁又知道，下一世本就该为狗，再下一世本就该为人。")
        bcapi.talk("世界_木怜#世界_木怜", "你以为你在逆天改命，实际上不过是天命所归，顺应而为，跟你做与不做，没有关系。")
        bcapi.talk("世界_木怜#世界_木怜", "呵呵呵哈哈哈……做什么都是徒劳……做什么都改不了！")

        local Boss = bcapi.get_role(1, "世界_木怜")
        bcapi.resume_and_show_ui()
        bcapi.use_skill_to_role(Boss,"世界_木怜_变身",Boss,1)
        --bcapi.resume_and_show_ui()
        bcapi.add_condition_trigger("[%enemy:世界_木怜二阶段->flag:二阶段大招%][=]1", 0, enemySuperSkill)
    end)
end

function enemySuperSkill()
    bcapi.async_call(function()
        --bcapi.wait(0.5)
        bcapi.pause_and_hide_ui()
        bcapi.talk("世界_木怜二阶段#世界_木怜二阶段", "我等过无数人，但无数人都好似不值我等，唯独你，稍显例外。")
        bcapi.talk("世界_木怜二阶段#世界_木怜二阶段", "地狱，饿鬼，畜生道，皆在其道，或悲或喜，或乐或苦，都该受着。")
        bcapi.talk("世界_木怜二阶段#世界_木怜二阶段", "听我唱这一曲吧，恩人。曲罢方知，谁主轮回。")
        local Boss = bcapi.get_role(1, "世界_木怜二阶段")
        bcapi.use_skill_to_role(Boss,"世界_木怜二阶段_技能1",Boss,1)
        --Boss.Stat.BattleFlagDic:set_Item("可以变身", "1")
        bcapi.resume_and_show_ui()
    end)
end