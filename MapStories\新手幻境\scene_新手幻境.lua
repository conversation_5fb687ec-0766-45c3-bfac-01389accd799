
---@type 新手幻境
local context = require("MapStories/新手幻境/scene_新手幻境_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

---@type NewbieApi
local newbieApi = require("NewbieApi")

s.loadFlags(context.flags)
---@type flags_新手幻境
local flags = context.flags --[[@as flags_新手幻境]]
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
--每次载入调用
function start()
    
    local ui = CS.UnityEngine.GameObject.Find("UIRoot/GameMapHUD")
    if(ui ~= nil) then
        ui.transform:Find("GameObject/StoryName").gameObject:SetActive(false)
        ui.transform:Find("GameObject/Buttons").gameObject:SetActive(false)
    end
    
    s.setPlayerMoveSpeed(2)
    s.setPos(npcs.jingcheng, pos.start)
    s.readyToStart()
    s.talk(npcs.jingcheng, "这……是哪？")
    s.talk(npcs.jingcheng, "远处……的那个光亮是？")
    s.aside("快……过来……")
    s.aside("提示：使用虚拟摇杆控制角色移动。滑动屏幕控制视角。")
end


function step1()
    s.setActive(objs.obj1,false)
    --s.aside("快，坚持住。勿有杂念。")
    --s.aside("此时为紧要关头，切勿分心。")
    s.appendAsyncAside("?", 0, "快，坚持住。勿有杂念。")
    s.wait(1.5)
    s.appendAsyncAside("?", 1, "此时为紧要关头，切勿分心。")
    s.wait(1.5)
end

function step2()
    s.setActive(objs.obj2,false)
    newbieApi.showStartGuide("醒醒", 1, true)
    newbieApi.showStartGuide("快醒醒", 2, false)
    local rst = s.selectTalk("", "……", {
        "楚青？",
        "莫大师？"
    })
    newbieApi.showStartGuide("不要被记忆里的碎影束缚", 1, true)
    newbieApi.showStartGuide("那是荆成的记忆", 2, false)
    newbieApi.showStartGuide("你不是他", 1, true)
    newbieApi.showStartGuide("快想起你的名字", 2, false)
    newbieApi.showStartGuide("你的名字……", 3, false)
    s.selectTalk("", "", {
        newbieApi.getPlayerName(),
    })
    newbieApi.showStartGuide(newbieApi.getPlayerName(), 1, true)
    newbieApi.showStartGuide("那些破碎的记忆还在等着你", 2, false)
    newbieApi.showStartGuide("快回来", 3, true)
    newbieApi.showStartGuide("快回到————", 4, false)
    newbieApi.showStartGuide("剑之川", 3, true)
    newbieApi.closeStartGuide()
    s.finishInfiniteStory(true);
end