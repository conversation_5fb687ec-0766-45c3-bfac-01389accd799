local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)
local flag = 1
local reviveFlag = 0
local hasWin = 0

function init()
    bcapi.ta_event_track("PlayerStartStory", "Step", "BattleTutor_Step5")
    battle.HideCardLevel = true

    bcapi.disable_auto_battle()
    bcapi.disable_speed_up()
    -- bcapi.disable_dart()

    bcapi.hide_ui("自动按钮")
    -- bcapi.hide_ui("身法按钮")
    bcapi.hide_ui("疗伤")
    -- bcapi.hide_ui("技能区")
    -- bcapi.hide_ui("怒气条")

    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        -- 创建玩家角色
        bcapi.create_join_role_with_card("荆成", 8, team, bcapi.Vector2(-1, 0), 0)
        --给荆成加点攻速
        JingCheng = bcapi.get_player_role("荆成")
        ---@diagnostic disable-next-line
        JingCheng.Buff:AddBuff("通用_教学关_加攻速", 300, 1, 1, JingCheng)
        ---@diagnostic disable-next-line
        JingCheng.Buff:AddBuff("通用_阻塞", 1.3, 1, 1, JingCheng)
        -- 创建敌人
        -- bcapi.create_join_role("剧情_坏楚青", 3, eTeam, bcapi.Vector2(2, 0), 0)
        
        bcapi.wait(0.1)
    end)
end

function start()
    Talk1()
end

function Talk1()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("楚青#剧情_坏楚青#开心", "荆少侠，勿要妄动仁心，勿要轻信他人。")
        bcapi.talk("楚青#剧情_坏楚青#开心", "这就是我给你的初入江湖第一课。")
        bcapi.talk("荆成#荆成#严肃", "……")
        bcapi.resume_and_show_ui()
        
        bcapi.add_condition_trigger("[%c->hp%][<]500", 1, FriendJoin)
        bcapi.add_condition_trigger("[%enemy:剧情_坏楚青->enemy:荆成%][<]1", 1, JinChengDie)
        bcapi.add_condition_trigger("[%enemy:剧情_坏楚青->hp_pct%][<]0.35&[%enemy:剧情_坏楚青->flag:已经释放召唤%][=]0", 1, ChuQingUseSkill)
        bcapi.add_condition_trigger("[%enemy:剧情_坏楚青->hp_pct%][<=]0.10", 0, triggerWin)
    end)
end

function FriendJoin()
    bcapi.async_call(function()
    --设置0.1为了播放登场动画，0不播放登场动画
        bcapi.create_join_role_with_card("云舞笙", 8, team, bcapi.Vector2(-6, 0), 0.1)
        bcapi.create_join_role_with_card("莫知酉", 8, team, bcapi.Vector2(-2, -2), 0.1)
        bcapi.block_wait(0.5)
        bcapi.pause_and_hide_ui()
        bcapi.talk("莫知酉#莫知酉#玩味笑", "总算找到你了，楚姑娘。")
        bcapi.talk("云舞笙#云舞笙", "这位小兄弟，这妖女绝非简单人物，我二人受楚家庄庄主之托前来除祸，也定会护你周全。")
        bcapi.talk("楚青#剧情_坏楚青#严肃", "除祸？谁是祸患还不一定呢！")
        bcapi.resume_and_show_ui()
        --给云舞笙加点攻速
        YunWusheng = bcapi.get_player_role("云舞笙")
        YunWusheng.Buff:AddBuff("通用_教学关_加攻速", 300, 1, 1, YunWusheng)
        --给莫知酉加点攻速
        MoZhiyou = bcapi.get_player_role("莫知酉")
        MoZhiyou.Buff:AddBuff("通用_教学关_加攻速", 300, 1, 1, MoZhiyou)
        bcapi.block_wait(0.3)

        bcapi.show_role_skill_card_desc_panel(1, 0, "云舞笙", "云舞笙_绝招")
        bcapi.show_role_skill_card_desc_panel(1, 0, "莫知酉", "莫知酉_绝招")
    end)
end

function JinChengDie()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        flag = 2
        bcapi.talk("荆成#荆成#受伤", "唔……")
        bcapi.talk("莫知酉#莫知酉", "小兄弟，无妨，你先下去休息一阵吧。")
        bcapi.show_hint_desc_panel("复活", "疗伤与退场", "当角色被击倒时，会消耗<color=red>疗伤</color>次数，退场进行休息。当角色在场下时，会快速恢复生命值，并在5秒后可以再次登场。", "知道了")
        bcapi.resume_and_show_ui()

        bcapi.show_ui("疗伤")
        bcapi.pause()
        bcapi.show_ui_guide("疗伤", "单场战斗中，疗伤的次数有限，千万注意不要让侠客被击倒过多次了！", 400, -300)

        bcapi.pause_and_hide_ui()
        bcapi.talk("楚青#剧情_坏楚青#开心", "哼，少了他，你俩能行么？")
        bcapi.talk("莫知酉#莫知酉#玩味笑", "呵呵……行与不行，楚姑娘尽管和你的手下们一起攻过来便是。")
        
        --local enemyRole = bcapi.get_role(1, "剧情_坏楚青")
        --让楚青伤害变低一些
        --enemyRole.Buff:AddBuff("静默攻击弱化", 10, 40, 1, enemyRole,false, false, true)

        bcapi.add_strategy_card("梧叶舞秋风", 1, team)
        bcapi.add_strategy_card("血沸剑魂", 1, team)
        bcapi.add_strategy_card("大藏心经", 1, team)
        bcapi.add_strategy_card("光明圣火", 13, team)
        bcapi.resume_and_show_ui()
        bcapi.wait(5)
        if (battle ~= nil) then
            RoleRevive()
        end
    end)
end

function RoleRevive()
    bcapi.async_call(function()
        if (flag == 2) then
            bcapi.wait(0.3)
            bcapi.pause_and_hide_ui()
            bcapi.talk("荆成#荆成#严肃", "(差不多了，是时候重返战斗了。)")
            bcapi.resume_and_show_ui()
            bcapi.show_ui("角色技区")
            bcapi.hide_ui("策略技区")
            bcapi.show_join_role_guide("荆成", "将荆成拖至场上，令其重新加入战斗吧！")
            bcapi.wait(1)
            bcapi.show_ui("策略技区")
            bcapi.add_condition_trigger("[%enemy:剧情_坏楚青->enemy:荆成%][>=]1", 0, RoleReviveTalk)
        end
    end)
end

function RoleReviveTalk()
    bcapi.async_call(function()
        local jingCheng = bcapi.get_role(0, "荆成")
        if (jingCheng.Stat.RoleState == CS.H2.BattleRoleState.IsFighting and reviveFlag == 0) then
            reviveFlag = 1
            bcapi.wait(1.2)
            bcapi.pause_and_hide_ui()
            bcapi.talk("楚青#剧情_坏楚青#严肃", "哼，这小子还真是顽强，你这么快就恢复好了？")
            bcapi.talk("荆成#荆成", "承蒙楚姑娘费心，在下恢复得不错。")
            bcapi.talk("莫知酉#莫知酉#大笑", "呵呵，小兄弟果真天赋异禀，我们一起先拿下她吧。")
            bcapi.resume_and_show_ui()
        end
    end)
end

function ChuQingUseSkill()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("楚青#剧情_坏楚青#愤怒", "看不出你们还挺厉害，让手下陪你们玩玩。")
        local Boss = bcapi.get_role(1, "剧情_坏楚青")
        Boss.Buff:AddBuff("静默攻击弱化", 300, 40, 1, Boss)
        Boss.Stat.BattleFlagDic:set_Item("释放召唤", "1")
        bcapi.resume_and_show_ui()
        bcapi.wait(1)
        bcapi.set_all_card_cd(team, 0)
        team.NuqiSystem.CurNuqi = 10
        bcapi.pause_and_hide_ui()
        bcapi.talk("旁白", "楚青此时将回到后场休息，无法被攻击。请灵活运用策略技以及云舞笙、莫知酉的绝招，优先应对她手下。")
        bcapi.resume_and_show_ui()
    end)
end
function triggerWin()
    if hasWin == 1 then 
        return;
    end
    hasWin = 1
    bcapi.async_call(function()
        bcapi.hide_ui("画布")
        bcapi.hide_ui("技能区")
        bcapi.hide_ui("怒气条")
        bcapi.hide_ui("愈伤")
        bcapi.hide_ui("战斗时间")
        bcapi.hide_ui("暂停按钮")
        bcapi.hide_ui("加速按钮")
        bcapi.hide_ui("身法按钮")
        bcapi.hide_ui("角色技区")
        local Boss = bcapi.get_role(1, "剧情_坏楚青")
        bcapi.use_skill_to_role(Boss, "剧情_楚青_跪地", Boss, 1)
        bcapi.block_wait(2)
        bcapi.pause()
        bcapi.talk("莫知酉#莫知酉", "你败了，放心，我们不会要你性命。")
        bcapi.talk("楚青#剧情_坏楚青#痛苦", "呵，江湖前辈也以多欺少？")
        bcapi.talk("云舞笙#云舞笙#开心", "我可不是什么前辈，楚姑娘，跟我们走吧。")
        bcapi.win()
        bcapi.resume()
    end)
end