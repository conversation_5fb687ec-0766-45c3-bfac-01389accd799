
---@type 蜃境_雾锁寒山_06荒原雪林
local context = require("MapStories/蜃境_雾锁寒山_06荒原雪林/scene_蜃境_雾锁寒山_06荒原雪林_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_雾锁寒山_06荒原雪林
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

function getFlags(...)
    return flags
end

local capi = require("ChapterApi")

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")

function test()
    flags.start_show = 1
end

--每次载入调用
function start()
    print("开始场景00000")
    refreshMapStates()
    print("开始场景11111")

    try_execute_newbieGuide()

    --开场演出
    --暂时关闭雾气和敌人
    if flags.start_show == 0 then
        s.setActive(npcs.elite_10,false)
        s.setActive(npcs.enemy_1,false)
        s.setActive(npcs.enemy_2,false)
        s.setActive(npcs.enemy_3,false)
        s.setActive(npcs.enemy_4,false)
        s.setActive(npcs.enemy_5,false)
        s.setActive(npcs.enemy_6,false)

        s.camera(cameras.cam_start_far,true,true,blendHintEnum.Cut)

        print("开始场景22222")
        s.readyToStart(true)
        s.wait(0.5)
        s.cameraAsync(cameras.cam_start_near,true,true,blendHintEnum.Custom,15)
        s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_荒原雪林","荒原雪林_进入")
        s.camera(cameras.cam_main,true,true,blendHintEnum.Custom,2,true,true)

        
        s.setActive(npcs.elite_10, true)
        s.setActive(npcs.enemy_1,true)
        s.setActive(npcs.enemy_2,true)
        s.setActive(npcs.enemy_3,true)
        s.setActive(npcs.enemy_4,true)
        s.setActive(npcs.enemy_5,true)
        s.setActive(npcs.enemy_6,true)  

        flags.start_show = 1
    end

    --如果开启了BOSS房，下次进去传送到BOSS房门前
    if flags.open_boss_room == 1 then
        s.setPos(npcs.zhujue,objs.bossRoom_enter)
        s.wait(0.5)
        s.readyToStart(true)
    end
end

--精英怪战斗
function eliteBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

--boss战斗
function bossBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end

--最终boss战斗
function finalBossBattle(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id)
end


function get_level_step()
    return flags.level_step
end

---设置关卡步数
function set_level_step(value)
    flags.level_step = value
end

function refreshMapStates()
    local step = get_level_step() --当前关卡步数
    s.playMusic("蜃境")

    --任务状态激活
    local taskStep = "forwardToExplore"
    if (step >= 0) and (step < 6) then
        taskStep = "forwardToExplore"
    elseif (step == 6) then
        taskStep = "enterTele"
    elseif (step == 7) then
        taskStep = "finished"
    end
    s.setTaskStep("雾锁寒山荒原雪林", taskStep)

    reset_all()
    --第一次进入场景
    if step == 0 then
        s.setTaskStep("雾锁寒山荒原雪林","forwardToExplore")
        next_level_step(true)
    elseif step == 1 then
        level_guide_to(npcs.wshs_17, 1)
        --虚影动作设置
        s.animState(npcs.aliang_shadow,roleAnims.BSit_Loop,true)
    elseif step == 2 then
        level_guide_to(npcs.wshs_18, 1)
    elseif step == 3 then
        level_guide_to(npcs.wshs_19, 1)

        -- --虚影动作设置
        -- s.animState(npcs.zhugelu_shadow,"BAssault_Loop",true)
        -- s.animState(npcs.moqi_shadow1,"BSquat_Loop",true)
    elseif step == 4 then
        level_guide_to(npcs.wshs_20, 1)
    elseif step == 5 then   
        boss_room()
        s.setTaskStep("雾锁寒山荒原雪林","enterTele")
        next_level_step(true)
    elseif step == 6 then
        level_guide_to(objs.bossRoom_enter)
    else
        level_guide_to(objs.exit)
    end
end

--默认初始化的地图显隐状态
function reset_all()
    --隐藏所有的剧情节点
    s.setActive(npcs.wshs_17, false)
    s.setActive(npcs.wshs_18, false)
    s.setActive(npcs.wshs_19, false)
    s.setActive(npcs.wshs_20, false)

    --删除所有的引导点
    s.removeAllGameMapGuides()

    --设置boss房间的显隐状态
    if flags.open_boss_room == 1 then
        s.setActive(objs.bossRoom_enter,true)
    else
        s.setActive(objs.bossRoom_enter,false)
    end

    --设置人物状态机
    s.animState(npcs.shudu_1,"BSit_Loop",true)

    if flags.collect_1 == 1 then
        s.setActive(objs.wshs6_1009,false)
    else
        s.setActive(objs.wshs6_1009,true)
    end
end

function exitScene()
    print("离开场景")
    local capi = require("ChapterApi")
    capi.exitMemoryScene("雾锁寒山")
end

-------------------地图跑图---------------------
function boss_room()
    if flags.open_boss_room == 1 then
        local ret = s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_荒原雪林","荒原雪林_最终房间_进入")
        if ret == 1 then
            s.changeMapStory("蜃境_雾锁寒山_06最终房")
        end
    else
        s.cameraAsync(cameras.cam_portal,true,true,blendHintEnum.Cut,0,true,true)
        s.setActive(objs.bossRoom_enter,true)
        local ret = s.runAvgTag("蜃境/蜃境_雾锁寒山/蜃境_雾锁寒山_荒原雪林","荒原雪林_最终房间")
        if ret == 1 then
            s.changeMapStory("蜃境_雾锁寒山_06最终房")
        else 
            s.cameraAsync(cameras.cam_main,true,true,blendHintEnum.Cut,0,true,true)
            return
        end
        flags.open_boss_room = 1
    end
end

-------------------关卡记忆碎片和战斗实现---------------------
function wshs_17()
    executeMemSlot("wshs_17", function()
        require("Chapters/雾锁寒山").wshs6_17()
    end)
end

function wshs_18()
    executeMemSlot("wshs_18", function()
        require("Chapters/雾锁寒山").wshs6_18()
    end)
end

function wshs_19()
    executeMemSlot("wshs_19", function()
        require("Chapters/雾锁寒山").wshs6_19()
    end)
end

function wshs_20()
    executeMemSlot("wshs_20", function()
        require("Chapters/雾锁寒山").wshs6_20()
    end,function()
        --开启最终房间
        -- boss_room()
        next_level_step(true)
    end)
end

function enemy_battle(id,enemyKey)
    print("敌人战斗")
    local colliderKey = enemyKey.."_collider"

    s.popInfo("你已被发现！！！")

    --s.setActive(objs[colliderKey],false)
    s.turnTo(npcs[enemyKey],npcs.zhujue)
    s.turnTo(npcs.zhujue,npcs[enemyKey])

    s.animState(npcs[enemyKey],"BAssault_Loop",true)
    s.wait(2)

    require("MapStories/MapStoryCommonTools").roamMapBattle(id,function()
        s.popInfo("战斗胜利！！！")
        flags[enemyKey.."_battle"] = 1
        s.setActive(objs[colliderKey],false)
        s.setActive(npcs[enemyKey],false)
    end,
    function()
        battle_cancle(id,enemyKey)
    end,
    function()
        battle_lose(id,enemyKey)
    end
)

    s.wait(0.5)
    refreshMapStates()
end

function battle_lose(id,enemyKey)
    local colliderKey = enemyKey.."_collider"
    print("战斗失败")
    s.popInfo("战斗失败！！！")

    s.blackScreen()
    s.animState(npcs[enemyKey],"BAssault_Loop",false)
    s.setActive(objs[colliderKey],true)
    s.setPos(npcs.zhujue,pos[enemyKey],false)
    s.lightScreen()
end
function battle_cancle(id,enemyKey)
    local colliderKey = enemyKey.."_collider"

    s.blackScreen()
    s.animState(npcs[enemyKey],"BAssault_Loop",false)
    s.setActive(objs[colliderKey],true)
    s.setPos(npcs.zhujue,pos[enemyKey],false)
    s.lightScreen()
end

function fightInBack(enemyKey)
    local colliderKey = enemyKey.."_collider"
    local enemyDownKey = enemyKey.."_down"
    s.setActive(objs[colliderKey],false)

    s.animState(npcs.zhujue,"Special_2",true)
    s.wait(1)
    s.playSound("sfx","拳击3")
    s.wait(0.6)
    s.animState(npcs[enemyKey],"BDie",true)
    s.setDeath(npcs[enemyKey],0.5,false)

    flags[enemyDownKey] = 1
    s.popInfo("敌人暂时被击倒！！！")

    refreshMapStates()
end

function evilAir_1(id)
    executeMemSlot(id,function()
        s.playTimeline(assets.timeline_wolfFight,false)
    end,
    function()
        s.lightScreen()
        s.popInfo("战斗胜利！！！")
    end,
    function()
        s.setPos(npcs.zhujue,pos.elite_9)
        s.lightScreen()
    end,
    function()
        s.setPos(npcs.zhujue,pos.elite_9)
        s.lightScreen()
    end
)
end

function evilAir_2(id)
    s.setActive(objs.evilAir_2,false)
    s.setActive(npcs.role_elite_10, true)

    s.turnTo(npcs.role_elite_10,npcs.zhujue)
    s.animState(npcs.role_elite_10,"BAssault_Loop",true)
    s.wait(2)
    elite_10_fight(id)
end

function elite_10_fight(id)
    require("MapStories/MapStoryCommonTools").roamMapBattle(id,
    function()
        refreshMapStates()
    end,
    function()
        s.animState(npcs.role_elite_10,"BAssault_Loop",false) 
    end,
    function()
        s.animState(npcs.role_elite_10,"BAssault_Loop",false)
    end
    )
end


function shudu_1009(id)
    require("MapStories/MapStoryCommonTools").roamMapCollection(id)
    flags.collect_1 = 1
end
