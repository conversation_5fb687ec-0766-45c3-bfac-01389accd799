local s = require("StoryApi")

local extension = require 'MapStories/挑战_夜战开封/extension'
---@type flags_挑战_夜战开封
local flags = require("MapStories/挑战_夜战开封/flags_挑战_夜战开封")
s.loadFlags(flags)

-- ---@type StringWidget
local stringWidget = extension.widgets.StringWidgetInfo.widget
-- ---@type 挑战_夜战开封_StringItemData
local stringItemData = extension.widgets.StringWidgetInfo.items
---@type ItemWidget
local itemWidget = extension.widgets.ItemWidgetInfo.widget
---@type 挑战_夜战开封_ItemData
local itemData = extension.widgets.ItemWidgetInfo.items

---不可改名
function get_string_item_desc(itemKey,sceneId)
    return "没有描述"
end

---不可改名
function on_use_item(itemKey,sceneId)
    
end

