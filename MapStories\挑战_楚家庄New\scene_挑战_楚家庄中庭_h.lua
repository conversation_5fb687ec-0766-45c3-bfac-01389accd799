--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_楚家庄New/flags_挑战_楚家庄New")


---@class 角色_挑战_楚家庄中庭
local npcs = {
    enemy_6 = "角色/enemy_6",
    enemy_7 = "角色/enemy_7",
    enemy_8 = "角色/enemy_8",
    enemy_9 = "角色/enemy_9",
    enemy_10 = "角色/enemy_10",
    enemy_11 = "角色/enemy_11",
    enemy_12 = "角色/enemy_12",
    boss2 = "角色/boss2",
    zhujue = "角色/zhujue",
    shadow = "角色/shadow",
}

---@class 物体_挑战_楚家庄中庭
local objs = {
    middleyardHint1 = "物体/middleyardHint1",
    middleyardHint2 = "物体/middleyardHint2",
    boss2 = "物体/boss2",
    teleport3 = "物体/teleport3",
    teleport4 = "物体/teleport4",
    medicinal_2 = "物体/medicinal_2",
    trigger_medicinal_2 = "物体/trigger_medicinal_2",
}

---@class 相机_挑战_楚家庄中庭
local cameras = {
    camera_boss2_1 = "相机/camera_boss2_1",
    camera_boss2_2 = "相机/camera_boss2_2",
    camera_boss2_3 = "相机/camera_boss2_3",
}

---@class 位置_挑战_楚家庄中庭
local pos = {
    start = "位置/start",
    teleport1 = "位置/teleport1",
    pos_boss2_1 = "位置/pos_boss2_1",
    pos_boss2_2 = "位置/pos_boss2_2",
    pos_boss2_3 = "位置/pos_boss2_3",
    pos_boss2_4 = "位置/pos_boss2_4",
}

---@class 资产_挑战_楚家庄中庭
local assets = {
}

---@class 动作_挑战_楚家庄中庭
local animationClips = {
}

---@class 挑战_楚家庄中庭
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
