local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.hide_ui("疗伤")
    createRoles()
end

function start()  
    bcapi.add_condition_trigger("[%enemy:世界_爱而不得_岚任->flag:表演技能%][=]0", 1, showSkill)
    bcapi.add_timer_trigger(1, checkLose)
end

-- 取玩家队伍存的字典，后面两个函数中用到
local dict = bcapi.get_team(0).ValueDict

function createRoles()
    bcapi.async_call(function()
        --创建角色
        bcapi.create_join_role("世界_爱而不得_玉瑾", 10, team, bcapi.Vector2(-7, 0), 0)
    end)
end

function showSkill()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("青年男子心机", "我看你是还没有得到教训。")
        bcapi.talk("素衣男青年", "哼，还想用那招，你就来试试看吧！")
        local Boss = bcapi.get_role(1, "世界_爱而不得_岚任")
        Boss.Stat.BattleFlagDic:set_Item("开始表演", "1")
        bcapi.resume_and_show_ui()
    end)
end

function checkLose()
    bcapi.add_condition_trigger("[%c->teammate:世界_爱而不得_玉瑾%][<]1", 0, lose)
end

function lose()
    bcapi.async_call(function()
        bcapi.lose()
    end)
end
