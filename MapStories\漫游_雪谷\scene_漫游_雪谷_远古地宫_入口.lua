
---@type 漫游_雪谷_远古地宫_入口
local context = require("MapStories/漫游_雪谷/scene_漫游_雪谷_远古地宫_入口_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

---组件相关
local extensions = require("MapStories/漫游_雪谷/extension")
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type StringWidget
local stringWidget = extensions.widgets.StringWidgetInfo.widget
---@type 雪谷_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 雪谷_ItemData]]
---@type 雪谷_StringItemData
local stringItemData = extensions.widgets.StringWidgetInfo.items

s.loadFlags(context.flags)
---@type flags_漫游_雪谷
local flags = context.flags

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.readyToStart()
    reloadAllSceneStates()
end

--第一次进入地图
function triggerOpening()
    s.blackScreen()
    s.camera(cameras.start,true)
    s.setPos(npcs.moqi,pos.MQ_start)
    s.animState(npcs.dizi,roleAnims.BHurt_Loop,true)
    s.lightScreen()
    
    s.talk("玄阙宫弟子","不要进入地宫！")
    s.animState(npcs.dizi,roleAnims.BHurt_Loop,false)
    s.aside("此人昏死过去，再没了声息。怀里抱着一块奇怪的零件。")
    s.animState(npcs.dizi,roleAnims.BDie)
    s.move(npcs.moqi,pos.MQ_squart,1,true,"",0,true)
    s.animState(npcs.moqi,roleAnims.BSquat_Loop,true)
    s.talk(npcs.moqi,"这是……")
    
    itemWidget:addItem(itemData.digongkey1,1)

    s.talk(npcs.moqi,"安息吧。")
    s.aside("墨七把这人埋了起来。")
    flags.dg_opening_flag = 1
    s.blackScreen()
    reloadAllSceneStates()
    s.camera(cameras.start,false)
    s.lightScreen()
end

function reloadAllSceneStates()
    --受伤的动物
    s.setActive(objs.animal, flags.yd_lurou_step < 2)

    --地宫大门
    s.setActive(objs.teleport, flags.dg_unlock_step == 3)
    s.setActive(objs.door, flags.dg_unlock_step ~= 3)
    
    --玄阙宫弟子
    s.setActive(npcs.dizi, flags.dg_opening_flag == 0)
    --初始事件触发器
    s.setActive(objs.triggerOpening, flags.dg_opening_flag == 0)
    
    --玄武血
    s.setActive(objs.xuanwu, flags.dg_main_xuanwu == 0)
end


function wrappedReloadAllSceneStates()
    s.blackScreen()
    reloadAllSceneStates()
    s.lightScreen()
end


function eventDoor()
    local opt = {}
    local showConds = {}
    local ret = 0
    
    ::A::
    if flags.dg_unlock_step == 2 then
        s.selectTalk(npcs.moqi, "……", {"打开大门"})
        
        s.aside("大门咔咔作响，被打开了。")
        flags.dg_unlock_step = 3
        wrappedReloadAllSceneStates()
        return
    end
    
    if flags.dg_unlock_step == 0 then
        s.talkThink(npcs.moqi,"（看起来这扇大门缺少两个机关零件才能打开）")
    elseif flags.dg_unlock_step == 1 then
        s.talkThink(npcs.moqi,"（看起来这扇大门缺少一个机关零件才能打开）")
    end
    
    opt = {}
    opt[1] = "放入机关零件"
    opt[2] = "离开"
    showConds = {}
    showConds[1] = itemWidget:getItemCount(itemData.digongkey1) ~= 0 or itemWidget:getItemCount(itemData.digongkey2) ~= 0
    showConds[2] = true
    ret = s.selectTalkExWithCondition(npcs.moqi, "……", opt,showConds)

    if ret == 1 then
        local submitItem = s.submitTempItem("请提交机关零件！")
        if (submitItem ~= nil) then
            if (submitItem[0].Key == itemData.digongkey1.key) then
                itemWidget:removeItem(itemData.digongkey1,1)

                s.aside("严丝合缝！")
                flags.dg_unlock_step = flags.dg_unlock_step + 1
                goto A
            elseif(submitItem == itemData.digongkey2.key) then
                itemWidget:removeItem(itemData.digongkey2,1)

                s.aside("严丝合缝！")
                flags.dg_unlock_step = flags.dg_unlock_step + 1
                goto A
            else
                s.aside("你提交错了！这不是合适的机关零件。")
            end
        else
            s.aside("你没有提交任何东西！")
        end
    end
end


function eventAnimal()
    if (flags.yd_lurou_step == 0) then
        eventAnimal000()
    else
        eventAnimal001()
    end
end

function eventAnimal000()
    s.aside("这是一个重伤的猎物。")
end

function eventAnimal001()
    --todo：具体如何获取这个收益待调整
    --todo：部分可能涉及到合规性的叙事可以通过特殊的“回忆/联想”剧情来回避
    s.aside("这是一个重伤的猎物。你获得了鹿肉！")

    itemWidget:addItem(itemData.rou,1)
    s.finishTask(400014, 1)

    flags.yd_lurou_step = 2

    --刷新
    wrappedReloadAllSceneStates()
end

function eventXuanwu()
    if flags.dg_main_xuanwu == 0 then
        s.aside("这是玄武血。")

        flags.dg_main_xuanwu = 1

        itemWidget:addItem(itemData.xuanwuxue,1)
        wrappedReloadAllSceneStates()
    end
end

function transportYingdi()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_流亡者营地.unity", "位置/Digong")
end

function transportNeath()
    s.changeMap("Assets/GameScenes/游历场景/漫游_雪谷/漫游_雪谷_远古地宫_地下.unity", "位置/Entre")
end
