
---@type 挑战_楚家庄中庭
local context = require("MapStories/挑战_楚家庄New/scene_挑战_楚家庄中庭_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_挑战_楚家庄New
local flags = context.flags

---@type RpgMapApi
local rapi = require("RpgMapApi")

local rpgFlags = require("RpgMap/flags")
rapi.loadFlags(rpgFlags)

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    local isTeleport = false
    --如果是传送，就不能setPos
    if(args ~= nil and args:ContainsKey("IsTransport"))then
        isTeleport = true
    end

    --如果不是传送，设置主角到初始位置
    if(not isTeleport)then
        s.setPos(npcs.zhujue, pos.start)
    end
    
    --重复演出
    s.playMusic("tense")

    if(s.getChallengeMapPersistentFlag("挑战_楚家庄New","播放过Boss2战前演出") == 0)then
        s.setPos(npcs.boss2, pos.pos_boss2_2)
    end

    local herb2Count = s.getRpgMapFlag("yfxf_get_medical2")

    --启用支线任务，药方修复
    if s.getCurrentTaskStep("药方修复") == "getMedical" and herb2Count == 0 then
        s.setActive(objs.trigger_medicinal_2,true)
        s.setActive(objs.medicinal_2,true)
    end

    --处理断线重连
    InitDungeon()

    s.readyToStart()
end

function InitDungeon()
    --处理boss2
    if(flags.boss2 == 1)then
        s.setActive(npcs.boss2,false)
    end
    
    --处理对话演出
    if(flags.MiddleYardHint1 == -1)then
        s.setActive(objs.middleyardHint1,false)
    end
    if(flags.MiddleYardHint2 == -1)then
        s.setActive(objs.middleyardHint2,false)
    end
end

--通用小怪战斗
function enemyBattle(id)
    local isWin,bResult = s.challengeMapBattle(id)
    if(isWin)then
        finalBossLvUp()
    end
end

--战斗胜利后对话演出
function finalBossLvUp()
    local wordTb = {
        "哼，哪来的小子，有趣的很……\n",
        "你这小子，不容小觑啊。\n",
        "来，让我多多观察下你的招式——\n",
        "嘿嘿嘿，功夫还算得上有一手！\n",
        "让我这些手下，再陪你多玩会！\n",
    }
    local word = wordTb[math.random(1,5)]
    s.appendAsyncAside(npcs.shadow,0,word,"","默认")
end

--走廊对话演出1
function middleYardHint1()
    s.setActive(objs.middleyardHint1,false)
    s.appendAsyncAside(npcs.zhujue,0,"巡逻的喽啰多了起来，看来有大人物在此。","","默认")
    flags.MiddleYardHint1 = -1
end

--走廊对话演出2
function middleYardHint2()
    s.setActive(objs.middleyardHint2,false)
    s.appendAsyncAside(npcs.zhujue,0,"穿过这条走廊，就到楚家庄的后山了，不知前面又是什么光景。","","默认")
    flags.MiddleYardHint2 = -1
end

--boss2战斗
function boss2()
    if(s.getChallengeMapPersistentFlag("挑战_楚家庄New","播放过Boss2战前演出") == 0)then
        --一次性演出
        s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_楚家庄_首次Boss2对话_1", nil, 0)
        s.turnTo(npcs.zhujue, npcs.boss2, true)
        s.camera(cameras.camera_boss2_1, true,true,blendHintEnum.EaseInOut,0,true,true)
        s.setActive(npcs.boss2,true)
        s.animState(npcs.boss2, "Special_2",true,false,0.5,true,true,1.5)
        s.wait(0.2)
        s.setPos(npcs.boss2, pos.pos_boss2_3)
        s.animState(npcs.boss2, "BApplaud",true,false,0,false,false,0,false)
        s.camera(cameras.camera_boss2_2, true,true,blendHintEnum.EaseInOut,1,true,true)
        s.moveAsync(npcs.zhujue,pos.pos_boss2_1,2,true)
        s.setChallengeMapPersistentFlag("挑战_楚家庄New","播放过Boss2战前演出",1)
    else
        --重复性演出
        s.camera(cameras.camera_boss2_3, true,true,blendHintEnum.EaseInOut,1,true,true)
        s.turnTo(npcs.boss2, npcs.zhujue, true)
        s.turnTo(npcs.zhujue, npcs.boss2, true)
    end
    --重复演出
    s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_楚家庄_Boss2战前对话_1", nil, 0)
    s.turnTo(npcs.boss2, npcs.zhujue, true)
    s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_楚家庄_Boss2战前对话_2", nil, 0)

    local isWin,bResult = s.challengeMapBattle("16")
    if(isWin)then
        s.camera(cameras.camera_boss2_2,false)
        s.camera(cameras.camera_boss2_3,false)
        s.setActive(npcs.boss2,false)
        
        flags.boss2 = 1

        --下发一次性奖励
        if(s.hasTask(992003,1))then
            s.finishTask(992003,1)
            s.setChallengeMapPersistentFlag("挑战_楚家庄New","富贵少爷",1)
        end

        --清除此场景所有小怪
        enemyRunAway()
        s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_楚家庄_Boss2击败触发逃跑对话", nil, 0)

        --播放对话演出
        s.wait(0.5)
        finalBossLvUp()

        --设置天书flag，记录BOSS
        s.setGuideBookFlag("boss_fuguigongzi")
    else
        s.camera(cameras.camera_boss2_3,false)

        --处理boss2第一次演出，战斗失败后的情况
        s.camera(cameras.camera_boss2_2,false)
        s.setPos(npcs.boss2, pos.pos_boss2_4)
    end
end

function enemyRunAway()
    for k,v in pairs(npcs) do
        if(string.find(k,"enemy") ~= nil and flags[k] ~= nil)then
            --只处理未击败的敌人
            if(flags[k] == 0)then
                ---@diagnostic disable-next-line: inject-field-fail
                flags[k] = -1
                s.setActive(v,false)
            end
        end
    end
end

--传送到楚家庄前院
function transPortToFront1()
    if(flags.boss2 == 0) then
        s.talk(npcs.zhujue, "还没有击败盘踞在此的敌方头目，不要四处闲逛了。")
        return
    end
    s.changeMap("Assets/GameScenes/游历场景/挑战_楚家庄New/挑战_楚家庄前院.unity", "位置/telePos1")
end

--传送到楚家庄后山
function transPortToBack()
    if(flags.boss2 == 0) then
        s.talk(npcs.zhujue, "还没有击败盘踞在此的敌方头目，不要四处闲逛了。")
        return
    end
    s.changeMap("Assets/GameScenes/游历场景/挑战_楚家庄New/挑战_楚家庄后山.unity", "位置/start")
end

--副本支线2，药方修复
function findMedicinal()
    require("RpgMap/任务线/药方修复").yfxf_getSecond()

    local herbCount = s.getRpgMapFlag("yfxf_get_medical_count")
    herbCount = herbCount + 1
    s.setRpgMapFlag("yfxf_get_medical_count",herbCount)
    s.setRpgMapFlag("yfxf_get_medical2",1)

    if herbCount >= 3 then
        require("RpgMap/任务线/药方修复").judge_medical_count()

        if s.getCurrentTaskStep("药方修复") == "getMedical" then    
            s.setTaskStep("药方修复","feedBackLaoPu")
        end
    end
    
    s.setActive(objs.trigger_medicinal_2,false)
    --s.setAnimationClip(npcs.zhujue,"Assets/BuildSource/Animations/Humanoid/Others/基础通用动画库/Samurai_Katana/Animations/UseItem/Pick_Floor.FBX")
    s.setActive(objs.medicinal_2,false)
    --s.setAnimationClip(npcs.zhujue,"")
end


