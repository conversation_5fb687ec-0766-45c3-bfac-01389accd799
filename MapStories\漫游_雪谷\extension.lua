
local context = require("MapStories/漫游_雪谷/scene_漫游_雪谷_古渡人家_h")
local s = context.sapi
local flags = context.flags
s.loadFlags(flags)

---@class 雪谷_StringItemData
local stringItemData = {
    yd_rep = {key = "营地声望",showName="营地声望",desc = "墨七在营地的声望",value = 30},
    xl_rep = {key = "雪林声望",showName="雪林声望",desc = "墨七在雪林的声望",value = 0},
    gd_rep = {key = "古渡声望",showName="古渡声望",desc = "墨七在古渡的声望",value = 0},
}

---@class 雪谷_ItemData
local itemData = {
    midai = {key = "midai",showName = "米袋",desc = "米的袋",icon = "achi_title_2_jinnang_big", canUse = false},
    shaojiu = {key = "shaojiu",showName = "烧酒",desc = "烧酒",icon = "jiu", canUse = false},
    dousha = {key = "dousha",showName = "豆沙",desc = "豆沙",icon = "mount_wolf_a", canUse = false},
    rougutou = {key = "rougutou",showName = "肉骨头",desc = "肉骨头",icon = "baozi", canUse = false},
    
    yanlei = {key = "yanlei",showName = "枯萎的枫叶",desc = "少女为思念之人落下的泪水渗入枫叶，使之不再平凡。",icon = "jiu", canUse = false},
    maopi = {key = "maopi",showName = "毛皮",desc = "毛皮",icon = "langpi", canUse = false},
    xuejing = {key = "xuejing",showName = "无垢的雪晶",desc = "无垢的雪晶",icon = "pearl2", canUse = false},
    wuqi = {key = "wuqi",showName = "凶手的武器",desc = "凶手的武器",icon = "bazhechangdao", canUse = false},
    bingxueniang = {key = "bingxueniang",showName = "冰雪酿",desc = "冰雪酿",icon = "jiu", canUse = false},
    
    rou = {key = "rou",showName = "肉",desc = "肉",icon = "zongzi", canUse = false},
    
    fengye = {key = "fengye",showName = "枯萎的枫叶",desc = "已经枯萎的枫叶，一直被小心翼翼地保存着。",icon = "liuye", canUse = false},
    yaofang = {key = "yaofang",showName = "残破的药方",desc = "残破的药方",icon = "chishu", canUse = false},
    canglongjing = {key = "canglongjing",showName = "苍龙精",desc = "苍龙精",icon = "jinyuzao", canUse = false},
    baihugu = {key = "baihugu",showName = "白虎骨",desc = "白虎骨",icon = "huangcen", canUse = false},
    zhuquexin = {key = "zhuquexin",showName = "朱雀心",desc = "朱雀心",icon = "huangbai", canUse = false},
    xuanwuxue = {key = "xuanwuxue",showName = "玄武血",desc = "玄武血",icon = "huaban", canUse = false},
    yaofen =  {key = "yaofen",showName = "奇异的药粉",desc = "色彩斑斓的药粉，或许有某种特殊的功效。",icon = "peiyao", canUse = false},
    
    afeijiu = {key = "afeijiu",showName = "阿飞的酒",desc = "阿飞的酒",icon = "jiu", canUse = false},
    zhuzi = {key = "zhuzi",showName = "竹子",desc = "竹子",icon = "mucai", canUse = false},
    qinpu = {key = "qinpu",showName = "琴谱",desc = "琴谱",icon = "scrolls2", canUse = false},
    
    huixiangcao = {key = "huixiangcao",showName = "回香草",desc = "回香草",icon = "kongguyoulan", canUse = false},
    zhuzishen = {key = "zhuzishen",showName = "珠子参",desc = "一种止血化瘀的草药",icon = "tianshanxuelian", canUse = false},
    baojian = {key = "baojian",showName = "宝剑",desc = "镇派宝剑",icon = "jiu", canUse = false},
    
    digongkey1 = {key = "digongkey1",showName = "地宫大门钥匙",desc = "地宫大门钥匙",icon = "yejintianmingbi", canUse = false},
    digongkey2 = {key = "digongkey2",showName = "地宫大门钥匙",desc = "地宫大门钥匙",icon = "yejintianmingbi", canUse = false},
    
    jiguanlingjian1 = {key = "jiguanlingjian1",showName = "琳琅玉键",desc = "这并非普通玉器，似乎能够组合为某物……",icon = "yaoshi2", canUse = false},
    jiguanlingjian2 = {key = "jiguanlingjian2",showName = "琳琅玉键",desc = "这并非普通玉器，似乎能够组合为某物……",icon = "yaoshi2", canUse = false},
    jiguanlingjian3 = {key = "jiguanlingjian3",showName = "琳琅玉键",desc = "这并非普通玉器，似乎能够组合为某物……",icon = "yaoshi2", canUse = false},
    jiguanlingjian4 = {key = "jiguanlingjian4",showName = "琳琅玉键",desc = "这并非普通玉器，似乎能够组合为某物……",icon = "yaoshi2", canUse = false},
    jiguanlingjian5 = {key = "jiguanlingjian5",showName = "琳琅玉键",desc = "这并非普通玉器，似乎能够组合为某物……",icon = "yaoshi2", canUse = false},

    yangpizhi1 = {key = "yangpizhi1",showName = "羊皮纸残页",desc = "一块撕裂的羊皮纸，画着歪歪扭扭的图形，如果能找到剩余部分就好了。",icon = "jiu", canUse = false},
    yangpizhi2 = {key = "yangpizhi2",showName = "羊皮纸残页",desc = "一块撕裂的羊皮纸，画着歪歪扭扭的图形，如果能找到剩余部分就好了。",icon = "jiu", canUse = false},
    yangpizhi3 = {key = "yangpizhi3",showName = "羊皮纸残页",desc = "一块撕裂的羊皮纸，画着歪歪扭扭的图形，如果能找到剩余部分就好了。",icon = "jiu", canUse = false},
    
    fengmao = {key = "fengmao",showName = "疯猫爪功",desc = "一本破破烂烂的武学书籍，上面的文字多半已看不清楚，似乎都是极其阴损的下三滥杀招。",icon = "jiu", canUse = false},
    lingpai = {key = "lingpai",showName = "褪色的令牌",desc = "令牌表面的红漆已褪色大半，写着“霸拳”二字。",icon = "xiejianpei", canUse = false},
    tudao = {key = "tudao",showName = "沉重的屠刀",desc = "令人望而生畏的大刀，只有臂力极强的人才能挥舞。",icon = "dao2", canUse = false},
}

local stringWidget = StringWidget:new(stringItemData,flags)
local itemWidget = ItemWidget:new(itemData,flags)

local itemWidgetInfo = {
    title = "道具",
    items = itemData,
    widget = itemWidget
}

local stringWidgetInfo = {
    title = "声望",
    desc = "墨七在各地的声望",
    items = stringItemData,
    widget = stringWidget
}


extensions = {
    useWidgets = {"StringWidget","ItemWidget"},
    widgets = {
        StringWidgetInfo = stringWidgetInfo,
        ItemWidgetInfo = itemWidgetInfo
    }
}

context.sapi.syncExtendsInfo(extensions)
return extensions