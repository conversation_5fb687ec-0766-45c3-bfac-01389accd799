---墨七之章
---@type ChapterApi
local capi = require("ChapterApi")

---@type NewbieApi
local newbieApi = require("NewbieApi") -- 不允许改名

---@type StageMapApi
local stageApi = require("StageMapApi")

---@type NewbieFlags
local flags = require("Newbie/flags")

s = require("StoryApi")

local avg = require("AVGApi")

function playAvgAndCustomBattle()
    innerPlayAvg(args:get_Item("avgKey"))
    newbieApi.battle(args:get_Item("battleKey"), true)
    capi.pass(7)
end

function quick_play_avg()
    local avgKey = args:get_Item("avgKey")
    local avg = require("AVGApi")
    avg.run(avgKey) 
end

function quick_map_story()
    local mapStoryKey = args:get_Item("mapStoryKey")
    --s.aside(mapStoryKey .. ":changeMapStory切地点有问题，待程序修复。先为了串流程直接跳过")
    if(capi.isGameMapRunning()) then
        s.changeMapStory(mapStoryKey)
    else
        capi.mapStory(mapStoryKey,false)
    end
end

function quick_play_timeline()
    local timelineKey = args:get_Item("timelineSceneKey")
    s.playTimelineScene("Assets/GameScenes/动画演出场景/" .. timelineKey)
end


function on_unlock()
    -- 播放开门视频
    newbieApi.playVideo("CriAtom/蜃景-full.usm")
end


-- 从记忆碎片画廊点击执行,注意这里是纯演出,不应该包含任何值的变化
function wshs_01()
    quick_play_avg()
    quick_play_timeline()
end

function wshs_02()
    quick_play_avg()
end

function wshs_03()
    quick_map_story()
end

function wshs_04()
    quick_play_avg()
end
function wshs_05()
    quick_play_timeline()
end
function wshs_06()
    quick_play_avg()
end
function wshs_07()
    quick_play_avg()
end
function wshs_08()
    quick_play_avg()
end
function wshs_09()
    quick_play_avg()
    quick_play_timeline()
end

function wshs_10_f1()
    quick_play_avg()
end
function wshs_10()
    quick_play_avg()
end
function wshs_11()
    quick_play_avg()
end
function wshs_12()
    quick_play_avg()
end
function wshs_13()
    quick_play_avg() 
end
function wshs_14()
    quick_play_avg()
end
function wshs_15()
    quick_play_avg()
end
function wshs_16()
    quick_play_avg()
end
function wshs_17()
    quick_play_avg()
end
function wshs_18()
    quick_play_avg()
end
function wshs_19()
    quick_play_avg()
end

function wshs_20()
    quick_play_avg()
end

return {
    wshs1_01 = wshs_01,
    wshs1_02 = wshs_02,
    wshs2_03 = wshs_03,
    wshs2_04 = wshs_04,
    wshs2_05 = wshs_05,
    wshs2_06 = wshs_06,
    wshs3_07 = wshs_07,
    wshs3_08 = wshs_08,
    wshs3_09 = wshs_09,
    wshs4_10_f1 = wshs_10_f1,
    wshs4_10 = wshs_10,
    wshs4_11 = wshs_11,
    wshs4_12 = wshs_12,
    wshs5_13 = wshs_13,
    wshs5_14 = wshs_14,
    wshs5_15 = wshs_15,
    wshs5_16 = wshs_16,
    wshs6_17 = wshs_17,
    wshs6_18 = wshs_18, 
    wshs6_19 = wshs_19,
    wshs6_20 = wshs_20,
}