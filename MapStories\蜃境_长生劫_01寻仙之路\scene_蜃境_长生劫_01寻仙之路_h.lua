--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_长生劫_01寻仙之路/flags_蜃境_长生劫_01寻仙之路")


---@class 角色_蜃境_长生劫_01寻仙之路
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "csj_01"
    csj_01 = "角色/csj_01",
    --- "csj_02"
    csj_02 = "角色/csj_02",
    --- "csj_03"
    csj_03 = "角色/csj_03",
    --- "csj_04"
    csj_04 = "角色/csj_04",
    --- "enemy_1"
    enemy_1 = "角色/enemy_1",
    --- "enemy_2"
    enemy_2 = "角色/enemy_2",
    --- "enemy_3"
    enemy_3 = "角色/enemy_3",
    --- "enemy_4"
    enemy_4 = "角色/enemy_4",
    --- "enemy_5"
    enemy_5 = "角色/enemy_5",
    --- "enemy_5"
    enemy_6 = "角色/enemy_6",
    --- "chunsui_shadow_3"
    chunsui_shadow_3 = "角色/chunsui_shadow_3",
    --- "chunsui_shadow_4"
    chunsui_shadow_4 = "角色/chunsui_shadow_4",
    chunsui = "角色/chunsui",
    --- "青年夫妇"
    XSZL_NPC = "角色/XSZL_NPC",
    --- "推动石块"
    csj_01_Puzzle_tuidongshikuai = "角色/csj_01_Puzzle_tuidongshikuai",
}

---@class 物体_蜃境_长生劫_01寻仙之路
local objs = {
    --- "出口"
    exit = "物体/exit",
    --- "chest_1"
    chest_1 = "物体/chest_1",
    enemy_1_collider = "物体/enemy_1_collider",
    enemy_2_collider = "物体/enemy_2_collider",
    triggger_chunsui_start = "物体/triggger_chunsui_start",
    exit1 = "物体/exit1",
    evilAirChest_1 = "物体/evilAirChest_1",
    csj_1001 = "物体/csj_1001",
    csj_1002 = "物体/csj_1002",
    --- "站立石块"
    csj_01_Puzzle_zhanlishikuai = "物体/csj_01_Puzzle_zhanlishikuai",
    --- "迷雾"
    csj_01_Puzzle_miwu = "物体/csj_01_Puzzle_miwu",
    --- "迷雾剧情触发器"
    csj_01_Puzzle_miwuchufaqi = "物体/csj_01_Puzzle_miwuchufaqi",
    --- "chest_2"
    chest_2 = "物体/chest_2",
    --- "推动石块"
    csj_01_Puzzle_tuidongshikuai = "物体/csj_01_Puzzle_tuidongshikuai",
}

---@class 相机_蜃境_长生劫_01寻仙之路
local cameras = {
    cam_start_near = "相机/cam_start_near",
    cam_start_far = "相机/cam_start_far",
    cam_main = "相机/cam_main",
    --- "谜题相机"
    cam_puzzle = "相机/cam_puzzle",
}

---@class 位置_蜃境_长生劫_01寻仙之路
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    enemy_1 = "位置/enemy_1",
    enemy_2 = "位置/enemy_2",
    chuisui_start = "位置/chuisui_start",
    --- "解谜复位主角位置"
    csj_01_Puzzle_zhujue = "位置/csj_01_Puzzle_zhujue",
    --- "解谜复位石块位置"
    csj_01_Puzzle_tuidongshikuai = "位置/csj_01_Puzzle_tuidongshikuai",
}

---@class 资产_蜃境_长生劫_01寻仙之路
local assets = {
    --- "chunsui_start"
    chunsui_start = "资产/chunsui_start",
    --- "chunsui_jump"
    chunsui_jump = "资产/chunsui_jump",
}

---@class 动作_蜃境_长生劫_01寻仙之路
local animationClips = {
}

---@class 蜃境_长生劫_01寻仙之路
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
