
---@type 上山小路
local context = require("MapStories/美术专用/scene_上山小路_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_美术专用
local flags = context.flags --[[@as flags_美术专用]]

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
end

--第一次进入地图
function first_time_into_map()
    -- 填写逻辑
end

function hello()
    s.aside("你好")
end
