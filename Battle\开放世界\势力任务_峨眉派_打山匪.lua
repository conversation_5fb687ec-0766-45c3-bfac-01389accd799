local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.async_call(function()
        local lv = 0
        local cnt = 0
        for i = 0, team.AliveRoleCount - 1 do
            local rid = team.AliveRidList[i]
            local role = team.RoleDict[rid]
            cnt = cnt + 1
            lv = lv + role.Level
        end
        
        if(cnt == 0)then
            cnt = 1
        end

        local level = lv/cnt

        local enemyTb = {"通用_贼寇_小怪_恶徒男_剑","通用_贼寇_小怪_恶徒男_斧","通用_贼寇_小怪_恶徒男_狼牙棒","通用_贼寇_小怪_恶徒男_弓","通用_贼寇_小怪_恶徒男_双斧","通用_贼寇_小怪_恶徒男_斧盾"}

        
        --随机召唤其中4个角色，等级为level
        local index = math.random(1, #enemyTb)
        local hpRate = 1 + 0.001*level*math.random(15, 25)
        local opRate = 1 + 0.001*level*math.random(15, 25)
        local odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(4, 3), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(4, -3), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(6, 3), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        index = math.random(1, #enemyTb)
        hpRate = 1 + 0.001*level*math.random(15, 25)
        opRate = 1 + 0.001*level*math.random(15, 25)
        odRate = 1 + 0.001*level*math.random(15, 25)
        bcapi.create_join_role(enemyTb[index], level, bcapi.get_team(1), bcapi.Vector2(6, -3), 0.1,-1,hpRate,opRate,odRate,opRate,odRate,"")

        local sLevel = math.floor(1 + level/5)
        local sTable = {"万钧重锤","霜天封雪","银针返命","风露杏雪","大藏心经","吐纳法","不动如山","天罡真气","龙虎心法","铁甲阵法",
                        "藏锋积锐","霸王怒吼","梧叶舞秋风","浴火涅槃","江湖刀客","叱咤狂熊","木人筑","豪云剑魂","血沸剑魂",
                        "刺隐剑魂","如归剑魂","青莲剑魂","朔风吹雪","千面无常","分身解厄","灵兽助阵","迷风剪影","神射手",
                        "神机诛恶","光明圣火"}

        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
        index = math.random(1, #sTable)
        bcapi.add_strategy_card(sTable[index], sLevel + math.random(1, 2), eTeam)
    end)
end

function start()
    
end
