---@type 大熔炉
local context = require("MapStories/大熔炉/scene_大熔炉_h")
local s = context.sapi
local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local flags = context.flags
local assets = context.assets
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

local extensions = require 'MapStories/大熔炉/extension'
---@type ItemWidget
local itemWidget = extensions.widgets.ItemWidgetInfo.widget
---@type StringWidget
local stringWidget = extensions.widgets.StringWidgetInfo.widget
---@type 大熔炉_ItemData
local itemData = extensions.widgets.ItemWidgetInfo.items --[[@as 大熔炉_ItemData]]
---@type 大熔炉_StringItemData
local stringItemData = extensions.widgets.StringWidgetInfo.items
s.loadFlags(flags)

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    reloadAllSceneStates()
    s.talk(npcs.jingcheng, "加载地图触发事件")
end

function on_use_item(itemKey)
    s.talk(npcs.jingcheng, "使用物品" .. itemKey)
    if itemKey == "test1" then
        s.talk(npcs.jingcheng, "使用了物品1")
    elseif itemKey == "test2" then
        s.talk(npcs.jingcheng, "使用了物品2")
    end
end

function testTalk1()
    local ops = {
        "选项1",
        "选项2"
    }
    s.selectTalk(npcs.jingcheng, "测试对话", ops)
end

function testRotate()
    s.talk(npcs.jingcheng, "转身")
    s.turnTo(npcs.jingcheng, objs.objA)
    s.talk(npcs.jingcheng, "转身")
    s.turnTo(npcs.jingcheng, objs.objB)

end

function testSetMapStatus()
    s.setMapStatus("123", "你好啊")
end

function testGetMapStatus()
    local status = s.getMapStatusString("123")
    s.talk(npcs.jingcheng, "获取到的状态是" .. status)
end

function testTalk()
    local ops = {
        "选项1",
        "选项2"
    }
    local opsEnabled = {
        true,
        false
    }
    s.selectTalkWithCondition(npcs.jingcheng, "测试对话", ops,opsEnabled)
end

function testArtSceneObjReferecne() 
    s.talk(npcs.jingcheng, "测试艺术场景物体引用")
    s.talk(npcs.jingcheng,"移动到点位1")
    s.setPos(objs.testObj1, pos.pos1)
    s.talk(npcs.jingcheng,"移动到点位2")
    s.setPos(objs.testObj1, pos.pos2)
end

function testSelectEx()
    -- 假设4个选择，是否显示是有条件的
    ops = {}
    local cond1 = true
    local cond2 = true
    local cond3 = false
    local cond4 = true
    showConds = {}
    if (cond1) then
        ops[1] = "选项1"
        showConds[1] = true
    end
    if (cond2) then
        ops[2] = "选项2"
        showConds[2] = false
    end
    if (cond3) then
        ops[3] = "选项3"
        showConds[3] = true
    end
    if (cond4) then
        ops[5] = "选项4"
        showConds[5] = true
    end
    local returnIndex = s.selectTalkExWithCondition(npcs.jingcheng, "测试对话", ops, showConds)
    if (returnIndex == 1) then
        s.talk(npcs.jingcheng, "选择了1")
    elseif (returnIndex == 2) then
        s.talk(npcs.jingcheng, "选择了2")
    elseif (returnIndex == 3) then
        s.talk(npcs.jingcheng, "选择了3")
    elseif (returnIndex == 5) then
        s.talk(npcs.jingcheng, "选择了4")
    end
end

function TestMoveTrigger()
    s.camera(cameras.vcam1, true)
    -- 荆成移动到点位3
    s.setPos(npcs.jingcheng, pos.pos2)
    CS.UnityEngine.Debug.LogError("荆成移动到点位3")
    s.wait(1)
    s.turnTo(npcs.jingcheng, pos.pos3)
    s.move(npcs.jingcheng, pos.pos3, 3,true)
    s.wait(5)
    
end

function testPlayMusic()
    s.playMusic("huanxing")
end

--- 测试IK旋转
function TestRotateNew()
    s.turnTo(npcs.jingcheng2, npcs.jingcheng)
    s.wait(2)
end

function TestAddString()
    stringWidget:showItem(stringItemData.test1)
end

function TestAddItem()
    itemWidget:addItem(itemData.test1,1)
    itemWidget:addItem(itemData.test2,1)
    itemWidget:addItem(itemData.test3,1)
    itemWidget:addItem(itemData.test4,1)
    itemWidget:addItem(itemData.test5,1)
    local cnt = itemWidget:getItemCount(itemData.test1)
    s.talk(npcs.jingcheng, "物品1的数量是" .. cnt)
end

function TestSubmitItem() 
    local card = s.submitRoleCard("选一个最牛逼的卡")
    if (card ~= nil) then
        s.talk(npcs.jingcheng, "选中的卡是" .. card[0].Key)
    end
    card = s.submitStrategyCard("选一个最牛逼的卡")
    if (card ~= nil) then
        s.talk(npcs.jingcheng, "选中的卡是" .. card[0].Key)
    end
    -- 获得一张卡先
    itemWidget:addItem(itemData.test1,1)
    card = s.submitTempItem("选一个最牛逼的道具")
    if (card ~= nil) then
        s.talk(npcs.jingcheng, "选中的道具是" .. card[0].Key)
    end
end

function reloadAllSceneStates()
    s.registerBarks(npcs.bear,{"嗷嗷嗷啊","呀哈哈！"},false,{0,1.5,0})
    s.registerBarks(npcs.mozhiyou,{"我是莫只有","想起我心爱的姑娘。。"},false,{0,1.75,0})
    -- 宝箱开启判断
    if flags.box_open == 1 then
        s.soloAnimState(objs.chest, objAnims.TurnOnDirect)
    end
end

function testUnRegisterBark()
    s.registerBarks(npcs.bear, nil)
end

function hello()
    s.talk(npcs.jingcheng, "要是能凑齐藏宝图，宝藏那不是手到擒来。")
    ret = s.selectTalk(npcs.jingcheng, "选择物体2的显示。", {
        "显示",
        "隐藏"
    })
    if ret == 0 then
        s.setActive(objs.objB, true)
    else
        s.setActive(objs.objB, false)
    end
    s.talk(npcs.jingcheng, "现在移动物体B到点位2")
    -- 移动objB到pos2
    s.move(objs.objB, pos.pos2,1)
    -- 移动objA到pos1
    s.talk(npcs.jingcheng, "现在移动物体A到点位1")
    s.move(objs.objA, pos.pos1,1)
    -- IK看向objA
    s.talk(npcs.jingcheng, "现在看向物体A")
    s.lookAt(npcs.jingcheng, CS.UnityEngine.Vector3.zero, objs.objA) -- 待封装Vector
end

function testSetFlagNew()
    -- 废弃set get Flag方法，直接赋值
    s.talk(npcs.jingcheng,"现在flag1的值是".. flags.flag1)
    s.talk(npcs.jingcheng, "设置1个flag")
    flags.flag1 = 1
    s.talk(npcs.jingcheng, "再设置多个flag")
    flags.flag1 = flags.flag1 + 3
    flags.flag2 = 3
    s.talk(npcs.jingcheng,"现在flag1的值是"..flags.flag1)
    s.talk(npcs.jingcheng,"现在flag2的值是"..flags.flag2)
    -- flag1大于flag2吗？
    if flags.flag1 > flags.flag2 then
        s.talk(npcs.jingcheng,"flag1大于flag2")
    else
        s.talk(npcs.jingcheng,"flag1小于等于flag2")
    end
end

-- 测试镜头
function testVcam()
    s.camera(cameras.vcam1, true)
    s.talk(npcs.jingcheng, "转到镜头1")
    s.camera(cameras.vcam1, false)
    s.talk(npcs.jingcheng, "关闭镜头1")
    s.camera(cameras.vcam2, true)
    s.talk(npcs.jingcheng, "转到镜头2")
    s.camera(cameras.vcam2, false)
    s.talk(npcs.jingcheng, "关闭镜头2")
end

-- 测试转身
function testTurn()
    s.talk(npcs.jingcheng, "转身")
    s.turnTo(npcs.jingcheng, objs.objA)
    s.talk(npcs.jingcheng, "转身")
    s.turnTo(npcs.jingcheng, objs.objB)
end

-- 测试动画
function testAnimation()
    s.talk(npcs.jingcheng, "播放受伤")
    s.animState(npcs.jingcheng, RoleTrigger.BHurt_Loop,true)
    s.talk(npcs.jingcheng, "取消播放")
    -- 取消播放
    s.animState(npcs.jingcheng, RoleTrigger.BHurt_Loop,false)
    s.talk(npcs.jingcheng, "播放大笑")
    s.animState(npcs.jingcheng, RoleTrigger.BLaugh)
    -- 取消播放
    s.talk(npcs.jingcheng, "取消播放")
    s.animState(npcs.jingcheng, RoleTrigger.BLaugh,false)
end

-- 测试箱子动画
function testChestAnimation()
    s.talk(npcs.jingcheng, "打开宝箱")
    s.soloAnimState(objs.chest, objAnims.TurnOn)
    -- 关闭
    s.talk(npcs.jingcheng, "关闭宝箱")
    s.soloAnimState(objs.chest, objAnims.TurnOff)
    -- 马上打开
    s.talk(npcs.jingcheng, "马上打开宝箱")
    s.soloAnimState(objs.chest, objAnims.TurnOnDirect)
    -- 马上关闭
    s.talk(npcs.jingcheng, "马上关闭宝箱")
    s.soloAnimState(objs.chest, objAnims.TurnOffDirect)
end

-- 测试战斗
function testBattle()
    s.talk(npcs.jingcheng, "开始战斗")
    local isWin,info = s.battle("测试战斗")
    s.talk(npcs.jingcheng, "战斗结果" .. tostring(isWin))
    s.talk(npcs.jingcheng, "战斗场景" .. info.BattleDuration)
    s.talk(npcs.jingcheng, "战斗结束")
end

function loadScene() 
    s.changeMap("Assets/H2.GameMap/Scenes/小熔炉.unity","位置/pos1")
end

-- 测试转身
function testTurn()
    s.talk(npcs.jingcheng, "转身")
    s.turnTo(npcs.npc1, npcs.jingcheng)
    s.talk(npcs.jingcheng, "转身回去")
end

-- 测试选择
function testSelect()
    s.talk(nil, "选择物体2的显示。")
    ret = s.selectTalk(nil, "选择物体2的显示。", {
        "显示",
        "隐藏"
    })
    if ret == 0 then
        s.setActive(objs.objB, true)
    else
        s.setActive(objs.objB, false)
    end
end

function testTrunto() 
    s.talk(npcs.mozhiyou, "转向物体")
    s.turnTo(npcs.mozhiyou, objs.objB)
    s.talk(npcs.mozhiyou, "转向物体")
    --s.turnTo(npcs.mozhiyou, objs.objB)
end

function testWait()
    s.talk(npcs.jingcheng, "等待1秒")
    s.wait(1)
    s.talk(npcs.jingcheng, "等待结束")
end

function testIKLookAt()
    -- jingcheng 看向 npc1
    s.talk(npcs.jingcheng, "看向npc1")
    s.lookAt(npcs.jingcheng, CS.UnityEngine.Vector3.zero, npcs.npc1) -- 待封装Vector
    s.talk(npcs.jingcheng, "看向npc1的头顶")
    s.lookAt(npcs.jingcheng, CS.UnityEngine.Vector3(0, 1.5, 0), npcs.npc1) -- 待封装Vector
    s.talk(npcs.jingcheng, "看向npc1的脚")
    s.lookAt(npcs.jingcheng, CS.UnityEngine.Vector3(0, -1.5, 0), npcs.npc1) -- 待封装Vector
end

function testOpenChest()
    s.talk(npcs.jingcheng, "打开宝箱")
    flags.box_open = 1
    s.openChest(objs.chest,"farmfield_Enemy_Common_Bonus")
end

function testAddTask()
    s.talk(npcs.jingcheng, "添加任务")
    s.addTask("200200", "1")
end

function tastFinishTask()
    s.talk(npcs.jingcheng, "完成任务第一步")
    s.finishTask("200200","1","2")
end

-- 测试任务计数的显示
function testCountTask()
    local f = flags.flag1
    flags.flag1 = f + 1
    s.talk(npcs.jingcheng, "设置flag为" .. flags.flag1)
    if flags.flag1 == 3 then
        s.talk(npcs.jingcheng, "任务完成")
        s.finishTask("200200", "1", "2")
    end
end

function testDefaultSetPosAndRotate()
    s.talk(npcs.bear, "演示黑屏设置默认位置")
end

function testForCameraChange()
    s.talk(npcs.jingcheng, "演示镜头渐入")
    print(blendHintEnum.EaseIn)
    s.camera(cameras.vcam1, true,true, blendHintEnum.EaseIn,0.5)
    s.talk(npcs.jingcheng, "演示镜头渐出")
    s.camera(cameras.vcam2, true,true, blendHintEnum.EaseIn,0.5)
end

function testSetPos()
    s.talk(npcs.jingcheng, "设置位置")
    s.setPos(npcs.jingcheng2, pos.pos4)
    s.talk(npcs.jingcheng, "设置位置")
    s.setPos(npcs.jingcheng2, pos.pos3)
    
end


function testMove()
    s.talk(npcs.jingcheng, "移动到点位1")
    s.move(npcs.jingcheng, pos.pos1, 1)
    s.talk(npcs.jingcheng, "移动到点位2")
    s.move(npcs.jingcheng, pos.pos2, 1)
end

function testAsyncFunctions()
    -- 测试异步播放动画
    --s.playAnimationAsync(npcs.jingcheng, "anim_idle")
    -- 测试异步人物IK看向
    --s.lookAtAsync(npcs.jingcheng, CS.UnityEngine.Vector3(0, 1.5, 0), npcs.npc1)
    -- 测试异步人物转向
    s.turnToAsync(npcs.jingcheng, npcs.npc1)
    -- 测试异步移动
    s.moveAsync(npcs.bear, pos.pos1, 1, false, "", 0, true, true, true, 0.5)
    s.moveAsync(npcs.mozhiyou, pos.pos2, 1, false, "", 0, true, true, true, 0.5)
    -- 测试异步控制镜头
    --s.cameraAsync(cameras.vcam1, true, false, blendHintEnum.EaseIn, 0.5, true, true)
    -- 测试异步黑屏
    s.blackScreen()
    s.wait(1)
    -- 测试异步亮屏
    s.lightScreen()
end

function testTalkWithHead()
    s.talk(npcs.jingcheng, "思索", "思索")
    s.talk(npcs.jingcheng, "惊讶", "惊讶")
    s.talk(npcs.jingcheng, "回忆", "回忆")
end

function testPlayTimeline()
    s.talk(npcs.jingcheng, "播放故事板")
    s.playTimeline(assets.timeline1)
end

function testAsideTalk()
    s.appendAsyncAside("楚青",0,"楚青：我是楚家庄的青年弟子，负责在庄内巡逻。")
    s.wait(1)
    s.appendAsyncAside("荆成",1,"荆成：我是荆成，楚家庄的客卿弟子。")
    s.wait(1)
    s.appendAsyncAside("楚青",2,"楚青：我是楚家庄的青年弟子，负责在庄内巡逻。")
    s.wait(3)
    s.appendAsyncAside("楚青",0,"楚青：我是楚家庄的青年弟子，负责在庄内巡逻。")

end

-- 测试切换镜头
function testSwitchCamera()
    s.talk(npcs.jingcheng, "切换镜头")
    s.camera(cameras.vcam1, true)
    s.wait(1)
    s.camera(cameras.vcam2, true)
    s.wait(1)
    s.camera(cameras.vcam1, true)
end

function testPrintTeamInfo()
    -- local heroCardGroup = s.getHeroCardGroup()
    -- for i = 0, heroCardGroup.Length - 1 do
    --     print(heroCardGroup[i])
    -- end
end

-- 测试记忆碎片
function touchMemoryPieces1()
    -- local ret = s.showMemorySlot(1)
    -- if (ret == true) then
    --     testAvg()
    -- end
end

function testAvg()
    local capi = require("ChapterApi")
    capi.startMemorySlot("qyl_testAvg")
    local script = require("Chapters/测试")
    script.testAvg()
    capi.finishMemorySlot("qyl_testAvg")
    s.nextStep("testAvg", "")
end

function testLocalMapStory()
    s.changeMapStory("主线_青羽录10_楚家庄后山夜")
end

function testLocalBattle()
    local isWin = s.battle("开放世界_破败楚家庄_初始战斗")
    if isWin then
        s.talk(npcs.jingcheng, "战斗胜利")
    else
        s.talk(npcs.jingcheng, "战斗失败")
    end
end

function testNewbieBattle()
    local capi = require("ChapterApi")
    local script = require("Chapters/测试")
    capi.startMemorySlot("qyl_testBattle")
    script.testNewbieBattle()
    capi.finishMemorySlot("qyl_testBattle")
end

function testMapStory()
    local capi = require("ChapterApi")
    local script = require("Chapters/测试")
    capi.startMemorySlot("qyl_testMapStory")
    script.testMapStory()
    capi.finishMemorySlot("qyl_testMapStory")
end

function onReady()
    print("onReady")
end
