---@type flags_挑战_风雪神道
local flags = require("MapStories/挑战_风雪神道/flags_挑战_风雪神道")
local s = require("StoryApi")

s.loadFlags(flags)


---@class 挑战_风雪神道_StringItemData
local stringItemData = {
}

---@class 挑战_风雪神道_ItemData
local itemData = {
    StructureFire = {key = "StructureFire",showName = "营地篝火", rare = 2,desc = "建造需求:资源 × <color=#157DEC>5</color>\n\n效果:战斗中初始怒气提高5点",icon = "huoba", canUse = true},
    StructureFood = {key = "StructureFood",showName = "烤肉架子", rare = 2,desc = "建造需求:补给 × <color=#157DEC>7</color>\n\n效果:战斗中，我方侠客每5秒获得5%的最大生命恢复",icon = "food", canUse = true},
    StructureAlcohol = {key = "StructureAlcohol",showName = "酿酒桶", rare = 2,desc = "建造需求:补给 × <color=#157DEC>7</color>, 资源 × <color=#157DEC>3</color>\n\n效果:战斗中怒气增长速度提高30%",icon = "jiutan", canUse = true},
    StructureClothes = {key = "StructureClothes",showName = "缝纫机", rare = 2,desc = "建造需求:资源 × <color=#157DEC>10</color>\n\n效果:战斗开始前15秒，我方所有侠客处于无敌状态",icon = "caisezhixian", canUse = true},
    StructureKnifeStone = {key = "StructureKnifeStone",showName = "磨刀石", rare = 2,desc = "建造需求:资源 × <color=#157DEC>12</color>\n\n效果:战斗开始前20秒，我方所有侠客造成的伤害必定产生暴击",icon = "tieding", canUse = true},
    StructureMap = {key = "StructureMap",showName = "风雪神道地图", rare = 2,desc = "建造需求:补给 × <color=#157DEC>8</color>, 资源 × <color=#157DEC>8</color>\n\n效果:遇到非首领敌人时，可以消耗1补给，直接击败该敌人",icon = "map", canUse = true},
    StructurePet = {key = "StructurePet",showName = "宠物蛋", rare = 2,desc = "建造需求:补给 × <color=#157DEC>16</color>\n\n效果:战斗开始时，获得随机一名善良猎人友军助战",icon = "egg", canUse = true},
    StructureEnhance = {key = "StructureEnhance",showName = "锻造台", rare = 2,desc = "建造需求:补给 × <color=#157DEC>8</color>, 资源 × <color=#157DEC>12</color>\n\n效果:战斗中，我方所有策略卡的等级提高2级",icon = "qixingchui", canUse = true},

    StructureFireDone = {key = "StructureFireDone",showName = "营地篝火(完成)", rare = 2,desc = "已建造\n\n效果:战斗中初始怒气提高5点",icon = "huoba", canUse = false},
    StructureFoodDone = {key = "StructureFoodDone",showName = "烤肉架子(完成)", rare = 2,desc = "已建造\n\n效果:战斗中，我方侠客每5秒获得5%的最大生命恢复",icon = "food", canUse = false},
    StructureAlcoholDone = {key = "StructureAlcoholDone",showName = "酿酒桶(完成)", rare = 2,desc = "已建造\n\n效果:战斗中怒气增长速度提高30%",icon = "jiutan", canUse = false},
    StructureClothesDone = {key = "StructureClothesDone",showName = "缝纫机(完成)", rare = 2,desc = "已建造\n\n效果:战斗开始前15秒，我方所有侠客处于无敌状态",icon = "caisezhixian", canUse = false},
    StructureKnifeStoneDone = {key = "StructureKnifeStoneDone",showName = "磨刀石(完成)", rare = 2,desc = "已建造\n\n效果:战斗开始前20秒，我方所有侠客造成的伤害必定产生暴击",icon = "tieding", canUse = false},
    StructureMapDone = {key = "StructureMapDone",showName = "风雪神道地图(完成)", rare = 2,desc = "已建造\n\n效果:遇到非首领敌人时，可以消耗1补给，直接击败该敌人",icon = "map", canUse = false},
    StructurePetDone = {key = "StructurePetDone",showName = "宠物蛋(完成)", rare = 2,desc = "已建造\n\n效果:战斗开始时，获得随机一名善良猎人友军助战",icon = "egg", canUse = false},
    StructureEnhanceDone = {key = "StructureEnhanceDone",showName = "锻造台(完成)", rare = 2,desc = "已建造\n\n效果:战斗中，我方所有策略卡的等级提高2级",icon = "qixingchui", canUse = false},

    TrashDone = {key = "TrashDone",showName = "补给", rare = 1,desc = "当前已收集的补给物资",icon = "tili", canUse = false},
    ResourceDone = {key = "ResourceDone",showName = "资源", rare = 1,desc = "当前已收集的资源物资",icon = "mucai", canUse = false},
}

local stringWidget = StringWidget:new(stringItemData,flags)
local itemWidget = ItemWidget:new(itemData,flags)

local stringWidgetInfo = {
    title = "统计信息",
    desc = "记录在当前副本中已建造的各类工具并统计其效果。\n",
    items = stringItemData,
    widget = stringWidget
}

local itemWidgetInfo = {
    title = "建造面板",
    items = itemData,
    widget = itemWidget
}

extensions = {
    useWidgets = {"ItemWidget"},
    widgets = {
        ItemWidgetInfo = itemWidgetInfo,
        --StringWidgetInfo = stringWidgetInfo
    }
}

s.syncExtendsInfo(extensions)
return extensions