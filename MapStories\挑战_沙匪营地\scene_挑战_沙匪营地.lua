
---@type 挑战_沙匪营地
local context = require("MapStories/挑战_沙匪营地/scene_挑战_沙匪营地_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger
---@type RpgMapApi
local rapi = require("RpgMapApi")

local rpgFlags = require("RpgMap/flags")
rapi.loadFlags(rpgFlags)

s.loadFlags(context.flags)
---@type flags_挑战_沙匪营地
local flags = context.flags

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    --有bug，在start里播放会被打断。
    s.playMusic("kingdom")

    --设置主角位置
    if(s.getChallengeMapPersistentFlag("挑战_风雪神道","播放过开场演出") == 0) then
        s.setPos(npcs.zhujue,pos.pre_start)
    else
        s.setPos(npcs.zhujue,pos.start)
    end
    
    --添加副本内主线任务
    if not s.hasTaskGroup("沙匪营地副本内")then
        s.setTaskStep("沙匪营地副本内","beatBoss")
    end

    LoadRecord()

    s.readyToStart()
    
    --开场演出，一次性
    if(s.getChallengeMapPersistentFlag("挑战_风雪神道","播放过开场演出") == 0) then
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_首次门口对话", nil, 0)
        s.setChallengeMapTicketFlag("挑战_沙匪营地","播放过开场演出",1)
    end
end

--手动退出副本或杀进程，重新载入副本时，判断哪些需要刷新，相当于写了一个存档功能
function LoadRecord()
    --隐藏本次副本已击败的Boss。
    if(flags.boss1Defeated == 1)then
        s.setActive(npcs.boss_1,false)
    end
    if(flags.boss2Defeated == 1)then
        s.setActive(npcs.boss_2,false)
    end
    if(flags.boss3Defeated == 1)then
        s.setActive(npcs.boss_3,false)
    end
    if(flags.boss4Defeated == 1)then
        s.setActive(npcs.boss_4,false)
    end

    s.setActive(npcs.enemy_2_1,true)

    --如果此次门票已击败所有Boss，则显示仓库的传送点。
    if(flags.boss1Defeated == 1
    and flags.boss2Defeated == 1
    and flags.boss3Defeated == 1
    and flags.boss4Defeated == 1)then
        s.setActive(objs.Trigger_Leave_2,true)
    end

    --此账号首次打开枯井机关后，后面任意情况再进副本无需再次打开。
    if(s.getChallengeMapPersistentFlag("挑战_沙匪营地","首次走进枯井") == 1)then
        s.setActive(objs.Trigger_Well_Enter,false)
        s.setActive(objs.Trigger_Well_Discover,true)
    elseif(s.getChallengeMapPersistentFlag("挑战_沙匪营地","首次走进枯井") == 2)then
        s.setActive(objs.Trigger_Well_Enter,false)
        s.setActive(objs.Trigger_Well_Discover,false)
        s.setActive(objs.Trigger_Well_Down,true)
        s.setActive(objs.Trigger_Well_Up,true)
    end
end

--走出峡谷的演出
function Enter()
    s.setActive(objs.Trigger_Camera_Start,false)
    s.playMusic("wendingyuntian")

    --是否播放过走出峡谷的演出
    if(s.getChallengeMapPersistentFlag("挑战_沙匪营地","首次走出峡谷") == 0)then
        s.camera(cameras.camera_enter,true,true,blendHintEnum.EaseInOut,3,true,true)
        s.wait(1)
        s.camera(cameras.camera_enter,false,true,blendHintEnum.EaseInOut,1.5,true,true)
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_首次走出峡谷对话", nil, 0)
        s.setChallengeMapPersistentFlag("挑战_沙匪营地","首次走出峡谷",1)
    end
end

--触发小怪1战斗
function EnemyBattle1(roleKey)
    s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_小怪1对话", nil, 0)
    local isWin,bResult = s.challengeMapBattle("1")
end

--触发小怪2战斗
function EnemyBattle2(roleKey)
    --一次性演出
    if(s.getChallengeMapPersistentFlag("挑战_沙匪营地","播放过小怪2战前演出") == 0)then
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_首次小怪2对话", nil, 0)
        s.setChallengeMapPersistentFlag("挑战_沙匪营地","播放过小怪2战前演出",1)
    end
    --重复演出
    s.turnTo(npcs.enemy_2, npcs.zhujue)
    s.turnTo(npcs.enemy_2_1, npcs.zhujue)
    s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_小怪2对话", nil, 0)
    local isWin,bResult = s.challengeMapBattle("2")
end

--触发Boss1战斗
function BossBattle1()
    s.camera(cameras.camera_boss1,true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.turnTo(npcs.boss_1, npcs.zhujue,true)
    s.turnTo(npcs.zhujue, npcs.boss_1,true)

    if(s.getChallengeMapPersistentFlag("挑战_沙匪营地","播放过Boss1战前演出") == 0)then
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_首次Boss1战前对话", nil, 0)
        s.setChallengeMapPersistentFlag("挑战_沙匪营地","播放过Boss1战前演出",1)
    else
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_Boss1战前对话", nil, 0)
    end

    --触发战斗
    local isWin,bResult = s.challengeMapBattle("101")
    if(isWin)then
        s.camera(cameras.camera_boss1,false)
        s.setActive(npcs.boss_1,false)
        flags.boss1Defeated = 1

        --设置天书flag，记录BOSS
        s.setGuideBookFlag("boss_huobuqi")

        FinishMainTask()
    end
end

--触发Boss2战斗
function BossBattle2()
    s.camera(cameras.camera_boss2,true,true,blendHintEnum.EaseInOut,1.5,true,true)

    if(s.getChallengeMapPersistentFlag("挑战_沙匪营地","播放过Boss2战前演出") == 0)then
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_首次Boss2战前对话", nil, 0)
        s.setChallengeMapPersistentFlag("挑战_沙匪营地","播放过Boss2战前演出",1)
    else
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_Boss2战前对话", nil, 0)
    end

    --触发战斗
    local isWin,bResult = s.challengeMapBattle("102")
    if(isWin)then
        s.camera(cameras.camera_boss2,false)
        s.setActive(npcs.boss_2,false)
        flags.boss2Defeated = 1

        --设置天书flag，记录BOSS
        s.setGuideBookFlag("boss_shimanshen")

        FinishMainTask()
    end
end

--进入上山道路的演出
function EnterMusic()
    s.setActive(objs.Trigger_Music_Mount,false)
    s.playMusic("qinmo")
end

--触发Boss3战斗
function BossBattle3()
    s.camera(cameras.camera_boss3,true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.turnTo(npcs.boss_3, npcs.zhujue,true)
    s.turnTo(npcs.zhujue, npcs.boss_3,true)

    if(s.getChallengeMapPersistentFlag("挑战_沙匪营地","播放过Boss3战前演出") == 0)then
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_首次Boss3战前对话", nil, 0)
        s.setChallengeMapPersistentFlag("挑战_沙匪营地","播放过Boss3战前演出",1)
    else
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_Boss3战前对话", nil, 0)
    end

    --触发战斗
    local isWin,bResult = s.challengeMapBattle("103")
    if(isWin)then
        s.camera(cameras.camera_boss3,false)
        s.setActive(npcs.boss_3,false)
        flags.boss3Defeated = 1

        -- 完成大世界任务
        if(s.getChallengeMapPersistentFlag("挑战_沙匪营地","首次击败过大当家") == 0)then
            s.setChallengeMapPersistentFlag("挑战_沙匪营地","首次击败过大当家",1)

            --判断是否持有漠上曲任务，有就完成。
            if s.getCurrentTaskStep("漠上曲") == "toGucheng" then
                s.setRpgMapFlag("msq_status_flag",2)
                s.setTaskStep("漠上曲","finished")
            end

            ---注入角逐塞外蜃石统计
            if rpgFlags.jzsw_hsb_shenshi == 0 then 
                s.setRpgMapFlag("jzsw_hsb_shenshi",1) 
                if rpgFlags.jzsw_shenshi_count < 3 then 
                    s.setRpgMapFlag("jzsw_shenshi_count",rpgFlags.jzsw_shenshi_count + 1) 
                end
            end
        end

        --设置天书flag，记录BOSS
        s.setGuideBookFlag("boss_qiaosaiqiu")

        s.completeChallengeMap()

        FinishMainTask()
    end
end

--首次发现枯井第一步
function EnterSecret()
    s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_首次枯井对话_1", nil, 0)
    s.setActive(objs.Trigger_Well_Enter,false)
    s.setActive(objs.Trigger_Well_Discover,true)
    s.setChallengeMapPersistentFlag("挑战_沙匪营地","首次走进枯井",1)
end

--首次发现枯井第二步
function DiscoverSecret()
    s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_首次枯井对话_2", nil, 0)
    s.setActive(objs.Trigger_Well_Discover,false)
    s.setActive(objs.Trigger_Well_Down,true)
    s.setActive(objs.Trigger_Well_Up,true)
    s.setChallengeMapPersistentFlag("挑战_沙匪营地","首次走进枯井",2)
end

--进入枯井
function DownSecret()
    s.blackScreen()
    s.setPos(npcs.zhujue, pos.Pos_Well_Bottom)
    s.lightScreen()
end

--离开枯井
function UpSecret()
    s.blackScreen()
    s.setPos(npcs.zhujue, pos.Pos_Well_Roof)
    s.lightScreen()
end

--进入枯井密道演出1
function EnterSecretDialogue1()
    s.setActive(objs.Trigger_Well_Dialogue_1,false)
    s.appendAsyncAside(npcs.boss_4,0,"哈哈哈哈哈哈！","","默认")
end

--进入枯井密道演出2
function EnterSecretDialogue2()
    s.setActive(objs.Trigger_Well_Dialogue_2,false)
    s.appendAsyncAside(npcs.boss_4,0,"桥塞狄，看来还是我技高一筹！","","默认")
end

--触发Boss4战斗
function BossBattle4()
    s.camera(cameras.camera_boss4,true,true,blendHintEnum.EaseInOut,1.5,true,true)
    s.turnTo(npcs.zhujue, npcs.boss_4)

    if(s.getChallengeMapPersistentFlag("挑战_沙匪营地","播放过Boss4战前演出") == 0)then
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_首次Boss4战前对话_1", nil, 0)
        s.turnTo(npcs.boss_4, npcs.zhujue)
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_首次Boss4战前对话_2", nil, 0)
        s.setChallengeMapPersistentFlag("挑战_沙匪营地","播放过Boss4战前演出",1)
    else
        s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_Boss4战前对话", nil, 0)
    end

    --触发战斗
    local isWin,bResult = s.challengeMapBattle("104")
    if(isWin)then
        s.camera(cameras.camera_boss4,false)
        s.setActive(npcs.boss_4,false)
        flags.boss4Defeated = 1

        --设置天书flag，记录BOSS
        s.setGuideBookFlag("boss_shunyi")

        FinishMainTask()
    end
end

--是否完成副本主线任务
function FinishMainTask()
    if(flags.boss1Defeated == 1
    and flags.boss2Defeated == 1
    and flags.boss3Defeated == 1)then
        s.wait(1)
        if(flags.boss4Defeated == 0)then
            s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_击败3个Boss对话", nil, 0)
            if s.hasTaskGroup("沙匪营地副本内")then
                s.setTaskStep("沙匪营地副本内","leaveScene1")
            end
        elseif(flags.boss4Defeated == 1)then
            s.runAvgTag("副本/副本_沙匪营地/副本_沙匪营地", "副本_沙匪营地_击败所有Boss对话", nil, 0)
            --如果本次副本老大、老二、老三都被击败，则显示传送门。
            s.setActive(objs.Trigger_Leave_2,true)
            if s.hasTaskGroup("沙匪营地副本内")then
                s.setTaskStep("沙匪营地副本内","leaveScene2")
            end
        end
    end
end

--场景出口
function Leave()
    s.showConfirmResetChallengeMap()
    if s.getCurrentTaskStep("沙匪营地副本内") == "leaveScene2" then
        s.setTaskStep("沙匪营地副本内","finished")
    end
end