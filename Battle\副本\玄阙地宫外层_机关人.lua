local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()

end

function start()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_触发器", 1, eTeam, bcapi.Vector2(0, 0), 0)
        bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0.1, winTalk)
    end)
end

function winTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("玄阙地宫外层_月无心", "遗憾……不舍……")
        bcapi.talk("玄阙地宫外层_月无心", "这便是……心……")
        bcapi.talk("副本_玄阙地宫_巧筑", "这人形到底是因何而行动，又是为何被囚禁于此？")
        bcapi.talk("副本_玄阙地宫_木茗", "玄阙地宫……看来还有更多秘密等着我们去探寻。")
        bcapi.resume_and_show_ui()  
        bcapi.win()
    end)
end