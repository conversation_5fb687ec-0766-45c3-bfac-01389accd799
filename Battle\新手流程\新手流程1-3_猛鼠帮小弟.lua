local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.ta_event_track("PlayerStartStory", "Step", "BattleTutor_Step4")

    battle.HideCardLevel = true

    bcapi.disable_auto_battle()
    bcapi.disable_speed_up()
    -- bcapi.disable_dart()
    
    bcapi.hide_ui("疗伤")
    bcapi.hide_ui("身法按钮")
    bcapi.hide_ui("自动按钮")

    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        -- 创建玩家角色
    bcapi.create_join_new_player_with_card_newbie(2, team, bcapi.Vector2(-2, 0), 0)
    -- 创建荆成和云舞笙
    bcapi.create_join_role_with_card("荆成", 2, team, bcapi.Vector2(-4, 3), 0)
    bcapi.create_join_role_with_card("云舞笙", 2, team, bcapi.Vector2(-4, -3), 0)
    bcapi.create_join_role_with_card("药曲老", 2, team, bcapi.Vector2(-6, 0), 0)
        ZhuJue = bcapi.get_player_role("主角_新手战斗")
        ---@diagnostic disable-next-line
        ZhuJue.Buff:AddBuff("通用_教学关_加攻速", 300, 1, 1, ZhuJue)
        bcapi.add_strategy_card("吐纳法", 1, team)
        bcapi.add_strategy_card("大藏心经", 1, team)
        bcapi.add_strategy_card("血沸剑魂", 1, team)
        -- 创建敌人
        bcapi.create_join_role("新手_猛鼠帮_小弟_拳", 1, eTeam, bcapi.Vector2(2, -2), 0)
        bcapi.create_join_role("新手_猛鼠帮_小弟_拳", 1, eTeam, bcapi.Vector2(2, 2), 0)
        bcapi.create_join_role("新手_猛鼠帮_小弟_脚", 1, eTeam, bcapi.Vector2(4, -4), 0)   
        bcapi.create_join_role("新手_猛鼠帮_小弟_脚_教学", 1, eTeam, bcapi.Vector2(4, 0), 0)
        bcapi.create_join_role("新手_猛鼠帮_小弟_脚", 1, eTeam, bcapi.Vector2(4, 4), 0)
        bcapi.create_join_role("新手_猛鼠帮_小弟_暗器_教学1", 1, eTeam, bcapi.Vector2(6, 2), 0)
        bcapi.create_join_role("新手_猛鼠帮_小弟_暗器_教学2", 1, eTeam, bcapi.Vector2(6, -2), 0)
    end)
end

function start()
    EnemyTalk()
end

function EnemyTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("新手_猛鼠帮_小弟_暗器#新手_猛鼠帮_小弟_暗器", "兄弟们上，不过一愣头青带一糟平民老人，怕什么！")
        bcapi.resume_and_show_ui()
        bcapi.wait(2)
        bcapi.pause_and_hide_ui()
        bcapi.talk("药曲老#药曲老", "阁主，敌人来势汹汹，需要使用策略卡「光明圣火」配合云舞笙，将敌人束缚并击溃！")
        bcapi.resume_and_show_ui()
        zhujue = bcapi.get_player_role("主角_新手战斗")
        YaoQulao = bcapi.get_player_role("药曲老")
        JingCheng = bcapi.get_player_role("荆成")
        zhujue.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, zhujue)
        YaoQulao.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, YaoQulao)
        JingCheng.Buff:AddBuff("通用_无法选中_无显示", 0.5, 1, 1, JingCheng)
        bcapi.show_strategy_guide("光明圣火", "云舞笙", "拖拽卡牌到你的位置，然后松手释放", 10)
        bcapi.add_strategy_card("光明圣火", 10, team, true)
        bcapi.wait(1)
        bcapi.show_unique_skill_guide("云舞笙", "新手_猛鼠帮_小弟_脚_教学", "拖拽绝招按钮至指定区域，释放绝招「定魂天歌」")
        bcapi.wait(3)
        bcapi.pause_and_hide_ui()
        bcapi.talk("药曲老#药曲老", "阁主，后面的猛鼠帮小弟会呼叫其他同伴前来增援，需使用「身法技」配合荆成优先将其歼灭！")
        bcapi.resume_and_show_ui()
        bcapi.show_ui("身法按钮");
        bcapi.show_dart_guide("主角_新手战斗", "新手_猛鼠帮_小弟_暗器_教学2", "点击身法按钮，进入身法模式", "拖拽角色头像至敌人身边，释放身法")
        bcapi.wait(0.5)
        bcapi.show_unique_skill_guide("荆成", "新手_猛鼠帮_小弟_暗器_教学1", "拖拽绝招按钮至指定目标，释放绝招「阴阳无极」")
        bcapi.wait(3)
        bcapi.show_pop_info("灵活使用技能与绝招，击败敌人吧！!", 2)
    end)
end