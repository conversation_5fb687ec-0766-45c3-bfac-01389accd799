--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_楚家庄New/flags_挑战_楚家庄New")


---@class 角色_挑战_楚家庄后山
local npcs = {
    zhujue = "角色/zhujue",
    boss3 = "角色/boss3",
    enemy_13 = "角色/enemy_13",
    enemy_14 = "角色/enemy_14",
    npc_1 = "角色/npc_1",
    npc_2 = "角色/npc_2",
    npc_3 = "角色/npc_3",
    npc_4 = "角色/npc_4",
    npc_5 = "角色/npc_5",
    npc_6 = "角色/npc_6",
    npc_7 = "角色/npc_7",
    npc_8 = "角色/npc_8",
    npc_9 = "角色/npc_9",
    npc_10 = "角色/npc_10",
    npc_11 = "角色/npc_11",
    npc_12 = "角色/npc_12",
}

---@class 物体_挑战_楚家庄后山
local objs = {
    boss3 = "物体/boss3",
    quit = "物体/quit",
    teleport5 = "物体/teleport5",
    tingzi = "物体/tingzi",
    trigger_road = "物体/trigger_road",
    medicinal_3 = "物体/medicinal_3",
    trigger_medicinal_3 = "物体/trigger_medicinal_3",
    chest2 = "物体/chest2",
}

---@class 相机_挑战_楚家庄后山
local cameras = {
    tingzi = "相机/tingzi",
    boss3_1 = "相机/boss3_1",
}

---@class 位置_挑战_楚家庄后山
local pos = {
    start = "位置/start",
    pos_boss_1 = "位置/pos_boss_1",
    pos_tingzi = "位置/pos_tingzi",
}

---@class 资产_挑战_楚家庄后山
local assets = {
    Timeline_1 = "资产/Timeline_1",
}

---@class 动作_挑战_楚家庄后山
local animationClips = {
}

---@class 挑战_楚家庄后山
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
