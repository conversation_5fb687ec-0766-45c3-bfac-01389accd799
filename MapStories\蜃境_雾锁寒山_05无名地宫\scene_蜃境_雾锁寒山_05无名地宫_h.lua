--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_雾锁寒山_05无名地宫/flags_蜃境_雾锁寒山_05无名地宫")


---@class 角色_蜃境_雾锁寒山_05无名地宫
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "wshs_13"
    wshs_13 = "角色/wshs_13",
    --- "wshs_14"
    wshs_14 = "角色/wshs_14",
    --- "wshs_15"
    wshs_15 = "角色/wshs_15",
    --- "wshs_16"
    wshs_16 = "角色/wshs_16",
    --- "enemy_1"
    enemy_1 = "角色/enemy_1",
    --- "enemy_2"
    enemy_2 = "角色/enemy_2",
    --- "enemy_3"
    enemy_3 = "角色/enemy_3",
    --- "elite_4"
    elite_4 = "角色/elite_4",
    --- "elite_5"
    elite_5 = "角色/elite_5",
    --- "boss_6"
    boss_6 = "角色/boss_6",
    --- "elite_7"
    elite_7 = "角色/elite_7",
    --- "elite_8"
    elite_8 = "角色/elite_8",
    --- "祖师爷"
    zushiye = "角色/zushiye",
    --- "role_elite_7"
    role_elite_7 = "角色/role_elite_7",
    --- "role_elite_8"
    role_elite_8 = "角色/role_elite_8",
}

---@class 物体_蜃境_雾锁寒山_05无名地宫
local objs = {
    --- "出口"
    exit = "物体/exit",
    --- "chest_1"
    chest_1 = "物体/chest_1",
    --- "chest_2"
    chest_2 = "物体/chest_2",
    --- "chest_3"
    chest_3 = "物体/chest_3",
    door_left = "物体/door_left",
    door_right = "物体/door_right",
    enemy_1_collider = "物体/enemy_1_collider",
    --- "wshs5_1007"
    wshs5_1007 = "物体/wshs5_1007",
    --- "wshs5_1008"
    wshs5_1008 = "物体/wshs5_1008",
    evilAirChest_1 = "物体/evilAirChest_1",
    evilAir_1 = "物体/evilAir_1",
    evilAir_2 = "物体/evilAir_2",
    evilAirChest_2 = "物体/evilAirChest_2",
    enemy_2_collider = "物体/enemy_2_collider",
    enemy_3_collider = "物体/enemy_3_collider",
    enemy_4_collider = "物体/enemy_4_collider",
    enemy_5_collider = "物体/enemy_5_collider",
    enemy_6_collider = "物体/enemy_6_collider",
}

---@class 相机_蜃境_雾锁寒山_05无名地宫
local cameras = {
    cam_start = "相机/cam_start",
    cam_main = "相机/cam_main",
    cam_start1 = "相机/cam_start1",
}

---@class 位置_蜃境_雾锁寒山_05无名地宫
local pos = {
    --- "初始位置"
    pos_start = "位置/pos_start",
    enemy_1 = "位置/enemy_1",
    enemy_2 = "位置/enemy_2",
    enemy_3 = "位置/enemy_3",
    enemy_4 = "位置/enemy_4",
    enemy_5 = "位置/enemy_5",
    enemy_6 = "位置/enemy_6",
}

---@class 资产_蜃境_雾锁寒山_05无名地宫
local assets = {
}

---@class 动作_蜃境_雾锁寒山_05无名地宫
local animationClips = {
}

---@class 蜃境_雾锁寒山_05无名地宫
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
