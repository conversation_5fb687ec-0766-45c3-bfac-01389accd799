local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
end

function start()
    bcapi.add_timer_trigger(0, TalkStart)
end

function TalkStart()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()

        bcapi.talk("主角#主角", "这人好像是......？黑刹教的混元手——雷正！")

        local playTb = {}
        local playerTeam = bcapi.get_player_team()
        for i = 0, playerTeam.RoleList.Count-1 do
            local role = playerTeam.RoleList[i]
            if(role.Stat.IsSummon == false)then
                table.insert(playTb,role.Key .. "#" .. role.Key)
            end
        end
        if #playTb > 0 then
            local randomIndex = math.random(1,#playTb)
            local randomRole = playTb[randomIndex]
            bcapi.talk(randomRole, "想不到，这位传闻中的药婆竟如此冷血，自己人都......")
        end

        bcapi.talk("副本_黑刹教_药婆#副本_黑刹教_药婆", "你们几个小辈，胆子却大，竟有胆闯这总坛。")
        bcapi.talk("副本_黑刹教_药婆#副本_黑刹教_药婆", "我老太婆多年不曾出手，今日怕是躲不过。也罢，让我老太婆活动活动筋骨！")
        bcapi.resume_and_show_ui()
    end)
end