local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
--玩家队伍
local team = bcapi.get_team(0)
--敌人队伍
local eTeam = bcapi.get_team(1)

function init()
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        bcapi.create_join_role("玉尺", 75, team, bcapi.Vector2(-4, 0), 0)
    end)
end

function start()
    StartTalk()
end

function StartTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        team = bcapi.get_team(0)
        bcapi.talk("玉尺", "思思！你为何要这么做！")
        bcapi.talk("玉尺", "你背后是谁指使！你们到底是为何而来！")
        bcapi.talk("玉尺", "这地牢里是不是已经关押过许多人了，你掳人是不是要送去玄阙宫！")
        bcapi.talk("幻箫罗刹", "聋子，我已经说了我不是思思！")
        bcapi.resume_and_show_ui()
    end)
end

function BattleLose()
    bcapi.lose()
end

function BattleWin()
    bcapi.win()
end
