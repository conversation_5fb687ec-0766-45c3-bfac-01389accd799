local bcapi = require("BattleClientA<PERSON>")
local battle = args["battle"]
bcapi.battle = battle

function init()
end

function start()
    bcapi.add_timer_trigger(0, TalkStart)
    bcapi.add_timer_trigger(90, SecondEnemys)
end

function TalkStart()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_沙匪营地_老二#副本_沙匪营地_老二", "我三弟素来鲁莽，自上山之日，便知有此一天！")
        bcapi.talk("副本_沙匪营地_老二#副本_沙匪营地_老二", "命也，命也！皆乃此世命数！既如此，那便由我试试几位施主的命数！")
        bcapi.resume_and_show_ui()
    end)
end

function SecondEnemys()
    bcapi.async_call(function()
        bcapi.create_join_role("副本_沙匪营地_沙匪_弓箭", 40, bcapi.get_team(1), bcapi.Vector2(5, 2), 0.1)
        bcapi.create_join_role("副本_沙匪营地_沙匪_弓箭", 40, bcapi.get_team(1), bcapi.Vector2(5, -2), 0.1)
    end)
end