--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/副本_醉花荫/flags_副本_醉花荫")


---@class 角色_副本_醉花荫
local npcs = {
    zhujue = "角色/zhujue",
    boss_1 = "角色/boss_1",
    boss_2 = "角色/boss_2",
    boss_3 = "角色/boss_3",
    boss_4 = "角色/boss_4",
    enemy_101 = "角色/enemy_101",
    enemy_101_1 = "角色/enemy_101_1",
    enemy_102 = "角色/enemy_102",
    enemy_201 = "角色/enemy_201",
    enemy_202 = "角色/enemy_202",
    enemy_3 = "角色/enemy_3",
    enemy_401 = "角色/enemy_401",
    enemy_402 = "角色/enemy_402",
    enemy_501 = "角色/enemy_501",
    enemy_502 = "角色/enemy_502",
    enemy_601 = "角色/enemy_601",
    enemy_602 = "角色/enemy_602",
    branchTask_S2_npc = "角色/branchTask_S2_npc",
    branchTask_S6_npc = "角色/branchTask_S6_npc",
}

---@class 物体_副本_醉花荫
local objs = {
    Trigger_Battle_Boss1 = "物体/Trigger_Battle_Boss1",
    Trigger_Battle_Boss2 = "物体/Trigger_Battle_Boss2",
    Trigger_Battle_Boss3 = "物体/Trigger_Battle_Boss3",
    Trigger_Battle_Boss4 = "物体/Trigger_Battle_Boss4",
    Trigger_Leave = "物体/Trigger_Leave",
    Trigger_Battle_Enemy_201 = "物体/Trigger_Battle_Enemy_201",
    Trigger_Battle_Enemy_202 = "物体/Trigger_Battle_Enemy_202",
    Trigger_Battle_Enemy_401 = "物体/Trigger_Battle_Enemy_401",
    Trigger_Battle_Enemy_402 = "物体/Trigger_Battle_Enemy_402",
    Trigger_Battle_Enemy_601 = "物体/Trigger_Battle_Enemy_601",
    Trigger_Battle_Enemy_602 = "物体/Trigger_Battle_Enemy_602",
    teleport_1 = "物体/teleport_1",
    teleport_2 = "物体/teleport_2",
    teleport_3 = "物体/teleport_3",
    teleport_4 = "物体/teleport_4",
    teleport_5 = "物体/teleport_5",
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
    chest_3 = "物体/chest_3",
    branchTask_yzzy_S1 = "物体/branchTask_yzzy_S1",
    branchTask_yzzy_S2 = "物体/branchTask_yzzy_S2",
    branchTask_yzzy_S6 = "物体/branchTask_yzzy_S6",
    branchTask_yqwg_fq = "物体/branchTask_yqwg_fq",
    branchTask_yqwg_fj = "物体/branchTask_yqwg_fj",
}

---@class 相机_副本_醉花荫
local cameras = {
    Camera_Enter = "相机/Camera_Enter",
    Camera_Boss1 = "相机/Camera_Boss1",
    Camera_Boss2 = "相机/Camera_Boss2",
    Camera_Boss3 = "相机/Camera_Boss3",
    Camera_Boss4 = "相机/Camera_Boss4",
}

---@class 位置_副本_醉花荫
local pos = {
    start = "位置/start",
    Pos_S2npc = "位置/Pos_S2npc",
    Pos_S2npc_1 = "位置/Pos_S2npc_1",
    Pos_S6npc = "位置/Pos_S6npc",
}

---@class 资产_副本_醉花荫
local assets = {
}

---@class 动作_副本_醉花荫
local animationClips = {
}

---@class 副本_醉花荫
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
