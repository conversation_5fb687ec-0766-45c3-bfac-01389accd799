local context = require("MapStories/蜃境_青羽录_06芦苇地/scene_蜃境_青羽录_06芦苇地_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
s = context.sapi

-- 添加缺失的RoleTrigger和ObjectTrigger引用
RoleTrigger = require("MapStories/RoleAnimTriggers")
ObjectTrigger = require("MapStories/ObjectAnimTriggers")

---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjectTrigger

s.loadFlags(context.flags)
---@type flags_蜃境_青羽录_06芦苇地
local flags = context.flags
--不可省略，用于外部获取flags
local newbieApi = require("NewbieApi")
local newbieFlags = require("Newbie/flags")

local capi = require("ChapterApi")

function getFlags(...)
    return flags
end

-- 初始化flag的安全函数
local function initializeFlags()
    if flags.XMGS_0_step == nil then flags.XMGS_0_step = 0 end
    if flags.XMGS2_step == nil then flags.XMGS2_step = 0 end
    if flags.XMGS3_step == nil then flags.XMGS3_step = 0 end
    if flags.level_step == nil then flags.level_step = 0 end
end

-- 安全的音效播放函数
local function safePlaySound(soundName, volume, loop)
    if soundName and soundName ~= "" then
        pcall(function()
            s.playSound(soundName, volume or 0.5, loop or false)
        end)
    end
end

-- 安全的音效停止函数
local function safeStopSound(soundName)
    if soundName and soundName ~= "" then
        pcall(function()
            s.stopSound(soundName)
        end)
    end
end

-- 安全的特效管理函数（使用物体激活/停用）
local function safePlayEffect(effectObjName, isActive, duration)
    if effectObjName and effectObjName ~= "" then
        pcall(function()
            -- 激活特效物体
            s.setActive(effectObjName, isActive or true)
            
            -- 如果设置了持续时间，则在指定时间后自动关闭
            if duration and duration > 0 and isActive ~= false then
                -- 这里可以添加定时器逻辑来自动关闭特效
                -- 暂时使用简单的延时关闭（需要根据项目实际情况调整）
            end
        end)
    end
end

-- 安全的摄像机特效函数
local function safeCameraEffect(duration)
    if duration and duration > 0 then
        pcall(function()
            s.cameraEffect(duration)
        end)
    end
end

-- 安全的Timeline播放函数
local function safePlayTimeline(timelinePath, waitForCompletion)
    if timelinePath and timelinePath ~= "" then
        pcall(function()
            s.playTimelineScene(timelinePath, waitForCompletion or false)
        end)
    end
end

-- 安全的NPC检查函数
local function safeNpcOperation(npc, operation)
    if npc and npc ~= "" then
        pcall(function()
            operation()
        end)
    end
end

-- 安全的动画播放函数
local function safePlayAnimation(npc, animName)
    if npc and animName and npc ~= "" and animName ~= "" then
        pcall(function()
            s.soloAnimState(npc, animName)
        end)
    end
end

----------------------OVERRIDE----------------------------------
require("MapStories/MemorySlotHelper")
require("MapStories/MapStoryCommonTools")

function start()
    print("start called")
    -- 初始化flags
    initializeFlags()
    
    s.setPos(npcs.zhujue, pos.start)
    s.readyToStart(true)
    refreshMapStates()
    try_execute_newbieGuide()
end

function get_level_step()
    return flags.level_step or 0
end

function set_level_step(value)
    flags.level_step = value or 0
end

function try_execute_newbieGuide(...)
    s.nextStep("newbieGuideInLevel")
end

function newbieGuideInLevel()

end

--默认初始化的地图显隐状态
function reset_all()
    s.setActive(npcs.qyl_24, (flags.level_step or 0) == 1)
    s.setActive(objs.door, (flags.level_step or 0) >= 2)

    s.setActive(objs.qyl6_1010,not s.hasUnlockMemorySlot("qyl6_1010"))
    s.setActive(objs.qyl6_1011,not s.hasUnlockMemorySlot("qyl6_1011"))
    s.setActive(objs.hitevent, (flags.XMGS_0_step or 0) == 0)
    s.setActive(npcs.XMGS_NPC, s.getCurrentTaskStep("青羽录蜃境之主支线") ~= "finished")
    s.setActive(objs.XMGS_WMS, s.getCurrentTaskStep("青羽录蜃境之主支线") == "backToWumingshi"  or s.getCurrentTaskStep("青羽录蜃境之主支线") == "battle")
    s.setActive(objs.XMGS_LZ, s.getCurrentTaskStep("青羽录蜃境之主支线") == "findenemy")
    s.setActive(objs.XMGS_SS, s.getCurrentTaskStep("青羽录蜃境之主支线") == "findenemy")
    s.setActive(npcs.elite_03, s.hasUnlockMemorySlot("qyl6_battle_101"))
    s.setActive(npcs.boss_04, s.hasUnlockMemorySlot("qyl6_battle_102"))
    s.removeAllGameMapGuides()
end

function refreshMapStates()
    local step = get_level_step()
    s.playMusic("startjourney")

    reset_all()
    s.unTraceTask(10000001,50)
    if step == 0 then
        ----第一次进入场景, 这里加入入场演出
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_青羽录_告别之地_入口", nil, 0)
        s.setTaskStep("青羽录蜃楼之主","forwardToExplore")
        next_level_step(true)
    elseif step == 1 then
        s.setTaskStep("青羽录蜃楼之主","forwardToExplore")
        level_guide_to(npcs.qyl_24, 0.8)
    elseif step == 2 then
        s.setTaskStep("青羽录蜃楼之主","beatChuQingXieZi")
        level_guide_to(objs.door, 0)
    else
        s.setTaskStep("青羽录蜃楼之主","leaveLuWeiDiShenJing")
        level_guide_to(objs.exit)
    end
end

----------------------OVERRIDE----------------------------------

function qyl_24()
    executeMemSlot("qyl_24", function() 
        s.blackScreen(0)
        s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录11_告别之地.unity", false)
        s.playTimelineScene("Assets/GameScenes/动画演出场景/001荆成之章/主线_青羽录11_回忆之地.unity")
        s.setTaskStep("青羽录蜃楼之主","beatChuQingXieZi")    
    end,function()
        s.setActive(npcs.qyl_24,false)
        s.setActive(objs.door,true)
        s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_青羽录_告别之地_渲染", nil, 0)
        next_level_step(true)
        refreshMapStates()
    end)
end

--支线剧情 寻觅高手 - 青羽录风格优化版本
function XMGS_0()
    if (flags.XMGS_0_step or 0) ~= 1 then
        -- 【优化】初遇无名侠客的高规格演出
        s.blackScreen(0.5)
        
        -- 尝试播放专用Timeline，如果不存在则使用默认动画
        safePlayTimeline("Assets/GameScenes/动画演出场景/001荆成之章/支线_寻觅高手01_神秘登场.unity", false)
        
        -- 环境音效设置 - 使用安全播放
        safePlaySound("Wind_Reed", 0.3, true) -- 风吹芦苇
        safePlaySound("Water_Stream", 0.2, true) -- 流水潺潺
        safePlaySound("Birds_Ambient", 0.15, true) -- 鸟叫
        
        -- 角色动画和特效 - 安全操作
        safeNpcOperation(npcs.wumingshi, function()
            safePlayAnimation(npcs.wumingshi, "BScratch") -- 使用确定存在的动画
            s.turnToAsync(npcs.zhujue, npcs.wumingshi)
            s.turnToAsync(npcs.wumingshi, npcs.zhujue)
            s.lookAt(npcs.zhujue, nil, npcs.wumingshi, true)
        end)
        
        -- 使用摄像机特效增强氛围
        safeCameraEffect(2.0)
        
        -- 运行剧情对话 - 使用原版的标签
        pcall(function()
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地","蜃境_告别之地_支线_寻觅高手")
        end)
        
        s.setActive(objs.hitevent,false)
        flags.XMGS_0_step = 1
        s.setTaskStep("青羽录蜃境之主支线","findenemy")
        s.setActive(objs.XMGS_SS,true)
        s.setActive(objs.XMGS_LZ,true)
        
        -- 解锁芦苇地探索音效
        safePlaySound("Exploration_Theme", 0.4, true)
    end
end

function XMGS_1()
    if s.getCurrentTaskStep("青羽录蜃境之主支线") == "backToWumingshi"  or s.getCurrentTaskStep("青羽录蜃境之主支线") == "battle" then
        -- 【优化】决斗前的高规格演出
        s.blackScreen(0.3)
        
        -- 尝试播放战前对峙Timeline
        safePlayTimeline("Assets/GameScenes/动画演出场景/001荆成之章/支线_寻觅高手04_战前对峙.unity", false)
        
        -- 决斗氛围音效
        safeStopSound("Exploration_Theme")
        s.playMusic("startjourney", 0.7, 2.0) -- 使用已知存在的音乐
        safePlaySound("SwordDraw", 0.5) -- 拔剑音效
        
        -- 角色定位和动画 - 安全操作
        safeNpcOperation(npcs.wumingshi, function()
            s.turnToAsync(npcs.zhujue, npcs.wumingshi)
            s.turnToAsync(npcs.wumingshi, npcs.zhujue)
            s.lookAt(npcs.zhujue, nil, npcs.wumingshi, true)
        end)
        
        -- 决斗前摄像机特效
        safeCameraEffect(1.5)
        
        -- 播放决斗邀请对话 - 使用原版标签
        pcall(function()
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地","蜃境_告别之地_支线_寻觅高手4")
        end)
        
        s.setTaskStep("青羽录蜃境之主支线","battle")
        
        -- 尝试播放华丽决斗Timeline
        safePlayTimeline("Assets/GameScenes/动画演出场景/001荆成之章/支线_寻觅高手05_华丽决斗.unity", false)
        
        safePlaySound("InnerPower_Charge", 0.4) -- 内力运转
        
        -- 安全的战斗调用
        local isWin = false
        pcall(function()
            isWin = s.battle("蜃境_告别之地_支线_寻觅高手4")
        end)
        
        if isWin then
            -- 【优化】战后升华演出
            s.blackScreen(0.5)
            safePlayTimeline("Assets/GameScenes/动画演出场景/001荆成之章/支线_寻觅高手06_战后升华.unity", false)
            
            -- 战后音乐转换
            s.playMusic("startjourney", 0.6, 3.0) -- 使用已知音乐
            safePlaySound("TaoXun", 0.4) -- 陶埙音效
            safePlaySound("Bell_Distant", 0.2) -- 远钟
            
            -- 环境恢复到宁静状态
            safePlaySound("Peaceful_Stream", 0.2, true)
            
            -- 战后摄像机特效
            safeCameraEffect(3.0)
            
            -- 使用原版的结束标签
            pcall(function()
                s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_告别之地_支线_寻觅高手5", nil, 0)
            end)
            
            s.setActive(objs.XMGS_WMS,false)
            s.setTaskStep("青羽录蜃境之主支线","finished")
            
            pcall(function()
                capi.finishMemorySlot("qyl_sideTask_llzy2")
            end)
            
            -- 停止所有战斗相关音效 - 安全停止
            safeStopSound("Wind_Reed")
            safeStopSound("Water_Stream") 
            safeStopSound("Birds_Ambient")
            safeStopSound("Peaceful_Stream")
        end
    end
end

function XMGS_2()
    if s.getCurrentTaskStep("青羽录蜃境之主支线") == "findenemy" then
        -- 【优化】寻访过程的动态演出
        safePlayTimeline("Assets/GameScenes/动画演出场景/001荆成之章/支线_寻觅高手02_芦苇地寻访.unity", false)
        
        -- 寻访音效
        safePlaySound("Footsteps_Reed", 0.3) -- 脚步声
        
        -- 安全的NPC交互
        safeNpcOperation(npcs.laozhe, function()
            s.turnToAsync(npcs.zhujue, npcs.laozhe)
            s.turnToAsync(npcs.laozhe, npcs.zhujue)
            s.lookAt(npcs.zhujue, nil, npcs.laozhe, true)
        end)
        
        pcall(function()
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_告别之地_支线_寻觅高手2", nil, 0)
        end)
        
        flags.XMGS2_step = 1
        
        if (flags.XMGS3_step or 0) == 1 then
            -- 播放寻访完成的摄像机特效
            safeCameraEffect(1.0)
            s.setTaskStep("青羽录蜃境之主支线","backToWumingshi")  
            s.setActive(objs.XMGS_WMS,true)
            s.setActive(objs.XMGS_SS,false)
            s.setActive(objs.XMGS_LZ,false)
        end
    end
end

function XMGS_3()
    if s.getCurrentTaskStep("青羽录蜃境之主支线") == "findenemy" then
        -- 【优化】书生互动的幽默演出
        safePlayTimeline("Assets/GameScenes/动画演出场景/001荆成之章/支线_寻觅高手03_书生交流.unity", false)
        
        -- 安全的NPC交互
        safeNpcOperation(npcs.shusheng, function()
            s.turnToAsync(npcs.zhujue, npcs.shusheng)
            s.turnToAsync(npcs.shusheng, npcs.zhujue)
            s.lookAt(npcs.zhujue, nil, npcs.shusheng, true)
        end)
        
        -- 幽默氛围音效
        safePlaySound("Comedy_Timing", 0.3)
        
        pcall(function()
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_告别之地_支线_寻觅高手3", nil, 0)
        end)
        
        -- 书生扎马步的特殊动画
        safePlayAnimation(npcs.shusheng, "Special_1")
        safePlaySound("Embarrassing_Moment", 0.4) -- 尴尬时刻音效
        
        pcall(function()
            s.runAvgTag("蜃境/蜃境_青羽录/蜃境_青羽录_告别之地", "蜃境_告别之地_支线_寻觅高手3演出", nil, 0)
        end)
        
        flags.XMGS3_step = 1
        
        if (flags.XMGS2_step or 0) == 1 then
            -- 寻访完成演出
            safeCameraEffect(1.0)
            safePlaySound("Task_Progress", 0.4)
            s.setTaskStep("青羽录蜃境之主支线","backToWumingshi")  
            s.setActive(objs.XMGS_WMS,true)
            s.setActive(objs.XMGS_SS,false)
            s.setActive(objs.XMGS_LZ,false)
        end
    end
end

function exitScene()
    print("离开场景")
    pcall(function()
        local capi = require("ChapterApi")
        capi.exitMemoryScene()
    end)
end

function door()
    pcall(function()
        s.changeMap("Assets/GameScenes/游历场景/蜃境_青羽录_06最终房/蜃境_青羽录_06最终房.unity", "位置/start")
    end)
end

function test()
    local step = get_level_step()
    s.popInfo(tostring(step))
end 