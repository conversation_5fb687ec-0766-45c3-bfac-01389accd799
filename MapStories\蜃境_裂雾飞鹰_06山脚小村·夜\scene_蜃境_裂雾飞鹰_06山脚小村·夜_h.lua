--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_裂雾飞鹰_06山脚小村·夜/flags_蜃境_裂雾飞鹰_06山脚小村·夜")


---@class 角色_蜃境_裂雾飞鹰_06山脚小村·夜
local npcs = {
    zhujue = "角色/zhujue",
    lwfy_20 = "角色/lwfy_20",
    lwfy_21 = "角色/lwfy_21",
    lwfy_22 = "角色/lwfy_22",
    lwfy_23 = "角色/lwfy_23",
    lwfy_24 = "角色/lwfy_24",
    enemy_1 = "角色/enemy_1",
    enemy_2 = "角色/enemy_2",
    enemy_3 = "角色/enemy_3",
    enemy_4 = "角色/enemy_4",
    elite_5 = "角色/elite_5",
    boss_6 = "角色/boss_6",
    xnh_1 = "角色/xnh_1",
    xnh_1_1 = "角色/xnh_1_1",
    npc_1 = "角色/npc_1",
    npc_2 = "角色/npc_2",
    xnh_1_2 = "角色/xnh_1_2",
    yuchi = "角色/yuchi",
}

---@class 物体_蜃境_裂雾飞鹰_06山脚小村·夜
local objs = {
    exit = "物体/exit",
    chest_1 = "物体/chest_1",
    lwfy_1008 = "物体/lwfy_1008",
    lwfy_1009 = "物体/lwfy_1009",
    trigger_parents = "物体/trigger_parents",
    trigger_xnhTalk_1 = "物体/trigger_xnhTalk_1",
    guqin = "物体/guqin",
    luoye = "物体/luoye",
    door = "物体/door",
}

---@class 相机_蜃境_裂雾飞鹰_06山脚小村·夜
local cameras = {
    camera_parents = "相机/camera_parents",
    camera1 = "相机/camera1",
}

---@class 位置_蜃境_裂雾飞鹰_06山脚小村·夜
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
    pos_xnh_1_2 = "位置/pos_xnh_1_2",
    pos_zhujue_miti = "位置/pos_zhujue_miti",
    pos_set_start_miti = "位置/pos_set_start_miti",
}

---@class 资产_蜃境_裂雾飞鹰_06山脚小村·夜
local assets = {
}

---@class 动作_蜃境_裂雾飞鹰_06山脚小村·夜
local animationClips = {
}

---@class 蜃境_裂雾飞鹰_06山脚小村·夜
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
