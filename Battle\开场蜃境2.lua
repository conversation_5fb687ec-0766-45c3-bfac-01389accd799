local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.ta_event_track("PlayerStartStory", "Step", "BattleTutor_Step1")
    ---@diagnostic disable-next-line: inject-field-fail
    battle.HideCardLevel = true
    bcapi.disable_auto_battle()
    bcapi.disable_speed_up()
    bcapi.disable_dart()
    bcapi.hide_ui("技能区")
    bcapi.hide_ui("怒气条")
    StartBattle()
end

function start()
    bcapi.add_timer_trigger(1, Talk2)
end

function StartBattle()
    bcapi.async_call(function()
        CreateRoles()
        bcapi.show_env("开场蜃境_战斗2_Env")
        Talk1()
    end)
end



function CreateRoles()
    bcapi.async_call(function()
        -- 创建玩家角色
        bcapi.create_join_new_player_with_card(30, team, bcapi.Vector2(0, -2), 0)
        bcapi.create_join_role_with_card("荆成", 30, team, bcapi.Vector2(0.88, -1), 0)
        bcapi.create_join_role_with_card("云舞笙", 30, team, bcapi.Vector2(-0.91, -0.53), 0)

        -- 创建敌人
        local enemy = bcapi.create_join_role("剧情_珍娘_开场2", 20, eTeam, bcapi.Vector2(7.83, 0), 0)
        enemy.Stat.Hp = math.floor(enemy.Attr.MaxHp * 0.4)
        local jingCheng = bcapi.get_player_role("荆成")
        enemy.Movement:LookAt(jingCheng.Transform.Position)

        -- 创建蝎子
        CreateScorpion(3.56, 3)
        CreateScorpion(4.33, -0.85)
        -- CreateScorpion(4.33, -0.85)
        -- CreateScorpion(1.14, -3.83)
        CreateScorpion(-3.25, -2.12)
        CreateScorpion(-4.41, 0.02)
        -- CreateScorpion(-5, 4.82)
        CreateScorpion(5.71, 2.15)
        CreateScorpion(5.6, -1.78)
        CreateScorpion(3.4, -2.6)
        -- CreateScorpion(-1.52, -3.13)
        CreateScorpion(-5.91, -1.12)
        CreateScorpion(-5.29, 2.48)
        CreateScorpion(5.73, 4)
    end)
end

function CreateScorpion(posX, posY)
    bcapi.async_call(function()
        local newScorpion = bcapi.create_and_summon_role("剧情_珍娘_开场蝎子", 10, eTeam, bcapi.Vector2(posX, posY), 0)
        local jingCheng = bcapi.get_player_role("荆成")
        newScorpion.Movement:LookAt(jingCheng.Transform.Position)
    end)
end

function Talk1()
    bcapi.async_call(function()
        team.NuqiSystem.CurNuqi = 10
        bcapi.talk("云舞笙", "是[PlayerName]！我就知道什么都困不住你。")
        bcapi.talk("荆成", "不要放松警惕，我们先解决掉这些<color=red>蜃境幻影</color>。")
        bcapi.talk("主角#主角","唔……（不知怎的，体内好像涌出了一股力量！）")
        bcapi.manual_start()
    end)
end

function Talk2()
    bcapi.async_call(function()
        -- 销掉绝招牌
        -- local playerCard1 = bcapi.get_player_unique_card("荆成");
        -- local playerCard2 = bcapi.get_player_unique_card("云舞笙");
        -- team.CardSystem:CostCard(playerCard1)
        -- team.CardSystem:CostCard(playerCard2)
        -- local playerCard3 = bcapi.get_player_unique_card("主角");
        -- playerCard3.IsCostAfterExecuted = true

        bcapi.show_ui("技能区")
        bcapi.show_ui("怒气条")

        team.NuqiSystem.CurNuqi = 10
        bcapi.wait(0.1)

        local zhujue = bcapi.get_role(0,"主角")
        zhujue.Buff:AddBuff("攻击强化", 300, 200, 1, zhujue)
        bcapi.wait(2)
        zhujue.Buff:AddBuff("攻速强化", 300, 100, 1, zhujue)
        bcapi.wait(2)
        zhujue.Buff:AddBuff("暴击率提升", 300, 30, 1, zhujue)
        bcapi.wait(2)
        zhujue.Buff:AddBuff("暴击伤害提升", 300, 50, 1, zhujue)


        -- bcapi.pause()
        -- bcapi.aside("剑川之主，你已有足够的力量施放绝招，找准时机将对手一举歼灭！",4)
        -- bcapi.show_unique_card_guide("主角", "拖拽卡牌至目标区域，释放绝招！")
    end)
end
