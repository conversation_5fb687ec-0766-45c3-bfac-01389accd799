local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

local level = 40

function init()
    bcapi.hide_ui("疗伤")
    createRoles()
end

function start()  
    bcapi.async_call(function()
        bcapi.add_timer_trigger(2, sideTalk)
        bcapi.add_condition_trigger("[%battle_time%][>]39", 0, winTalk)
    end)
end

function createRoles()
    bcapi.async_call(function()
        --创建角色
        bcapi.create_join_role("世界_七尺素玉_鹏越", level, team, bcapi.Vector2(-4, 0), 0)
    end)
end

function sideTalk(...)
    bcapi.async_call(function()
        bcapi.append_async_aside("峨眉弟子",0,"公子，请作罢。","","峨眉派男弟子")
        bcapi.wait(4)
        bcapi.append_async_aside("鹏越",0,"今天我就要带走经书。","","书生")
        bcapi.wait(4)
        bcapi.append_async_aside("峨眉弟子",0,"不会让你摸到经书的。","","峨眉派男弟子")
        bcapi.wait(4)
        bcapi.append_async_aside("鹏越",0,"那我就硬抢！","","书生")
        bcapi.wait(4)
        bcapi.append_async_aside("峨眉弟子",0,"请回去吧。","","峨眉派男弟子")
        bcapi.wait(4)
        bcapi.append_async_aside("鹏越",0,"莫费口舌了。","","书生")
    end)
end

function winTalk(...)
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("鹏越", "少侠，今日难以成事，我们先退！")
        bcapi.resume_and_show_ui()
        bcapi.win()
    end)
end