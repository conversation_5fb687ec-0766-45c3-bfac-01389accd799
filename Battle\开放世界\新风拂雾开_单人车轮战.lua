local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    -- 创建角色
    createRoles()
end

function start()  
    bcapi.add_timer_trigger(0.1, firstTalk)
    -- 用标签控制演出
    bcapi.add_condition_trigger("[%enemy:世界_新风拂雾开_归天盟刺客B替身->flag:召唤A%][=]1", 0, callForFriendA)
    bcapi.add_condition_trigger("[%enemy:世界_新风拂雾开_归天盟刺客B替身->flag:召唤A对话%][=]1", 0, changeEnemyTalk)

end

function createRoles()
    bcapi.async_call(function()
        bcapi.create_join_role_with_card("荆成", 50, team, bcapi.Vector2(-6, -1), 0)
        local enemyA = bcapi.get_role(1, "世界_新风拂雾开_归天盟刺客A替身")
        enemyA.Buff:AddBuff("世界_通用BUFF_状态_不移动不攻击", 300, 1, 1, enemyA)
    end)
end

function callForFriendA()
    bcapi.async_call(function()
        -- bcapi.create_join_role("世界_新风拂雾开_归天盟刺客A", 25, eTeam, bcapi.Vector2(4, 4), 0)
        local enemyA = bcapi.get_role(1, "世界_新风拂雾开_归天盟刺客A替身")
        bcapi.use_skill_to_role(enemyA,"世界_新风拂雾开_归天人登场",enemyA,1)
        enemyA.Buff:AddBuff("世界_通用BUFF_状态_不移动不攻击", 300, 1, 1, enemyA)
        -- bcapi.add_timer_trigger(1, changeEnemyTalk)
    end)
end

function callForFriendB()
    bcapi.async_call(function()
        -- bcapi.create_join_role("世界_新风拂雾开_归天盟刺客B", 25, eTeam, bcapi.Vector2(4, -4), 0)
        local enemyB = bcapi.get_role(1, "世界_新风拂雾开_归天盟刺客B替身")
        bcapi.use_skill_to_role(enemyB,"世界_新风拂雾开_归天人登场",enemyB,1)
        enemyB.Buff:AddBuff("世界_通用BUFF_状态_不移动不攻击", 300, 1, 1, enemyB)
    end)
end

function firstTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("归天盟_刺客B", "搜东西要紧，不必与他们纠缠，你先下去找找，我来顶住！")
        bcapi.talk("归天盟_刺客A", "好，务必当心！")
        bcapi.resume_and_show_ui()
        bcapi.add_timer_trigger(0.2, firstExit)
    end)
end

function firstExit(...)
    bcapi.async_call(function()
        local enemyA = bcapi.get_role(1, "世界_新风拂雾开_归天盟刺客A替身")
        bcapi.use_skill_to_role(enemyA,"世界_新风拂雾开_归天盟刺客A_新版退场",enemyA,1)
    end)
end

function changeEnemyTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("归天盟_刺客B", "啧！这些人都还有点本事，大意不得……")
        bcapi.talk("归天盟_刺客A", "哥，书架上根本没有要找的。莫非书就藏在他们身上？")
        bcapi.talk("归天盟_刺客B", "我来仔细找找，且和我换阵！")
        bcapi.talk("归天盟_刺客A", "好！大哥一人支撑已久，务必留心伤势！")
        local enemyB = bcapi.get_role(1, "世界_新风拂雾开_归天盟刺客B替身")
        local enemyA = bcapi.get_role(1, "世界_新风拂雾开_归天盟刺客A替身")
        bcapi.resume_and_show_ui()
        enemyA.Buff:MinusBuffLevel("世界_通用BUFF_状态_不移动不攻击", 300, enemyA)
        bcapi.use_skill_to_role(enemyB,"世界_新风拂雾开_归天盟刺客B_退场",enemyB,1)

        bcapi.add_condition_trigger("[%enemy:世界_新风拂雾开_归天盟刺客A替身->flag:召唤B%][=]1", 0, callForFriendB)
        bcapi.add_condition_trigger("[%enemy:世界_新风拂雾开_归天盟刺客A替身->flag:全退场对话%][=]1", 0, enemyExitTalk)
    end)
end

function enemyExitTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("归天盟_刺客A", "唔……这配合天衣无缝，想必他们是早有防备……")
        bcapi.talk("归天盟_刺客B", "我找到《瀚海功》了！")
        local enemyA = bcapi.get_role(1, "世界_新风拂雾开_归天盟刺客A替身")
        local enemyB = bcapi.get_role(1, "世界_新风拂雾开_归天盟刺客B替身")
        bcapi.use_skill_to_role(enemyA,"世界_新风拂雾开_归天盟刺客A_最后退场",enemyA,1)
        bcapi.use_skill_to_role(enemyB,"世界_新风拂雾开_归天盟刺客B_最后退场",enemyB,1)
        bcapi.resume_and_show_ui()

        bcapi.wait(2)
        bcapi.win()
    end)
end
