--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/楚家庄破败_开局/flags_楚家庄破败_开局")


---@class 角色_楚家庄破败_开局
local npcs = {
    --- "主角"
    zhujue = "角色/zhujue",
    --- "老人"
    laoren = "角色/laoren",
    --- "青年"
    qingnian = "角色/qingnian",
    --- "药曲老"
    yaoqulao = "角色/yaoqulao",
    --- "无面剑客"
    swordsman = "角色/swordsman",
    --- "拿着剑的无面剑客"
    swordsmanWithSword = "角色/swordsmanWithSword",
}

---@class 物体_楚家庄破败_开局
local objs = {
    --- "大门触发器"
    trigger_door = "物体/trigger_door",
    --- "镜头黑幕"
    camera_black = "物体/camera_black",
    --- "大门外阻挡"
    collide_door = "物体/collide_door",
    --- "爪痕"
    obj_claw = "物体/obj_claw",
    obj_evilAir = "物体/obj_evilAir",
    obj_blood = "物体/obj_blood",
    --- "大门音乐"
    obj_door_music = "物体/obj_door_music",
    --- "邪祟之气交互"
    trigger_evilAir = "物体/trigger_evilAir",
}

---@class 相机_楚家庄破败_开局
local cameras = {
    --- "主镜头"
    Main_camera = "相机/Main_camera",
    --- "大门镜头"
    camera_door = "相机/camera_door",
    --- "左边新闪回"
    cam_left_new = "相机/cam_left_new",
    --- "左边闪回"
    cam_left = "相机/cam_left",
    --- "中间闪回"
    cam_midddle = "相机/cam_midddle",
    --- "中间闪回_1"
    cam_midddle_1 = "相机/cam_midddle_1",
    --- "主角药曲老对话漫游镜头_01"
    camera_manyou_01 = "相机/camera_manyou_01",
    --- "主角药曲老对话漫游镜头_02"
    camera_manyou_02 = "相机/camera_manyou_02",
    cam_start_talk = "相机/cam_start_talk",
    cam_yaoqulao_talk2 = "相机/cam_yaoqulao_talk2",
    cam_yaoqulao_talk_start = "相机/cam_yaoqulao_talk_start",
    --- "主角回忆镜头"
    cam_memory = "相机/cam_memory",
    --- "无面剑客初始镜头"
    cam_swordsman_start = "相机/cam_swordsman_start",
    --- "无面剑客滑动开始镜头"
    cam_swordsmanSlip_start = "相机/cam_swordsmanSlip_start",
    --- "无面剑客滑动结束镜头"
    cam_swordsmanSlip_end = "相机/cam_swordsmanSlip_end",
    cam_swordsman_fight = "相机/cam_swordsman_fight",
    --- "无面剑客战斗胜利"
    cam_fightEnd = "相机/cam_fightEnd",
    --- "战斗结束药曲老镜头"
    cam_fightEnd_yaoqulao = "相机/cam_fightEnd_yaoqulao",
    --- "无面剑客战斗结束滑动开始"
    cam_fightEnd_slipStart = "相机/cam_fightEnd_slipStart",
    --- "无面剑客战斗结束滑动结束"
    cam_fightEnd_slipEnd = "相机/cam_fightEnd_slipEnd",
    --- "无面剑客战斗将死"
    cam_swordmanDead = "相机/cam_swordmanDead",
    --- "调查完思考滑动开始"
    cam_thinkSlip_start = "相机/cam_thinkSlip_start",
    --- "调查完思考滑动结束"
    cam_thinkSlip_end = "相机/cam_thinkSlip_end",
    cam_yaoqulao_run = "相机/cam_yaoqulao_run",
    --- "无面剑客死亡开始"
    cam_swordman_dead = "相机/cam_swordman_dead",
    cam_swordman_far = "相机/cam_swordman_far",
    cam_midddle_goOn = "相机/cam_midddle_goOn",
    cam_start_3 = "相机/cam_start_3",
}

---@class 位置_楚家庄破败_开局
local pos = {
    --- "玩家入境位置"
    pos_start = "位置/pos_start",
    --- "对话前走"
    Pos_moveon = "位置/Pos_moveon",
    --- "对话开始"
    Pos_movestart = "位置/Pos_movestart",
    --- "测试结素站位"
    Pos_moveToDoor_end = "位置/Pos_moveToDoor_end",
    --- "测试开始站位"
    Pos_moveToDoor_start = "位置/Pos_moveToDoor_start",
    --- "青年离开位置"
    Pos_qingnian_leave = "位置/Pos_qingnian_leave",
    --- "老人离开位置"
    Pos_laoren_leave = "位置/Pos_laoren_leave",
    pos_yaoqulao_start = "位置/pos_yaoqulao_start",
    pos_yaoqulao_end = "位置/pos_yaoqulao_end",
    --- "无面剑客位置"
    pos_swordsman_start = "位置/pos_swordsman_start",
    --- "回忆位置"
    pos_memory = "位置/pos_memory",
    --- "无面剑客战斗位置"
    pos_swordsman_fight = "位置/pos_swordsman_fight",
    --- "药曲老来开始位置"
    pos_laoqulao_come_start = "位置/pos_laoqulao_come_start",
    --- "药曲老来结束位置"
    pos_laoqulao_come_end = "位置/pos_laoqulao_come_end",
    pos_memory_fight = "位置/pos_memory_fight",
    --- "邪祟之气近景"
    pos_evilAir_closeCam = "位置/pos_evilAir_closeCam",
    --- "邪祟之气返回"
    pos_evilAirBack = "位置/pos_evilAirBack",
}

---@class 资产_楚家庄破败_开局
local assets = {
    --- "污染之气TL"
    timeline_air = "资产/timeline_air",
    --- "无面剑客跳下"
    timeline_swordManJump = "资产/timeline_swordManJump",
    --- "无面剑客死亡"
    timeline_swordManDie = "资产/timeline_swordManDie",
    memory_timeline = "资产/memory_timeline",
    memory_timeline_woman = "资产/memory_timeline_woman",
}

---@class 动作_楚家庄破败_开局
local animationClips = {
}

---@class 楚家庄破败_开局
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
