local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    
end

function start()
    bcapi.add_condition_trigger("[%enemy:世界_诺迭_友方版->flag:世界_诺迭_已死亡标签%][=]1",0,checkAnotherCondition)
end

function checkAnotherCondition()
    bcapi.async_call(function()
        bcapi.add_condition_trigger("[%enemy:世界_银狼王_友方版->flag:世界_银狼王_已死亡标签%][=]1",0,win) 
    end)
end

function win()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("灵湫", "我们成功阻止了他们！")
        bcapi.resume_and_show_ui()
        bcapi.win()
    end)
end