
---@type 主线_破冰而出_蜃境
local context = require("MapStories/序章/scene_主线_破冰而出_蜃境_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger


---@type NewbieApi
local newbieApi = require("NewbieApi") -- 不允许改名

s.loadFlags(context.flags)
---@type flags_序章
local flags = context.flags 
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
--每次载入调用
function start()
    -- local avg = require("AVGApi")
    -- avg.run("序章/序章_第一幕")
    
    -- local newbieApi = require("NewbieApi")
    -- newbieApi.setPlayerNameAndGender(avg.info["playerName"],tonumber(avg.info["playerGender"]))
    
    -- print(PlayerName)
    -- print(Gender)
    
    -- avg.run("序章/序章_第二幕_001")
    
    -- local isWin = 0
    
    -- ::BATTLE::
    -- isWin = newbieApi.newbieBattle("firstBattle") 

    -- if isWin then
    --     avg.run("序章/序章_第二幕_002")
    -- else
    --     goto BATTLE
    -- end

    s.setPos(npcs.zhujue,pos.startpos)

    s.readyToStart()
    s.playMusic("huanxing")
    s.addTask(100001, 1,true)
    s.aside("你感觉浑身疲惫，意识有些模糊。")
    s.aside("你看着眼前二人，努力回想，这正是不久之前，受你感召来到剑之川的两位侠客。")
    s.aside("凡有心救世者，皆为侠客。")
    
    showHint()

    --avg.run("序章_第二幕_002")
end

function showHint()
    
    newbieApi.showHintDescPanel("跑图", "「探索地图」", "操控角色在地图中行走和交互。\n通过虚拟摇杆操作进行地图探索，亦可滑动屏幕进行旋转。", "知道了")
    newbieApi.showHintDescPanel("跑图交互", "「地图交互」", "靠近可交互角色和物体，会显示交互按钮，可点击与其交互。", "知道了")
   -- s.wait(3)
  --  s.move(npcs.zhujue,pos.NpcPos,1,true)
end

function speak()
    -- body
    s.appendAsyncAside(npcs.jingcheng,1,"这些植物,好生怪异","","惊讶")
    s.wait(2)
    s.appendAsyncAside(npcs.yunwusheng,1,"像是在盯着我们","","惊讶")
    s.setActive(objs.SpeakTrigger,false)
end


function leave()
    --s.aside("操控主角跑图，由石壁坍塌后的道路至出口处离开")
    s.wait(1)
    s.moveAsync(npcs.zhujue,pos.RunPos_zhujue,1,true)
    s.setPos(npcs.jingcheng,pos.SetEndPos_jinchen)
    s.setPos(npcs.yunwusheng,pos.SetEndPos_yunwusheng)
    s.moveAsync(npcs.jingcheng,pos.RunPos_jinchen,3,true)
    s.move(npcs.yunwusheng,pos.RunPos_yunwusheng,3,true)
    s.setActive(objs.EndTrigger,false)
    s.talk(npcs.zhujue, "应该就是这里了……先赶紧离开吧") 
    s.moveAsync(npcs.jingcheng,pos.EndPos_jinchen,3,true)
    s.moveAsync(npcs.yunwusheng,pos.EndPos_yunwusheng,3,true)
    s.move(npcs.zhujue,pos.EndPos,3,true)
  --  s.wait(3)
    s.finishTask(100001, 1)
    s.finishInfiniteStory(true)
end

function talk()
    print("talkToJingcheng")
    s.animState(npcs.zhujue, roleAnims.BHostile, true)
    s.turnToAsync(npcs.jingcheng, npcs.zhujue)
    s.turnToAsync(npcs.yunwusheng, npcs.zhujue)

    s.wait(1)
   
    local offset = CS.UnityEngine.Vector3(0,1.75,0)
    s.lookAt(npcs.zhujue, offset, npcs.yunwusheng, true)
    s.talk(npcs.yunwusheng, "太好了，你没事。")

    s.animState(npcs.jingcheng, roleAnims.BRubNose)
    
    s.lookAt(npcs.zhujue, offset, npcs.jingcheng, true)
    s.talk(npcs.jingcheng, "有什么话我们离开蜃境再说吧。")
    s.camera(cameras.DoorCamera,true)
    s.wait(1)
    s.talk(npcs.jingcheng, "这里看着像是出去的路。")
    s.camera(cameras.MainCamera,true)

    s.animState(npcs.zhujue, roleAnims.BAgree)
    s.animState(npcs.zhujue, roleAnims.BHostile, false,true)
    s.wait(1)
end


