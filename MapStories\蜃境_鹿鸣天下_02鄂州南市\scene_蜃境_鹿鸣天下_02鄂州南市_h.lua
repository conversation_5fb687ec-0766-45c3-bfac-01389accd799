--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/蜃境_鹿鸣天下_02鄂州南市/flags_蜃境_鹿鸣天下_02鄂州南市")


---@class 角色_蜃境_鹿鸣天下_02鄂州南市
local npcs = {
    lmtx_06 = "角色/lmtx_06",
    lmtx_07 = "角色/lmtx_07",
    lmtx_08 = "角色/lmtx_08",
    lmtx_09 = "角色/lmtx_09",
    lmtx_10 = "角色/lmtx_10",
    lmtx_11 = "角色/lmtx_11",
    enemy_01 = "角色/enemy_01",
    enemy_02 = "角色/enemy_02",
    enemy_03 = "角色/enemy_03",
    enemy_04 = "角色/enemy_04",
    enemy_elite_01 = "角色/enemy_elite_01",
    branchTask_Qing_S0 = "角色/branchTask_Qing_S0",
    branchTask_Qing_S1 = "角色/branchTask_Qing_S1",
    branchTask_Dizi_S2 = "角色/branchTask_Dizi_S2",
    Player = "角色/Player",
    lmtx_09_ZhuGeLu = "角色/lmtx_09_ZhuGeLu",
    lmtx_09_ZhuLingLong = "角色/lmtx_09_ZhuLingLong",
    lmtx_10_ZhuGeLu = "角色/lmtx_10_ZhuGeLu",
    lmtx_10_ChuWanJun = "角色/lmtx_10_ChuWanJun",
    lmtx_11_ZhuLingLong = "角色/lmtx_11_ZhuLingLong",
    lmtx_11_ChuWanJun = "角色/lmtx_11_ChuWanJun",
    branchTask_Dizi_S2_after = "角色/branchTask_Dizi_S2_after",
}

---@class 物体_蜃境_鹿鸣天下_02鄂州南市
local objs = {
    exit = "物体/exit",
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
    branchTaskStep2_Trriger = "物体/branchTaskStep2_Trriger",
    puzzleKey_beizi = "物体/puzzleKey_beizi",
    puzzleKey_shuihu = "物体/puzzleKey_shuihu",
    puzzleKey_dengzi = "物体/puzzleKey_dengzi",
    puzzleDoor_dengzi = "物体/puzzleDoor_dengzi",
    puzzleDoor_beizi = "物体/puzzleDoor_beizi",
    puzzleDoor_shuihu = "物体/puzzleDoor_shuihu",
    putDown_dengzi = "物体/putDown_dengzi",
    putDown_beizi = "物体/putDown_beizi",
    putDown_shuihu = "物体/putDown_shuihu",
    puzzleBox_1 = "物体/puzzleBox_1",
    puzzle_box_L1 = "物体/puzzle_box_L1",
    puzzle_box_L2 = "物体/puzzle_box_L2",
    lmtx_1003 = "物体/lmtx_1003",
}

---@class 相机_蜃境_鹿鸣天下_02鄂州南市
local cameras = {
    cam_2L = "相机/cam_2L",
}

---@class 位置_蜃境_鹿鸣天下_02鄂州南市
local pos = {
    zhujue_start_pos = "位置/zhujue_start_pos",
    enemy_1_start_pos = "位置/enemy_1_start_pos",
    enemy_2_start_pos = "位置/enemy_2_start_pos",
    enemy_4_start_pos = "位置/enemy_4_start_pos",
    L2_pos = "位置/L2_pos",
    L1_pos = "位置/L1_pos",
    zhujue_branchTask_S2 = "位置/zhujue_branchTask_S2",
}

---@class 资产_蜃境_鹿鸣天下_02鄂州南市
local assets = {
    Timeline_01 = "资产/Timeline_01",
    Timeline_01_Nv = "资产/Timeline_01_Nv",
    Timeline_02 = "资产/Timeline_02",
    Timeline_02_Nv = "资产/Timeline_02_Nv",
}

---@class 动作_蜃境_鹿鸣天下_02鄂州南市
local animationClips = {
}

---@class 蜃境_鹿鸣天下_02鄂州南市
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
