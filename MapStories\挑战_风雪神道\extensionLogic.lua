local s = require("StoryApi")

local extension = require 'MapStories/挑战_风雪神道/extension'
---@type flags_挑战_风雪神道
local flags = require("MapStories/挑战_风雪神道/flags_挑战_风雪神道")
s.loadFlags(flags)

-- ---@type StringWidget
-- local stringWidget = extension.widgets.StringWidgetInfo.widget
-- ---@type 挑战_风雪神道_StringItemData
-- local stringItemData = extension.widgets.StringWidgetInfo.items
---@type ItemWidget
local itemWidget = extension.widgets.ItemWidgetInfo.widget
---@type 挑战_风雪神道_ItemData
local itemData = extension.widgets.ItemWidgetInfo.items

---不可改名
function get_string_item_desc(itemKey,sceneId)
    return "没有描述"
end

---不可改名
function on_use_item(itemKey,sceneId)
    --itemdata里所有不包含"Done"的物品都有使用逻辑
    if itemKey == itemData.StructureFire.key then
        --如果对应资源够，则弹出对话框，二次确认
        if(flags.resource >= 5)then
            --todo:confirm一下，确认建造
            ConfirmStructureFire()
        --否则直接提示对应资源不足
        else
            s.popInfo("建造失败，物资不足!")
        end
    end
    if itemKey == itemData.StructureFood.key then
        --如果对应资源够，则弹出对话框，二次确认
        if(flags.trash >= 7)then
            ConfirmStructureFood()
        --否则直接提示对应资源不足
        else
            s.popInfo("建造失败，物资不足!")
        end
    end
    if itemKey == itemData.StructureAlcohol.key then
        --如果对应资源够，则弹出对话框，二次确认
        if(flags.trash >= 7 and flags.resource >= 3)then
            ConfirmStructureAlcohol()
        --否则直接提示对应资源不足
        else
            s.popInfo("建造失败，物资不足!")
        end
    end
    if itemKey == itemData.StructureClothes.key then
        --如果对应资源够，则弹出对话框，二次确认
        if(flags.resource >= 10)then
            ConfirmStructureClothes()
        --否则直接提示对应资源不足
        else
            s.popInfo("建造失败，物资不足!")
        end
    end
    if itemKey == itemData.StructureKnifeStone.key then
        --如果对应资源够，则弹出对话框，二次确认
        if(flags.resource >= 12)then
            ConfirmStructureKnifeStone()
        --否则直接提示对应资源不足
        else
            s.popInfo("建造失败，物资不足!")
        end
    end
    if itemKey == itemData.StructureMap.key then
        --如果对应资源够，则弹出对话框，二次确认
        if(flags.trash >= 8 and flags.resource >= 8)then
            ConfirmStructureMap()
        --否则直接提示对应资源不足
        else
            s.popInfo("建造失败，物资不足!")
        end
    end
    if itemKey == itemData.StructurePet.key then
        --如果对应资源够，则弹出对话框，二次确认
        if(flags.trash >= 16)then
            ConfirmStructurePet()
        --否则直接提示对应资源不足
        else
            s.popInfo("建造失败，物资不足!")
        end
    end
    if itemKey == itemData.StructureEnhance.key then
        --如果对应资源够，则弹出对话框，二次确认
        if(flags.trash >= 8 and flags.resource >= 12)then
            ConfirmStructureEnhance()
        --否则直接提示对应资源不足
        else
            s.popInfo("建造失败，物资不足!")
        end
    end
end

function ConfirmStructureFire()
    --资源减少，营地篝火建造完成
    flags.resource = flags.resource - 5
    s.setMapStatus("补给数量", flags.trash)
    s.setMapStatus("资源数量", flags.resource)
    flags.StructureFire = 1
    s.popInfo("建造成功!")
    --移除StructureFire，增加StructureFireDone
    itemWidget:removeItem(itemData.StructureFire, 1)
    itemWidget:addItem(itemData.StructureFireDone, 1)
    --资源对应ResourceDone，补给对应TrashDone，需要更新
    itemWidget:removeItem(itemData.ResourceDone, 5)
end

function ConfirmStructureFood()
    --资源减少，烤肉架子建造完成
    flags.trash = flags.trash - 7
    s.setMapStatus("补给数量", flags.trash)
    s.setMapStatus("资源数量", flags.resource)
    flags.StructureFood = 1
    s.popInfo("建造成功!")
    --移除StructureFood，增加StructureFoodDone
    itemWidget:removeItem(itemData.StructureFood, 1)
    itemWidget:addItem(itemData.StructureFoodDone, 1)
    --资源对应ResourceDone，补给对应TrashDone，需要更新
    itemWidget:removeItem(itemData.TrashDone, 7)
end

function ConfirmStructureAlcohol()
    --资源减少，酿酒桶建造完成
    flags.trash = flags.trash - 7
    flags.resource = flags.resource - 3
    s.setMapStatus("补给数量", flags.trash)
    s.setMapStatus("资源数量", flags.resource)
    flags.StructureAlcohol = 1
    s.popInfo("建造成功!")
    --移除StructureAlcohol，增加StructureAlcoholDone
    itemWidget:removeItem(itemData.StructureAlcohol, 1)
    itemWidget:addItem(itemData.StructureAlcoholDone, 1)
    --资源对应ResourceDone，补给对应TrashDone，需要更新
    itemWidget:removeItem(itemData.TrashDone, 7)
    itemWidget:removeItem(itemData.ResourceDone, 3)
end

function ConfirmStructureClothes()
    --资源减少，缝纫机建造完成
    flags.resource = flags.resource - 10
    s.setMapStatus("补给数量", flags.trash)
    s.setMapStatus("资源数量", flags.resource)
    flags.StructureClothes = 1
    s.popInfo("建造成功!")
    --移除StructureClothes，增加StructureClothesDone
    itemWidget:removeItem(itemData.StructureClothes, 1)
    itemWidget:addItem(itemData.StructureClothesDone, 1)
    --资源对应ResourceDone，补给对应TrashDone，需要更新
    itemWidget:removeItem(itemData.ResourceDone, 10)
end

function ConfirmStructureKnifeStone()
    --资源减少，磨刀石建造完成
    flags.resource = flags.resource - 12
    s.setMapStatus("补给数量", flags.trash)
    s.setMapStatus("资源数量", flags.resource)
    flags.StructureKnifeStone = 1
    s.popInfo("建造成功!")
    --移除StructureKnifeStone，增加StructureKnifeStoneDone
    itemWidget:removeItem(itemData.StructureKnifeStone, 1)
    itemWidget:addItem(itemData.StructureKnifeStoneDone, 1)
    --资源对应ResourceDone，补给对应TrashDone，需要更新
    itemWidget:removeItem(itemData.ResourceDone, 12)
end

function ConfirmStructureMap()
    --资源减少，风雪神道地图建造完成
    flags.trash = flags.trash - 8
    flags.resource = flags.resource - 8
    s.setMapStatus("补给数量", flags.trash)
    s.setMapStatus("资源数量", flags.resource)
    flags.StructureMap = 1
    s.popInfo("建造成功!")
    --移除StructureMap，增加StructureMapDone
    itemWidget:removeItem(itemData.StructureMap, 1)
    itemWidget:addItem(itemData.StructureMapDone, 1)
    --资源对应ResourceDone，补给对应TrashDone，需要更新
    itemWidget:removeItem(itemData.TrashDone, 8)
    itemWidget:removeItem(itemData.ResourceDone, 8)
end

function ConfirmStructurePet()
    --资源减少，宠物蛋建造完成
    flags.trash = flags.trash - 16
    s.setMapStatus("补给数量", flags.trash)
    s.setMapStatus("资源数量", flags.resource)
    flags.StructurePet = 1
    s.popInfo("建造成功!")
    --移除StructurePet，增加StructurePetDone
    itemWidget:removeItem(itemData.StructurePet, 1)
    itemWidget:addItem(itemData.StructurePetDone, 1)
    --资源对应ResourceDone，补给对应TrashDone，需要更新
    itemWidget:removeItem(itemData.TrashDone, 16)
end

function ConfirmStructureEnhance()
    --资源减少，锻造台建造完成
    flags.trash = flags.trash - 8
    flags.resource = flags.resource - 12
    s.setMapStatus("补给数量", flags.trash)
    s.setMapStatus("资源数量", flags.resource)
    flags.StructureEnhance = 1
    s.popInfo("建造成功!")
    --移除StructureEnhance，增加StructureEnhanceDone
    itemWidget:removeItem(itemData.StructureEnhance, 1)
    itemWidget:addItem(itemData.StructureEnhanceDone, 1)
    --资源对应ResourceDone，补给对应TrashDone，需要更新
    itemWidget:removeItem(itemData.TrashDone, 8)
    itemWidget:removeItem(itemData.ResourceDone, 12)
end