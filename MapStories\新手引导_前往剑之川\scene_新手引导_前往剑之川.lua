
---@type 新手引导_前往剑之川
local context = require("MapStories/新手引导_前往剑之川/scene_新手引导_前往剑之川_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

---@type NewbieApi
local newbieApi = require("NewbieApi")

s.loadFlags(context.flags)
---@type flags_新手引导_前往剑之川
local flags = context.flags --[[@as flags_新手引导_前往剑之川]]
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end
--每次载入调用
function start()
    
    local ui = CS.UnityEngine.GameObject.Find("UIRoot/GameMapHUD")
    if(ui ~= nil) then
        ui.transform:Find("GameObject/StoryName").gameObject:SetActive(false)
        ui.transform:Find("GameObject/Buttons").gameObject:SetActive(false)
    end
    
    s.setPlayerMoveSpeed(2)
    s.setPos(npcs.jingcheng, pos.posStart)

    s.readyToStart()
    s.talk(npcs.jingcheng, "这……是哪？", "受伤")
    s.talk(npcs.jingcheng, "远处……的那个光亮是？", "严肃受伤")
    s.aside("快……过来……")
    s.aside("提示：使用虚拟摇杆控制角色移动。滑动屏幕控制视角。")
end


function step1()
    s.setActive(objs.step1,false)
    s.appendAsyncAside("?", 0, "快，坚持住。勿有杂念。")
end

function step2()
    s.setActive(objs.step2,false)
    s.appendAsyncAside("?", 1, "此时为紧要关头，切勿分心。")
end

function step3()
    s.setActive(objs.step3,false)
    s.appendAsyncAside("?", 0, "就快到了，坚持住……")
end

function step4()
    s.setActive(objs.step4,false)
    s.appendAsyncAside("?", 1, "快到了，快到了……")
end


function stepfinal()
    s.setActive(objs.stepfinal,false)
    newbieApi.showStartGuide("醒醒", 1, true)
    newbieApi.showStartGuide("快醒醒", 2, false)
    s.talk(npcs.jingcheng, "是谁……", "严肃受伤")
    local rst = s.selectTalk("", "……", {
        "楚青？",
        "莫大师？"
    })
    newbieApi.showStartGuide("不要被记忆里的碎影束缚", 1, true)
    newbieApi.showStartGuide("那是荆成的记忆", 2, false)
    newbieApi.showStartGuide("你不是他", 1, true)
    newbieApi.showStartGuide("快想起你的名字", 2, false)
    newbieApi.showStartGuide("你的名字……", 3, false)
    s.selectTalk("", "", {
        newbieApi.getPlayerName(),
    })
    newbieApi.showStartGuide(newbieApi.getPlayerName(), 1, true)
    newbieApi.showStartGuide("那些破碎的记忆还在等着你", 2, false)
    newbieApi.showStartGuide("快回来", 3, true)
    newbieApi.showStartGuide("快回到————", 4, false)
    newbieApi.showStartGuide("剑之川", 3, true)
    newbieApi.closeStartGuide()
    s.finishInfiniteStory(true);
end