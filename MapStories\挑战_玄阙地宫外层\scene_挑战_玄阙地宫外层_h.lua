--本文件自动生成，请勿手动修改
local s = require("StoryApi")
local flags = require("MapStories/挑战_玄阙地宫外层/flags_挑战_玄阙地宫外层")


---@class 角色_挑战_玄阙地宫外层
local npcs = {
    --- "墨七"
    zhujue = "角色/zhujue",
    --- "机关鹰"
    minor_enemies_1 = "角色/minor_enemies_1",
    --- "防卫机"
    minor_enemies_2 = "角色/minor_enemies_2",
    --- "防卫机"
    minor_enemies_3 = "角色/minor_enemies_3",
    --- "机关鹰"
    minor_enemies_4 = "角色/minor_enemies_4",
    --- "守护地宫自走炮"
    boss_1 = "角色/boss_1",
    --- "月无心"
    boss_2 = "角色/boss_2",
    --- "守护地宫机关犬"
    boss_3 = "角色/boss_3",
    npc_1 = "角色/npc_1",
    npc_2 = "角色/npc_2",
    npc_3 = "角色/npc_3",
    npc_4 = "角色/npc_4",
    minor_enemies_special = "角色/minor_enemies_special",
}

---@class 物体_挑战_玄阙地宫外层
local objs = {
    teleport_1 = "物体/teleport_1",
    teleport_2 = "物体/teleport_2",
    boss_1_Trigger = "物体/boss_1_Trigger",
    chest_1 = "物体/chest_1",
    chest_2 = "物体/chest_2",
    chest_3 = "物体/chest_3",
    Gate_Left = "物体/Gate_Left",
    boss_2_Trigger = "物体/boss_2_Trigger",
    boss_3_Trigger = "物体/boss_3_Trigger",
    chest_2_Lock = "物体/chest_2_Lock",
    leave_Trigger = "物体/leave_Trigger",
    Gate_Right = "物体/Gate_Right",
    show_entergate = "物体/show_entergate",
    show_entercenter = "物体/show_entercenter",
    effect_boss_1 = "物体/effect_boss_1",
    effect_boss_1_02 = "物体/effect_boss_1_02",
    show_enterboss2 = "物体/show_enterboss2",
    CageDoor = "物体/CageDoor",
    effect_boss_2_sakura = "物体/effect_boss_2_sakura",
    boss_2_gate = "物体/boss_2_gate",
}

---@class 相机_挑战_玄阙地宫外层
local cameras = {
    camera_show01_01 = "相机/camera_show01_01",
    camera_show01_02 = "相机/camera_show01_02",
    boss_1_camera_1 = "相机/boss_1_camera_1",
    boss_1_camera_2 = "相机/boss_1_camera_2",
    boss_3_camera_1 = "相机/boss_3_camera_1",
    special_1_camera_1 = "相机/special_1_camera_1",
    special_1_camera_2 = "相机/special_1_camera_2",
    boss_2_camera_1 = "相机/boss_2_camera_1",
    boss_2_camera_2 = "相机/boss_2_camera_2",
    boss_2_camera_3 = "相机/boss_2_camera_3",
    boss_2_camera_4 = "相机/boss_2_camera_4",
    boss_2_guide_1 = "相机/boss_2_guide_1",
    boss_2_guide_2 = "相机/boss_2_guide_2",
    boss_2_guide_3 = "相机/boss_2_guide_3",
    boss_2_guide_4 = "相机/boss_2_guide_4",
    boss_2_guide_5 = "相机/boss_2_guide_5",
    boss_2_camera_5 = "相机/boss_2_camera_5",
}

---@class 位置_挑战_玄阙地宫外层
local pos = {
    start = "位置/start",
    gate_Right_Open = "位置/gate_Right_Open",
    gate_Left_Open = "位置/gate_Left_Open",
    boss_1_RetryPos = "位置/boss_1_RetryPos",
    boss_2_3_RetryPos = "位置/boss_2_3_RetryPos",
    enemy_1_RetryPos = "位置/enemy_1_RetryPos",
    show_01_npc_1_1 = "位置/show_01_npc_1_1",
    show_01_npc_2_1 = "位置/show_01_npc_2_1",
    gate_left_StartPos = "位置/gate_left_StartPos",
    gate_right_StartPos = "位置/gate_right_StartPos",
    boss_1_Show_StartPos = "位置/boss_1_Show_StartPos",
    boss_1_Show_LandPos = "位置/boss_1_Show_LandPos",
    boss_1_Show_FirePos = "位置/boss_1_Show_FirePos",
    boss_2_Show_EndPos = "位置/boss_2_Show_EndPos",
    special_talk_Pos = "位置/special_talk_Pos",
    boss_2_Show_DashPos = "位置/boss_2_Show_DashPos",
    boss_2_Show_FinalPos = "位置/boss_2_Show_FinalPos",
    boss_2_Show_zhujue_FinalPos = "位置/boss_2_Show_zhujue_FinalPos",
}

---@class 资产_挑战_玄阙地宫外层
local assets = {
}

---@class 动作_挑战_玄阙地宫外层
local animationClips = {
}

---@class 挑战_玄阙地宫外层
local context = {
    npcs = npcs,
    objs = objs,
    cameras = cameras,
    pos = pos,
    assets = assets,
    animationClips = animationClips,
    sapi = s,
    flags = flags,
}

return context
