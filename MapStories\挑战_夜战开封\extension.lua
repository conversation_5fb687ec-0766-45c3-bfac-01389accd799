---@type flags_挑战_夜战开封
local flags = require("MapStories/挑战_夜战开封/flags_挑战_夜战开封")
local s = require("StoryApi")

s.loadFlags(flags)


---@class 挑战_夜战开封_StringItemData
local stringItemData = {
}

---@class 挑战_夜战开封_ItemData
local itemData = {
    
}

local stringWidget = StringWidget:new(stringItemData,flags)
local itemWidget = ItemWidget:new(itemData,flags)

local stringWidgetInfo = {
    
}

local itemWidgetInfo = {
    
}

extensions = {
    useWidgets = {},
    widgets = {
        ItemWidgetInfo = itemWidgetInfo,
        StringWidgetInfo = stringWidgetInfo
    }
}

s.syncExtendsInfo(extensions)
return extensions