
---@type 主线_鹿鸣天下5_3
local context = require("MapStories/主线_鹿鸣天下5_3/scene_主线_鹿鸣天下5_3_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local assets = context.assets
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger


s.loadFlags(context.flags)
---@type flags_主线_鹿鸣天下5_3
local flags = context.flags 
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

--每次载入调用
function start()
    s.animState(npcs.changguai3,roleAnims.BDie,true)
    s.animState(npcs.changguai4,roleAnims.BDie,true)
    s.setPos(npcs.zhujue,pos.pos_start)
    s.playMusic("雾染辛夷")
    s.readyToStart(false,0.2)

    s.playTimeline(assets.lmtx5_3_1,false)
    --s.closeBlackPanel()
    s.lightScreen() 

    s.cameraAsync(cameras.cam_main,true,true,blendHintEnum.Cut,0,true,true)
    s.setActive(npcs.zhugelu_timeline,false)
end

function talk_2()
    s.setActive(objs.talk_2,false)
    s.setActive(npcs.zhujue,false)
    s.playTimeline(assets.lmtx5_3_2,false)
    --s.closeBlackPanel()
    s.lightScreen()

    s.setActive(npcs.zhugelu_timeline,false)
    s.setActive(npcs.zhujue,true)
    s.camera(cameras.cam_main,true,true,blendHintEnum.Cut,0,true,true)
end

function talk_3()
    s.setActive(objs.talk_3,false)
    s.playTimeline(assets.lmtx5_3_3,false)
    s.setActive(npcs.zhugelu_avg,false)
    s.playSound("sfx","水滴声",1,1,1,false)
    --s.setPlayer("诸葛鹿_拳套_拳套密室")
    s.camera(cameras.cam_backroom_start,true,true,blendHintEnum.Cut,1,true,true)
    s.setPos(npcs.zhujue,pos.pos_backroom)
    s.lightScreen() 

    s.camera(cameras.cam_main,true,true,blendHintEnum.EaseInOut,2,true,true)
end

function shitai()
    s.setActive(objs.shitai_fx,false)
    s.playSound("ui","button4",0,1,0)
    s.talk(npcs.zhujue,"这石台的大小和形制，是用来盛放某样东西的？")
    s.talk(npcs.unknownNPC,"要不你看看自己手上戴着什么？")
    s.talk(npcs.zhujue,"拳套？！什么时候我戴上了这东西……","惊讶")
    s.talk(npcs.unknownNPC,"唉，傻小子，什么时候才能弄清楚情况……")

    flags.research=flags.research+1
    end_research()
end

function door()
    s.setActive(objs.door_left_fx,false)
    s.playSound("ui","button4",0,1,0)
    s.soloAnimState(npcs.zhujue,roleAnims.BDeny,true)
    s.talk(npcs.zhujue,"这大门……推不动，也没有找到开关。难道是在另一面？")
    s.talk(npcs.zhujue,"如果将全身内力集中于拳上，能否将其击破？")
    s.soloAnimState(npcs.zhujue,roleAnims.BDeny,false)
    s.talk(npcs.unknownNPC,"别别别！此门乃以山中精石所制，若非开天之力绝不可能伤其分毫！")
    s.talk(npcs.unknownNPC,"你伤着自己就算了，别把我给搭上！")

    flags.research=flags.research+1
    end_research()
end

function cave()
    s.playSound("ui","button4",0,1,0)
    s.setActive(objs.cave_1,false)
    s.setActive(objs.cave_2,false)
    s.playSound("sfx","老鼠叫声")
    s.talk(npcs.zhujue,"这里的笼子怎么都是打开的……","惊讶")
    s.talk(npcs.zhujue,"我还是赶快寻找离开的路比较好。")

    flags.research=flags.research+1
    end_research()
end

function end_research()
    if flags.research==3 then
        s.playTimeline(assets.lmtx5_3_4,true)
        s.setNewbieFlags("chapter_lmtx_20_finished",1)
        s.finishInfiniteStory(true)
    end
end