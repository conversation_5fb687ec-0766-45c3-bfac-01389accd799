
---@type 主线_长生劫5_屋内搜索
local context = require("MapStories/主线_长生劫5/scene_主线_长生劫5_屋内搜索_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_主线_长生劫5
local flags = context.flags
--每次载入调用
--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end

function start()
    s.camera(cameras.cam_main,true,true,blendHintEnum.Cut,0,true,true)
    s.playMusic("ruin")
    s.setPos(npcs.zhujue,pos.pos_start)
    s.readyToStart()
    
    s.talk(npcs.zhujue, "裘地仙退走了……")
    s.talk(npcs.zhujue, "既然如此，便趁此机会看看你是何方神圣！")
    s.addTask(505001,1)
end

function TriggerLetter()
    s.setActive(objs.Letter,false) -- 关闭触发器

    s.talk(npcs.zhujue, "信？")
    s.talk(npcs.zhujue, "信中内容：老爷，近时府中风波连连，先是九夫人不幸流产，接而二夫人病夭。值此时，三公子趁守灵之机，悄然离家。据手下人查探，三公子应是往无忧村而去。其此举，背离孝道，疑或已生歹心，故请老爷戒备为先。")
    s.talk(npcs.zhujue, "这个老爷应该指的就是……")
    local ret = s.selectTalk(npcs.zhujue, "这个老爷应该指的就是……", {"村长","裘地仙"})
        if(ret == 0) then
            s.talk(npcs.zhujue, "村长的信怎会在裘地仙卧榻之上。")
            s.talk(npcs.zhujue, "村里也没人提及地仙同时担任了村长。")
        else
            s.talk(npcs.zhujue, "嗯，没想到他在这里做地仙，在别处做老爷。")
        end
    s.talk(npcs.zhujue, "信中提及“九夫人”、“二夫人”、“三公子”，说明裘地仙……")
    local ret = s.selectTalk(npcs.zhujue, "信中提及“九夫人”、“二夫人”、“三公子”，说明裘地仙……", {"家族庞大","妻儿成群"})
        if(ret == 0) then
            s.talk(npcs.zhujue, "至少娶了九位夫人，育有三个儿子，呵。")
        else
            s.talk(npcs.zhujue, "这样的祸害竟也为人夫，为人父。")
        end
    s.talk(npcs.zhujue, "这三公子离家，写信之人却叫裘地仙戒备，说明……")
    local ret = s.selectTalk(npcs.zhujue, "这三公子离家，写信之人却叫裘地仙戒备，说明……", {"父子不和","此事危急"})
        if(ret == 0) then
            s.talk(npcs.zhujue, "若非一丘之貉，怎会敬如此禽兽为父。")
        else
            s.talk(npcs.zhujue, "也许这三公子有些本事，或因其是亲近之人，所以更加危险难防。")
        end
    s.talk(npcs.zhujue, "还有，这封信是从何处寄来的？")
    local ret = s.selectTalk(npcs.zhujue, "还有，这封信是从何处寄来的？", {"村郊","江陵"})
        if(ret == 0) then
            s.talk(npcs.zhujue, "……不对。")
            s.talk(npcs.zhujue, "这个地方有人曾经提过，“江陵城里有一座员外府，金碧辉煌。”")
            s.talk(npcs.zhujue, "裘地仙的老巢，在江陵。")
        else
            s.talk(npcs.zhujue, "“江陵城里有一座员外府，金碧辉煌。”原来那个青年早就发现了裘地仙的秘密。")
            s.talk(npcs.zhujue, "他的老巢，在江陵。")
        end
    
    flags.FinishTriggerLetter = 1

    TriggerJudgeTask()
end

function TriggerAQiao()
    s.setActive(objs.AQiao,false) -- 关闭触发器

    s.animState(npcs.zhujue,roleAnims.BSquat_Loop)
    s.talk(npcs.zhujue, "！！")
    s.talk(npcs.zhujue, "胸口那一掌没有要她的命，但她脖子处有一处新伤，颈骨被……")
    s.talk(npcs.zhujue, "是趁着烟雾障眼时，对她下手了吗……")
    s.triggerReset(npcs.zhujue)
    local ret = s.selectTalk(npcs.zhujue, "是趁着烟雾障眼时，对她下手了吗……", {"抱歉……","我会为你报仇!","你信他，他却丝毫不信你。","（不发一语）"})
        if(ret == 0) then
            s.talk(npcs.zhujue, "我不杀你，你却因我而死。")
        elseif(ret == 1) then
            s.talk(npcs.zhujue, "你放心。")
        elseif(ret == 2) then
            s.talk(npcs.zhujue, "甚至临行前还要堵死你的生路。")
        else
            s.talk(npcs.zhujue, "逝者已矣。")
        end
    
    flags.FinishTriggerAQiao = 1

    TriggerJudgeTask()
end

function TriggerDogHole()
    s.setActive(objs.DogHole,false) -- 关闭触发器

    s.camera(cameras.LensDogHole,true)
    s.talk(npcs.zhujue, "为逝去爱犬而留……一派胡言。")
    s.talk(npcs.zhujue, "怕是辟谷时便是以此通道送来饭食，以这种低劣的手段来欺骗……")
    s.talk(npcs.zhujue, "……")
    s.talk(npcs.zhujue, "原来你是从这儿逃走的，裘地仙。")
    local ret = s.selectTalk(npcs.zhujue, "原来你是从这儿逃走的，裘地仙。", {"屋顶","狗洞"})
        if(ret == 0) then
            s.talk(npcs.zhujue, "不，他的轻功没有那么好，裘地仙正是从眼前的狗洞爬出去的。")
        else
            s.talk(npcs.zhujue, "不错。")
        end
    s.talk(npcs.zhujue, "这狗洞窄小，以他的身材断不可通过，他能从这个狗洞爬出，只因……")
    local ret = s.selectTalk(npcs.zhujue, "这狗洞窄小，以他的身材断不可通过，他能从这个狗洞爬出，只因……", {"他的武学","有机关"})
        if(ret == 0) then
            s.talk(npcs.zhujue, "是，他的武学……很特殊。")
        else
            s.talk(npcs.zhujue, "这附近没有什么机关。")
            s.talk(npcs.zhujue, "……是他的武学，他这门功夫很特殊")
        end
    s.talk(npcs.zhujue, "父亲的书中，记载了一种功夫，乃内家绝技，能使身形忽大忽小，穿墙过洞。")
    s.talk(npcs.zhujue, "据传此功修习艰难，需自幼入门，又看资质天赋。")
    s.talk(npcs.zhujue, "即使三岁便开始，半数会患骨病，身形不再生长，能无恙练成者，屈指可数。")
    s.talk(npcs.zhujue, "这门功夫正是……")
    local ret = s.selectTalk(npcs.zhujue, "这门功夫正是……", {"错骨手","缩骨功","白骨爪"})
        if(ret == 0) then
            s.talk(npcs.zhujue, "不，并非内家绝技，能让人控制全身筋骨，改变身型的秘术，正是……")
            s.talk(npcs.zhujue, "缩骨功。")
        elseif(ret == 1) then
            s.talk(npcs.zhujue, "原本以为这种武学只存在于传闻之中，没想到今日还能亲眼得见。")
        else
            s.talk(npcs.zhujue, "不，并非内家绝技，能让人控制全身筋骨，改变身型的秘术，正是……")
            s.talk(npcs.zhujue, "缩骨功。")
        end
    s.talk(npcs.zhujue, "如此一来，许多事情便能说得通。")
    s.talk(npcs.zhujue, "他只需以闭关修行为由，便可趁机由狗洞外出。")
    s.talk(npcs.zhujue, "呵，地仙？不过是一条丧家之犬。")
    s.talk(npcs.zhujue, "借由缩骨功能做出一夜之间返老还童的骗局吗？")
    s.talk(npcs.zhujue, "如果是那样……不，再想想。")
    s.camera(cameras.LensDogHole,false)
    
    flags.FinishTriggerDogHole = 1

    TriggerJudgeTask()
end

function TriggerJudgeTask()
    if(flags.FinishTriggerLetter == 1 and flags.FinishTriggerAQiao == 1 and flags.FinishTriggerDogHole == 1) then
        s.runAvgTag("游历场景/主线_长生劫/scene_主线_长生劫5_屋内搜索","开始")
        s.setNewbieFlags("chapter_csj_17_finished",1)
        s.finishInfiniteStory(true)


    end
end