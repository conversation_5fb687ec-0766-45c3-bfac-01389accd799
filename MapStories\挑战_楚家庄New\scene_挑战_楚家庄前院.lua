
---@type 挑战_楚家庄前院
local context = require("MapStories/挑战_楚家庄New/scene_挑战_楚家庄前院_h")

local npcs = context.npcs
local objs = context.objs
local cameras = context.cameras
local pos = context.pos
local animationClips = context.animationClips
local s = context.sapi
---@type RoleTrigger
local roleAnims = RoleTrigger
---@type ObjectTrigger
local objAnims = ObjTrigger

s.loadFlags(context.flags)
---@type flags_挑战_楚家庄New
local flags = context.flags

---@type RpgMapApi
local rapi = require("RpgMapApi")

local rpgFlags = require("RpgMap/flags")
rapi.loadFlags(rpgFlags)

local extensions = require 'MapStories/挑战_楚家庄New/extension'
---@type StringWidget
local stringWidget = extensions.widgets.StringWidgetInfo.widget
local stringItemData = extensions.widgets.StringWidgetInfo.items

--不可省略，用于外部获取flags
function getFlags(...)
    return flags
end


--每次载入调用
function start()
    local isTeleport = false
    --如果是传送，就不能setPos
    if(args ~= nil and args:ContainsKey("IsTransport"))then
        isTeleport = true

    end

    --各重复演出准备
    s.playMusic("tense")
    s.animState(npcs.boss1, "BSquat_Loop",true)
    s.animState(npcs.npc_4, "BDie",true)
    s.animState(npcs.npc_5, "BDie",true)
    s.animState(npcs.npc_6, "BDie",true)
    s.animState(npcs.npc_7, "BDie",true)

    --一次性内容
    if(s.getChallengeMapPersistentFlag("挑战_楚家庄New","播放过开场演出") == 0)then
        --开场演出准备
        s.camera(cameras.overall, true)
        s.setPos(npcs.zhujue, pos.pre_start)
    else
        --如果不是传送，设置主角到初始位置
        if(not isTeleport)then
            s.setPos(npcs.zhujue, pos.start)
        end
    end
    if(s.getChallengeMapPersistentFlag("挑战_楚家庄New","播放过进门对话演出") == 0)then
        --激活进门对话演出触发器
        s.setActive(objs.enterHint1,true)
    end
    
    --启用支线任务，神秘救兵
    if s.getCurrentTaskStep("神秘救兵") == "findShenMiXiaKe" then
        s.setActive(objs.trigger_em,true)
        s.setActive(npcs.npc_em,true)
        s.animState(npcs.npc_em, "BHurt_Loop",true)
    end

    local herb1Count = s.getRpgMapFlag("yfxf_get_medical1")

    if s.getCurrentTaskStep("药方修复") == "getMedical" and herb1Count == 0 then
        s.setActive(objs.trigger_medicinal_1,true)
        s.setActive(objs.medicinal_1,true)
    end

    --处理断线重连
    InitDungeon()

    s.readyToStart()

    --一次性内容
    if(s.getChallengeMapPersistentFlag("挑战_楚家庄New","播放过开场演出") == 0)then
        --播放开场演出
        s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_楚家庄_首次门口对话_1", nil, 0)
        s.cameraAsync(cameras.overall, false,true,blendHintEnum.EaseInOut,1.5,true,false)
        s.wait(1)
        s.moveAsync(npcs.zhujue,pos.start,5,true)
        s.setActive(npcs.npc_1,true)
        s.setActive(npcs.npc_2,true)
        s.setActive(npcs.npc_3,true)
        s.agentMoveToAsync(npcs.npc_1,pos.pos_npc_1,5,5)
        s.wait(0.2)
        s.agentMoveToAsync(npcs.npc_2,pos.pos_npc_2,5,5)
        s.wait(0.3)
        s.agentMoveToAsync(npcs.npc_3,pos.pos_npc_3,5,5)
        s.wait(3.5)
        s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_楚家庄_首次门口对话_2", nil, 0)
        s.setActive(npcs.npc_1,false)
        s.setActive(npcs.npc_2,false)
        s.setActive(npcs.npc_3,false)
        s.setChallengeMapPersistentFlag("挑战_楚家庄New","播放过开场演出",1)
    end

    --添加副本内主线任务
    if not s.hasTaskGroup("楚家庄副本内")then
        s.setTaskStep("楚家庄副本内","beatBoss")
    end
end

function InitDungeon()
    --处理boss1
    if(flags.boss1 == 1)then
        s.setActive(npcs.boss1,false)
        enemyRunAway()
    end
end

--进门对话演出
function enterMapHintTrigger()
    s.setActive(objs.enterHint1,false)
    s.appendAsyncAside(npcs.zhujue,0,"庄里当下遍布着打劫的匪徒，遇上了不免有一番搏斗。","","默认")
    s.setChallengeMapPersistentFlag("挑战_楚家庄New","播放过进门对话演出",1)
end

--通用小怪战斗后演出
function enemyBattle(id)
    local isWin,bResult = s.challengeMapBattle(id)
    if(isWin)then
        --播放演出对话
        s.wait(0.5)
        finalBossLvUp()
    end
end

--击败小怪后邪魔道人对话演出
function finalBossLvUp()
    local wordTb = {
        "哼，哪来的小子，有趣的很……\n",
        "你这小子，不容小觑啊。\n",
        "来，让我多多观察下你的招式——\n",
        "嘿嘿嘿，功夫还算得上有一手！\n",
        "让我这些手下，再陪你多玩会！\n",
    }
    local word = wordTb[math.random(1,5)]
    s.appendAsyncAside(npcs.shadow,0,word,"","默认")
end

--触发boss1战斗
function boss1()
    s.moveAsync(npcs.zhujue,pos.boss1Pos,5,true)
    s.camera(cameras.boss1_1,true,true,blendHintEnum.EaseInOut,1,true,true)
    s.turnTo(npcs.zhujue, npcs.boss1,true)
    s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_楚家庄_Boss1战前对话_1", nil, 0)
    s.talk(npcs.boss1, "……")
    s.animState(npcs.boss1, "BSquat_Loop",false)
    s.wait(0.5)
    s.turnTo(npcs.boss1, npcs.zhujue,true)
    s.camera(cameras.boss1_1,false)
    s.camera(cameras.boss1_2,true,true,blendHintEnum.EaseInOut,0.5,true,true)
    s.animState(npcs.boss1, "Special_1",true)
    s.wait(1)
    s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_楚家庄_Boss1战前对话_2", nil, 0)
    s.camera(cameras.boss1_2,false)

    local isWin,bResult = s.challengeMapBattle("15")
    if(isWin)then
        s.setDeath(npcs.boss1,3)
        
        flags.boss1 = 1

        --下发一次性奖励
        if(s.hasTask(992002,1))then
            s.finishTask(992002,1)
        end

        --清除此场景所有小怪
        enemyRunAway()
        s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_楚家庄_Boss1击败触发逃跑对话", nil, 0)

        --播放对话演出
        s.wait(0.5)
        finalBossLvUp()

        --设置天书flag，记录BOSS
        s.setGuideBookFlag("boss_fengjianke")
    else
        s.setPos(npcs.boss1, pos.pos_boss1)
        s.animState(npcs.boss1, "BSquat_Loop",true)
    end
end

function enemyRunAway()
    for k,v in pairs(npcs) do
        if(string.find(k,"enemy") ~= nil and flags[k] ~= nil)then
            --只处理未击败的敌人
            if(flags[k] == 0 or flags[k] == -1)then
                ---@diagnostic disable-next-line: inject-field-fail
                flags[k] = -1
                s.setActive(v,false)
            end
        end
    end
end

--传送到楚家庄中庭
function transPortToMiddle1()
    if(flags.boss1 == 0) then
        s.runAvgTag("副本/副本_楚家庄/副本_楚家庄", "副本_楚家庄_Boss未击败触发传送对话", nil, 0)
        return
    end
    s.changeMap("Assets/GameScenes/游历场景/挑战_楚家庄New/挑战_楚家庄中庭.unity", "位置/start")
end

--副本支线1，神秘救兵
function findEm()
    s.setActive(objs.trigger_em,false)
    require("RpgMap/任务线/神秘救兵").smjb_at_cjz_smxk1()
    s.animState(npcs.npc_em, "BHurt_Loop",false,false,0,false,false,0,false)
    s.setDeath(npcs.npc_em)
    s.wait(1.5)
    s.animState(npcs.zhujue, "BSquat_Loop",true)
    s.wait(1.5)
    require("RpgMap/任务线/神秘救兵").smjb_at_cjz_smxk2()
    s.animState(npcs.zhujue, "BSquat_Loop",false)

    if s.getCurrentTaskStep("神秘救兵") == "findShenMiXiaKe" then
        s.setTaskStep("神秘救兵","feedBackYaHuan")
    end
end

--副本支线2，药方修复
function findMedicinal()
    require("RpgMap/任务线/药方修复").yfxf_getFirst()
    local herbCount = s.getRpgMapFlag("yfxf_get_medical_count")

    --采集数量自增
    herbCount = herbCount + 1
    s.setRpgMapFlag("yfxf_get_medical_count",herbCount)
    --设置1号药材为已采集状态
    s.setRpgMapFlag("yfxf_get_medical1",1)

    if herbCount >= 3 then
        require("RpgMap/任务线/药方修复").judge_medical_count()

        if s.getCurrentTaskStep("药方修复") == "getMedical" then
            s.setTaskStep("药方修复","feedBackLaoPu")
        end
    end

    s.setActive(objs.trigger_medicinal_1,false)
    s.setActive(objs.medicinal_1,false)
end