local bcapi = require("BattleClientA<PERSON>")
local battle = args["battle"]
bcapi.battle = battle

function init()
end

function start()
    bcapi.add_timer_trigger(0, SecondEnemys)
    bcapi.add_timer_trigger(0.3, TalkStart)
end

function TalkStart()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_醉花荫_诗人#副本_醉花荫_诗人", "难得与高手交战，你们两个把握机会，用心感悟！")
        bcapi.talk("副本_醉花荫_诗人_弟子#副本_醉花荫_诗人_弟子", "是，老师！")
        bcapi.talk("副本_醉花荫_诗人#副本_醉花荫_诗人", "哈哈哈哈，几位贵客请出招！")
        bcapi.resume_and_show_ui()
    end)
end

function SecondEnemys()
    bcapi.async_call(function()
        bcapi.create_join_role_with_card("副本_醉花荫_诗人_弟子", 73, bcapi.get_team(1), bcapi.Vector2(5, 2), 0.1)
        bcapi.create_join_role_with_card("副本_醉花荫_诗人_弟子", 73, bcapi.get_team(1), bcapi.Vector2(5, -2), 0.1)
    end)
end