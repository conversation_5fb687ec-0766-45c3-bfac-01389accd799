local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)
function init()
    local TeamPct = 100
    local EnemyCnt = 0

    if(bcapi.get_team_dict_has_key(0, "迷宫队伍状态"))then
        TeamPct = bcapi.get_team_dict_int_value(0, "迷宫队伍状态", TeamPct)
    end
    if(bcapi.get_team_dict_has_key(0, "敌人数量"))then
        EnemyCnt = bcapi.get_team_dict_int_value(0, "敌人数量", EnemyCnt)
    end
    --将队伍内的所有人的HP设置为TeamPct的百分比
    for i = 0, team.RoleList.Count - 1 do
        local role = team.RoleList[i]
        role.Stat.Hp = role.Attr.MaxHp * TeamPct/100
    end

    
end

function start()
    local EnemyCnt = 0
    if(bcapi.get_team_dict_has_key(0, "敌人数量"))then
        EnemyCnt = bcapi.get_team_dict_int_value(0, "敌人数量", EnemyCnt)
    end
    --根据敌人的数量设置初始敌人数量和援军数量
    if(EnemyCnt > 0)then
        for i=0, EnemyCnt-1 do
            bcapi.add_timer_trigger(12*(i+1), JoinEnemy)
        end
    end
end

function JoinEnemy()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情江湖人士_和尚", 30, eTeam, bcapi.Vector2(2, -1), 0.1)
        bcapi.create_join_role("剧情江湖人士_和尚", 30, eTeam, bcapi.Vector2(4, 2), 0.1)
        bcapi.create_join_role("剧情江湖人士_和尚", 30, eTeam, bcapi.Vector2(2, 1), 0.1)
        bcapi.create_join_role("剧情江湖人士_和尚", 30, eTeam, bcapi.Vector2(4, -2), 0.1)
    end)
end