---鹿鸣天下
---@type ChapterApi
local capi = require("ChapterApi")

---@type StageMapApi
local stageApi = require("StageMapApi")

function quick_play_avg()
    local avgKey = args:get_Item("avgKey")
    local avg = require("AVGApi")
    avg.run(avgKey) 
end

s = require("StoryApi")

local avg = require("AVGApi")

function playAvgAndCustomBattle()
    innerPlayAvg(args:get_Item("avgKey"))
    newbieApi.battle(args:get_Item("battleKey"), true)
    capi.pass(7)
end

function quick_play_avg()
    local avgKey = args:get_Item("avgKey")
    local avg = require("AVGApi")
    avg.run(avgKey) 
end

function quick_map_story()
    local mapStoryKey = args:get_Item("mapStoryKey")
    if(capi.isGameMapRunning()) then
        s.changeMapStory(mapStoryKey)
    else
        capi.mapStory(mapStoryKey,false)
    end
end

function quick_play_timeline()
    local timelineKey = args:get_Item("timelineSceneKey")
    s.playTimelineScene("Assets/GameScenes/动画演出场景/" .. timelineKey)
end


function on_unlock()
    newbieApi.playVideo("CriAtom/蜃景-full.usm")
end

-- 从记忆碎片画廊点击执行,注意这里是纯演出,不应该包含任何值的变化
function lmtx_01()
    quick_play_avg()
end

function lmtx_02()
    quick_play_avg()
end

function lmtx_03()
    quick_play_avg()
end

function lmtx_04()
    quick_play_avg()
end

function lmtx_05()
    quick_play_avg()
end

function lmtx_06()
    quick_play_avg()
end

function lmtx_07()
    quick_play_avg()
end

function lmtx_08()
    quick_play_avg()
end

function lmtx_09()
    quick_play_avg()
end

function lmtx_10()
    quick_play_avg()
end             

function lmtx_11()
    quick_play_avg()
end

function lmtx_12()
    quick_play_avg()
end 

function lmtx_13()
    quick_play_avg()
end

function lmtx_14()
    quick_play_avg()
end

function lmtx_15()   
    quick_play_avg()
end

function lmtx_16()
    quick_play_avg()
end

function lmtx_17()
    quick_play_avg()
end

function lmtx_18()
    quick_play_avg()
end

function lmtx_19()
    quick_play_avg()
end

function lmtx_20()
    quick_map_story()
end

function lmtx_21()
    quick_play_avg()
end

function lmtx_22()
    quick_play_avg()
end

function lmtx_23()
    quick_play_avg()
end

function lmtx_24()
    quick_play_avg()
end

function lmtx_25()
    quick_play_avg()
end

function lmtx_26()
    quick_play_avg()
end

function lmtx_27()
    quick_play_avg()
end

function lmtx_28()
    quick_play_avg()
end


return {
    lmtx_01 = lmtx_01,
    lmtx_02 = lmtx_02,
    lmtx_03 = lmtx_03,
    lmtx_04 = lmtx_04,
    lmtx_05 = lmtx_05,
    lmtx_06 = lmtx_06,
    lmtx_07 = lmtx_07,
    lmtx_08 = lmtx_08,
    lmtx_09 = lmtx_09,
    lmtx_10 = lmtx_10,
    lmtx_11 = lmtx_11,
    lmtx_12 = lmtx_12,
    lmtx_13 = lmtx_13,
    lmtx_14 = lmtx_14,
    lmtx_15 = lmtx_15,
    lmtx_16 = lmtx_16,
    lmtx_17 = lmtx_17,
    lmtx_18 = lmtx_18,
    lmtx_19 = lmtx_19,
    lmtx_20 = lmtx_20,
    lmtx_21 = lmtx_21,
    lmtx_22 = lmtx_22,
    lmtx_23 = lmtx_23,
    lmtx_24 = lmtx_24,
    lmtx_25 = lmtx_25,
    lmtx_26 = lmtx_26,
    lmtx_27 = lmtx_27,
    lmtx_28 = lmtx_28,
}