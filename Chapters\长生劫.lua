---椿岁之章
---@type ChapterApi
local capi = require("ChapterApi")

---@type NewbieApi
local newbieApi = require("NewbieApi") -- 不允许改名

---@type StageMapApi
local stageApi = require("StageMapApi")

---@type NewbieFlags
local flags = require("Newbie/flags")

s = require("StoryApi")

local avg = require("AVGApi")

function playAvgAndCustomBattle()
    innerPlayAvg(args:get_Item("avgKey"))
    newbieApi.battle(args:get_Item("battleKey"), true)
    capi.pass(7)
end

function quick_play_avg()
    local avgKey = args:get_Item("avgKey")
    local avg = require("AVGApi")
    avg.run(avgKey) 
end

function quick_map_story()
    local mapStoryKey = args:get_Item("mapStoryKey")
    if(capi.isGameMapRunning()) then
        s.changeMapStory(mapStoryKey)
    else
        capi.mapStory(mapStoryKey,false)
    end
end

function quick_play_timeline()
    local timelineKey = args:get_Item("timelineSceneKey")
    s.playTimelineScene("Assets/GameScenes/动画演出场景/" .. timelineKey)
end


function on_unlock()
    newbieApi.playVideo("CriAtom/蜃景-full.usm")
end

-- 从记忆碎片画廊点击执行,注意这里是纯演出,不应该包含任何值的变化
function csj_01()
    quick_play_avg()
end

function csj_02()
    quick_play_timeline()
end

function csj_03()
    quick_play_avg()
end

function csj_04()
    quick_play_avg()
end

function csj_05()
    quick_play_timeline()
end

function csj_06()
    quick_play_timeline()
end

function csj_07()
    quick_play_avg()
end

function csj_08()
    quick_play_avg()
end

function csj_09()
    quick_play_avg()
end

function csj_10()
    quick_map_story()
end             

function csj_11()
    quick_play_timeline()
end

function csj_12()
    quick_play_timeline()
end 

function csj_13()
    quick_play_avg()
end

function csj_14()
    quick_play_timeline()
end

function csj_15()
    quick_play_avg()
end

function csj_16()
    quick_play_timeline()
end

function csj_17()
    quick_map_story()
end

function csj_18()
    quick_play_avg()
end

function csj_19()
    quick_play_avg()
end

function csj_20()
    quick_play_avg()
end

function csj_21()
    quick_play_avg()
end

function csj_22()
    quick_play_avg()
end

return {
    csj_01 = csj_01,
    csj_02 = csj_02,
    csj_03 = csj_03,
    csj_04 = csj_04,
    csj_05 = csj_05,
    csj_06 = csj_06,
    csj_07 = csj_07,
    csj_08 = csj_08,
    csj_09 = csj_09,
    csj_10 = csj_10,
    csj_11 = csj_11,
    csj_12 = csj_12,
    csj_13 = csj_13,
    csj_14 = csj_14,
}