local bcapi = require("BattleClientApi")
---@type BattleParams
local battle = args["battle"]
bcapi.battle = battle
local team = bcapi.get_team(0)
local eTeam = bcapi.get_team(1)

function init()
    bcapi.ta_event_track("PlayerStartStory", "Step", "BattleTutor_Step3")

    battle.ForceNotEndBattle = true
    battle.HideCardLevel = true

    bcapi.disable_auto_battle()
    bcapi.disable_speed_up()
    --bcapi.disable_dart()

    -- bcapi.hide_ui("疗伤")
    -- bcapi.hide_ui("画布")
    bcapi.hide_ui("技能区")
    bcapi.hide_ui("怒气条")
    bcapi.hide_ui("角色技区")
    bcapi.hide_ui("策略技区")
    -- bcapi.hide_ui("战斗时间")
    -- bcapi.hide_ui("暂停按钮")
    bcapi.hide_ui("加速按钮")
    bcapi.hide_ui("自动按钮")
    bcapi.hide_ui("身法按钮")
    
    -- 开局创建角色
    CreateRoles()
end

function CreateRoles()
    bcapi.async_call(function()
        -- 创建玩家角色（不带绝招牌）
        bcapi.create_join_role_with_card("荆成", 5, team, bcapi.Vector2(-5, 0), 0)
        --给荆成加点攻速
        JingCheng = bcapi.get_player_role("荆成")
        ---@diagnostic disable-next-line
        JingCheng.Buff:AddBuff("通用_教学关_加攻速", 300, 1, 1, JingCheng)

        -- 创建敌人
        bcapi.create_join_role("剧情_青羽录1-2_师兄", 2, eTeam, bcapi.Vector2(5, 0), 0)
    end)
end

function start()
    Talk1()
    bcapi.add_timer_trigger(4.5, Talk2)
    bcapi.add_condition_trigger("[%c->fighting_enemy_count%][<]1", 0, EnemyDie)
end

function Talk1()
    bcapi.async_call(function()
        team.NuqiSystem.CurNuqi = 0
        bcapi.pause_and_hide_ui()
        bcapi.talk("荆成#荆成#惊讶", "……师兄？")
        bcapi.talk("荆成师父", "勿要分神！荆成！与你师兄过过招。")
        bcapi.resume_and_show_ui()
        bcapi.show_ui("技能区")
        bcapi.show_ui("怒气条")
        -- bcapi.show_ui("角色技区")
        bcapi.show_ui("策略技区")
    end)
end

function Talk2()
    bcapi.async_call(function()
        bcapi.pause()
        bcapi.talk("荆成师父", "你的招式太慢了！秋风扫叶，可是一片片慢慢扫的？！")
        bcapi.talk("荆成#荆成#思索", "师父是在提醒我用那招……「梧叶舞秋风」！")
        bcapi.show_strategy_guide("梧叶舞秋风", "荆成", "拖拽卡牌到荆成位置，然后松手释放", 1)
        

        --埋点
        bcapi.ta_event_track("player_start_story", "id", "221")
        bcapi.add_timer_trigger(5, Talk3)
    end)
end

function Talk3()
    bcapi.async_call(function()
        bcapi.pause()
        bcapi.talk("荆成师父", "很好，功夫总算是没有荒废。")
        bcapi.talk("荆成师父", "现在真气充盈着你的全身，是时候使出<color=red>师门绝学</color>了！")
        bcapi.show_ui("技能区")
        bcapi.show_ui("角色技区")
        bcapi.show_unique_skill_guide("荆成", "剧情_青羽录1-2_师兄", "拖拽绝招按钮至敌人身上，释放绝招「阴阳一气」")
    end)
end

function EnemyDie()
    bcapi.async_call(function()
        bcapi.create_join_role("剧情_青羽录1-2_师兄", 1, eTeam, bcapi.Vector2(3, 0), 0.1)
        bcapi.create_join_role("剧情_青羽录1-2_师兄远程", 1, eTeam, bcapi.Vector2(7, -2), 0.1)
        bcapi.create_join_role("剧情_青羽录1-2_师兄远程", 1, eTeam, bcapi.Vector2(7, 2), 0.1)

        bcapi.wait(1)
        bcapi.pause()
        bcapi.talk("荆成师父", "你们几个，一起上。")
        bcapi.talk("荆成#荆成#严肃", "各位，请赐教。")
        bcapi.add_strategy_card("大藏心经", 1, team)
        bcapi.add_strategy_card("吐纳法", 1, team)
        bcapi.wait(0.1)        
        bcapi.show_ui("技能区")
        bcapi.resume()
        
        battle.ForceNotEndBattle = false
        bcapi.wait(0.2)
        bcapi.set_all_card_cd(team, 0)
        team.NuqiSystem.CurNuqi = 10
        
        bcapi.wait(2)
        
        bcapi.show_ui("身法按钮");
        bcapi.show_dart_guide("荆成", "剧情_青羽录1-2_师兄远程", "点击身法按钮，进入身法模式", "拖拽角色头像至敌人身边，释放身法")
        bcapi.wait(1)
        bcapi.show_strategy_guide("天罡真气", "荆成", "拖拽策略卡到荆成位置，然后松手释放", 1)
        bcapi.add_strategy_card("天罡真气", 1, team, true)
        bcapi.show_pop_info("灵活使用技能与绝招，击败敌人吧！!", 2)
    end)
end
