local bcapi = require("BattleClientApi")
local battle = args["battle"]
bcapi.battle = battle

function init()
    
end

function start()
    bcapi.add_timer_trigger(0, EnemyTalk)
    --bcapi.add_timer_trigger(2, Effects)
    -- bcapi.add_condition_trigger("[%enemy:挑战_风雪神道_狩狼士->hp_pct%][<=]0.7", 1, EnemyStage2)
    bcapi.add_condition_trigger("[%c->enemy:副本_风雪神道_狩狼士_机关人%][>=]1", 2, WudiHint)
end

function EnemyStage2()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("副本_风雪神道_狩狼士#进入第二阶段!")
        bcapi.resume_and_show_ui()
        -- bcapi.add_condition_trigger("[%enemy:挑战_风雪神道_狩狼士->hp_pct%][<=]0.4", 1, EnemyStage3)
    end)
end

function WudiHint()
    bcapi.async_call(function()
        local conditionRole = bcapi.get_index_role(0,0)
        conditionRole.Stat.BattleFlagDic:set_Item("禁止播放提示", "1")
        conditionRole.Stat.BattleFlagDic:set_Item("CMD__last_setted_flag", "禁止播放提示")
        -- bcapi.pause_and_hide_ui()
        -- bcapi.aside("机关人存活期间，狩狼士将处于无敌状态!")
        -- bcapi.resume_and_show_ui()
        bcapi.show_pop_info("机关人存活期间，狩狼士将处于无敌状态!", 8)
        bcapi.add_condition_trigger("[%c->enemy:副本_风雪神道_狩狼士_机关人%][<=]0", 0, WudiFlagClear)
    end)
end


function WudiFlagClear()
    bcapi.async_call(function()
        local conditionRole = bcapi.get_index_role(0,0)
        conditionRole.Stat.BattleFlagDic:set_Item("禁止播放提示", "0")
        conditionRole.Stat.BattleFlagDic:set_Item("CMD__last_setted_flag", "禁止播放提示")

        bcapi.add_condition_trigger("[%c->enemy:副本_风雪神道_狩狼士_机关人%][>=]1&[%c->flag:禁止播放提示%][<=]0", 2, WudiHint)
    end)
end

function EnemyTalk()
    bcapi.async_call(function()
        bcapi.pause_and_hide_ui()
        bcapi.talk("狩狼士#副本_风雪神道_狩狼士", "这——这是你们逼我的！")
        bcapi.resume_and_show_ui()
    end)
end